{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-pc-ui@4.9.29_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/button/index.js", "../../../../../node_modules/.pnpm/vxe-pc-ui@4.9.29_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-button/index.js"], "sourcesContent": ["import { VxeUI } from '@vxe-ui/core';\nimport VxeButtonComponent from './src/button';\nimport { dynamicApp } from '../dynamics';\nexport const VxeButton = Object.assign({}, VxeButtonComponent, {\n    install(app) {\n        app.component(VxeButtonComponent.name, VxeButtonComponent);\n    }\n});\ndynamicApp.use(VxeButton);\nVxeUI.component(VxeButtonComponent);\nexport const Button = VxeButton;\nexport default VxeButton;\n", "import VxeButton from '../button';\nexport * from '../button';\nexport default VxeButton;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAGO,IAAM,YAAY,OAAO,OAAO,CAAC,GAAG,gBAAoB;AAAA,EAC3D,QAAQ,KAAK;AACT,QAAI,UAAU,eAAmB,MAAM,cAAkB;AAAA,EAC7D;AACJ,CAAC;AACD,WAAW,IAAI,SAAS;AACxB,MAAM,UAAU,cAAkB;AAC3B,IAAM,SAAS;AACtB,IAAOA,kBAAQ;;;ACTf,IAAO,qBAAQC;", "names": ["button_default", "button_default"]}