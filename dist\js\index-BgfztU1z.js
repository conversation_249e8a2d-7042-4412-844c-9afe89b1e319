import{bS as V,bE as I,bW as U,aT as H}from"./bootstrap-DlHXJWd_.js";import{_ as L}from"./analytics-visits-source.vue_vue_type_script_setup_true_lang-DusSXKM2.js";import{_ as S}from"./avatar.vue_vue_type_script_setup_true_lang-CiaVJ8BJ.js";import{a4 as p,av as i,ab as n,x as a,aq as C,b2 as B,a7 as e,a8 as W,aa as v,ac as o,ai as f,aj as c,F as x,aC as b,aV as N,aB as l,P as M,a1 as E,bT as q}from"../jse/index-index-DYNcUVMZ.js";import{_ as $,a as k,b as w,c as y,d as A}from"./analysis-chart-card.vue_vue_type_script_setup_true_lang-C_d0KbGY.js";import"./use-echarts-DVa43yCy.js";const G={class:"card-box p-4 py-6 lg:flex"},P={key:0,class:"flex flex-col justify-center md:ml-6 md:mt-0"},R={key:0,class:"text-md font-semibold md:text-xl"},D={key:1,class:"text-foreground/80 mt-1"},F=p({name:"WorkbenchHeader",__name:"workbench-header",props:{avatar:{default:""}},setup(m){return(s,d)=>(n(),i("div",G,[a(e(S),{src:s.avatar,class:"size-20"},null,8,["src"]),s.$slots.title||s.$slots.description?(n(),i("div",P,[s.$slots.title?(n(),i("h1",R,[W(s.$slots,"title")])):C("",!0),s.$slots.description?(n(),i("span",D,[W(s.$slots,"description")])):C("",!0)])):C("",!0),d[0]||(d[0]=B('<div class="mt-4 flex flex-1 justify-end md:mt-0"><div class="flex flex-col justify-center text-right"><span class="text-foreground/80"> 待办 </span><span class="text-2xl">2/10</span></div><div class="mx-12 flex flex-col justify-center text-right md:mx-16"><span class="text-foreground/80"> 项目 </span><span class="text-2xl">8</span></div><div class="mr-4 flex flex-col justify-center text-right md:mr-10"><span class="text-foreground/80"> 团队 </span><span class="text-2xl">300</span></div></div>',1))]))}}),J={class:"flex items-center"},Q={class:"ml-4 text-lg font-medium"},K={class:"text-foreground/80 mt-4 flex h-10"},O={class:"text-foreground/80 flex justify-between"},X=p({name:"WorkbenchProject",__name:"workbench-project",props:{items:{default:()=>[]},title:{}},emits:["click"],setup(m){return(s,d)=>(n(),v(e(y),null,{default:o(()=>[a(e($),{class:"py-4"},{default:o(()=>[a(e(k),{class:"text-lg"},{default:o(()=>[f(c(s.title),1)]),_:1})]),_:1}),a(e(w),{class:"flex flex-wrap p-0"},{default:o(()=>[(n(!0),i(x,null,b(s.items,(t,r)=>(n(),i("div",{key:t.title,class:N([{"border-r-0":r%3===2,"border-b-0":r<3,"pb-4":r>2,"rounded-bl-xl":r===s.items.length-3,"rounded-br-xl":r===s.items.length-1},"border-border group w-full cursor-pointer border-r border-t p-4 transition-all hover:shadow-xl md:w-1/2 lg:w-1/3"])},[l("div",J,[a(e(V),{color:t.color,icon:t.icon,class:"size-8 transition-all duration-300 group-hover:scale-110",onClick:j=>s.$emit("click",t)},null,8,["color","icon","onClick"]),l("span",Q,c(t.title),1)]),l("div",K,c(t.content),1),l("div",O,[l("span",null,c(t.group),1),l("span",null,c(t.date),1)])],2))),128))]),_:1})]),_:1}))}}),Y=["onClick"],Z={class:"text-md mt-2 truncate"},tt=p({name:"WorkbenchQuickNav",__name:"workbench-quick-nav",props:{items:{default:()=>[]},title:{}},emits:["click"],setup(m){return(s,d)=>(n(),v(e(y),null,{default:o(()=>[a(e($),{class:"py-4"},{default:o(()=>[a(e(k),{class:"text-lg"},{default:o(()=>[f(c(s.title),1)]),_:1})]),_:1}),a(e(w),{class:"flex flex-wrap p-0"},{default:o(()=>[(n(!0),i(x,null,b(s.items,(t,r)=>(n(),i("div",{key:t.title,class:N([{"border-r-0":r%3===2,"border-b-0":r<3,"pb-4":r>2,"rounded-bl-xl":r===s.items.length-3,"rounded-br-xl":r===s.items.length-1},"flex-col-center border-border group w-1/3 cursor-pointer border-r border-t py-8 hover:shadow-xl"]),onClick:j=>s.$emit("click",t)},[a(e(V),{color:t.color,icon:t.icon,class:"size-7 transition-all duration-300 group-hover:scale-125"},null,8,["color","icon"]),l("span",Z,c(t.title),1)],10,Y))),128))]),_:1})]),_:1}))}}),et={class:"divide-border w-full divide-y",role:"list"},st={class:"flex min-w-0 items-center gap-x-4"},at={class:"min-w-0 flex-auto"},lt={class:"text-foreground text-sm font-semibold leading-6"},ot=["innerHTML"],nt={class:"hidden h-full shrink-0 sm:flex sm:flex-col sm:items-end"},rt={class:"text-foreground/80 mt-6 text-xs leading-6"},ct=p({name:"WorkbenchTodo",__name:"workbench-todo",props:{items:{default:()=>[]},title:{}},setup(m){return(s,d)=>(n(),v(e(y),null,{default:o(()=>[a(e($),{class:"py-4"},{default:o(()=>[a(e(k),{class:"text-lg"},{default:o(()=>[f(c(s.title),1)]),_:1})]),_:1}),a(e(w),{class:"flex flex-wrap p-5 pt-0"},{default:o(()=>[l("ul",et,[(n(!0),i(x,null,b(s.items,t=>(n(),i("li",{key:t.title,class:N([{"select-none line-through opacity-60":t.completed},"flex cursor-pointer justify-between gap-x-6 py-5"])},[l("div",st,[a(e(I),{checked:t.completed,"onUpdate:checked":r=>t.completed=r,name:"completed"},null,8,["checked","onUpdate:checked"]),l("div",at,[l("p",lt,c(t.title),1),l("p",{class:"text-foreground/80 *:text-primary mt-1 truncate text-xs leading-5",innerHTML:t.content},null,8,ot)])]),l("div",nt,[l("span",rt,c(t.date),1)])],2))),128))])]),_:1})]),_:1}))}}),it={class:"divide-border w-full divide-y",role:"list"},dt={class:"flex min-w-0 items-center gap-x-4"},ut={class:"min-w-0 flex-auto"},ft={class:"text-foreground text-sm font-semibold leading-6"},pt=["innerHTML"],mt={class:"hidden h-full shrink-0 sm:flex sm:flex-col sm:items-end"},_t={class:"text-foreground/80 mt-6 text-xs leading-6"},ht=p({name:"WorkbenchTrends",__name:"workbench-trends",props:{items:{default:()=>[]},title:{}},setup(m){return(s,d)=>(n(),v(e(y),null,{default:o(()=>[a(e($),{class:"py-4"},{default:o(()=>[a(e(k),{class:"text-lg"},{default:o(()=>[f(c(s.title),1)]),_:1})]),_:1}),a(e(w),{class:"flex flex-wrap p-5 pt-0"},{default:o(()=>[l("ul",it,[(n(!0),i(x,null,b(s.items,t=>(n(),i("li",{key:t.title,class:"flex justify-between gap-x-6 py-5"},[l("div",dt,[a(e(V),{icon:t.avatar,alt:"",class:"size-10 flex-none rounded-full"},null,8,["icon"]),l("div",ut,[l("p",ft,c(t.title),1),l("p",{class:"text-foreground/80 *:text-primary mt-1 truncate text-xs leading-5",innerHTML:t.content},null,8,pt)])]),l("div",mt,[l("span",_t,c(t.date),1)])]))),128))])]),_:1})]),_:1}))}}),gt={class:"p-5"},vt={class:"mt-5 flex flex-col lg:flex-row"},xt={class:"mr-4 w-full lg:w-3/5"},bt={class:"w-full lg:w-2/5"},Vt=p({__name:"index",setup(m){const s=U(),d=[{color:"",content:"不要等待机会，而要创造机会。",date:"2021-04-01",group:"开源组",icon:"carbon:logo-github",title:"Github",url:"https://github.com"},{color:"#3fb27f",content:"现在的你决定将来的你。",date:"2021-04-01",group:"算法组",icon:"ion:logo-vue",title:"Vue",url:"https://vuejs.org"},{color:"#e18525",content:"没有什么才能比努力更重要。",date:"2021-04-01",group:"上班摸鱼",icon:"ion:logo-html5",title:"Html5",url:"https://developer.mozilla.org/zh-CN/docs/Web/HTML"},{color:"#bf0c2c",content:"热情和欲望可以突破一切难关。",date:"2021-04-01",group:"UI",icon:"ion:logo-angular",title:"Angular",url:"https://angular.io"},{color:"#00d8ff",content:"健康的身体是实现目标的基石。",date:"2021-04-01",group:"技术牛",icon:"bx:bxl-react",title:"React",url:"https://reactjs.org"},{color:"#EBD94E",content:"路是走出来的，而不是空想出来的。",date:"2021-04-01",group:"架构组",icon:"ion:logo-javascript",title:"Js",url:"https://developer.mozilla.org/zh-CN/docs/Web/JavaScript"}],t=[{color:"#1fdaca",icon:"ion:home-outline",title:"首页",url:"/"},{color:"#bf0c2c",icon:"ion:grid-outline",title:"仪表盘",url:"/dashboard"},{color:"#e18525",icon:"ion:layers-outline",title:"组件",url:"/demos/features/icons"},{color:"#3fb27f",icon:"ion:settings-outline",title:"系统管理",url:"/demos/features/login-expired"},{color:"#4daf1bc9",icon:"ion:key-outline",title:"权限管理",url:"/demos/access/page-control"},{color:"#00d8ff",icon:"ion:bar-chart-outline",title:"图表",url:"/analytics"}],r=M([{completed:!1,content:"审查最近提交到Git仓库的前端代码，确保代码质量和规范。",date:"2024-07-30 11:00:00",title:"审查前端代码提交"},{completed:!0,content:"检查并优化系统性能，降低CPU使用率。",date:"2024-07-30 11:00:00",title:"系统性能优化"},{completed:!1,content:"进行系统安全检查，确保没有安全漏洞或未授权的访问。 ",date:"2024-07-30 11:00:00",title:"安全检查"},{completed:!1,content:"更新项目中的所有npm依赖包，确保使用最新版本。",date:"2024-07-30 11:00:00",title:"更新项目依赖"},{completed:!1,content:"修复用户报告的页面UI显示问题，确保在不同浏览器中显示一致。 ",date:"2024-07-30 11:00:00",title:"修复UI显示问题"}]),j=[{avatar:"svg:avatar-1",content:"在 <a>开源组</a> 创建了项目 <a>Vue</a>",date:"刚刚",title:"威廉"},{avatar:"svg:avatar-2",content:"关注了 <a>威廉</a> ",date:"1个小时前",title:"艾文"},{avatar:"svg:avatar-3",content:"发布了 <a>个人动态</a> ",date:"1天前",title:"克里斯"},{avatar:"svg:avatar-4",content:"发表文章 <a>如何编写一个Vite插件</a> ",date:"2天前",title:"Vben"},{avatar:"svg:avatar-1",content:"回复了 <a>杰克</a> 的问题 <a>如何进行项目优化？</a>",date:"3天前",title:"皮特"},{avatar:"svg:avatar-2",content:"关闭了问题 <a>如何运行项目</a> ",date:"1周前",title:"杰克"},{avatar:"svg:avatar-3",content:"发布了 <a>个人动态</a> ",date:"1周前",title:"威廉"},{avatar:"svg:avatar-4",content:"推送了代码到 <a>Github</a>",date:"2021-04-01 20:00",title:"威廉"},{avatar:"svg:avatar-4",content:"发表文章 <a>如何编写使用 Admin Vben</a> ",date:"2021-03-01 20:00",title:"Vben"}],z=H();function T(u){var _,h;if((_=u.url)!=null&&_.startsWith("http")){q(u.url);return}(h=u.url)!=null&&h.startsWith("/")?z.push(u.url).catch(g=>{console.error("Navigation failed:",g)}):console.warn(`Unknown URL for navigation item: ${u.title} -> ${u.url}`)}return(u,_)=>{var h;return n(),i("div",gt,[a(e(F),{avatar:((h=e(s).userInfo)==null?void 0:h.avatar)||e(E).app.defaultAvatar},{title:o(()=>{var g;return[f(" 早安, "+c((g=e(s).userInfo)==null?void 0:g.realName)+", 开始您一天的工作吧！ ",1)]}),description:o(()=>_[0]||(_[0]=[f(" 今日晴，20℃ - 32℃！ ")])),_:1},8,["avatar"]),l("div",vt,[l("div",xt,[a(e(X),{items:d,title:"项目",onClick:T}),a(e(ht),{items:j,class:"mt-5",title:"最新动态"})]),l("div",bt,[a(e(tt),{items:t,class:"mt-5 lg:mt-0",title:"快捷导航",onClick:T}),a(e(ct),{items:r.value,class:"mt-5",title:"待办事项"},null,8,["items"]),a(e(A),{class:"mt-5",title:"访问来源"},{default:o(()=>[a(L)]),_:1})])])])}}});export{Vt as default};
