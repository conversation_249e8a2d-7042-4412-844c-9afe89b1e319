import {
  getFuncText
} from "./chunk-JWKCLBKN.js";
import {
  defineVxeComponent,
  dynamicApp
} from "./chunk-KAYKK4JO.js";
import {
  require_xe_utils
} from "./chunk-RWHEUJNV.js";
import {
  VxeUI,
  createEvent,
  getConfig,
  getIcon,
  useSize
} from "./chunk-KJAC55GV.js";
import "./chunk-G6CDOZZI.js";
import {
  computed,
  h,
  inject,
  provide,
  reactive
} from "./chunk-ZCM5A7SR.js";
import "./chunk-WDDBQLJB.js";
import {
  __toESM
} from "./chunk-V4OQ3NZ2.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.9.29_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/radio/src/group.js
var import_xe_utils3 = __toESM(require_xe_utils());

// ../../node_modules/.pnpm/vxe-pc-ui@4.9.29_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/radio/src/radio.js
var import_xe_utils = __toESM(require_xe_utils());
var radio_default = defineVxeComponent({
  name: "VxeRadio",
  props: {
    modelValue: [String, Number, Boolean],
    checkedValue: {
      type: [String, Number, Boolean],
      default: void 0
    },
    title: [String, Number],
    content: [String, Number],
    disabled: {
      type: Boolean,
      default: null
    },
    name: String,
    strict: {
      type: Boolean,
      default: () => getConfig().radio.strict
    },
    size: {
      type: String,
      default: () => getConfig().radio.size || getConfig().size
    },
    /**
     * 已废弃，被 checkedValue 替换
     */
    label: {
      type: [String, Number, Boolean],
      default: null
    }
  },
  emits: [
    "update:modelValue",
    "change"
  ],
  setup(props, context) {
    const { slots, emit } = context;
    const $xeForm = inject("$xeForm", null);
    const formItemInfo = inject("xeFormItemInfo", null);
    const $xeRadioGroup = inject("$xeRadioGroup", null);
    const xID = import_xe_utils.default.uniqueId();
    const reactData = reactive({});
    const $xeRadio = {
      xID,
      props,
      context,
      reactData
    };
    const { computeSize } = useSize(props);
    const computeIsDisabled = computed(() => {
      const { disabled } = props;
      if (disabled === null) {
        if ($xeRadioGroup) {
          const { computeIsDisabled: computeIsDisabled2 } = $xeRadioGroup.getComputeMaps();
          return computeIsDisabled2.value;
        }
      }
      return disabled;
    });
    const computeName = computed(() => {
      return $xeRadioGroup ? $xeRadioGroup.name : props.name;
    });
    const computeStrict = computed(() => {
      return $xeRadioGroup ? $xeRadioGroup.props.strict : props.strict;
    });
    const computeChecked = computed(() => {
      const { label, checkedValue } = props;
      const radioValue = import_xe_utils.default.isUndefined(checkedValue) ? label : checkedValue;
      return $xeRadioGroup ? $xeRadioGroup.props.modelValue === radioValue : props.modelValue === radioValue;
    });
    const handleValue = (checkedValue, evnt) => {
      if ($xeRadioGroup) {
        $xeRadioGroup.handleChecked({ label: checkedValue, checkedValue }, evnt);
      } else {
        emit("update:modelValue", checkedValue);
        dispatchEvent("change", { value: checkedValue, label: checkedValue, checkedValue }, evnt);
        if ($xeForm && formItemInfo) {
          $xeForm.triggerItemEvent(evnt, formItemInfo.itemConfig.field, checkedValue);
        }
      }
    };
    const changeEvent = (evnt) => {
      const isDisabled = computeIsDisabled.value;
      if (!isDisabled) {
        const { label, checkedValue } = props;
        const radioValue = import_xe_utils.default.isUndefined(checkedValue) ? label : checkedValue;
        handleValue(radioValue, evnt);
      }
    };
    const clickEvent = (evnt) => {
      const isDisabled = computeIsDisabled.value;
      const isStrict = computeStrict.value;
      if (!isDisabled && !isStrict) {
        const { label, checkedValue } = props;
        const radioValue = import_xe_utils.default.isUndefined(checkedValue) ? label : checkedValue;
        if (radioValue === ($xeRadioGroup ? $xeRadioGroup.props.modelValue : props.modelValue)) {
          handleValue(null, evnt);
        }
      }
    };
    const dispatchEvent = (type, params, evnt) => {
      emit(type, createEvent(evnt, { $radio: $xeRadio }, params));
    };
    const radioMethods = {
      dispatchEvent
    };
    const radioPrivateMethods = {};
    Object.assign($xeRadio, radioMethods, radioPrivateMethods);
    const renderVN = () => {
      const { label, checkedValue } = props;
      const radioValue = import_xe_utils.default.isUndefined(checkedValue) ? label : checkedValue;
      const vSize = computeSize.value;
      const isDisabled = computeIsDisabled.value;
      const name = computeName.value;
      const isChecked = computeChecked.value;
      return h("label", {
        key: radioValue,
        class: ["vxe-radio vxe-radio--default", {
          [`size--${vSize}`]: vSize,
          "is--checked": isChecked,
          "is--disabled": isDisabled
        }],
        title: props.title
      }, [
        h("input", {
          class: "vxe-radio--input",
          type: "radio",
          name,
          checked: isChecked,
          disabled: isDisabled,
          onChange: changeEvent,
          onClick: clickEvent
        }),
        h("span", {
          class: ["vxe-radio--icon", isChecked ? getIcon().RADIO_CHECKED : isDisabled ? getIcon().RADIO_DISABLED_UNCHECKED : getIcon().RADIO_UNCHECKED]
        }),
        h("span", {
          class: "vxe-radio--label"
        }, slots.default ? slots.default({}) : getFuncText(props.content))
      ]);
    };
    $xeRadio.renderVN = renderVN;
    return $xeRadio;
  },
  render() {
    return this.renderVN();
  }
});

// ../../node_modules/.pnpm/vxe-pc-ui@4.9.29_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/radio/src/button.js
var import_xe_utils2 = __toESM(require_xe_utils());
var button_default = defineVxeComponent({
  name: "VxeRadioButton",
  props: {
    modelValue: [String, Number, Boolean],
    checkedValue: {
      type: [String, Number, Boolean],
      default: void 0
    },
    title: [String, Number],
    content: [String, Number],
    disabled: {
      type: Boolean,
      default: null
    },
    strict: {
      type: Boolean,
      default: () => getConfig().radioButton.strict
    },
    size: {
      type: String,
      default: () => getConfig().radioButton.size || getConfig().size
    },
    /**
     * 已废弃，被 checkedValue 替换
     */
    label: {
      type: [String, Number, Boolean],
      default: null
    }
  },
  emits: [
    "update:modelValue",
    "change"
  ],
  setup(props, context) {
    const { slots, emit } = context;
    const $xeForm = inject("$xeForm", null);
    const formItemInfo = inject("xeFormItemInfo", null);
    const $xeRadioGroup = inject("$xeRadioGroup", null);
    const xID = import_xe_utils2.default.uniqueId();
    const reactData = reactive({});
    const { computeSize } = useSize(props);
    const $xeRadioButton = {
      xID,
      props,
      context,
      reactData
    };
    const computeIsDisabled = computed(() => {
      const { disabled } = props;
      if (disabled === null) {
        if ($xeRadioGroup) {
          const { computeIsDisabled: computeIsDisabled2 } = $xeRadioGroup.getComputeMaps();
          return computeIsDisabled2.value;
        }
      }
      return disabled;
    });
    const computeName = computed(() => {
      return $xeRadioGroup ? $xeRadioGroup.name : null;
    });
    const computeStrict = computed(() => {
      return $xeRadioGroup ? $xeRadioGroup.props.strict : props.strict;
    });
    const computeChecked = computed(() => {
      const { label, checkedValue } = props;
      const radioValue = import_xe_utils2.default.isUndefined(checkedValue) ? label : checkedValue;
      return $xeRadioGroup ? $xeRadioGroup.props.modelValue === radioValue : props.modelValue === radioValue;
    });
    const radioButtonMethods = {
      dispatchEvent(type, params, evnt) {
        emit(type, createEvent(evnt, { $radioButton: $xeRadioButton }, params));
      }
    };
    const radioButtonPrivateMethods = {};
    Object.assign($xeRadioButton, radioButtonMethods, radioButtonPrivateMethods);
    const handleValue = (checkedValue, evnt) => {
      if ($xeRadioGroup) {
        $xeRadioGroup.handleChecked({ label: checkedValue, checkedValue }, evnt);
      } else {
        emit("update:modelValue", checkedValue);
        radioButtonMethods.dispatchEvent("change", { value: checkedValue, label: checkedValue }, evnt);
        if ($xeForm && formItemInfo) {
          $xeForm.triggerItemEvent(evnt, formItemInfo.itemConfig.field, checkedValue);
        }
      }
    };
    const changeEvent = (evnt) => {
      const isDisabled = computeIsDisabled.value;
      if (!isDisabled) {
        const { label, checkedValue } = props;
        const radioValue = import_xe_utils2.default.isUndefined(checkedValue) ? label : checkedValue;
        handleValue(radioValue, evnt);
      }
    };
    const clickEvent = (evnt) => {
      const isDisabled = computeIsDisabled.value;
      const isStrict = computeStrict.value;
      if (!isDisabled && !isStrict) {
        const { label, checkedValue } = props;
        const radioValue = import_xe_utils2.default.isUndefined(checkedValue) ? label : checkedValue;
        if (radioValue === ($xeRadioGroup ? $xeRadioGroup.props.modelValue : props.modelValue)) {
          handleValue(null, evnt);
        }
      }
    };
    const renderVN = () => {
      const { label, checkedValue } = props;
      const radioValue = import_xe_utils2.default.isUndefined(checkedValue) ? label : checkedValue;
      const vSize = computeSize.value;
      const isDisabled = computeIsDisabled.value;
      const name = computeName.value;
      const isChecked = computeChecked.value;
      return h("label", {
        key: radioValue,
        class: ["vxe-radio vxe-radio--button", {
          [`size--${vSize}`]: vSize,
          "is--disabled": isDisabled
        }],
        title: props.title
      }, [
        h("input", {
          class: "vxe-radio--input",
          type: "radio",
          name,
          checked: isChecked,
          disabled: isDisabled,
          onChange: changeEvent,
          onClick: clickEvent
        }),
        h("span", {
          class: "vxe-radio--label"
        }, slots.default ? slots.default({}) : getFuncText(props.content))
      ]);
    };
    $xeRadioButton.renderVN = renderVN;
    return renderVN;
  }
});

// ../../node_modules/.pnpm/vxe-pc-ui@4.9.29_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/radio/src/group.js
var group_default = defineVxeComponent({
  name: "VxeRadioGroup",
  props: {
    modelValue: [String, Number, Boolean],
    disabled: {
      type: Boolean,
      default: null
    },
    type: String,
    options: Array,
    optionProps: Object,
    strict: {
      type: Boolean,
      default: () => getConfig().radioGroup.strict
    },
    size: {
      type: String,
      default: () => getConfig().radioGroup.size || getConfig().size
    }
  },
  emits: [
    "update:modelValue",
    "change"
  ],
  setup(props, context) {
    const { slots, emit } = context;
    const $xeForm = inject("$xeForm", null);
    const formItemInfo = inject("xeFormItemInfo", null);
    const xID = import_xe_utils3.default.uniqueId();
    const { computeSize } = useSize(props);
    const reactData = reactive({});
    const computeIsDisabled = computed(() => {
      const { disabled } = props;
      if (disabled === null) {
        if ($xeForm) {
          return $xeForm.props.readonly || $xeForm.props.disabled;
        }
        return false;
      }
      return disabled;
    });
    const computeMaps = {
      computeIsDisabled
    };
    const $xeRadioGroup = {
      xID,
      props,
      context,
      reactData,
      name: import_xe_utils3.default.uniqueId("xe_group_"),
      getComputeMaps: () => computeMaps
    };
    const computePropsOpts = computed(() => {
      return Object.assign({}, props.optionProps);
    });
    const computeLabelField = computed(() => {
      const propsOpts = computePropsOpts.value;
      return propsOpts.label || "label";
    });
    const computeValueField = computed(() => {
      const propsOpts = computePropsOpts.value;
      return propsOpts.value || "value";
    });
    const computeDisabledField = computed(() => {
      const propsOpts = computePropsOpts.value;
      return propsOpts.disabled || "disabled";
    });
    const dispatchEvent = (type, params, evnt) => {
      emit(type, createEvent(evnt, { $radioGroup: $xeRadioGroup }, params));
    };
    const radioGroupMethods = {
      dispatchEvent
    };
    const radioGroupPrivateMethods = {
      handleChecked(params, evnt) {
        const value = params.checkedValue;
        emit("update:modelValue", value);
        dispatchEvent("change", { value, label: value, checkedValue: value }, evnt);
        if ($xeForm && formItemInfo) {
          $xeForm.triggerItemEvent(evnt, formItemInfo.itemConfig.field, value);
        }
      }
    };
    Object.assign($xeRadioGroup, radioGroupMethods, radioGroupPrivateMethods);
    const renderVN = () => {
      const { options, type } = props;
      const vSize = computeSize.value;
      const defaultSlot = slots.default;
      const valueField = computeValueField.value;
      const labelField = computeLabelField.value;
      const disabledField = computeDisabledField.value;
      const btnComp = type === "button" ? button_default : radio_default;
      return h("div", {
        class: ["vxe-radio-group", {
          [`size--${vSize}`]: vSize
        }]
      }, defaultSlot ? defaultSlot({}) : options ? options.map((item) => {
        return h(btnComp, {
          key: item[valueField],
          checkedValue: item[valueField],
          content: item[labelField],
          disabled: item[disabledField]
        });
      }) : []);
    };
    provide("$xeRadioGroup", $xeRadioGroup);
    $xeRadioGroup.renderVN = renderVN;
    return $xeRadioGroup;
  },
  render() {
    return this.renderVN();
  }
});

// ../../node_modules/.pnpm/vxe-pc-ui@4.9.29_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/radio-group/index.js
var VxeRadioGroup = Object.assign(group_default, {
  install: function(app) {
    app.component(group_default.name, group_default);
  }
});
dynamicApp.use(VxeRadioGroup);
VxeUI.component(group_default);
var RadioGroup = VxeRadioGroup;
var radio_group_default = VxeRadioGroup;

// ../../node_modules/.pnpm/vxe-pc-ui@4.9.29_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-radio-group/index.js
var vxe_radio_group_default = radio_group_default;
export {
  RadioGroup,
  VxeRadioGroup,
  vxe_radio_group_default as default
};
//# sourceMappingURL=vxe-pc-ui_es_vxe-radio-group_index__js.js.map
