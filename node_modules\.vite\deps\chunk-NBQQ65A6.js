import {
  devWarning_default
} from "./chunk-B322KET6.js";
import {
  createContext_default
} from "./chunk-CKTQP5WV.js";
import {
  computed,
  defineComponent,
  getCurrentInstance,
  inject,
  onBeforeUnmount,
  provide,
  ref,
  watch
} from "./chunk-ZCM5A7SR.js";

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/form/FormItemContext.js
var ContextKey = Symbol("ContextProps");
var InternalContextKey = Symbol("InternalContextProps");
var useProvideFormItemContext = function(props) {
  let useValidation = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : computed(() => true);
  const formItemFields = ref(/* @__PURE__ */ new Map());
  const addFormItemField = (key, type) => {
    formItemFields.value.set(key, type);
    formItemFields.value = new Map(formItemFields.value);
  };
  const removeFormItemField = (key) => {
    formItemFields.value.delete(key);
    formItemFields.value = new Map(formItemFields.value);
  };
  const instance = getCurrentInstance();
  watch([useValidation, formItemFields], () => {
    if (true) {
      if (useValidation.value && formItemFields.value.size > 1) {
        devWarning_default(false, "Form.Item", `FormItem can only collect one field item, you haved set ${[...formItemFields.value.values()].map((v) => `\`${v.name}\``).join(", ")} ${formItemFields.value.size} field items.
        You can set not need to be collected fields into \`a-form-item-rest\``);
        let cur = instance;
        while (cur.parent) {
          console.warn("at", cur.type);
          cur = cur.parent;
        }
      }
    }
  });
  provide(ContextKey, props);
  provide(InternalContextKey, {
    addFormItemField,
    removeFormItemField
  });
};
var defaultContext = {
  id: computed(() => void 0),
  onFieldBlur: () => {
  },
  onFieldChange: () => {
  },
  clearValidate: () => {
  }
};
var defaultInternalContext = {
  addFormItemField: () => {
  },
  removeFormItemField: () => {
  }
};
var useInjectFormItemContext = () => {
  const internalContext = inject(InternalContextKey, defaultInternalContext);
  const formItemFieldKey = Symbol("FormItemFieldKey");
  const instance = getCurrentInstance();
  internalContext.addFormItemField(formItemFieldKey, instance.type);
  onBeforeUnmount(() => {
    internalContext.removeFormItemField(formItemFieldKey);
  });
  provide(InternalContextKey, defaultInternalContext);
  provide(ContextKey, defaultContext);
  return inject(ContextKey, defaultContext);
};
var FormItemContext_default = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "AFormItemRest",
  setup(_, _ref) {
    let {
      slots
    } = _ref;
    provide(InternalContextKey, defaultInternalContext);
    provide(ContextKey, defaultContext);
    return () => {
      var _a;
      return (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots);
    };
  }
});
var FormItemInputContext = createContext_default({});
var NoFormStatus = defineComponent({
  name: "NoFormStatus",
  setup(_, _ref2) {
    let {
      slots
    } = _ref2;
    FormItemInputContext.useProvide({});
    return () => {
      var _a;
      return (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots);
    };
  }
});

export {
  useProvideFormItemContext,
  useInjectFormItemContext,
  FormItemContext_default,
  FormItemInputContext,
  NoFormStatus
};
//# sourceMappingURL=chunk-NBQQ65A6.js.map
