import {
  tooltip_default
} from "./chunk-6V4MV7LO.js";
import "./chunk-TOENFBLC.js";
import "./chunk-6CXTBDBP.js";
import "./chunk-JWKCLBKN.js";
import {
  dynamicApp
} from "./chunk-KAYKK4JO.js";
import "./chunk-RWHEUJNV.js";
import {
  VxeUI
} from "./chunk-KJAC55GV.js";
import "./chunk-G6CDOZZI.js";
import "./chunk-ZCM5A7SR.js";
import "./chunk-WDDBQLJB.js";
import "./chunk-V4OQ3NZ2.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.9.29_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/tooltip/index.js
var VxeTooltip = Object.assign({}, tooltip_default, {
  install(app) {
    app.component(tooltip_default.name, tooltip_default);
  }
});
dynamicApp.use(VxeTooltip);
VxeUI.component(tooltip_default);
var Tooltip = VxeTooltip;
var tooltip_default2 = VxeTooltip;

// ../../node_modules/.pnpm/vxe-pc-ui@4.9.29_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-tooltip/index.js
var vxe_tooltip_default = tooltip_default2;
export {
  Tooltip,
  VxeTooltip,
  vxe_tooltip_default as default
};
//# sourceMappingURL=vxe-pc-ui_es_vxe-tooltip_index__js.js.map
