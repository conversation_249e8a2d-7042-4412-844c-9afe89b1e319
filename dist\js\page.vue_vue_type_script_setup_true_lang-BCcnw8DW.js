var y=(t,d,s)=>new Promise((u,r)=>{var h=e=>{try{n(s.next(e))}catch(a){r(a)}},c=e=>{try{n(s.throw(e))}catch(a){r(a)}},n=e=>e.done?u(e.value):Promise.resolve(e.value).then(h,c);n((s=s.apply(t,d)).next())});import{b6 as H}from"./bootstrap-CFDAkNgp.js";import{a4 as k,P as g,bx as C,J as R,a9 as T,av as o,ab as l,aq as i,aB as $,aV as m,a7 as p,aW as v,a8 as f,aj as b,aw as B,n as O}from"../jse/index-index-B2UBupFX.js";const S={class:"relative flex min-h-full flex-col"},A={class:"flex-auto"},E={key:0,class:"mb-2 flex text-lg font-semibold"},N={key:0,class:"text-muted-foreground"},V={key:0},L=k({name:"Page",__name:"page",props:{title:{},description:{},contentClass:{},autoContentHeight:{type:Boolean,default:!1},headerClass:{},footerClass:{},heightOffset:{default:0}},setup(t){const d=g(0),s=g(0),u=g(!1),r=C("headerRef"),h=C("footerRef"),c=R(()=>t.autoContentHeight?{height:`calc(var(${H}) - ${d.value}px - ${s.value}px - ${typeof t.heightOffset=="number"?`${t.heightOffset}px`:t.heightOffset})`,overflowY:u.value?"auto":"unset"}:{});function n(){return y(this,null,function*(){var e,a;t.autoContentHeight&&(yield O(),d.value=((e=r.value)==null?void 0:e.offsetHeight)||0,s.value=((a=h.value)==null?void 0:a.offsetHeight)||0,setTimeout(()=>{u.value=!0},30))})}return T(()=>{n()}),(e,a)=>(l(),o("div",S,[e.description||e.$slots.description||e.title||e.$slots.title||e.$slots.extra?(l(),o("div",{key:0,ref_key:"headerRef",ref:r,class:m(p(v)("bg-card border-border relative flex items-end border-b px-6 py-4",e.headerClass))},[$("div",A,[f(e.$slots,"title",{},()=>[e.title?(l(),o("div",E,b(e.title),1)):i("",!0)]),f(e.$slots,"description",{},()=>[e.description?(l(),o("p",N,b(e.description),1)):i("",!0)])]),e.$slots.extra?(l(),o("div",V,[f(e.$slots,"extra")])):i("",!0)],2)):i("",!0),$("div",{class:m(p(v)("h-full p-4",e.contentClass)),style:B(c.value)},[f(e.$slots,"default")],6),e.$slots.footer?(l(),o("div",{key:1,ref_key:"footerRef",ref:h,class:m(p(v)("bg-card align-center flex px-6 py-4",e.footerClass))},[f(e.$slots,"footer")],2)):i("",!0)]))}});export{L as _};
