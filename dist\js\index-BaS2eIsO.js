var Se=Object.defineProperty,Te=Object.defineProperties;var Ie=Object.getOwnPropertyDescriptors;var Y=Object.getOwnPropertySymbols;var De=Object.prototype.hasOwnProperty,Be=Object.prototype.propertyIsEnumerable;var ee=(a,l,r)=>l in a?Se(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,V=(a,l)=>{for(var r in l||(l={}))De.call(l,r)&&ee(a,r,l[r]);if(Y)for(var r of Y(l))Be.call(l,r)&&ee(a,r,l[r]);return a},te=(a,l)=>Te(a,Ie(l));var oe=(a,l,r)=>new Promise((u,d)=>{var m=y=>{try{c(r.next(y))}catch(x){d(x)}},s=y=>{try{c(r.throw(y))}catch(x){d(x)}},c=y=>y.done?u(y.value):Promise.resolve(y.value).then(m,s);c((r=r.apply(a,l)).next())});import{F as ne,a as w}from"./index-BUPhRcAb.js";import T from"./index-BCzosP6o.js";import f from"./index-CGqxGK2L.js";import ae from"./index-BE7dsRID.js";import{g as ce,m as Me,aH as Ne,_ as k,r as ze,d as ve,h as fe,w as ge,a as ye,j as xe,b as N,a5 as Ce,o as be,l as I,O as le,L as Fe,a4 as he,k as re,s as je,cD as Ue,aT as Ae,cE as ie,F as B,cF as Ee,K as We,cm as se,c9 as H,bu as Re}from"./bootstrap-CFDAkNgp.js";import{C as ue}from"./index-DOXLVRHg.js";import{T as qe}from"./index-BhH5F5SY.js";import{b as Ve,T as He,t as ke,c as we}from"./index-tPQFuBU-.js";import{P as Le}from"./colors-KzMfSzFw.js";import{a4 as K,P as M,J as U,x as t,F as Ke,_ as Xe,T as de,av as Ge,ab as L,ac as i,a7 as n,ai as v,aa as pe,aq as Je,aj as Ze}from"../jse/index-index-B2UBupFX.js";import{u as Qe}from"./useMergedState-C4x1IDb9.js";import me from"./index-DR_dTvJi.js";import"./index-mn9Xzl0e.js";import{T as Ye}from"./index-DEfsrzsO.js";import"./Trigger-D2zZP_An.js";import"./ResizeObserver.es-CDE7jhPe.js";import"./hasIn-Bt_d2Zq4.js";import"./isPlainObject-0t1li2J1.js";import"./Col-Bjak4A2I.js";import"./useFlexGapSupport-TvfJoknb.js";import"./_arrayIncludes-B8uzE354.js";import"./debounce-CesRCMoz.js";import"./collapseMotion-DiwOar_A.js";import"./FormItemContext-CoieKSxA.js";import"./statusUtils-D62pPzYs.js";import"./BaseInput-Dslq5mxC.js";import"./index-C5ScQeGh.js";import"./SearchOutlined-DqQ4RgbY.js";import"./index-DQZjs6Lb.js";import"./EyeOutlined-MQsW2UzL.js";import"./index-B2Lu6Z2W.js";import"./Overflow--DaGju1E.js";import"./isMobile-8sZ0LT6r.js";import"./DownOutlined-CVWF16Fu.js";import"./CheckOutlined-CZeS3E7o.js";import"./move-IXaXzbNk.js";import"./slide-BhgK1D9k.js";import"./index-1BiZfdtR.js";import"./shallowequal-CNCY1mYq.js";import"./useRefs-f0KzY-v7.js";import"./index-DYfzhziO.js";import"./index-C5usYyko.js";import"./index-DfHX-m0D.js";import"./index-BDBY1qBK.js";import"./Checkbox-Dt0Z6J8a.js";import"./index-UTpExPeP.js";const et=a=>{const{componentCls:l,popoverBg:r,popoverColor:u,width:d,fontWeightStrong:m,popoverPadding:s,boxShadowSecondary:c,colorTextHeading:y,borderRadiusLG:x,zIndexPopup:P,marginXS:$,colorBgElevated:C}=a;return[{[l]:k(k({},ze(a)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:P,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--antd-arrow-background-color":C,"&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${l}-content`]:{position:"relative"},[`${l}-inner`]:{backgroundColor:r,backgroundClip:"padding-box",borderRadius:x,boxShadow:c,padding:s},[`${l}-title`]:{minWidth:d,marginBottom:$,color:y,fontWeight:m},[`${l}-inner-content`]:{color:u}})},Ve(a,{colorBg:"var(--antd-arrow-background-color)"}),{[`${l}-pure`]:{position:"relative",maxWidth:"none",[`${l}-content`]:{display:"inline-block"}}}]},tt=a=>{const{componentCls:l}=a;return{[l]:Le.map(r=>{const u=a[`${r}-6`];return{[`&${l}-${r}`]:{"--antd-arrow-background-color":u,[`${l}-inner`]:{backgroundColor:u},[`${l}-arrow`]:{background:"transparent"}}}})}},ot=a=>{const{componentCls:l,lineWidth:r,lineType:u,colorSplit:d,paddingSM:m,controlHeight:s,fontSize:c,lineHeight:y,padding:x}=a,P=s-Math.round(c*y),$=P/2,C=P/2-r,_=x;return{[l]:{[`${l}-inner`]:{padding:0},[`${l}-title`]:{margin:0,padding:`${$}px ${_}px ${C}px`,borderBottom:`${r}px ${u} ${d}`},[`${l}-inner-content`]:{padding:`${m}px ${_}px`}}}},nt=ce("Popover",a=>{const{colorBgElevated:l,colorText:r,wireframe:u}=a,d=Me(a,{popoverBg:l,popoverColor:r,popoverPadding:12});return[et(d),tt(d),u&&ot(d),Ne(d,"zoom-big")]},a=>{let{zIndexPopupBase:l}=a;return{zIndexPopup:l+30,width:177}}),at=()=>k(k({},we()),{content:I(),title:I()}),lt=K({compatConfig:{MODE:3},name:"APopover",inheritAttrs:!1,props:fe(at(),k(k({},ke()),{trigger:"hover",placement:"top",mouseEnterDelay:.1,mouseLeaveDelay:.1})),setup(a,l){let{expose:r,slots:u,attrs:d}=l;const m=M();ge(a.visible===void 0),r({getPopupDomNode:()=>{var C,_;return(_=(C=m.value)===null||C===void 0?void 0:C.getPopupDomNode)===null||_===void 0?void 0:_.call(C)}});const{prefixCls:s,configProvider:c}=ye("popover",a),[y,x]=nt(s),P=U(()=>c.getPrefixCls()),$=()=>{var C,_;const{title:O=le((C=u.title)===null||C===void 0?void 0:C.call(u)),content:b=le((_=u.content)===null||_===void 0?void 0:_.call(u))}=a,D=!!(Array.isArray(O)?O.length:O),z=!!(Array.isArray(b)?b.length:O);return!D&&!z?null:t(Ke,null,[D&&t("div",{class:`${s.value}-title`},[O]),t("div",{class:`${s.value}-inner-content`},[b])])};return()=>{const C=xe(a.overlayClassName,x.value);return y(t(He,N(N(N({},be(a,["title","content"])),d),{},{prefixCls:s.value,ref:m,overlayClassName:C,transitionName:Ce(P.value,"zoom-big",a.transitionName),"data-popover-inject":!0}),{title:$,default:u.default}))}}}),rt=ve(lt),it=a=>{const{componentCls:l,iconCls:r,zIndexPopup:u,colorText:d,colorWarning:m,marginXS:s,fontSize:c,fontWeightStrong:y,lineHeight:x}=a;return{[l]:{zIndex:u,[`${l}-inner-content`]:{color:d},[`${l}-message`]:{position:"relative",marginBottom:s,color:d,fontSize:c,display:"flex",flexWrap:"nowrap",alignItems:"start",[`> ${l}-message-icon ${r}`]:{color:m,fontSize:c,flex:"none",lineHeight:1,paddingTop:(Math.round(c*x)-c)/2},"&-title":{flex:"auto",marginInlineStart:s},"&-title-only":{fontWeight:y}},[`${l}-description`]:{position:"relative",marginInlineStart:c+s,marginBottom:s,color:d,fontSize:c},[`${l}-buttons`]:{textAlign:"end",button:{marginInlineStart:s}}}}},st=ce("Popconfirm",a=>it(a),a=>{const{zIndexPopupBase:l}=a;return{zIndexPopup:l+60}});var ut=function(a,l){var r={};for(var u in a)Object.prototype.hasOwnProperty.call(a,u)&&l.indexOf(u)<0&&(r[u]=a[u]);if(a!=null&&typeof Object.getOwnPropertySymbols=="function")for(var d=0,u=Object.getOwnPropertySymbols(a);d<u.length;d++)l.indexOf(u[d])<0&&Object.prototype.propertyIsEnumerable.call(a,u[d])&&(r[u[d]]=a[u[d]]);return r};const dt=()=>k(k({},we()),{prefixCls:String,content:I(),title:I(),description:I(),okType:je("primary"),disabled:{type:Boolean,default:!1},okText:I(),cancelText:I(),icon:I(),okButtonProps:re(),cancelButtonProps:re(),showCancel:{type:Boolean,default:!0},onConfirm:Function,onCancel:Function}),pt=K({compatConfig:{MODE:3},name:"APopconfirm",inheritAttrs:!1,props:fe(dt(),k(k({},ke()),{trigger:"click",placement:"top",mouseEnterDelay:.1,mouseLeaveDelay:.1,arrowPointAtCenter:!1,autoAdjustOverflow:!0,okType:"primary",disabled:!1})),slots:Object,setup(a,l){let{slots:r,emit:u,expose:d,attrs:m}=l;const s=M();ge(a.visible===void 0),d({getPopupDomNode:()=>{var o,p;return(p=(o=s.value)===null||o===void 0?void 0:o.getPopupDomNode)===null||p===void 0?void 0:p.call(o)}});const[c,y]=Qe(!1,{value:Xe(a,"open")}),x=(o,p)=>{a.open===void 0&&y(o),u("update:open",o),u("openChange",o,p)},P=o=>{x(!1,o)},$=o=>{var p;return(p=a.onConfirm)===null||p===void 0?void 0:p.call(a,o)},C=o=>{var p;x(!1,o),(p=a.onCancel)===null||p===void 0||p.call(a,o)},_=o=>{o.keyCode===We.ESC&&c&&x(!1,o)},O=o=>{const{disabled:p}=a;p||x(o)},{prefixCls:b,getPrefixCls:D}=ye("popconfirm",a),z=U(()=>D()),A=U(()=>D("btn")),[E]=st(b),[g]=Fe("Popconfirm",he.Popconfirm),e=()=>{var o,p,S,F,h;const{okButtonProps:j,cancelButtonProps:W,title:R=(o=r.title)===null||o===void 0?void 0:o.call(r),description:q=(p=r.description)===null||p===void 0?void 0:p.call(r),cancelText:_e=(S=r.cancel)===null||S===void 0?void 0:S.call(r),okText:Pe=(F=r.okText)===null||F===void 0?void 0:F.call(r),okType:X,icon:G=((h=r.icon)===null||h===void 0?void 0:h.call(r))||t(Ae,null,null),showCancel:$e=!0}=a,{cancelButton:J,okButton:Z}=r,Q=k({onClick:C,size:"small"},W),Oe=k(k(k({onClick:$},ie(X)),{size:"small"}),j);return t("div",{class:`${b.value}-inner-content`},[t("div",{class:`${b.value}-message`},[G&&t("span",{class:`${b.value}-message-icon`},[G]),t("div",{class:[`${b.value}-message-title`,{[`${b.value}-message-title-only`]:!!q}]},[R])]),q&&t("div",{class:`${b.value}-description`},[q]),t("div",{class:`${b.value}-buttons`},[$e?J?J(Q):t(B,Q,{default:()=>[_e||g.value.cancelText]}):null,Z?Z(Oe):t(Ee,{buttonProps:k(k({size:"small"},ie(X)),j),actionFn:$,close:P,prefixCls:A.value,quitOnNullishReturnValue:!0,emitEvent:!0},{default:()=>[Pe||g.value.okText]})])])};return()=>{var o;const{placement:p,overlayClassName:S,trigger:F="click"}=a,h=ut(a,["placement","overlayClassName","trigger"]),j=be(h,["title","content","cancelText","okText","onUpdate:open","onConfirm","onCancel","prefixCls"]),W=xe(b.value,S);return E(t(rt,N(N(N({},j),m),{},{trigger:F,placement:p,onOpenChange:O,open:c.value,overlayClassName:W,transitionName:Ce(z.value,"zoom-big",a.transitionName),ref:s,"data-popover-inject":!0}),{default:()=>[Ue(((o=r.default)===null||o===void 0?void 0:o.call(r))||[],{onKeydown:R=>{_(R)}},!1)],content:e}))}}}),mt=ve(pt),ct={class:"p-5"},vt=K({name:"IrrigationDevice",__name:"index",setup(a){const l=M(),r=M(!1),u=M(""),d=M(!1),m=de({name:"",status:"",type:""}),s=de({id:null,name:"",type:"",model:"",power:null,voltage:null,current:null,manufacturer:"",installDate:null,lastMaintenance:null,nextMaintenance:null,status:"normal",location:"",manager:"",phone:"",remark:""}),c=M([{id:1,name:"东风灌站主水泵",type:"离心泵",model:"IS200-150-400",power:450,voltage:380,current:125.8,manufacturer:"上海水泵厂",installDate:"2018-05-15",lastMaintenance:"2024-01-10",nextMaintenance:"2024-04-10",status:"normal",location:"东风灌站泵房",manager:"张三",phone:"13800138001",remark:"主要供水设备"},{id:2,name:"红旗灌站变频器",type:"变频器",model:"VFD-M-55kW",power:55,voltage:380,current:0,manufacturer:"施耐德电气",installDate:"2015-08-20",lastMaintenance:"2023-12-15",nextMaintenance:"2024-03-15",status:"maintenance",location:"红旗灌站控制室",manager:"李四",phone:"13800138002",remark:"需要更换滤波器"},{id:3,name:"胜利灌站流量计",type:"流量计",model:"EMF-DN300",power:24,voltage:24,current:4.2,manufacturer:"横河电机",installDate:"2020-03-10",lastMaintenance:"2024-01-05",nextMaintenance:"2024-07-05",status:"normal",location:"胜利灌站出水口",manager:"王五",phone:"13800138003",remark:"精度良好"}]),y=[{title:"设备名称",dataIndex:"name",key:"name",width:200},{title:"设备类型",dataIndex:"type",key:"type",width:120},{title:"型号",dataIndex:"model",key:"model",width:150},{title:"功率(kW)",dataIndex:"power",key:"power",width:100},{title:"状态",dataIndex:"status",key:"status",width:100},{title:"安装位置",dataIndex:"location",key:"location",width:150},{title:"管理员",dataIndex:"manager",key:"manager",width:100},{title:"下次维护",dataIndex:"nextMaintenance",key:"nextMaintenance",width:120},{title:"操作",key:"action",width:200,fixed:"right"}],x=U(()=>c.value.filter(g=>{const e=!m.name||g.name.includes(m.name),o=!m.status||g.status===m.status,p=!m.type||g.type===m.type;return e&&o&&p})),P=g=>{switch(g){case"normal":return{color:"green",text:"正常"};case"maintenance":return{color:"orange",text:"维修中"};case"fault":return{color:"red",text:"故障"};case"offline":return{color:"gray",text:"离线"};default:return{color:"default",text:"未知"}}},$=()=>{Object.assign(m,{name:"",status:"",type:""})},C=()=>{u.value="新增设备",d.value=!1,b(),r.value=!0},_=g=>{u.value="编辑设备",d.value=!0,Object.assign(s,g),r.value=!0},O=g=>{const e=c.value.findIndex(o=>o.id===g);e>-1&&(c.value.splice(e,1),H.success("删除成功"))},b=()=>{var g;Object.assign(s,{id:null,name:"",type:"",model:"",power:null,voltage:null,current:null,manufacturer:"",installDate:null,lastMaintenance:null,nextMaintenance:null,status:"normal",location:"",manager:"",phone:"",remark:""}),(g=l.value)==null||g.resetFields()},D=()=>oe(null,null,function*(){var g;try{if(yield(g=l.value)==null?void 0:g.validate(),d.value){const e=c.value.findIndex(o=>o.id===s.id);e>-1&&(c.value[e]=V({},s),H.success("编辑成功"))}else{const e=te(V({},s),{id:Date.now()});c.value.push(e),H.success("新增成功")}r.value=!1,b()}catch(e){console.error("表单验证失败:",e)}}),z=()=>{r.value=!1,b()},A=g=>{se.info({title:"维护记录",content:`${g.name} 的维护记录功能开发中...`,width:600})},E={name:[{required:!0,message:"请输入设备名称"}],type:[{required:!0,message:"请选择设备类型"}],model:[{required:!0,message:"请输入设备型号"}],power:[{required:!0,message:"请输入设备功率"}],location:[{required:!0,message:"请输入安装位置"}],manager:[{required:!0,message:"请输入管理员"}],phone:[{required:!0,message:"请输入联系电话"}]};return(g,e)=>(L(),Ge("div",ct,[t(n(ue),{title:"设备查询",class:"mb-5"},{default:i(()=>[t(n(ne),{layout:"inline",model:m},{default:i(()=>[t(n(w),{label:"设备名称"},{default:i(()=>[t(n(T),{value:m.name,"onUpdate:value":e[0]||(e[0]=o=>m.name=o),placeholder:"请输入设备名称",style:{width:"200px"}},null,8,["value"])]),_:1}),t(n(w),{label:"设备状态"},{default:i(()=>[t(n(f),{value:m.status,"onUpdate:value":e[1]||(e[1]=o=>m.status=o),placeholder:"请选择状态",style:{width:"120px"},"allow-clear":""},{default:i(()=>[t(n(f).Option,{value:"normal"},{default:i(()=>e[15]||(e[15]=[v("正常")])),_:1,__:[15]}),t(n(f).Option,{value:"maintenance"},{default:i(()=>e[16]||(e[16]=[v("维修中")])),_:1,__:[16]}),t(n(f).Option,{value:"fault"},{default:i(()=>e[17]||(e[17]=[v("故障")])),_:1,__:[17]}),t(n(f).Option,{value:"offline"},{default:i(()=>e[18]||(e[18]=[v("离线")])),_:1,__:[18]})]),_:1},8,["value"])]),_:1}),t(n(w),{label:"设备类型"},{default:i(()=>[t(n(f),{value:m.type,"onUpdate:value":e[2]||(e[2]=o=>m.type=o),placeholder:"请选择类型",style:{width:"120px"},"allow-clear":""},{default:i(()=>[t(n(f).Option,{value:"离心泵"},{default:i(()=>e[19]||(e[19]=[v("离心泵")])),_:1,__:[19]}),t(n(f).Option,{value:"变频器"},{default:i(()=>e[20]||(e[20]=[v("变频器")])),_:1,__:[20]}),t(n(f).Option,{value:"流量计"},{default:i(()=>e[21]||(e[21]=[v("流量计")])),_:1,__:[21]}),t(n(f).Option,{value:"压力表"},{default:i(()=>e[22]||(e[22]=[v("压力表")])),_:1,__:[22]}),t(n(f).Option,{value:"控制器"},{default:i(()=>e[23]||(e[23]=[v("控制器")])),_:1,__:[23]})]),_:1},8,["value"])]),_:1}),t(n(w),null,{default:i(()=>[t(n(ae),null,{default:i(()=>[t(n(B),{type:"primary"},{default:i(()=>e[24]||(e[24]=[v("查询")])),_:1,__:[24]}),t(n(B),{onClick:$},{default:i(()=>e[25]||(e[25]=[v("重置")])),_:1,__:[25]})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),t(n(ue),{title:"设备列表"},{extra:i(()=>[t(n(B),{type:"primary",onClick:C},{default:i(()=>e[26]||(e[26]=[v("新增设备")])),_:1,__:[26]})]),default:i(()=>[t(n(Ye),{columns:y,"data-source":x.value,scroll:{x:1200},"row-key":"id"},{bodyCell:i(({column:o,record:p})=>[o.key==="status"?(L(),pe(n(qe),{key:0,color:P(p.status).color},{default:i(()=>[v(Ze(P(p.status).text),1)]),_:2},1032,["color"])):o.key==="action"?(L(),pe(n(ae),{key:1},{default:i(()=>[t(n(B),{type:"link",size:"small",onClick:S=>_(p)},{default:i(()=>e[27]||(e[27]=[v(" 编辑 ")])),_:2,__:[27]},1032,["onClick"]),t(n(B),{type:"link",size:"small",onClick:S=>A(p)},{default:i(()=>e[28]||(e[28]=[v(" 维护记录 ")])),_:2,__:[28]},1032,["onClick"]),t(n(mt),{title:"确定要删除这个设备吗？",onConfirm:S=>O(p.id)},{default:i(()=>[t(n(B),{type:"link",size:"small",danger:""},{default:i(()=>e[29]||(e[29]=[v("删除")])),_:1,__:[29]})]),_:2},1032,["onConfirm"])]),_:2},1024)):Je("",!0)]),_:1},8,["data-source"])]),_:1}),t(n(se),{open:r.value,"onUpdate:open":e[14]||(e[14]=o=>r.value=o),title:u.value,width:"800px",onOk:D,onCancel:z},{default:i(()=>[t(n(ne),{ref_key:"formRef",ref:l,model:s,rules:E,"label-col":{span:6},"wrapper-col":{span:18}},{default:i(()=>[t(n(w),{label:"设备名称",name:"name"},{default:i(()=>[t(n(T),{value:s.name,"onUpdate:value":e[3]||(e[3]=o=>s.name=o),placeholder:"请输入设备名称"},null,8,["value"])]),_:1}),t(n(w),{label:"设备类型",name:"type"},{default:i(()=>[t(n(f),{value:s.type,"onUpdate:value":e[4]||(e[4]=o=>s.type=o),placeholder:"请选择设备类型"},{default:i(()=>[t(n(f).Option,{value:"离心泵"},{default:i(()=>e[30]||(e[30]=[v("离心泵")])),_:1,__:[30]}),t(n(f).Option,{value:"变频器"},{default:i(()=>e[31]||(e[31]=[v("变频器")])),_:1,__:[31]}),t(n(f).Option,{value:"流量计"},{default:i(()=>e[32]||(e[32]=[v("流量计")])),_:1,__:[32]}),t(n(f).Option,{value:"压力表"},{default:i(()=>e[33]||(e[33]=[v("压力表")])),_:1,__:[33]}),t(n(f).Option,{value:"控制器"},{default:i(()=>e[34]||(e[34]=[v("控制器")])),_:1,__:[34]})]),_:1},8,["value"])]),_:1}),t(n(w),{label:"设备型号",name:"model"},{default:i(()=>[t(n(T),{value:s.model,"onUpdate:value":e[5]||(e[5]=o=>s.model=o),placeholder:"请输入设备型号"},null,8,["value"])]),_:1}),t(n(w),{label:"设备功率(kW)",name:"power"},{default:i(()=>[t(n(me),{value:s.power,"onUpdate:value":e[6]||(e[6]=o=>s.power=o),placeholder:"请输入设备功率",style:{width:"100%"},min:0},null,8,["value"])]),_:1}),t(n(w),{label:"额定电压(V)"},{default:i(()=>[t(n(me),{value:s.voltage,"onUpdate:value":e[7]||(e[7]=o=>s.voltage=o),placeholder:"请输入额定电压",style:{width:"100%"},min:0},null,8,["value"])]),_:1}),t(n(w),{label:"制造商"},{default:i(()=>[t(n(T),{value:s.manufacturer,"onUpdate:value":e[8]||(e[8]=o=>s.manufacturer=o),placeholder:"请输入制造商"},null,8,["value"])]),_:1}),t(n(w),{label:"安装位置",name:"location"},{default:i(()=>[t(n(T),{value:s.location,"onUpdate:value":e[9]||(e[9]=o=>s.location=o),placeholder:"请输入安装位置"},null,8,["value"])]),_:1}),t(n(w),{label:"管理员",name:"manager"},{default:i(()=>[t(n(T),{value:s.manager,"onUpdate:value":e[10]||(e[10]=o=>s.manager=o),placeholder:"请输入管理员"},null,8,["value"])]),_:1}),t(n(w),{label:"联系电话",name:"phone"},{default:i(()=>[t(n(T),{value:s.phone,"onUpdate:value":e[11]||(e[11]=o=>s.phone=o),placeholder:"请输入联系电话"},null,8,["value"])]),_:1}),t(n(w),{label:"设备状态"},{default:i(()=>[t(n(f),{value:s.status,"onUpdate:value":e[12]||(e[12]=o=>s.status=o)},{default:i(()=>[t(n(f).Option,{value:"normal"},{default:i(()=>e[35]||(e[35]=[v("正常")])),_:1,__:[35]}),t(n(f).Option,{value:"maintenance"},{default:i(()=>e[36]||(e[36]=[v("维修中")])),_:1,__:[36]}),t(n(f).Option,{value:"fault"},{default:i(()=>e[37]||(e[37]=[v("故障")])),_:1,__:[37]}),t(n(f).Option,{value:"offline"},{default:i(()=>e[38]||(e[38]=[v("离线")])),_:1,__:[38]})]),_:1},8,["value"])]),_:1}),t(n(w),{label:"备注"},{default:i(()=>[t(n(T).TextArea,{value:s.remark,"onUpdate:value":e[13]||(e[13]=o=>s.remark=o),placeholder:"请输入备注信息",rows:3},null,8,["value"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open","title"])]))}}),so=Re(vt,[["__scopeId","data-v-28386182"]]);export{so as default};
