import{_ as t,s as U,g as c,Y as D,b as d,o as O}from"./bootstrap-DlHXJWd_.js";import{g as H,c as N,d as M,r as V,a as W}from"./index-N5LJhOjA.js";import{u as B}from"./FormItemContext-DlTJXH8o.js";import{a4 as w,P as R,x as S}from"../jse/index-index-DYNcUVMZ.js";import"./colors-DiwvFbr5.js";import"./vnode-wTLMd7r4.js";import"./useMergedState-C62ndRnY.js";import"./Trigger-DqFxRNhn.js";import"./ResizeObserver.es-CDE7jhPe.js";import"./move-CmFQWh0R.js";import"./shallowequal-DYGI_FZo.js";import"./statusUtils-1R4B2N1T.js";import"./index-CMWcCtKN.js";import"./slide-DSq39mcs.js";const T=()=>({format:String,showNow:c(),showHour:c(),showMinute:c(),showSecond:c(),use12Hours:c(),hourStep:Number,minuteStep:Number,secondStep:Number,hideDisabledOptions:c(),popupClassName:String,status:U()});function Y(p){const _=H(p,t(t({},T()),{order:{type:Boolean,default:!0}})),{TimePicker:I,RangePicker:y}=_,j=w({name:"ATimePicker",inheritAttrs:!1,props:t(t(t(t({},N()),M()),T()),{addon:{type:Function}}),slots:Object,setup(m,g){let{slots:i,expose:C,emit:n,attrs:f}=g;const r=m,a=B();D(!(i.addon||r.addon),"TimePicker","`addon` is deprecated. Please use `v-slot:renderExtraFooter` instead.");const s=R();C({focus:()=>{var o;(o=s.value)===null||o===void 0||o.focus()},blur:()=>{var o;(o=s.value)===null||o===void 0||o.blur()}});const h=(o,F)=>{n("update:value",o),n("change",o,F),a.onFieldChange()},k=o=>{n("update:open",o),n("openChange",o)},P=o=>{n("focus",o)},v=o=>{n("blur",o),a.onFieldBlur()},b=o=>{n("ok",o)};return()=>{const{id:o=a.id.value}=r;return S(I,d(d(d({},f),O(r,["onUpdate:value","onUpdate:open"])),{},{id:o,dropdownClassName:r.popupClassName,mode:void 0,ref:s,renderExtraFooter:r.addon||i.addon||r.renderExtraFooter||i.renderExtraFooter,onChange:h,onOpenChange:k,onFocus:P,onBlur:v,onOk:b}),i)}}}),A=w({name:"ATimeRangePicker",inheritAttrs:!1,props:t(t(t(t({},N()),V()),T()),{order:{type:Boolean,default:!0}}),slots:Object,setup(m,g){let{slots:i,expose:C,emit:n,attrs:f}=g;const r=m,a=R(),s=B();C({focus:()=>{var e;(e=a.value)===null||e===void 0||e.focus()},blur:()=>{var e;(e=a.value)===null||e===void 0||e.blur()}});const h=(e,u)=>{n("update:value",e),n("change",e,u),s.onFieldChange()},k=e=>{n("update:open",e),n("openChange",e)},P=e=>{n("focus",e)},v=e=>{n("blur",e),s.onFieldBlur()},b=(e,u)=>{n("panelChange",e,u)},o=e=>{n("ok",e)},F=(e,u,E)=>{n("calendarChange",e,u,E)};return()=>{const{id:e=s.id.value}=r;return S(y,d(d(d({},f),O(r,["onUpdate:open","onUpdate:value"])),{},{id:e,dropdownClassName:r.popupClassName,picker:"time",mode:void 0,ref:a,onChange:h,onOpenChange:k,onFocus:P,onBlur:v,onPanelChange:b,onOk:o,onCalendarChange:F}),i)}}});return{TimePicker:j,TimeRangePicker:A}}const{TimePicker:l,TimeRangePicker:x}=Y(W),re=t(l,{TimePicker:l,TimeRangePicker:x,install:p=>(p.component(l.name,l),p.component(x.name,x),p)});export{l as TimePicker,x as TimeRangePicker,re as default};
