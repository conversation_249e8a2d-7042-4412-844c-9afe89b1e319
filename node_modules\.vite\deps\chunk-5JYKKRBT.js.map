{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/hooks/useMemo.js"], "sourcesContent": ["import { ref, watch } from 'vue';\nexport default function useMemo(getValue, condition, shouldUpdate) {\n  const cacheRef = ref(getValue());\n  watch(condition, (next, pre) => {\n    if (shouldUpdate) {\n      if (shouldUpdate(next, pre)) {\n        cacheRef.value = getValue();\n      }\n    } else {\n      cacheRef.value = getValue();\n    }\n  });\n  return cacheRef;\n}"], "mappings": ";;;;;;AACe,SAAR,QAAyB,UAAU,WAAW,cAAc;AACjE,QAAM,WAAW,IAAI,SAAS,CAAC;AAC/B,QAAM,WAAW,CAAC,MAAM,QAAQ;AAC9B,QAAI,cAAc;AAChB,UAAI,aAAa,MAAM,GAAG,GAAG;AAC3B,iBAAS,QAAQ,SAAS;AAAA,MAC5B;AAAA,IACF,OAAO;AACL,eAAS,QAAQ,SAAS;AAAA,IAC5B;AAAA,EACF,CAAC;AACD,SAAO;AACT;", "names": []}