import{b as k,d as B,e as T,f as w,c as A,_ as v,g as y,N as S,i as V,h as C,a as F}from"./layout.vue_vue_type_script_setup_true_lang-StDxCs92.js";import{A as N,_ as O,a as U}from"./authentication-DJjcFBOt.js";import{i as E,h as G}from"./theme-toggle.vue_vue_type_script_setup_true_lang-CM98aRHS.js";import{bu as o}from"./bootstrap-CFDAkNgp.js";import{P as a,av as t,ab as s}from"../jse/index-index-B2UBupFX.js";import"./avatar.vue_vue_type_script_setup_true_lang-B6ia9xAu.js";import"./use-vben-form-DwBeC3z-.js";import"./render-content.vue_vue_type_script_lang-DAf0xCTA.js";import"./use-modal-DK8XHZ9A.js";import"./index-DpxZFE0y.js";import"./TabsList.vue_vue_type_script_setup_true_lang-DM4no0zC.js";import"./rotate-cw-CQF0mzCK.js";const r=a(!1);function P(){function e(){r.value=!0}return{handleOpenPreference:e,openPreferences:r}}const n={};function c(e,i){return s(),t("div")}const x=o(n,[["render",c]]);export{N as AuthPageLayout,O as AuthenticationColorToggle,U as AuthenticationLayoutToggle,k as BasicLayout,B as Breadcrumb,T as CheckUpdates,w as GlobalSearch,A as IFrameRouterView,x as IFrameView,E as LanguageToggle,v as LockScreen,y as LockScreenModal,S as Notification,V as Preferences,C as PreferencesButton,G as ThemeToggle,F as UserDropdown,P as useOpenPreferences};
