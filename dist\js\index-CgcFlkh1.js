import{b,d as B,e as T,f as w,c as A,_ as v,g as y,N as S,i as V,h as C,a as F}from"./layout.vue_vue_type_script_setup_true_lang-DxruNl34.js";import{A as N,_ as O,a as U}from"./authentication-CJATLJan.js";import{i as E,h as G}from"./theme-toggle.vue_vue_type_script_setup_true_lang-BkcvcZZW.js";import{bk as o}from"./bootstrap-DlHXJWd_.js";import{P as a,av as t,ab as s}from"../jse/index-index-DYNcUVMZ.js";import"./avatar.vue_vue_type_script_setup_true_lang-CiaVJ8BJ.js";import"./use-vben-form-D_KgkLrU.js";import"./render-content.vue_vue_type_script_lang-D2VaNJ52.js";import"./use-modal-C76TZrr_.js";import"./index-cbK0tipA.js";import"./TabsList.vue_vue_type_script_setup_true_lang-DsT_45_5.js";import"./rotate-cw-C3yB0h8-.js";const r=a(!1);function P(){function e(){r.value=!0}return{handleOpenPreference:e,openPreferences:r}}const n={};function c(e,i){return s(),t("div")}const k=o(n,[["render",c]]);export{N as AuthPageLayout,O as AuthenticationColorToggle,U as AuthenticationLayoutToggle,b as BasicLayout,B as Breadcrumb,T as CheckUpdates,w as GlobalSearch,A as IFrameRouterView,k as IFrameView,E as LanguageToggle,v as LockScreen,y as LockScreenModal,S as Notification,V as Preferences,C as PreferencesButton,G as ThemeToggle,F as UserDropdown,P as useOpenPreferences};
