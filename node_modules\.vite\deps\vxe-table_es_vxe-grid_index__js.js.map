{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-table@4.16.11_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/grid/src/grid.js", "../../../../../node_modules/.pnpm/vxe-table@4.16.11_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/grid/src/props.js", "../../../../../node_modules/.pnpm/vxe-table@4.16.11_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/grid/src/emits.js", "../../../../../node_modules/.pnpm/vxe-table@4.16.11_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/grid/index.js", "../../../../../node_modules/.pnpm/vxe-table@4.16.11_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/vxe-grid/index.js"], "sourcesContent": ["import { h, ref, computed, provide, reactive, onUnmounted, watch, nextTick, onMounted } from 'vue';\nimport { defineVxeComponent } from '../../ui/src/comp';\nimport XEUtils from 'xe-utils';\nimport { getLastZIndex, nextZIndex, isEnableConf } from '../../ui/src/utils';\nimport { getOffsetHeight, getPaddingTopBottomSize, getDomNode, toCssUnit } from '../../ui/src/dom';\nimport { VxeUI } from '../../ui';\nimport { gridProps } from './props';\nimport { gridEmits } from './emits';\nimport { getSlotVNs } from '../../ui/src/vn';\nimport { warnLog, errLog } from '../../ui/src/log';\nimport { tableEmits } from '../../table/src/emits';\nimport { tableProps } from '../../table/src/props';\nimport VxeTableComponent from '../../table/src/table';\nimport VxeToolbarComponent from '../../toolbar/src/toolbar';\nconst { getConfig, getI18n, commands, hooks, useFns, createEvent, globalEvents, GLOBAL_EVENT_KEYS, renderEmptyElement } = VxeUI;\nconst tableComponentPropKeys = Object.keys(tableProps);\nconst tableComponentMethodKeys = ['clearAll', 'syncData', 'updateData', 'loadData', 'reloadData', 'reloadRow', 'loadColumn', 'reloadColumn', 'getRowNode', 'getColumnNode', 'getRowIndex', 'getVTRowIndex', 'getVMRowIndex', 'getColumnIndex', 'getVTColumnIndex', 'getVMColumnIndex', 'setRow', 'createData', 'createRow', 'revertData', 'clearData', 'isRemoveByRow', 'isInsertByRow', 'isUpdateByRow', 'getColumns', 'getColumnById', 'getColumnByField', 'getTableColumn', 'getFullColumns', 'getData', 'getCheckboxRecords', 'getParentRow', 'getTreeRowChildren', 'getTreeRowLevel', 'getTreeParentRow', 'getRowSeq', 'getRowById', 'getRowid', 'getTableData', 'getFullData', 'setColumnFixed', 'clearColumnFixed', 'setColumnWidth', 'getColumnWidth', 'recalcRowHeight', 'setRowHeightConf', 'getRowHeightConf', 'setRowHeight', 'getRowHeight', 'hideColumn', 'showColumn', 'resetColumn', 'refreshColumn', 'refreshScroll', 'recalculate', 'closeTooltip', 'isAllCheckboxChecked', 'isAllCheckboxIndeterminate', 'getCheckboxIndeterminateRecords', 'setCheckboxRow', 'setCheckboxRowKey', 'isCheckedByCheckboxRow', 'isCheckedByCheckboxRowKey', 'isIndeterminateByCheckboxRow', 'isIndeterminateByCheckboxRowKey', 'toggleCheckboxRow', 'setAllCheckboxRow', 'getRadioReserveRecord', 'clearRadioReserve', 'getCheckboxReserveRecords', 'clearCheckboxReserve', 'toggleAllCheckboxRow', 'clearCheckboxRow', 'setCurrentRow', 'isCheckedByRadioRow', 'isCheckedByRadioRowKey', 'setRadioRow', 'setRadioRowKey', 'clearCurrentRow', 'clearRadioRow', 'getCurrentRecord', 'getRadioRecord', 'getCurrentColumn', 'setCurrentColumn', 'clearCurrentColumn', 'setPendingRow', 'togglePendingRow', 'hasPendingByRow', 'isPendingByRow', 'getPendingRecords', 'clearPendingRow', 'setFilterByEvent', 'sort', 'setSort', 'setSortByEvent', 'clearSort', 'clearSortByEvent', 'isSort', 'getSortColumns', 'closeFilter', 'isFilter', 'clearFilterByEvent', 'isActiveFilterByColumn', 'isRowExpandLoaded', 'clearRowExpandLoaded', 'reloadRowExpand', 'reloadRowExpand', 'toggleRowExpand', 'setAllRowExpand', 'setRowExpand', 'isExpandByRow', 'isRowExpandByRow', 'clearRowExpand', 'clearRowExpandReserve', 'getRowExpandRecords', 'getTreeExpandRecords', 'isTreeExpandLoaded', 'clearTreeExpandLoaded', 'reloadTreeExpand', 'reloadTreeChilds', 'toggleTreeExpand', 'setAllTreeExpand', 'setTreeExpand', 'isTreeExpandByRow', 'clearTreeExpand', 'clearTreeExpandReserve', 'getScroll', 'scrollTo', 'scrollToRow', 'scrollToColumn', 'clearScroll', 'updateFooter', 'updateStatus', 'setMergeCells', 'removeInsertRow', 'removeMergeCells', 'getMergeCells', 'setMergeHeaderCells', 'removeMergeHeaderCells', 'getMergeHeaderCells', 'clearMergeHeaderCells', 'clearMergeCells', 'setMergeFooterItems', 'removeMergeFooterItems', 'getMergeFooterItems', 'clearMergeFooterItems', 'getCustomStoreData', 'setRowGroupExpand', 'setRowGroupExpandByField', 'setAllRowGroupExpand', 'clearRowGroupExpand', 'isRowGroupExpandByRow', 'isRowGroupRecord', 'isAggregateRecord', 'isAggregateExpandByRow', 'getAggregateContentByRow', 'getAggregateRowChildren', 'setRowGroups', 'clearRowGroups', 'openTooltip', 'moveColumnTo', 'moveRowTo', 'getCellLabel', 'getCellElement', 'focus', 'blur', 'connect', 'connectToolbar'];\nfunction createInternalData() {\n    return {};\n}\nexport default defineVxeComponent({\n    name: 'VxeGrid',\n    props: gridProps,\n    emits: gridEmits,\n    setup(props, context) {\n        var _a;\n        const { slots, emit } = context;\n        const xID = XEUtils.uniqueId();\n        // 使用已安装的组件，如果未安装则不渲染\n        const VxeUIFormComponent = VxeUI.getComponent('VxeForm');\n        const VxeUIPagerComponent = VxeUI.getComponent('VxePager');\n        const defaultLayouts = [['Form'], ['Toolbar', 'Top', 'Table', 'Bottom', 'Pager']];\n        const { computeSize } = useFns.useSize(props);\n        const reactData = reactive({\n            tableLoading: false,\n            proxyInited: false,\n            isZMax: false,\n            tableData: [],\n            filterData: [],\n            formData: {},\n            sortData: [],\n            tZindex: 0,\n            tablePage: {\n                total: 0,\n                pageSize: ((_a = getConfig().pager) === null || _a === void 0 ? void 0 : _a.pageSize) || 10,\n                currentPage: 1\n            }\n        });\n        const internalData = createInternalData();\n        const refElem = ref();\n        const refTable = ref();\n        const refForm = ref();\n        const refToolbar = ref();\n        const refPager = ref();\n        const refFormWrapper = ref();\n        const refToolbarWrapper = ref();\n        const refTopWrapper = ref();\n        const refBottomWrapper = ref();\n        const refPagerWrapper = ref();\n        const extendTableMethods = (methodKeys) => {\n            const funcs = {};\n            methodKeys.forEach(name => {\n                funcs[name] = (...args) => {\n                    const $xeTable = refTable.value;\n                    if ($xeTable && $xeTable[name]) {\n                        return $xeTable[name](...args);\n                    }\n                };\n            });\n            return funcs;\n        };\n        const gridExtendTableMethods = extendTableMethods(tableComponentMethodKeys);\n        tableComponentMethodKeys.forEach(name => {\n            gridExtendTableMethods[name] = (...args) => {\n                const $xeTable = refTable.value;\n                if ($xeTable && $xeTable[name]) {\n                    return $xeTable && $xeTable[name](...args);\n                }\n            };\n        });\n        const computeProxyOpts = computed(() => {\n            return XEUtils.merge({}, XEUtils.clone(getConfig().grid.proxyConfig, true), props.proxyConfig);\n        });\n        const computeIsRespMsg = computed(() => {\n            const proxyOpts = computeProxyOpts.value;\n            return !!(XEUtils.isBoolean(proxyOpts.message) ? proxyOpts.message : proxyOpts.showResponseMsg);\n        });\n        const computeIsActiveMsg = computed(() => {\n            const proxyOpts = computeProxyOpts.value;\n            return XEUtils.isBoolean(proxyOpts.showActionMsg) ? proxyOpts.showActionMsg : !!proxyOpts.showActiveMsg;\n        });\n        const computePagerOpts = computed(() => {\n            return Object.assign({}, getConfig().grid.pagerConfig, props.pagerConfig);\n        });\n        const computeFormOpts = computed(() => {\n            return Object.assign({}, getConfig().grid.formConfig, props.formConfig);\n        });\n        const computeToolbarOpts = computed(() => {\n            return Object.assign({}, getConfig().grid.toolbarConfig, props.toolbarConfig);\n        });\n        const computeZoomOpts = computed(() => {\n            return Object.assign({}, getConfig().grid.zoomConfig, props.zoomConfig);\n        });\n        const computeStyles = computed(() => {\n            const { height, maxHeight } = props;\n            const { isZMax, tZindex } = reactData;\n            const stys = {};\n            if (isZMax) {\n                stys.zIndex = tZindex;\n            }\n            else {\n                if (height) {\n                    stys.height = height === 'auto' || height === '100%' ? '100%' : toCssUnit(height);\n                }\n                if (maxHeight) {\n                    stys.maxHeight = maxHeight === 'auto' || maxHeight === '100%' ? '100%' : toCssUnit(maxHeight);\n                }\n            }\n            return stys;\n        });\n        const computeTableExtendProps = computed(() => {\n            const rest = {};\n            tableComponentPropKeys.forEach((key) => {\n                rest[key] = props[key];\n            });\n            return rest;\n        });\n        const computeTableProps = computed(() => {\n            const { seqConfig, pagerConfig, editConfig, proxyConfig } = props;\n            const { isZMax, tablePage } = reactData;\n            const tableExtendProps = computeTableExtendProps.value;\n            const proxyOpts = computeProxyOpts.value;\n            const pagerOpts = computePagerOpts.value;\n            const isLoading = computeIsLoading.value;\n            const tProps = Object.assign({}, tableExtendProps);\n            if (isZMax) {\n                if (tableExtendProps.maxHeight) {\n                    tProps.maxHeight = '100%';\n                }\n                else {\n                    tProps.height = '100%';\n                }\n            }\n            if (proxyConfig && isEnableConf(proxyOpts)) {\n                tProps.loading = isLoading;\n                if (pagerConfig && proxyOpts.seq && isEnableConf(pagerOpts)) {\n                    tProps.seqConfig = Object.assign({}, seqConfig, { startIndex: (tablePage.currentPage - 1) * tablePage.pageSize });\n                }\n            }\n            if (editConfig) {\n                tProps.editConfig = Object.assign({}, editConfig);\n            }\n            return tProps;\n        });\n        const computeCurrLayoutConf = computed(() => {\n            const { layouts } = props;\n            let confs = [];\n            if (layouts && layouts.length) {\n                confs = layouts;\n            }\n            else {\n                confs = getConfig().grid.layouts || defaultLayouts;\n            }\n            let headKeys = [];\n            let bodyKeys = [];\n            let footKeys = [];\n            if (confs.length) {\n                if (XEUtils.isArray(confs[0])) {\n                    headKeys = confs[0];\n                    bodyKeys = (confs[1] || []);\n                    footKeys = (confs[2] || []);\n                }\n                else {\n                    bodyKeys = confs;\n                }\n            }\n            return {\n                headKeys,\n                bodyKeys,\n                footKeys\n            };\n        });\n        const computeCustomCurrentPageFlag = computed(() => {\n            const pagerOpts = computePagerOpts.value;\n            return pagerOpts.currentPage;\n        });\n        const computeCustomPageSizeFlag = computed(() => {\n            const pagerOpts = computePagerOpts.value;\n            return pagerOpts.pageSize;\n        });\n        const computeCustomTotalFlag = computed(() => {\n            const pagerOpts = computePagerOpts.value;\n            return pagerOpts.total;\n        });\n        const computePageCount = computed(() => {\n            const { tablePage } = reactData;\n            return Math.max(Math.ceil(tablePage.total / tablePage.pageSize), 1);\n        });\n        const computeIsLoading = computed(() => {\n            const { loading, proxyConfig } = props;\n            const { tableLoading } = reactData;\n            const proxyOpts = computeProxyOpts.value;\n            const { showLoading } = proxyOpts;\n            return loading || (tableLoading && showLoading && proxyConfig && isEnableConf(proxyOpts));\n        });\n        const refMaps = {\n            refElem,\n            refTable,\n            refForm,\n            refToolbar,\n            refPager\n        };\n        const computeMaps = {\n            computeProxyOpts,\n            computePagerOpts,\n            computeFormOpts,\n            computeToolbarOpts,\n            computeZoomOpts\n        };\n        const $xeGrid = {\n            xID,\n            props: props,\n            context,\n            reactData,\n            internalData,\n            getRefMaps: () => refMaps,\n            getComputeMaps: () => computeMaps\n        };\n        const initToolbar = () => {\n            const toolbarOpts = computeToolbarOpts.value;\n            if (props.toolbarConfig && isEnableConf(toolbarOpts)) {\n                nextTick(() => {\n                    const $xeTable = refTable.value;\n                    const $xeToolbar = refToolbar.value;\n                    if ($xeTable && $xeToolbar) {\n                        $xeTable.connectToolbar($xeToolbar);\n                    }\n                });\n            }\n        };\n        const getFormData = () => {\n            const { proxyConfig } = props;\n            const { formData } = reactData;\n            const proxyOpts = computeProxyOpts.value;\n            const formOpts = computeFormOpts.value;\n            return proxyConfig && isEnableConf(proxyOpts) && proxyOpts.form ? formData : formOpts.data;\n        };\n        const initPages = (propKey) => {\n            const { tablePage } = reactData;\n            const { pagerConfig } = props;\n            const pagerOpts = computePagerOpts.value;\n            if (pagerConfig && isEnableConf(pagerOpts)) {\n                if (propKey) {\n                    if (pagerOpts[propKey]) {\n                        tablePage[propKey] = XEUtils.toNumber(pagerOpts[propKey]);\n                    }\n                }\n                else {\n                    const { currentPage, pageSize, total } = pagerOpts;\n                    if (currentPage) {\n                        tablePage.currentPage = currentPage;\n                    }\n                    if (pageSize) {\n                        tablePage.pageSize = pageSize;\n                    }\n                    if (total) {\n                        tablePage.total = total;\n                    }\n                }\n            }\n        };\n        const triggerPendingEvent = (code) => {\n            const isActiveMsg = computeIsActiveMsg.value;\n            const $xeTable = refTable.value;\n            const selectRecords = $xeTable ? $xeTable.getCheckboxRecords() : [];\n            if (selectRecords.length) {\n                if ($xeTable) {\n                    $xeTable.togglePendingRow(selectRecords);\n                }\n                $xeGrid.clearCheckboxRow();\n            }\n            else {\n                if (isActiveMsg) {\n                    if (VxeUI.modal) {\n                        VxeUI.modal.message({ id: code, content: getI18n('vxe.grid.selectOneRecord'), status: 'warning' });\n                    }\n                }\n            }\n        };\n        const getRespMsg = (rest, defaultMsg) => {\n            const proxyOpts = computeProxyOpts.value;\n            const resConfigs = proxyOpts.response || proxyOpts.props || {};\n            const messageProp = resConfigs.message;\n            const $xeTable = refTable.value;\n            let msg;\n            if (rest && messageProp) {\n                msg = XEUtils.isFunction(messageProp) ? messageProp({ data: rest, $table: $xeTable, $grid: $xeGrid, $gantt: null }) : XEUtils.get(rest, messageProp);\n            }\n            return msg || getI18n(defaultMsg);\n        };\n        const handleDeleteRow = (code, alertKey, callback) => {\n            const isActiveMsg = computeIsActiveMsg.value;\n            const selectRecords = $xeGrid.getCheckboxRecords();\n            if (isActiveMsg) {\n                if (selectRecords.length) {\n                    if (VxeUI.modal) {\n                        return VxeUI.modal.confirm({ id: `cfm_${code}`, content: getI18n(alertKey), escClosable: true }).then((type) => {\n                            if (type === 'confirm') {\n                                return callback();\n                            }\n                        });\n                    }\n                }\n                else {\n                    if (VxeUI.modal) {\n                        VxeUI.modal.message({ id: `msg_${code}`, content: getI18n('vxe.grid.selectOneRecord'), status: 'warning' });\n                    }\n                }\n            }\n            else {\n                if (selectRecords.length) {\n                    callback();\n                }\n            }\n            return Promise.resolve();\n        };\n        const pageChangeEvent = (params) => {\n            const { proxyConfig } = props;\n            const { tablePage } = reactData;\n            const { $event, currentPage, pageSize } = params;\n            const proxyOpts = computeProxyOpts.value;\n            tablePage.currentPage = currentPage;\n            tablePage.pageSize = pageSize;\n            $xeGrid.dispatchEvent('page-change', params, $event);\n            if (proxyConfig && isEnableConf(proxyOpts)) {\n                $xeGrid.commitProxy('query').then((rest) => {\n                    $xeGrid.dispatchEvent('proxy-query', rest, $event);\n                });\n            }\n        };\n        const handleSortEvent = (params) => {\n            const $xeTable = refTable.value;\n            const { proxyConfig } = props;\n            if (!$xeTable) {\n                return;\n            }\n            const { computeSortOpts } = $xeTable.getComputeMaps();\n            const proxyOpts = computeProxyOpts.value;\n            const sortOpts = computeSortOpts.value;\n            // 如果是服务端排序\n            if (sortOpts.remote) {\n                reactData.sortData = params.sortList;\n                if (proxyConfig && isEnableConf(proxyOpts)) {\n                    reactData.tablePage.currentPage = 1;\n                    $xeGrid.commitProxy('query').then((rest) => {\n                        $xeGrid.dispatchEvent('proxy-query', rest, params.$event);\n                    });\n                }\n            }\n        };\n        const sortChangeEvent = (params) => {\n            handleSortEvent(params);\n            $xeGrid.dispatchEvent('sort-change', params, params.$event);\n        };\n        const clearAllSortEvent = (params) => {\n            handleSortEvent(params);\n            $xeGrid.dispatchEvent('clear-all-sort', params, params.$event);\n        };\n        const handleFilterEvent = (params) => {\n            const $xeTable = refTable.value;\n            const { proxyConfig } = props;\n            if (!$xeTable) {\n                return;\n            }\n            const { computeFilterOpts } = $xeTable.getComputeMaps();\n            const proxyOpts = computeProxyOpts.value;\n            const filterOpts = computeFilterOpts.value;\n            // 如果是服务端过滤\n            if (filterOpts.remote) {\n                reactData.filterData = params.filterList;\n                if (proxyConfig && isEnableConf(proxyOpts)) {\n                    reactData.tablePage.currentPage = 1;\n                    $xeGrid.commitProxy('query').then((rest) => {\n                        $xeGrid.dispatchEvent('proxy-query', rest, params.$event);\n                    });\n                }\n            }\n        };\n        const filterChangeEvent = (params) => {\n            handleFilterEvent(params);\n            $xeGrid.dispatchEvent('filter-change', params, params.$event);\n        };\n        const clearAllFilterEvent = (params) => {\n            handleFilterEvent(params);\n            $xeGrid.dispatchEvent('clear-all-filter', params, params.$event);\n        };\n        const submitFormEvent = (params) => {\n            const { proxyConfig } = props;\n            const proxyOpts = computeProxyOpts.value;\n            if (reactData.tableLoading) {\n                return;\n            }\n            if (proxyConfig && isEnableConf(proxyOpts)) {\n                $xeGrid.commitProxy('reload').then((rest) => {\n                    $xeGrid.dispatchEvent('proxy-query', Object.assign(Object.assign({}, rest), { isReload: true }), params.$event);\n                });\n            }\n            $xeGrid.dispatchEvent('form-submit', params, params.$event);\n        };\n        const resetFormEvent = (params) => {\n            const $xeTable = refTable.value;\n            const { proxyConfig } = props;\n            const { $event } = params;\n            const proxyOpts = computeProxyOpts.value;\n            if (proxyConfig && isEnableConf(proxyOpts)) {\n                if ($xeTable) {\n                    $xeTable.clearScroll();\n                }\n                $xeGrid.commitProxy('reload').then((rest) => {\n                    $xeGrid.dispatchEvent('proxy-query', Object.assign(Object.assign({}, rest), { isReload: true }), $event);\n                });\n            }\n            $xeGrid.dispatchEvent('form-reset', params, $event);\n        };\n        const submitInvalidEvent = (params) => {\n            $xeGrid.dispatchEvent('form-submit-invalid', params, params.$event);\n        };\n        const collapseEvent = (params) => {\n            const { $event } = params;\n            $xeGrid.dispatchEvent('form-toggle-collapse', params, $event);\n            $xeGrid.dispatchEvent('form-collapse', params, $event);\n        };\n        const handleZoom = (isMax) => {\n            const { isZMax } = reactData;\n            if (isMax ? !isZMax : isZMax) {\n                reactData.isZMax = !isZMax;\n                if (reactData.tZindex < getLastZIndex()) {\n                    reactData.tZindex = nextZIndex();\n                }\n            }\n            return nextTick()\n                .then(() => $xeGrid.recalculate(true))\n                .then(() => {\n                setTimeout(() => $xeGrid.recalculate(true), 15);\n                return reactData.isZMax;\n            });\n        };\n        const getFuncSlot = (optSlots, slotKey) => {\n            const funcSlot = optSlots[slotKey];\n            if (funcSlot) {\n                if (XEUtils.isString(funcSlot)) {\n                    if (slots[funcSlot]) {\n                        return slots[funcSlot];\n                    }\n                    else {\n                        errLog('vxe.error.notSlot', [funcSlot]);\n                    }\n                }\n                else {\n                    return funcSlot;\n                }\n            }\n            return null;\n        };\n        const getConfigSlot = (slotConfigs) => {\n            const slotConf = {};\n            XEUtils.objectMap(slotConfigs, (slotFunc, slotKey) => {\n                if (slotFunc) {\n                    if (XEUtils.isString(slotFunc)) {\n                        if (slots[slotFunc]) {\n                            slotConf[slotKey] = slots[slotFunc];\n                        }\n                        else {\n                            errLog('vxe.error.notSlot', [slotFunc]);\n                        }\n                    }\n                    else {\n                        slotConf[slotKey] = slotFunc;\n                    }\n                }\n            });\n            return slotConf;\n        };\n        /**\n         * 渲染表单\n         */\n        const renderForm = () => {\n            const { formConfig, proxyConfig } = props;\n            const { formData } = reactData;\n            const proxyOpts = computeProxyOpts.value;\n            const formOpts = computeFormOpts.value;\n            if ((formConfig && isEnableConf(formOpts)) || slots.form) {\n                let slotVNs = [];\n                if (slots.form) {\n                    slotVNs = slots.form({ $grid: $xeGrid, $gantt: null });\n                }\n                else {\n                    if (formOpts.items) {\n                        const formSlots = {};\n                        if (!formOpts.inited) {\n                            formOpts.inited = true;\n                            const beforeItem = proxyOpts.beforeItem;\n                            if (proxyOpts && beforeItem) {\n                                formOpts.items.forEach((item) => {\n                                    beforeItem({ $grid: $xeGrid, $gantt: null, item });\n                                });\n                            }\n                        }\n                        // 处理插槽\n                        formOpts.items.forEach((item) => {\n                            XEUtils.each(item.slots, (func) => {\n                                if (!XEUtils.isFunction(func)) {\n                                    if (slots[func]) {\n                                        formSlots[func] = slots[func];\n                                    }\n                                }\n                            });\n                        });\n                        if (VxeUIFormComponent) {\n                            slotVNs.push(h(VxeUIFormComponent, Object.assign(Object.assign({ ref: refForm }, Object.assign({}, formOpts, {\n                                data: proxyConfig && isEnableConf(proxyOpts) && proxyOpts.form ? formData : formOpts.data\n                            })), { onSubmit: submitFormEvent, onReset: resetFormEvent, onSubmitInvalid: submitInvalidEvent, onCollapse: collapseEvent }), formSlots));\n                        }\n                    }\n                }\n                return h('div', {\n                    ref: refFormWrapper,\n                    key: 'form',\n                    class: 'vxe-grid--form-wrapper'\n                }, slotVNs);\n            }\n            return renderEmptyElement($xeGrid);\n        };\n        /**\n         * 渲染工具栏\n         */\n        const renderToolbar = () => {\n            const { toolbarConfig } = props;\n            const toolbarOpts = computeToolbarOpts.value;\n            const toolbarSlot = slots.toolbar;\n            if ((toolbarConfig && isEnableConf(toolbarOpts)) || toolbarSlot) {\n                let slotVNs = [];\n                if (toolbarSlot) {\n                    slotVNs = toolbarSlot({ $grid: $xeGrid, $gantt: null });\n                }\n                else {\n                    const toolbarOptSlots = toolbarOpts.slots;\n                    const toolbarSlots = {};\n                    if (toolbarOptSlots) {\n                        const buttonsSlot = getFuncSlot(toolbarOptSlots, 'buttons');\n                        const buttonPrefixSlot = getFuncSlot(toolbarOptSlots, 'buttonPrefix');\n                        const buttonSuffixSlot = getFuncSlot(toolbarOptSlots, 'buttonSuffix');\n                        const toolsSlot = getFuncSlot(toolbarOptSlots, 'tools');\n                        const toolPrefixSlot = getFuncSlot(toolbarOptSlots, 'toolPrefix');\n                        const toolSuffixSlot = getFuncSlot(toolbarOptSlots, 'toolSuffix');\n                        if (buttonsSlot) {\n                            toolbarSlots.buttons = buttonsSlot;\n                        }\n                        if (buttonPrefixSlot) {\n                            toolbarSlots.buttonPrefix = buttonPrefixSlot;\n                        }\n                        if (buttonSuffixSlot) {\n                            toolbarSlots.buttonSuffix = buttonSuffixSlot;\n                        }\n                        if (toolsSlot) {\n                            toolbarSlots.tools = toolsSlot;\n                        }\n                        if (toolPrefixSlot) {\n                            toolbarSlots.toolPrefix = toolPrefixSlot;\n                        }\n                        if (toolSuffixSlot) {\n                            toolbarSlots.toolSuffix = toolSuffixSlot;\n                        }\n                    }\n                    slotVNs.push(h(VxeToolbarComponent, Object.assign(Object.assign({ ref: refToolbar }, toolbarOpts), { slots: undefined }), toolbarSlots));\n                }\n                return h('div', {\n                    ref: refToolbarWrapper,\n                    key: 'toolbar',\n                    class: 'vxe-grid--toolbar-wrapper'\n                }, slotVNs);\n            }\n            return renderEmptyElement($xeGrid);\n        };\n        /**\n         * 渲染表格顶部区域\n         */\n        const renderTop = () => {\n            const topSlot = slots.top;\n            if (topSlot) {\n                return h('div', {\n                    ref: refTopWrapper,\n                    key: 'top',\n                    class: 'vxe-grid--top-wrapper'\n                }, topSlot({ $grid: $xeGrid, $gantt: null }));\n            }\n            return renderEmptyElement($xeGrid);\n        };\n        const renderTableLeft = () => {\n            const leftSlot = slots.left;\n            if (leftSlot) {\n                return h('div', {\n                    class: 'vxe-grid--left-wrapper'\n                }, leftSlot({ $grid: $xeGrid, $gantt: null }));\n            }\n            return renderEmptyElement($xeGrid);\n        };\n        const renderTableRight = () => {\n            const rightSlot = slots.right;\n            if (rightSlot) {\n                return h('div', {\n                    class: 'vxe-grid--right-wrapper'\n                }, rightSlot({ $grid: $xeGrid, $gantt: null }));\n            }\n            return renderEmptyElement($xeGrid);\n        };\n        /**\n         * 渲染表格\n         */\n        const renderTable = () => {\n            const { proxyConfig } = props;\n            const tableProps = computeTableProps.value;\n            const proxyOpts = computeProxyOpts.value;\n            const tableOns = Object.assign({}, tableCompEvents);\n            const emptySlot = slots.empty;\n            const loadingSlot = slots.loading;\n            const rowDragIconSlot = slots.rowDragIcon || slots['row-drag-icon'];\n            const columnDragIconSlot = slots.columnDragIcon || slots['column-drag-icon'];\n            const headerTooltipSlot = slots.headerTooltip || slots['header-tooltip'];\n            const tooltipSlot = slots.tooltip;\n            const footerTooltipSlot = slots.footerTooltip || slots['footer-tooltip'];\n            if (proxyConfig && isEnableConf(proxyOpts)) {\n                if (proxyOpts.sort) {\n                    tableOns.onSortChange = sortChangeEvent;\n                    tableOns.onClearAllSort = clearAllSortEvent;\n                }\n                if (proxyOpts.filter) {\n                    tableOns.onFilterChange = filterChangeEvent;\n                    tableOns.onClearAllFilter = clearAllFilterEvent;\n                }\n            }\n            const slotObj = {};\n            if (emptySlot) {\n                slotObj.empty = emptySlot;\n            }\n            if (loadingSlot) {\n                slotObj.loading = loadingSlot;\n            }\n            if (rowDragIconSlot) {\n                slotObj.rowDragIcon = rowDragIconSlot;\n            }\n            if (columnDragIconSlot) {\n                slotObj.columnDragIcon = columnDragIconSlot;\n            }\n            if (headerTooltipSlot) {\n                slotObj.headerTooltip = headerTooltipSlot;\n            }\n            if (tooltipSlot) {\n                slotObj.tooltip = tooltipSlot;\n            }\n            if (footerTooltipSlot) {\n                slotObj.footerTooltip = footerTooltipSlot;\n            }\n            return h('div', {\n                class: 'vxe-grid--table-wrapper'\n            }, [\n                h(VxeTableComponent, Object.assign(Object.assign({ ref: refTable }, tableProps), tableOns), slotObj)\n            ]);\n        };\n        /**\n         * 渲染表格底部区域\n         */\n        const renderBottom = () => {\n            if (slots.bottom) {\n                return h('div', {\n                    ref: refBottomWrapper,\n                    key: 'bottom',\n                    class: 'vxe-grid--bottom-wrapper'\n                }, slots.bottom({ $grid: $xeGrid, $gantt: null }));\n            }\n            return renderEmptyElement($xeGrid);\n        };\n        /**\n         * 渲染分页\n         */\n        const renderPager = () => {\n            const { proxyConfig, pagerConfig } = props;\n            const proxyOpts = computeProxyOpts.value;\n            const pagerOpts = computePagerOpts.value;\n            const pagerSlot = slots.pager;\n            if ((pagerConfig && isEnableConf(pagerOpts)) || slots.pager) {\n                return h('div', {\n                    ref: refPagerWrapper,\n                    key: 'pager',\n                    class: 'vxe-grid--pager-wrapper'\n                }, pagerSlot\n                    ? pagerSlot({ $grid: $xeGrid, $gantt: null })\n                    : [\n                        VxeUIPagerComponent\n                            ? h(VxeUIPagerComponent, Object.assign(Object.assign(Object.assign({ ref: refPager }, pagerOpts), (proxyConfig && isEnableConf(proxyOpts) ? reactData.tablePage : {})), { onPageChange: pageChangeEvent }), getConfigSlot(pagerOpts.slots))\n                            : renderEmptyElement($xeGrid)\n                    ]);\n            }\n            return renderEmptyElement($xeGrid);\n        };\n        const renderChildLayout = (layoutKeys) => {\n            const childVNs = [];\n            layoutKeys.forEach(key => {\n                switch (key) {\n                    case 'Form':\n                        childVNs.push(renderForm());\n                        break;\n                    case 'Toolbar':\n                        childVNs.push(renderToolbar());\n                        break;\n                    case 'Top':\n                        childVNs.push(renderTop());\n                        break;\n                    case 'Table':\n                        childVNs.push(h('div', {\n                            key: 'table',\n                            class: 'vxe-grid--table-container'\n                        }, [\n                            renderTableLeft(),\n                            renderTable(),\n                            renderTableRight()\n                        ]));\n                        break;\n                    case 'Bottom':\n                        childVNs.push(renderBottom());\n                        break;\n                    case 'Pager':\n                        childVNs.push(renderPager());\n                        break;\n                    default:\n                        errLog('vxe.error.notProp', [`layouts -> ${key}`]);\n                        break;\n                }\n            });\n            return childVNs;\n        };\n        const renderLayout = () => {\n            const currLayoutConf = computeCurrLayoutConf.value;\n            const { headKeys, bodyKeys, footKeys } = currLayoutConf;\n            const asideLeftSlot = slots.asideLeft || slots['aside-left'];\n            const asideRightSlot = slots.asideRight || slots['aside-right'];\n            return [\n                h('div', {\n                    class: 'vxe-grid--layout-header-wrapper'\n                }, renderChildLayout(headKeys)),\n                h('div', {\n                    class: 'vxe-grid--layout-body-wrapper'\n                }, [\n                    asideLeftSlot\n                        ? h('div', {\n                            class: 'vxe-grid--layout-aside-left-wrapper'\n                        }, asideLeftSlot({}))\n                        : renderEmptyElement($xeGrid),\n                    h('div', {\n                        class: 'vxe-grid--layout-body-content-wrapper'\n                    }, renderChildLayout(bodyKeys)),\n                    asideRightSlot\n                        ? h('div', {\n                            class: 'vxe-grid--layout-aside-right-wrapper'\n                        }, asideRightSlot({}))\n                        : renderEmptyElement($xeGrid)\n                ]),\n                h('div', {\n                    class: 'vxe-grid--layout-footer-wrapper'\n                }, renderChildLayout(footKeys))\n            ];\n        };\n        const tableCompEvents = {};\n        tableEmits.forEach(name => {\n            const type = XEUtils.camelCase(`on-${name}`);\n            tableCompEvents[type] = (...args) => emit(name, ...args);\n        });\n        const getDefaultFormData = () => {\n            const formOpts = computeFormOpts.value;\n            if (formOpts.items) {\n                const fData = {};\n                formOpts.items.forEach(item => {\n                    const { field, itemRender } = item;\n                    if (field) {\n                        let itemValue = null;\n                        if (itemRender) {\n                            const { startField, endField, defaultValue } = itemRender;\n                            if (XEUtils.isFunction(defaultValue)) {\n                                itemValue = defaultValue({ item });\n                            }\n                            else if (!XEUtils.isUndefined(defaultValue)) {\n                                itemValue = defaultValue;\n                            }\n                            if (startField && endField) {\n                                XEUtils.set(fData, startField, null);\n                                XEUtils.set(fData, endField, null);\n                            }\n                        }\n                        fData[field] = itemValue;\n                    }\n                });\n                return fData;\n            }\n            return {};\n        };\n        const initProxy = () => {\n            const { proxyConfig, formConfig } = props;\n            const { proxyInited } = reactData;\n            const proxyOpts = computeProxyOpts.value;\n            const formOpts = computeFormOpts.value;\n            if (proxyConfig && isEnableConf(proxyOpts)) {\n                if (formConfig && isEnableConf(formOpts) && proxyOpts.form && formOpts.items) {\n                    reactData.formData = getDefaultFormData();\n                }\n                if (!proxyInited) {\n                    reactData.proxyInited = true;\n                    if (proxyOpts.autoLoad !== false) {\n                        nextTick().then(() => $xeGrid.commitProxy('initial')).then((rest) => {\n                            dispatchEvent('proxy-query', Object.assign(Object.assign({}, rest), { isInited: true }), new Event('initial'));\n                        });\n                    }\n                }\n            }\n        };\n        const handleGlobalKeydownEvent = (evnt) => {\n            const zoomOpts = computeZoomOpts.value;\n            const isEsc = globalEvents.hasKey(evnt, GLOBAL_EVENT_KEYS.ESCAPE);\n            if (isEsc && reactData.isZMax && zoomOpts.escRestore !== false) {\n                $xeGrid.triggerZoomEvent(evnt);\n            }\n        };\n        const dispatchEvent = (type, params, evnt) => {\n            emit(type, createEvent(evnt, { $grid: $xeGrid, $gantt: null }, params));\n        };\n        const gridMethods = {\n            dispatchEvent,\n            getEl() {\n                return refElem.value;\n            },\n            /**\n             * 提交指令，支持 code 或 button\n             * @param {String/Object} code 字符串或对象\n             */\n            commitProxy(proxyTarget, ...args) {\n                const { proxyConfig, toolbarConfig, pagerConfig, editRules, validConfig } = props;\n                const { tablePage } = reactData;\n                const isActiveMsg = computeIsActiveMsg.value;\n                const isRespMsg = computeIsRespMsg.value;\n                const proxyOpts = computeProxyOpts.value;\n                const pagerOpts = computePagerOpts.value;\n                const toolbarOpts = computeToolbarOpts.value;\n                const { beforeQuery, afterQuery, beforeDelete, afterDelete, beforeSave, afterSave, ajax = {} } = proxyOpts;\n                const resConfigs = proxyOpts.response || proxyOpts.props || {};\n                const $xeTable = refTable.value;\n                if (!$xeTable) {\n                    return nextTick();\n                }\n                let formData = getFormData();\n                let button = null;\n                let code = null;\n                if (XEUtils.isString(proxyTarget)) {\n                    const { buttons } = toolbarOpts;\n                    const matchObj = toolbarConfig && isEnableConf(toolbarOpts) && buttons ? XEUtils.findTree(buttons, (item) => item.code === proxyTarget, { children: 'dropdowns' }) : null;\n                    button = matchObj ? matchObj.item : null;\n                    code = proxyTarget;\n                }\n                else {\n                    button = proxyTarget;\n                    code = button.code;\n                }\n                const btnParams = button ? button.params : null;\n                switch (code) {\n                    case 'insert':\n                        return $xeTable.insert({});\n                    case 'insert_edit':\n                        return $xeTable.insert({}).then(({ row }) => $xeTable.setEditRow(row, true));\n                    // 已废弃\n                    case 'insert_actived':\n                        return $xeTable.insert({}).then(({ row }) => $xeTable.setEditRow(row, true));\n                    // 已废弃\n                    case 'mark_cancel':\n                        triggerPendingEvent(code);\n                        break;\n                    case 'remove':\n                        return handleDeleteRow(code, 'vxe.grid.removeSelectRecord', () => $xeTable.removeCheckboxRow());\n                    case 'import':\n                        $xeTable.importData(btnParams);\n                        break;\n                    case 'open_import':\n                        $xeTable.openImport(btnParams);\n                        break;\n                    case 'export':\n                        $xeTable.exportData(btnParams);\n                        break;\n                    case 'open_export':\n                        $xeTable.openExport(btnParams);\n                        break;\n                    case 'reset_custom':\n                        return $xeTable.resetCustom(true);\n                    case 'initial':\n                    case 'reload':\n                    case 'query': {\n                        const ajaxMethods = ajax.query;\n                        const querySuccessMethods = ajax.querySuccess;\n                        const queryErrorMethods = ajax.queryError;\n                        if (ajaxMethods) {\n                            const isInited = code === 'initial';\n                            const isReload = code === 'reload';\n                            if (!isInited && reactData.tableLoading) {\n                                return nextTick();\n                            }\n                            let sortList = [];\n                            let filterList = [];\n                            let pageParams = {};\n                            if (pagerConfig) {\n                                if (isInited || isReload) {\n                                    // 重置分页\n                                    tablePage.currentPage = 1;\n                                }\n                                if (isEnableConf(pagerOpts)) {\n                                    pageParams = Object.assign({}, tablePage);\n                                }\n                            }\n                            if (isInited) {\n                                // 重置代理表单数据\n                                if (proxyConfig && isEnableConf(proxyOpts) && proxyOpts.form) {\n                                    formData = getDefaultFormData();\n                                    reactData.formData = formData;\n                                }\n                                if ($xeTable) {\n                                    const tableInternalData = $xeTable.internalData;\n                                    const { tableFullColumn, fullColumnFieldData } = tableInternalData;\n                                    const { computeSortOpts } = $xeTable.getComputeMaps();\n                                    const sortOpts = computeSortOpts.value;\n                                    let defaultSort = sortOpts.defaultSort;\n                                    tableFullColumn.forEach((column) => {\n                                        column.order = null;\n                                    });\n                                    // 如果使用默认排序\n                                    if (defaultSort) {\n                                        if (!XEUtils.isArray(defaultSort)) {\n                                            defaultSort = [defaultSort];\n                                        }\n                                        sortList = defaultSort.map((item) => {\n                                            const { field, order } = item;\n                                            const colRest = fullColumnFieldData[field];\n                                            if (colRest) {\n                                                const column = colRest.column;\n                                                if (column) {\n                                                    column.order = order;\n                                                }\n                                            }\n                                            return {\n                                                field,\n                                                property: field,\n                                                order\n                                            };\n                                        });\n                                    }\n                                    filterList = $xeTable.getCheckedFilters();\n                                }\n                            }\n                            else {\n                                if ($xeTable) {\n                                    if (isReload) {\n                                        $xeTable.clearAll();\n                                    }\n                                    else {\n                                        sortList = $xeTable.getSortColumns();\n                                        filterList = $xeTable.getCheckedFilters();\n                                    }\n                                }\n                            }\n                            const commitParams = {\n                                $table: $xeTable,\n                                $grid: $xeGrid,\n                                $gantt: null,\n                                code,\n                                button,\n                                isInited,\n                                isReload,\n                                page: pageParams,\n                                sort: sortList.length ? sortList[0] : {},\n                                sorts: sortList,\n                                filters: filterList,\n                                form: formData,\n                                options: ajaxMethods\n                            };\n                            reactData.sortData = sortList;\n                            reactData.filterData = filterList;\n                            reactData.tableLoading = true;\n                            return Promise.resolve((beforeQuery || ajaxMethods)(commitParams, ...args))\n                                .then(rest => {\n                                let tableData = [];\n                                reactData.tableLoading = false;\n                                if (rest) {\n                                    if (pagerConfig && isEnableConf(pagerOpts)) {\n                                        const totalProp = resConfigs.total;\n                                        const total = (XEUtils.isFunction(totalProp) ? totalProp({ data: rest, $table: $xeTable, $grid: $xeGrid, $gantt: null }) : XEUtils.get(rest, totalProp || 'page.total')) || 0;\n                                        tablePage.total = XEUtils.toNumber(total);\n                                        const resultProp = resConfigs.result;\n                                        tableData = (XEUtils.isFunction(resultProp) ? resultProp({ data: rest, $table: $xeTable, $grid: $xeGrid, $gantt: null }) : XEUtils.get(rest, resultProp || 'result')) || [];\n                                        // 检验当前页码，不能超出当前最大页数\n                                        const pageCount = Math.max(Math.ceil(total / tablePage.pageSize), 1);\n                                        if (tablePage.currentPage > pageCount) {\n                                            tablePage.currentPage = pageCount;\n                                        }\n                                    }\n                                    else {\n                                        const listProp = resConfigs.list;\n                                        tableData = (listProp ? (XEUtils.isFunction(listProp) ? listProp({ data: rest, $table: $xeTable, $grid: $xeGrid, $gantt: null }) : XEUtils.get(rest, listProp)) : rest) || [];\n                                    }\n                                }\n                                if ($xeTable) {\n                                    $xeTable.loadData(tableData);\n                                }\n                                else {\n                                    nextTick(() => {\n                                        if ($xeTable) {\n                                            $xeTable.loadData(tableData);\n                                        }\n                                    });\n                                }\n                                if (afterQuery) {\n                                    afterQuery(commitParams, ...args);\n                                }\n                                if (querySuccessMethods) {\n                                    querySuccessMethods(Object.assign(Object.assign({}, commitParams), { response: rest }));\n                                }\n                                return { status: true };\n                            }).catch((rest) => {\n                                reactData.tableLoading = false;\n                                if (queryErrorMethods) {\n                                    queryErrorMethods(Object.assign(Object.assign({}, commitParams), { response: rest }));\n                                }\n                                return { status: false };\n                            });\n                        }\n                        else {\n                            errLog('vxe.error.notFunc', ['proxy-config.ajax.query']);\n                        }\n                        break;\n                    }\n                    case 'delete': {\n                        const ajaxMethods = ajax.delete;\n                        const deleteSuccessMethods = ajax.deleteSuccess;\n                        const deleteErrorMethods = ajax.deleteError;\n                        if (ajaxMethods) {\n                            const selectRecords = $xeGrid.getCheckboxRecords();\n                            const removeRecords = selectRecords.filter(row => !$xeTable.isInsertByRow(row));\n                            const body = { removeRecords };\n                            const commitParams = {\n                                $table: $xeTable,\n                                $grid: $xeGrid,\n                                $gantt: null,\n                                code,\n                                button,\n                                body,\n                                form: formData,\n                                options: ajaxMethods\n                            };\n                            if (selectRecords.length) {\n                                return handleDeleteRow(code, 'vxe.grid.deleteSelectRecord', () => {\n                                    if (!removeRecords.length) {\n                                        return $xeTable.remove(selectRecords);\n                                    }\n                                    reactData.tableLoading = true;\n                                    return Promise.resolve((beforeDelete || ajaxMethods)(commitParams, ...args))\n                                        .then(rest => {\n                                        reactData.tableLoading = false;\n                                        $xeTable.setPendingRow(removeRecords, false);\n                                        if (isRespMsg) {\n                                            if (VxeUI.modal) {\n                                                VxeUI.modal.message({ content: getRespMsg(rest, 'vxe.grid.delSuccess'), status: 'success' });\n                                            }\n                                        }\n                                        if (afterDelete) {\n                                            afterDelete(commitParams, ...args);\n                                        }\n                                        else {\n                                            $xeGrid.commitProxy('query');\n                                        }\n                                        if (deleteSuccessMethods) {\n                                            deleteSuccessMethods(Object.assign(Object.assign({}, commitParams), { response: rest }));\n                                        }\n                                        return { status: true };\n                                    })\n                                        .catch(rest => {\n                                        reactData.tableLoading = false;\n                                        if (isRespMsg) {\n                                            if (VxeUI.modal) {\n                                                VxeUI.modal.message({ id: code, content: getRespMsg(rest, 'vxe.grid.operError'), status: 'error' });\n                                            }\n                                        }\n                                        if (deleteErrorMethods) {\n                                            deleteErrorMethods(Object.assign(Object.assign({}, commitParams), { response: rest }));\n                                        }\n                                        return { status: false };\n                                    });\n                                });\n                            }\n                            else {\n                                if (isActiveMsg) {\n                                    if (VxeUI.modal) {\n                                        VxeUI.modal.message({ id: code, content: getI18n('vxe.grid.selectOneRecord'), status: 'warning' });\n                                    }\n                                }\n                            }\n                        }\n                        else {\n                            errLog('vxe.error.notFunc', ['proxy-config.ajax.delete']);\n                        }\n                        break;\n                    }\n                    case 'save': {\n                        const ajaxMethods = ajax.save;\n                        const saveSuccessMethods = ajax.saveSuccess;\n                        const saveErrorMethods = ajax.saveError;\n                        if (ajaxMethods) {\n                            const body = $xeTable.getRecordset();\n                            const { insertRecords, removeRecords, updateRecords, pendingRecords } = body;\n                            const commitParams = {\n                                $table: $xeTable,\n                                $grid: $xeGrid,\n                                $gantt: null,\n                                code,\n                                button,\n                                body,\n                                form: formData,\n                                options: ajaxMethods\n                            };\n                            // 排除掉新增且标记为删除的数据\n                            if (insertRecords.length) {\n                                body.pendingRecords = pendingRecords.filter((row) => $xeTable.findRowIndexOf(insertRecords, row) === -1);\n                            }\n                            // 排除已标记为删除的数据\n                            if (pendingRecords.length) {\n                                body.insertRecords = insertRecords.filter((row) => $xeTable.findRowIndexOf(pendingRecords, row) === -1);\n                            }\n                            let restPromise = Promise.resolve();\n                            if (editRules) {\n                                // 只校验新增和修改的数据\n                                restPromise = $xeTable[validConfig && validConfig.msgMode === 'full' ? 'fullValidate' : 'validate'](body.insertRecords.concat(updateRecords));\n                            }\n                            return restPromise.then((errMap) => {\n                                if (errMap) {\n                                    // 如果校验不通过\n                                    return;\n                                }\n                                if (body.insertRecords.length || removeRecords.length || updateRecords.length || body.pendingRecords.length) {\n                                    reactData.tableLoading = true;\n                                    return Promise.resolve((beforeSave || ajaxMethods)(commitParams, ...args))\n                                        .then(rest => {\n                                        reactData.tableLoading = false;\n                                        $xeTable.clearPendingRow();\n                                        if (isRespMsg) {\n                                            if (VxeUI.modal) {\n                                                VxeUI.modal.message({ content: getRespMsg(rest, 'vxe.grid.saveSuccess'), status: 'success' });\n                                            }\n                                        }\n                                        if (afterSave) {\n                                            afterSave(commitParams, ...args);\n                                        }\n                                        else {\n                                            $xeGrid.commitProxy('query');\n                                        }\n                                        if (saveSuccessMethods) {\n                                            saveSuccessMethods(Object.assign(Object.assign({}, commitParams), { response: rest }));\n                                        }\n                                        return { status: true };\n                                    })\n                                        .catch(rest => {\n                                        reactData.tableLoading = false;\n                                        if (isRespMsg) {\n                                            if (VxeUI.modal) {\n                                                VxeUI.modal.message({ id: code, content: getRespMsg(rest, 'vxe.grid.operError'), status: 'error' });\n                                            }\n                                        }\n                                        if (saveErrorMethods) {\n                                            saveErrorMethods(Object.assign(Object.assign({}, commitParams), { response: rest }));\n                                        }\n                                        return { status: false };\n                                    });\n                                }\n                                else {\n                                    if (isActiveMsg) {\n                                        if (VxeUI.modal) {\n                                            VxeUI.modal.message({ id: code, content: getI18n('vxe.grid.dataUnchanged'), status: 'info' });\n                                        }\n                                    }\n                                }\n                            });\n                        }\n                        else {\n                            errLog('vxe.error.notFunc', ['proxy-config.ajax.save']);\n                        }\n                        break;\n                    }\n                    default: {\n                        const gCommandOpts = commands.get(code);\n                        if (gCommandOpts) {\n                            const tCommandMethod = gCommandOpts.tableCommandMethod || gCommandOpts.commandMethod;\n                            if (tCommandMethod) {\n                                tCommandMethod({ code, button, $grid: $xeGrid, $table: $xeTable, $gantt: null }, ...args);\n                            }\n                            else {\n                                errLog('vxe.error.notCommands', [code]);\n                            }\n                        }\n                    }\n                }\n                return nextTick();\n            },\n            getParams() {\n                return props.params;\n            },\n            zoom() {\n                if (reactData.isZMax) {\n                    return $xeGrid.revert();\n                }\n                return $xeGrid.maximize();\n            },\n            isMaximized() {\n                return reactData.isZMax;\n            },\n            maximize() {\n                return handleZoom(true);\n            },\n            revert() {\n                return handleZoom();\n            },\n            getFormData,\n            getFormItems(itemIndex) {\n                const formOpts = computeFormOpts.value;\n                const { formConfig } = props;\n                const { items } = formOpts;\n                const itemList = [];\n                XEUtils.eachTree(formConfig && isEnableConf(formOpts) && items ? items : [], item => {\n                    itemList.push(item);\n                }, { children: 'children' });\n                return XEUtils.isUndefined(itemIndex) ? itemList : itemList[itemIndex];\n            },\n            resetForm() {\n                const $form = refForm.value;\n                if ($form) {\n                    return $form.reset();\n                }\n                return nextTick();\n            },\n            validateForm() {\n                const $form = refForm.value;\n                if ($form) {\n                    return $form.validate();\n                }\n                return nextTick();\n            },\n            validateFormField(field) {\n                const $form = refForm.value;\n                if ($form) {\n                    return $form.validateField(field);\n                }\n                return nextTick();\n            },\n            clearFormValidate(field) {\n                const $form = refForm.value;\n                if ($form) {\n                    return $form.clearValidate(field);\n                }\n                return nextTick();\n            },\n            homePage() {\n                const { tablePage } = reactData;\n                tablePage.currentPage = 1;\n                return nextTick();\n            },\n            homePageByEvent(evnt) {\n                const $pager = refPager.value;\n                if ($pager) {\n                    $pager.homePageByEvent(evnt);\n                }\n            },\n            endPage() {\n                const { tablePage } = reactData;\n                const pageCount = computePageCount.value;\n                tablePage.currentPage = pageCount;\n                return nextTick();\n            },\n            endPageByEvent(evnt) {\n                const $pager = refPager.value;\n                if ($pager) {\n                    $pager.endPageByEvent(evnt);\n                }\n            },\n            setCurrentPage(currentPage) {\n                const { tablePage } = reactData;\n                const pageCount = computePageCount.value;\n                tablePage.currentPage = Math.min(pageCount, Math.max(1, XEUtils.toNumber(currentPage)));\n                return nextTick();\n            },\n            setCurrentPageByEvent(evnt, currentPage) {\n                const $pager = refPager.value;\n                if ($pager) {\n                    $pager.setCurrentPageByEvent(evnt, currentPage);\n                }\n            },\n            setPageSize(pageSize) {\n                const { tablePage } = reactData;\n                tablePage.pageSize = Math.max(1, XEUtils.toNumber(pageSize));\n                return nextTick();\n            },\n            setPageSizeByEvent(evnt, pageSize) {\n                const $pager = refPager.value;\n                if ($pager) {\n                    $pager.setPageSizeByEvent(evnt, pageSize);\n                }\n            },\n            getProxyInfo() {\n                const $xeTable = refTable.value;\n                if (props.proxyConfig) {\n                    const { sortData } = reactData;\n                    return {\n                        data: $xeTable ? $xeTable.getFullData() : [],\n                        filter: reactData.filterData,\n                        form: getFormData(),\n                        sort: sortData.length ? sortData[0] : {},\n                        sorts: sortData,\n                        pager: reactData.tablePage,\n                        pendingRecords: $xeTable ? $xeTable.getPendingRecords() : []\n                    };\n                }\n                return null;\n            }\n            // setProxyInfo (options) {\n            //   if (props.proxyConfig && options) {\n            //     const { pager, form } = options\n            //     const proxyOpts = computeProxyOpts.value\n            //     if (pager) {\n            //       if (pager.currentPage) {\n            //         reactData.tablePage.currentPage = Number(pager.currentPage)\n            //       }\n            //       if (pager.pageSize) {\n            //         reactData.tablePage.pageSize = Number(pager.pageSize)\n            //       }\n            //     }\n            //     if (proxyOpts.form && form) {\n            //       Object.assign(reactData.formData, form)\n            //     }\n            //   }\n            //   return nextTick()\n            // }\n        };\n        const gridPrivateMethods = {\n            extendTableMethods,\n            callSlot(slotFunc, params) {\n                if (slotFunc) {\n                    if (XEUtils.isString(slotFunc)) {\n                        slotFunc = slots[slotFunc] || null;\n                    }\n                    if (XEUtils.isFunction(slotFunc)) {\n                        return getSlotVNs(slotFunc(params));\n                    }\n                }\n                return [];\n            },\n            /**\n             * 获取需要排除的高度\n             */\n            getExcludeHeight() {\n                const { isZMax } = reactData;\n                const el = refElem.value;\n                if (el) {\n                    const formWrapper = refFormWrapper.value;\n                    const toolbarWrapper = refToolbarWrapper.value;\n                    const topWrapper = refTopWrapper.value;\n                    const bottomWrapper = refBottomWrapper.value;\n                    const pagerWrapper = refPagerWrapper.value;\n                    const parentEl = el.parentElement;\n                    const parentPaddingSize = isZMax ? 0 : (parentEl ? getPaddingTopBottomSize(parentEl) : 0);\n                    return parentPaddingSize + getPaddingTopBottomSize(el) + getOffsetHeight(formWrapper) + getOffsetHeight(toolbarWrapper) + getOffsetHeight(topWrapper) + getOffsetHeight(bottomWrapper) + getOffsetHeight(pagerWrapper);\n                }\n                return 0;\n            },\n            getParentHeight() {\n                const el = refElem.value;\n                if (el) {\n                    const parentEl = el.parentElement;\n                    return (reactData.isZMax ? getDomNode().visibleHeight : (parentEl ? XEUtils.toNumber(getComputedStyle(parentEl).height) : 0)) - gridPrivateMethods.getExcludeHeight();\n                }\n                return 0;\n            },\n            triggerToolbarCommitEvent(params, evnt) {\n                const { code } = params;\n                return $xeGrid.commitProxy(params, evnt).then((rest) => {\n                    if (code && rest && rest.status && ['query', 'reload', 'delete', 'save'].includes(code)) {\n                        $xeGrid.dispatchEvent(code === 'delete' || code === 'save' ? `proxy-${code}` : 'proxy-query', Object.assign(Object.assign({}, rest), { isReload: code === 'reload' }), evnt);\n                    }\n                });\n            },\n            triggerToolbarBtnEvent(button, evnt) {\n                $xeGrid.triggerToolbarCommitEvent(button, evnt);\n                $xeGrid.dispatchEvent('toolbar-button-click', { code: button.code, button }, evnt);\n            },\n            triggerToolbarTolEvent(tool, evnt) {\n                $xeGrid.triggerToolbarCommitEvent(tool, evnt);\n                $xeGrid.dispatchEvent('toolbar-tool-click', { code: tool.code, tool }, evnt);\n            },\n            triggerZoomEvent(evnt) {\n                $xeGrid.zoom();\n                $xeGrid.dispatchEvent('zoom', { type: reactData.isZMax ? 'max' : 'revert' }, evnt);\n            }\n        };\n        Object.assign($xeGrid, gridExtendTableMethods, gridMethods, gridPrivateMethods, {\n            // 检查插槽\n            loadColumn(columns) {\n                const $xeTable = refTable.value;\n                XEUtils.eachTree(columns, (column) => {\n                    if (column.slots) {\n                        XEUtils.each(column.slots, (func) => {\n                            if (!XEUtils.isFunction(func)) {\n                                if (!slots[func]) {\n                                    errLog('vxe.error.notSlot', [func]);\n                                }\n                            }\n                        });\n                    }\n                });\n                if ($xeTable) {\n                    return $xeTable.loadColumn(columns);\n                }\n                return nextTick();\n            },\n            reloadColumn(columns) {\n                $xeGrid.clearAll();\n                return $xeGrid.loadColumn(columns);\n            }\n        });\n        const renderVN = () => {\n            const vSize = computeSize.value;\n            const styles = computeStyles.value;\n            const isLoading = computeIsLoading.value;\n            return h('div', {\n                ref: refElem,\n                class: ['vxe-grid', {\n                        [`size--${vSize}`]: vSize,\n                        'is--animat': !!props.animat,\n                        'is--round': props.round,\n                        'is--maximize': reactData.isZMax,\n                        'is--loading': isLoading\n                    }],\n                style: styles\n            }, renderLayout());\n        };\n        const columnFlag = ref(0);\n        watch(() => props.columns ? props.columns.length : -1, () => {\n            columnFlag.value++;\n        });\n        watch(() => props.columns, () => {\n            columnFlag.value++;\n        });\n        watch(columnFlag, () => {\n            nextTick(() => $xeGrid.loadColumn(props.columns || []));\n        });\n        watch(() => props.toolbarConfig, () => {\n            initToolbar();\n        });\n        watch(computeCustomCurrentPageFlag, () => {\n            initPages('currentPage');\n        });\n        watch(computeCustomPageSizeFlag, () => {\n            initPages('pageSize');\n        });\n        watch(computeCustomTotalFlag, () => {\n            initPages('total');\n        });\n        watch(() => props.proxyConfig, () => {\n            initProxy();\n        });\n        hooks.forEach((options) => {\n            const { setupGrid } = options;\n            if (setupGrid) {\n                const hookRest = setupGrid($xeGrid);\n                if (hookRest && XEUtils.isObject(hookRest)) {\n                    Object.assign($xeGrid, hookRest);\n                }\n            }\n        });\n        initPages();\n        onMounted(() => {\n            nextTick(() => {\n                const { columns } = props;\n                const proxyOpts = computeProxyOpts.value;\n                if (props.formConfig) {\n                    if (!VxeUIFormComponent) {\n                        errLog('vxe.error.reqComp', ['vxe-form']);\n                    }\n                }\n                if (props.pagerConfig) {\n                    if (!VxeUIPagerComponent) {\n                        errLog('vxe.error.reqComp', ['vxe-pager']);\n                    }\n                }\n                // const { data, columns, proxyConfig } = props\n                // const formOpts = computeFormOpts.value\n                // if (isEnableConf(proxyConfig) && (data || (proxyOpts.form && formOpts.data))) {\n                //   errLog('vxe.error.errConflicts', ['grid.data', 'grid.proxy-config'])\n                // }\n                if (proxyOpts.props) {\n                    warnLog('vxe.error.delProp', ['proxy-config.props', 'proxy-config.response']);\n                }\n                if (columns && columns.length) {\n                    $xeGrid.loadColumn(columns);\n                }\n                initToolbar();\n                initProxy();\n            });\n            globalEvents.on($xeGrid, 'keydown', handleGlobalKeydownEvent);\n        });\n        onUnmounted(() => {\n            globalEvents.off($xeGrid, 'keydown');\n            XEUtils.assign(internalData, createInternalData());\n        });\n        $xeGrid.renderVN = renderVN;\n        provide('$xeGrid', $xeGrid);\n        provide('$xeGantt', null);\n        return $xeGrid;\n    },\n    render() {\n        return this.renderVN();\n    }\n});\n", "import { VxeUI } from '../../ui';\nimport { tableProps } from '../../table/src/props';\nconst { getConfig } = VxeUI;\nexport const gridProps = Object.assign(Object.assign({}, tableProps), { layouts: Array, columns: Array, pagerConfig: Object, proxyConfig: Object, toolbarConfig: Object, formConfig: Object, zoomConfig: Object, size: {\n        type: String,\n        default: () => getConfig().grid.size || getConfig().size\n    } });\n", "import { tableEmits } from '../../table/src/emits';\nexport const gridEmits = [\n    ...tableEmits,\n    'page-change',\n    'form-submit',\n    'form-submit-invalid',\n    'form-reset',\n    'form-collapse',\n    'form-toggle-collapse',\n    'proxy-query',\n    'proxy-delete',\n    'proxy-save',\n    'toolbar-button-click',\n    'toolbar-tool-click',\n    'zoom'\n];\n", "import { VxeUI } from '../ui';\nimport VxeGridComponent from './src/grid';\nexport const VxeGrid = Object.assign({}, VxeGridComponent, {\n    install(app) {\n        app.component(VxeGridComponent.name, VxeGridComponent);\n    }\n});\nif (VxeUI.dynamicApp) {\n    VxeUI.dynamicApp.component(VxeGridComponent.name, VxeGridComponent);\n}\nVxeUI.component(VxeGridComponent);\nexport const Grid = VxeGrid;\nexport default VxeGrid;\n", "import VxeGrid from '../grid';\nexport * from '../grid';\nexport default VxeGrid;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,sBAAoB;;;ACApB,IAAM,EAAE,UAAU,IAAI;AACf,IAAM,YAAY,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,UAAU,GAAG,EAAE,SAAS,OAAO,SAAS,OAAO,aAAa,QAAQ,aAAa,QAAQ,eAAe,QAAQ,YAAY,QAAQ,YAAY,QAAQ,MAAM;AAAA,EAC/M,MAAM;AAAA,EACN,SAAS,MAAM,UAAU,EAAE,KAAK,QAAQ,UAAU,EAAE;AACxD,EAAE,CAAC;;;ACLA,IAAM,YAAY;AAAA,EACrB,GAAG;AAAA,EACH;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;;;AFDA,IAAM,EAAE,WAAAA,YAAW,SAAS,UAAU,OAAO,QAAQ,aAAa,cAAc,mBAAmB,mBAAmB,IAAI;AAC1H,IAAM,yBAAyB,OAAO,KAAK,UAAU;AACrD,IAAM,2BAA2B,CAAC,YAAY,YAAY,cAAc,YAAY,cAAc,aAAa,cAAc,gBAAgB,cAAc,iBAAiB,eAAe,iBAAiB,iBAAiB,kBAAkB,oBAAoB,oBAAoB,UAAU,cAAc,aAAa,cAAc,aAAa,iBAAiB,iBAAiB,iBAAiB,cAAc,iBAAiB,oBAAoB,kBAAkB,kBAAkB,WAAW,sBAAsB,gBAAgB,sBAAsB,mBAAmB,oBAAoB,aAAa,cAAc,YAAY,gBAAgB,eAAe,kBAAkB,oBAAoB,kBAAkB,kBAAkB,mBAAmB,oBAAoB,oBAAoB,gBAAgB,gBAAgB,cAAc,cAAc,eAAe,iBAAiB,iBAAiB,eAAe,gBAAgB,wBAAwB,8BAA8B,mCAAmC,kBAAkB,qBAAqB,0BAA0B,6BAA6B,gCAAgC,mCAAmC,qBAAqB,qBAAqB,yBAAyB,qBAAqB,6BAA6B,wBAAwB,wBAAwB,oBAAoB,iBAAiB,uBAAuB,0BAA0B,eAAe,kBAAkB,mBAAmB,iBAAiB,oBAAoB,kBAAkB,oBAAoB,oBAAoB,sBAAsB,iBAAiB,oBAAoB,mBAAmB,kBAAkB,qBAAqB,mBAAmB,oBAAoB,QAAQ,WAAW,kBAAkB,aAAa,oBAAoB,UAAU,kBAAkB,eAAe,YAAY,sBAAsB,0BAA0B,qBAAqB,wBAAwB,mBAAmB,mBAAmB,mBAAmB,mBAAmB,gBAAgB,iBAAiB,oBAAoB,kBAAkB,yBAAyB,uBAAuB,wBAAwB,sBAAsB,yBAAyB,oBAAoB,oBAAoB,oBAAoB,oBAAoB,iBAAiB,qBAAqB,mBAAmB,0BAA0B,aAAa,YAAY,eAAe,kBAAkB,eAAe,gBAAgB,gBAAgB,iBAAiB,mBAAmB,oBAAoB,iBAAiB,uBAAuB,0BAA0B,uBAAuB,yBAAyB,mBAAmB,uBAAuB,0BAA0B,uBAAuB,yBAAyB,sBAAsB,qBAAqB,4BAA4B,wBAAwB,uBAAuB,yBAAyB,oBAAoB,qBAAqB,0BAA0B,4BAA4B,2BAA2B,gBAAgB,kBAAkB,eAAe,gBAAgB,aAAa,gBAAgB,kBAAkB,SAAS,QAAQ,WAAW,gBAAgB;AAC7nG,SAAS,qBAAqB;AAC1B,SAAO,CAAC;AACZ;AACA,IAAO,eAAQ,mBAAmB;AAAA,EAC9B,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,MAAM,OAAO,SAAS;AAClB,QAAI;AACJ,UAAM,EAAE,OAAO,KAAK,IAAI;AACxB,UAAM,MAAM,gBAAAC,QAAQ,SAAS;AAE7B,UAAM,qBAAqB,MAAM,aAAa,SAAS;AACvD,UAAM,sBAAsB,MAAM,aAAa,UAAU;AACzD,UAAM,iBAAiB,CAAC,CAAC,MAAM,GAAG,CAAC,WAAW,OAAO,SAAS,UAAU,OAAO,CAAC;AAChF,UAAM,EAAE,YAAY,IAAI,OAAO,QAAQ,KAAK;AAC5C,UAAM,YAAY,SAAS;AAAA,MACvB,cAAc;AAAA,MACd,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,WAAW,CAAC;AAAA,MACZ,YAAY,CAAC;AAAA,MACb,UAAU,CAAC;AAAA,MACX,UAAU,CAAC;AAAA,MACX,SAAS;AAAA,MACT,WAAW;AAAA,QACP,OAAO;AAAA,QACP,YAAY,KAAKD,WAAU,EAAE,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa;AAAA,QACzF,aAAa;AAAA,MACjB;AAAA,IACJ,CAAC;AACD,UAAM,eAAe,mBAAmB;AACxC,UAAM,UAAU,IAAI;AACpB,UAAM,WAAW,IAAI;AACrB,UAAM,UAAU,IAAI;AACpB,UAAM,aAAa,IAAI;AACvB,UAAM,WAAW,IAAI;AACrB,UAAM,iBAAiB,IAAI;AAC3B,UAAM,oBAAoB,IAAI;AAC9B,UAAM,gBAAgB,IAAI;AAC1B,UAAM,mBAAmB,IAAI;AAC7B,UAAM,kBAAkB,IAAI;AAC5B,UAAM,qBAAqB,CAAC,eAAe;AACvC,YAAM,QAAQ,CAAC;AACf,iBAAW,QAAQ,UAAQ;AACvB,cAAM,IAAI,IAAI,IAAI,SAAS;AACvB,gBAAM,WAAW,SAAS;AAC1B,cAAI,YAAY,SAAS,IAAI,GAAG;AAC5B,mBAAO,SAAS,IAAI,EAAE,GAAG,IAAI;AAAA,UACjC;AAAA,QACJ;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,IACX;AACA,UAAM,yBAAyB,mBAAmB,wBAAwB;AAC1E,6BAAyB,QAAQ,UAAQ;AACrC,6BAAuB,IAAI,IAAI,IAAI,SAAS;AACxC,cAAM,WAAW,SAAS;AAC1B,YAAI,YAAY,SAAS,IAAI,GAAG;AAC5B,iBAAO,YAAY,SAAS,IAAI,EAAE,GAAG,IAAI;AAAA,QAC7C;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,UAAM,mBAAmB,SAAS,MAAM;AACpC,aAAO,gBAAAC,QAAQ,MAAM,CAAC,GAAG,gBAAAA,QAAQ,MAAMD,WAAU,EAAE,KAAK,aAAa,IAAI,GAAG,MAAM,WAAW;AAAA,IACjG,CAAC;AACD,UAAM,mBAAmB,SAAS,MAAM;AACpC,YAAM,YAAY,iBAAiB;AACnC,aAAO,CAAC,EAAE,gBAAAC,QAAQ,UAAU,UAAU,OAAO,IAAI,UAAU,UAAU,UAAU;AAAA,IACnF,CAAC;AACD,UAAM,qBAAqB,SAAS,MAAM;AACtC,YAAM,YAAY,iBAAiB;AACnC,aAAO,gBAAAA,QAAQ,UAAU,UAAU,aAAa,IAAI,UAAU,gBAAgB,CAAC,CAAC,UAAU;AAAA,IAC9F,CAAC;AACD,UAAM,mBAAmB,SAAS,MAAM;AACpC,aAAO,OAAO,OAAO,CAAC,GAAGD,WAAU,EAAE,KAAK,aAAa,MAAM,WAAW;AAAA,IAC5E,CAAC;AACD,UAAM,kBAAkB,SAAS,MAAM;AACnC,aAAO,OAAO,OAAO,CAAC,GAAGA,WAAU,EAAE,KAAK,YAAY,MAAM,UAAU;AAAA,IAC1E,CAAC;AACD,UAAM,qBAAqB,SAAS,MAAM;AACtC,aAAO,OAAO,OAAO,CAAC,GAAGA,WAAU,EAAE,KAAK,eAAe,MAAM,aAAa;AAAA,IAChF,CAAC;AACD,UAAM,kBAAkB,SAAS,MAAM;AACnC,aAAO,OAAO,OAAO,CAAC,GAAGA,WAAU,EAAE,KAAK,YAAY,MAAM,UAAU;AAAA,IAC1E,CAAC;AACD,UAAM,gBAAgB,SAAS,MAAM;AACjC,YAAM,EAAE,QAAQ,UAAU,IAAI;AAC9B,YAAM,EAAE,QAAQ,QAAQ,IAAI;AAC5B,YAAM,OAAO,CAAC;AACd,UAAI,QAAQ;AACR,aAAK,SAAS;AAAA,MAClB,OACK;AACD,YAAI,QAAQ;AACR,eAAK,SAAS,WAAW,UAAU,WAAW,SAAS,SAAS,UAAU,MAAM;AAAA,QACpF;AACA,YAAI,WAAW;AACX,eAAK,YAAY,cAAc,UAAU,cAAc,SAAS,SAAS,UAAU,SAAS;AAAA,QAChG;AAAA,MACJ;AACA,aAAO;AAAA,IACX,CAAC;AACD,UAAM,0BAA0B,SAAS,MAAM;AAC3C,YAAM,OAAO,CAAC;AACd,6BAAuB,QAAQ,CAAC,QAAQ;AACpC,aAAK,GAAG,IAAI,MAAM,GAAG;AAAA,MACzB,CAAC;AACD,aAAO;AAAA,IACX,CAAC;AACD,UAAM,oBAAoB,SAAS,MAAM;AACrC,YAAM,EAAE,WAAW,aAAa,YAAY,YAAY,IAAI;AAC5D,YAAM,EAAE,QAAQ,UAAU,IAAI;AAC9B,YAAM,mBAAmB,wBAAwB;AACjD,YAAM,YAAY,iBAAiB;AACnC,YAAM,YAAY,iBAAiB;AACnC,YAAM,YAAY,iBAAiB;AACnC,YAAM,SAAS,OAAO,OAAO,CAAC,GAAG,gBAAgB;AACjD,UAAI,QAAQ;AACR,YAAI,iBAAiB,WAAW;AAC5B,iBAAO,YAAY;AAAA,QACvB,OACK;AACD,iBAAO,SAAS;AAAA,QACpB;AAAA,MACJ;AACA,UAAI,eAAe,aAAa,SAAS,GAAG;AACxC,eAAO,UAAU;AACjB,YAAI,eAAe,UAAU,OAAO,aAAa,SAAS,GAAG;AACzD,iBAAO,YAAY,OAAO,OAAO,CAAC,GAAG,WAAW,EAAE,aAAa,UAAU,cAAc,KAAK,UAAU,SAAS,CAAC;AAAA,QACpH;AAAA,MACJ;AACA,UAAI,YAAY;AACZ,eAAO,aAAa,OAAO,OAAO,CAAC,GAAG,UAAU;AAAA,MACpD;AACA,aAAO;AAAA,IACX,CAAC;AACD,UAAM,wBAAwB,SAAS,MAAM;AACzC,YAAM,EAAE,QAAQ,IAAI;AACpB,UAAI,QAAQ,CAAC;AACb,UAAI,WAAW,QAAQ,QAAQ;AAC3B,gBAAQ;AAAA,MACZ,OACK;AACD,gBAAQA,WAAU,EAAE,KAAK,WAAW;AAAA,MACxC;AACA,UAAI,WAAW,CAAC;AAChB,UAAI,WAAW,CAAC;AAChB,UAAI,WAAW,CAAC;AAChB,UAAI,MAAM,QAAQ;AACd,YAAI,gBAAAC,QAAQ,QAAQ,MAAM,CAAC,CAAC,GAAG;AAC3B,qBAAW,MAAM,CAAC;AAClB,qBAAY,MAAM,CAAC,KAAK,CAAC;AACzB,qBAAY,MAAM,CAAC,KAAK,CAAC;AAAA,QAC7B,OACK;AACD,qBAAW;AAAA,QACf;AAAA,MACJ;AACA,aAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,UAAM,+BAA+B,SAAS,MAAM;AAChD,YAAM,YAAY,iBAAiB;AACnC,aAAO,UAAU;AAAA,IACrB,CAAC;AACD,UAAM,4BAA4B,SAAS,MAAM;AAC7C,YAAM,YAAY,iBAAiB;AACnC,aAAO,UAAU;AAAA,IACrB,CAAC;AACD,UAAM,yBAAyB,SAAS,MAAM;AAC1C,YAAM,YAAY,iBAAiB;AACnC,aAAO,UAAU;AAAA,IACrB,CAAC;AACD,UAAM,mBAAmB,SAAS,MAAM;AACpC,YAAM,EAAE,UAAU,IAAI;AACtB,aAAO,KAAK,IAAI,KAAK,KAAK,UAAU,QAAQ,UAAU,QAAQ,GAAG,CAAC;AAAA,IACtE,CAAC;AACD,UAAM,mBAAmB,SAAS,MAAM;AACpC,YAAM,EAAE,SAAS,YAAY,IAAI;AACjC,YAAM,EAAE,aAAa,IAAI;AACzB,YAAM,YAAY,iBAAiB;AACnC,YAAM,EAAE,YAAY,IAAI;AACxB,aAAO,WAAY,gBAAgB,eAAe,eAAe,aAAa,SAAS;AAAA,IAC3F,CAAC;AACD,UAAM,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,UAAM,cAAc;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,UAAM,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAY,MAAM;AAAA,MAClB,gBAAgB,MAAM;AAAA,IAC1B;AACA,UAAM,cAAc,MAAM;AACtB,YAAM,cAAc,mBAAmB;AACvC,UAAI,MAAM,iBAAiB,aAAa,WAAW,GAAG;AAClD,iBAAS,MAAM;AACX,gBAAM,WAAW,SAAS;AAC1B,gBAAM,aAAa,WAAW;AAC9B,cAAI,YAAY,YAAY;AACxB,qBAAS,eAAe,UAAU;AAAA,UACtC;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AACA,UAAM,cAAc,MAAM;AACtB,YAAM,EAAE,YAAY,IAAI;AACxB,YAAM,EAAE,SAAS,IAAI;AACrB,YAAM,YAAY,iBAAiB;AACnC,YAAM,WAAW,gBAAgB;AACjC,aAAO,eAAe,aAAa,SAAS,KAAK,UAAU,OAAO,WAAW,SAAS;AAAA,IAC1F;AACA,UAAM,YAAY,CAAC,YAAY;AAC3B,YAAM,EAAE,UAAU,IAAI;AACtB,YAAM,EAAE,YAAY,IAAI;AACxB,YAAM,YAAY,iBAAiB;AACnC,UAAI,eAAe,aAAa,SAAS,GAAG;AACxC,YAAI,SAAS;AACT,cAAI,UAAU,OAAO,GAAG;AACpB,sBAAU,OAAO,IAAI,gBAAAA,QAAQ,SAAS,UAAU,OAAO,CAAC;AAAA,UAC5D;AAAA,QACJ,OACK;AACD,gBAAM,EAAE,aAAa,UAAU,MAAM,IAAI;AACzC,cAAI,aAAa;AACb,sBAAU,cAAc;AAAA,UAC5B;AACA,cAAI,UAAU;AACV,sBAAU,WAAW;AAAA,UACzB;AACA,cAAI,OAAO;AACP,sBAAU,QAAQ;AAAA,UACtB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,sBAAsB,CAAC,SAAS;AAClC,YAAM,cAAc,mBAAmB;AACvC,YAAM,WAAW,SAAS;AAC1B,YAAM,gBAAgB,WAAW,SAAS,mBAAmB,IAAI,CAAC;AAClE,UAAI,cAAc,QAAQ;AACtB,YAAI,UAAU;AACV,mBAAS,iBAAiB,aAAa;AAAA,QAC3C;AACA,gBAAQ,iBAAiB;AAAA,MAC7B,OACK;AACD,YAAI,aAAa;AACb,cAAI,MAAM,OAAO;AACb,kBAAM,MAAM,QAAQ,EAAE,IAAI,MAAM,SAAS,QAAQ,0BAA0B,GAAG,QAAQ,UAAU,CAAC;AAAA,UACrG;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,aAAa,CAAC,MAAM,eAAe;AACrC,YAAM,YAAY,iBAAiB;AACnC,YAAM,aAAa,UAAU,YAAY,UAAU,SAAS,CAAC;AAC7D,YAAM,cAAc,WAAW;AAC/B,YAAM,WAAW,SAAS;AAC1B,UAAI;AACJ,UAAI,QAAQ,aAAa;AACrB,cAAM,gBAAAA,QAAQ,WAAW,WAAW,IAAI,YAAY,EAAE,MAAM,MAAM,QAAQ,UAAU,OAAO,SAAS,QAAQ,KAAK,CAAC,IAAI,gBAAAA,QAAQ,IAAI,MAAM,WAAW;AAAA,MACvJ;AACA,aAAO,OAAO,QAAQ,UAAU;AAAA,IACpC;AACA,UAAM,kBAAkB,CAAC,MAAM,UAAU,aAAa;AAClD,YAAM,cAAc,mBAAmB;AACvC,YAAM,gBAAgB,QAAQ,mBAAmB;AACjD,UAAI,aAAa;AACb,YAAI,cAAc,QAAQ;AACtB,cAAI,MAAM,OAAO;AACb,mBAAO,MAAM,MAAM,QAAQ,EAAE,IAAI,OAAO,IAAI,IAAI,SAAS,QAAQ,QAAQ,GAAG,aAAa,KAAK,CAAC,EAAE,KAAK,CAAC,SAAS;AAC5G,kBAAI,SAAS,WAAW;AACpB,uBAAO,SAAS;AAAA,cACpB;AAAA,YACJ,CAAC;AAAA,UACL;AAAA,QACJ,OACK;AACD,cAAI,MAAM,OAAO;AACb,kBAAM,MAAM,QAAQ,EAAE,IAAI,OAAO,IAAI,IAAI,SAAS,QAAQ,0BAA0B,GAAG,QAAQ,UAAU,CAAC;AAAA,UAC9G;AAAA,QACJ;AAAA,MACJ,OACK;AACD,YAAI,cAAc,QAAQ;AACtB,mBAAS;AAAA,QACb;AAAA,MACJ;AACA,aAAO,QAAQ,QAAQ;AAAA,IAC3B;AACA,UAAM,kBAAkB,CAAC,WAAW;AAChC,YAAM,EAAE,YAAY,IAAI;AACxB,YAAM,EAAE,UAAU,IAAI;AACtB,YAAM,EAAE,QAAQ,aAAa,SAAS,IAAI;AAC1C,YAAM,YAAY,iBAAiB;AACnC,gBAAU,cAAc;AACxB,gBAAU,WAAW;AACrB,cAAQ,cAAc,eAAe,QAAQ,MAAM;AACnD,UAAI,eAAe,aAAa,SAAS,GAAG;AACxC,gBAAQ,YAAY,OAAO,EAAE,KAAK,CAAC,SAAS;AACxC,kBAAQ,cAAc,eAAe,MAAM,MAAM;AAAA,QACrD,CAAC;AAAA,MACL;AAAA,IACJ;AACA,UAAM,kBAAkB,CAAC,WAAW;AAChC,YAAM,WAAW,SAAS;AAC1B,YAAM,EAAE,YAAY,IAAI;AACxB,UAAI,CAAC,UAAU;AACX;AAAA,MACJ;AACA,YAAM,EAAE,gBAAgB,IAAI,SAAS,eAAe;AACpD,YAAM,YAAY,iBAAiB;AACnC,YAAM,WAAW,gBAAgB;AAEjC,UAAI,SAAS,QAAQ;AACjB,kBAAU,WAAW,OAAO;AAC5B,YAAI,eAAe,aAAa,SAAS,GAAG;AACxC,oBAAU,UAAU,cAAc;AAClC,kBAAQ,YAAY,OAAO,EAAE,KAAK,CAAC,SAAS;AACxC,oBAAQ,cAAc,eAAe,MAAM,OAAO,MAAM;AAAA,UAC5D,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,kBAAkB,CAAC,WAAW;AAChC,sBAAgB,MAAM;AACtB,cAAQ,cAAc,eAAe,QAAQ,OAAO,MAAM;AAAA,IAC9D;AACA,UAAM,oBAAoB,CAAC,WAAW;AAClC,sBAAgB,MAAM;AACtB,cAAQ,cAAc,kBAAkB,QAAQ,OAAO,MAAM;AAAA,IACjE;AACA,UAAM,oBAAoB,CAAC,WAAW;AAClC,YAAM,WAAW,SAAS;AAC1B,YAAM,EAAE,YAAY,IAAI;AACxB,UAAI,CAAC,UAAU;AACX;AAAA,MACJ;AACA,YAAM,EAAE,kBAAkB,IAAI,SAAS,eAAe;AACtD,YAAM,YAAY,iBAAiB;AACnC,YAAM,aAAa,kBAAkB;AAErC,UAAI,WAAW,QAAQ;AACnB,kBAAU,aAAa,OAAO;AAC9B,YAAI,eAAe,aAAa,SAAS,GAAG;AACxC,oBAAU,UAAU,cAAc;AAClC,kBAAQ,YAAY,OAAO,EAAE,KAAK,CAAC,SAAS;AACxC,oBAAQ,cAAc,eAAe,MAAM,OAAO,MAAM;AAAA,UAC5D,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,oBAAoB,CAAC,WAAW;AAClC,wBAAkB,MAAM;AACxB,cAAQ,cAAc,iBAAiB,QAAQ,OAAO,MAAM;AAAA,IAChE;AACA,UAAM,sBAAsB,CAAC,WAAW;AACpC,wBAAkB,MAAM;AACxB,cAAQ,cAAc,oBAAoB,QAAQ,OAAO,MAAM;AAAA,IACnE;AACA,UAAM,kBAAkB,CAAC,WAAW;AAChC,YAAM,EAAE,YAAY,IAAI;AACxB,YAAM,YAAY,iBAAiB;AACnC,UAAI,UAAU,cAAc;AACxB;AAAA,MACJ;AACA,UAAI,eAAe,aAAa,SAAS,GAAG;AACxC,gBAAQ,YAAY,QAAQ,EAAE,KAAK,CAAC,SAAS;AACzC,kBAAQ,cAAc,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,EAAE,UAAU,KAAK,CAAC,GAAG,OAAO,MAAM;AAAA,QAClH,CAAC;AAAA,MACL;AACA,cAAQ,cAAc,eAAe,QAAQ,OAAO,MAAM;AAAA,IAC9D;AACA,UAAM,iBAAiB,CAAC,WAAW;AAC/B,YAAM,WAAW,SAAS;AAC1B,YAAM,EAAE,YAAY,IAAI;AACxB,YAAM,EAAE,OAAO,IAAI;AACnB,YAAM,YAAY,iBAAiB;AACnC,UAAI,eAAe,aAAa,SAAS,GAAG;AACxC,YAAI,UAAU;AACV,mBAAS,YAAY;AAAA,QACzB;AACA,gBAAQ,YAAY,QAAQ,EAAE,KAAK,CAAC,SAAS;AACzC,kBAAQ,cAAc,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,EAAE,UAAU,KAAK,CAAC,GAAG,MAAM;AAAA,QAC3G,CAAC;AAAA,MACL;AACA,cAAQ,cAAc,cAAc,QAAQ,MAAM;AAAA,IACtD;AACA,UAAM,qBAAqB,CAAC,WAAW;AACnC,cAAQ,cAAc,uBAAuB,QAAQ,OAAO,MAAM;AAAA,IACtE;AACA,UAAM,gBAAgB,CAAC,WAAW;AAC9B,YAAM,EAAE,OAAO,IAAI;AACnB,cAAQ,cAAc,wBAAwB,QAAQ,MAAM;AAC5D,cAAQ,cAAc,iBAAiB,QAAQ,MAAM;AAAA,IACzD;AACA,UAAM,aAAa,CAAC,UAAU;AAC1B,YAAM,EAAE,OAAO,IAAI;AACnB,UAAI,QAAQ,CAAC,SAAS,QAAQ;AAC1B,kBAAU,SAAS,CAAC;AACpB,YAAI,UAAU,UAAU,cAAc,GAAG;AACrC,oBAAU,UAAU,WAAW;AAAA,QACnC;AAAA,MACJ;AACA,aAAO,SAAS,EACX,KAAK,MAAM,QAAQ,YAAY,IAAI,CAAC,EACpC,KAAK,MAAM;AACZ,mBAAW,MAAM,QAAQ,YAAY,IAAI,GAAG,EAAE;AAC9C,eAAO,UAAU;AAAA,MACrB,CAAC;AAAA,IACL;AACA,UAAM,cAAc,CAAC,UAAU,YAAY;AACvC,YAAM,WAAW,SAAS,OAAO;AACjC,UAAI,UAAU;AACV,YAAI,gBAAAA,QAAQ,SAAS,QAAQ,GAAG;AAC5B,cAAI,MAAM,QAAQ,GAAG;AACjB,mBAAO,MAAM,QAAQ;AAAA,UACzB,OACK;AACD,mBAAO,qBAAqB,CAAC,QAAQ,CAAC;AAAA,UAC1C;AAAA,QACJ,OACK;AACD,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,UAAM,gBAAgB,CAAC,gBAAgB;AACnC,YAAM,WAAW,CAAC;AAClB,sBAAAA,QAAQ,UAAU,aAAa,CAAC,UAAU,YAAY;AAClD,YAAI,UAAU;AACV,cAAI,gBAAAA,QAAQ,SAAS,QAAQ,GAAG;AAC5B,gBAAI,MAAM,QAAQ,GAAG;AACjB,uBAAS,OAAO,IAAI,MAAM,QAAQ;AAAA,YACtC,OACK;AACD,qBAAO,qBAAqB,CAAC,QAAQ,CAAC;AAAA,YAC1C;AAAA,UACJ,OACK;AACD,qBAAS,OAAO,IAAI;AAAA,UACxB;AAAA,QACJ;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,IACX;AAIA,UAAM,aAAa,MAAM;AACrB,YAAM,EAAE,YAAY,YAAY,IAAI;AACpC,YAAM,EAAE,SAAS,IAAI;AACrB,YAAM,YAAY,iBAAiB;AACnC,YAAM,WAAW,gBAAgB;AACjC,UAAK,cAAc,aAAa,QAAQ,KAAM,MAAM,MAAM;AACtD,YAAI,UAAU,CAAC;AACf,YAAI,MAAM,MAAM;AACZ,oBAAU,MAAM,KAAK,EAAE,OAAO,SAAS,QAAQ,KAAK,CAAC;AAAA,QACzD,OACK;AACD,cAAI,SAAS,OAAO;AAChB,kBAAM,YAAY,CAAC;AACnB,gBAAI,CAAC,SAAS,QAAQ;AAClB,uBAAS,SAAS;AAClB,oBAAM,aAAa,UAAU;AAC7B,kBAAI,aAAa,YAAY;AACzB,yBAAS,MAAM,QAAQ,CAAC,SAAS;AAC7B,6BAAW,EAAE,OAAO,SAAS,QAAQ,MAAM,KAAK,CAAC;AAAA,gBACrD,CAAC;AAAA,cACL;AAAA,YACJ;AAEA,qBAAS,MAAM,QAAQ,CAAC,SAAS;AAC7B,8BAAAA,QAAQ,KAAK,KAAK,OAAO,CAAC,SAAS;AAC/B,oBAAI,CAAC,gBAAAA,QAAQ,WAAW,IAAI,GAAG;AAC3B,sBAAI,MAAM,IAAI,GAAG;AACb,8BAAU,IAAI,IAAI,MAAM,IAAI;AAAA,kBAChC;AAAA,gBACJ;AAAA,cACJ,CAAC;AAAA,YACL,CAAC;AACD,gBAAI,oBAAoB;AACpB,sBAAQ,KAAK,EAAE,oBAAoB,OAAO,OAAO,OAAO,OAAO,EAAE,KAAK,QAAQ,GAAG,OAAO,OAAO,CAAC,GAAG,UAAU;AAAA,gBACzG,MAAM,eAAe,aAAa,SAAS,KAAK,UAAU,OAAO,WAAW,SAAS;AAAA,cACzF,CAAC,CAAC,GAAG,EAAE,UAAU,iBAAiB,SAAS,gBAAgB,iBAAiB,oBAAoB,YAAY,cAAc,CAAC,GAAG,SAAS,CAAC;AAAA,YAC5I;AAAA,UACJ;AAAA,QACJ;AACA,eAAO,EAAE,OAAO;AAAA,UACZ,KAAK;AAAA,UACL,KAAK;AAAA,UACL,OAAO;AAAA,QACX,GAAG,OAAO;AAAA,MACd;AACA,aAAO,mBAAmB,OAAO;AAAA,IACrC;AAIA,UAAM,gBAAgB,MAAM;AACxB,YAAM,EAAE,cAAc,IAAI;AAC1B,YAAM,cAAc,mBAAmB;AACvC,YAAM,cAAc,MAAM;AAC1B,UAAK,iBAAiB,aAAa,WAAW,KAAM,aAAa;AAC7D,YAAI,UAAU,CAAC;AACf,YAAI,aAAa;AACb,oBAAU,YAAY,EAAE,OAAO,SAAS,QAAQ,KAAK,CAAC;AAAA,QAC1D,OACK;AACD,gBAAM,kBAAkB,YAAY;AACpC,gBAAM,eAAe,CAAC;AACtB,cAAI,iBAAiB;AACjB,kBAAM,cAAc,YAAY,iBAAiB,SAAS;AAC1D,kBAAM,mBAAmB,YAAY,iBAAiB,cAAc;AACpE,kBAAM,mBAAmB,YAAY,iBAAiB,cAAc;AACpE,kBAAM,YAAY,YAAY,iBAAiB,OAAO;AACtD,kBAAM,iBAAiB,YAAY,iBAAiB,YAAY;AAChE,kBAAM,iBAAiB,YAAY,iBAAiB,YAAY;AAChE,gBAAI,aAAa;AACb,2BAAa,UAAU;AAAA,YAC3B;AACA,gBAAI,kBAAkB;AAClB,2BAAa,eAAe;AAAA,YAChC;AACA,gBAAI,kBAAkB;AAClB,2BAAa,eAAe;AAAA,YAChC;AACA,gBAAI,WAAW;AACX,2BAAa,QAAQ;AAAA,YACzB;AACA,gBAAI,gBAAgB;AAChB,2BAAa,aAAa;AAAA,YAC9B;AACA,gBAAI,gBAAgB;AAChB,2BAAa,aAAa;AAAA,YAC9B;AAAA,UACJ;AACA,kBAAQ,KAAK,EAAE,iBAAqB,OAAO,OAAO,OAAO,OAAO,EAAE,KAAK,WAAW,GAAG,WAAW,GAAG,EAAE,OAAO,OAAU,CAAC,GAAG,YAAY,CAAC;AAAA,QAC3I;AACA,eAAO,EAAE,OAAO;AAAA,UACZ,KAAK;AAAA,UACL,KAAK;AAAA,UACL,OAAO;AAAA,QACX,GAAG,OAAO;AAAA,MACd;AACA,aAAO,mBAAmB,OAAO;AAAA,IACrC;AAIA,UAAM,YAAY,MAAM;AACpB,YAAM,UAAU,MAAM;AACtB,UAAI,SAAS;AACT,eAAO,EAAE,OAAO;AAAA,UACZ,KAAK;AAAA,UACL,KAAK;AAAA,UACL,OAAO;AAAA,QACX,GAAG,QAAQ,EAAE,OAAO,SAAS,QAAQ,KAAK,CAAC,CAAC;AAAA,MAChD;AACA,aAAO,mBAAmB,OAAO;AAAA,IACrC;AACA,UAAM,kBAAkB,MAAM;AAC1B,YAAM,WAAW,MAAM;AACvB,UAAI,UAAU;AACV,eAAO,EAAE,OAAO;AAAA,UACZ,OAAO;AAAA,QACX,GAAG,SAAS,EAAE,OAAO,SAAS,QAAQ,KAAK,CAAC,CAAC;AAAA,MACjD;AACA,aAAO,mBAAmB,OAAO;AAAA,IACrC;AACA,UAAM,mBAAmB,MAAM;AAC3B,YAAM,YAAY,MAAM;AACxB,UAAI,WAAW;AACX,eAAO,EAAE,OAAO;AAAA,UACZ,OAAO;AAAA,QACX,GAAG,UAAU,EAAE,OAAO,SAAS,QAAQ,KAAK,CAAC,CAAC;AAAA,MAClD;AACA,aAAO,mBAAmB,OAAO;AAAA,IACrC;AAIA,UAAM,cAAc,MAAM;AACtB,YAAM,EAAE,YAAY,IAAI;AACxB,YAAMC,cAAa,kBAAkB;AACrC,YAAM,YAAY,iBAAiB;AACnC,YAAM,WAAW,OAAO,OAAO,CAAC,GAAG,eAAe;AAClD,YAAM,YAAY,MAAM;AACxB,YAAM,cAAc,MAAM;AAC1B,YAAM,kBAAkB,MAAM,eAAe,MAAM,eAAe;AAClE,YAAM,qBAAqB,MAAM,kBAAkB,MAAM,kBAAkB;AAC3E,YAAM,oBAAoB,MAAM,iBAAiB,MAAM,gBAAgB;AACvE,YAAM,cAAc,MAAM;AAC1B,YAAM,oBAAoB,MAAM,iBAAiB,MAAM,gBAAgB;AACvE,UAAI,eAAe,aAAa,SAAS,GAAG;AACxC,YAAI,UAAU,MAAM;AAChB,mBAAS,eAAe;AACxB,mBAAS,iBAAiB;AAAA,QAC9B;AACA,YAAI,UAAU,QAAQ;AAClB,mBAAS,iBAAiB;AAC1B,mBAAS,mBAAmB;AAAA,QAChC;AAAA,MACJ;AACA,YAAM,UAAU,CAAC;AACjB,UAAI,WAAW;AACX,gBAAQ,QAAQ;AAAA,MACpB;AACA,UAAI,aAAa;AACb,gBAAQ,UAAU;AAAA,MACtB;AACA,UAAI,iBAAiB;AACjB,gBAAQ,cAAc;AAAA,MAC1B;AACA,UAAI,oBAAoB;AACpB,gBAAQ,iBAAiB;AAAA,MAC7B;AACA,UAAI,mBAAmB;AACnB,gBAAQ,gBAAgB;AAAA,MAC5B;AACA,UAAI,aAAa;AACb,gBAAQ,UAAU;AAAA,MACtB;AACA,UAAI,mBAAmB;AACnB,gBAAQ,gBAAgB;AAAA,MAC5B;AACA,aAAO,EAAE,OAAO;AAAA,QACZ,OAAO;AAAA,MACX,GAAG;AAAA,QACC,EAAE,eAAmB,OAAO,OAAO,OAAO,OAAO,EAAE,KAAK,SAAS,GAAGA,WAAU,GAAG,QAAQ,GAAG,OAAO;AAAA,MACvG,CAAC;AAAA,IACL;AAIA,UAAM,eAAe,MAAM;AACvB,UAAI,MAAM,QAAQ;AACd,eAAO,EAAE,OAAO;AAAA,UACZ,KAAK;AAAA,UACL,KAAK;AAAA,UACL,OAAO;AAAA,QACX,GAAG,MAAM,OAAO,EAAE,OAAO,SAAS,QAAQ,KAAK,CAAC,CAAC;AAAA,MACrD;AACA,aAAO,mBAAmB,OAAO;AAAA,IACrC;AAIA,UAAM,cAAc,MAAM;AACtB,YAAM,EAAE,aAAa,YAAY,IAAI;AACrC,YAAM,YAAY,iBAAiB;AACnC,YAAM,YAAY,iBAAiB;AACnC,YAAM,YAAY,MAAM;AACxB,UAAK,eAAe,aAAa,SAAS,KAAM,MAAM,OAAO;AACzD,eAAO,EAAE,OAAO;AAAA,UACZ,KAAK;AAAA,UACL,KAAK;AAAA,UACL,OAAO;AAAA,QACX,GAAG,YACG,UAAU,EAAE,OAAO,SAAS,QAAQ,KAAK,CAAC,IAC1C;AAAA,UACE,sBACM,EAAE,qBAAqB,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,EAAE,KAAK,SAAS,GAAG,SAAS,GAAI,eAAe,aAAa,SAAS,IAAI,UAAU,YAAY,CAAC,CAAE,GAAG,EAAE,cAAc,gBAAgB,CAAC,GAAG,cAAc,UAAU,KAAK,CAAC,IACxO,mBAAmB,OAAO;AAAA,QACpC,CAAC;AAAA,MACT;AACA,aAAO,mBAAmB,OAAO;AAAA,IACrC;AACA,UAAM,oBAAoB,CAAC,eAAe;AACtC,YAAM,WAAW,CAAC;AAClB,iBAAW,QAAQ,SAAO;AACtB,gBAAQ,KAAK;AAAA,UACT,KAAK;AACD,qBAAS,KAAK,WAAW,CAAC;AAC1B;AAAA,UACJ,KAAK;AACD,qBAAS,KAAK,cAAc,CAAC;AAC7B;AAAA,UACJ,KAAK;AACD,qBAAS,KAAK,UAAU,CAAC;AACzB;AAAA,UACJ,KAAK;AACD,qBAAS,KAAK,EAAE,OAAO;AAAA,cACnB,KAAK;AAAA,cACL,OAAO;AAAA,YACX,GAAG;AAAA,cACC,gBAAgB;AAAA,cAChB,YAAY;AAAA,cACZ,iBAAiB;AAAA,YACrB,CAAC,CAAC;AACF;AAAA,UACJ,KAAK;AACD,qBAAS,KAAK,aAAa,CAAC;AAC5B;AAAA,UACJ,KAAK;AACD,qBAAS,KAAK,YAAY,CAAC;AAC3B;AAAA,UACJ;AACI,mBAAO,qBAAqB,CAAC,cAAc,GAAG,EAAE,CAAC;AACjD;AAAA,QACR;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,IACX;AACA,UAAM,eAAe,MAAM;AACvB,YAAM,iBAAiB,sBAAsB;AAC7C,YAAM,EAAE,UAAU,UAAU,SAAS,IAAI;AACzC,YAAM,gBAAgB,MAAM,aAAa,MAAM,YAAY;AAC3D,YAAM,iBAAiB,MAAM,cAAc,MAAM,aAAa;AAC9D,aAAO;AAAA,QACH,EAAE,OAAO;AAAA,UACL,OAAO;AAAA,QACX,GAAG,kBAAkB,QAAQ,CAAC;AAAA,QAC9B,EAAE,OAAO;AAAA,UACL,OAAO;AAAA,QACX,GAAG;AAAA,UACC,gBACM,EAAE,OAAO;AAAA,YACP,OAAO;AAAA,UACX,GAAG,cAAc,CAAC,CAAC,CAAC,IAClB,mBAAmB,OAAO;AAAA,UAChC,EAAE,OAAO;AAAA,YACL,OAAO;AAAA,UACX,GAAG,kBAAkB,QAAQ,CAAC;AAAA,UAC9B,iBACM,EAAE,OAAO;AAAA,YACP,OAAO;AAAA,UACX,GAAG,eAAe,CAAC,CAAC,CAAC,IACnB,mBAAmB,OAAO;AAAA,QACpC,CAAC;AAAA,QACD,EAAE,OAAO;AAAA,UACL,OAAO;AAAA,QACX,GAAG,kBAAkB,QAAQ,CAAC;AAAA,MAClC;AAAA,IACJ;AACA,UAAM,kBAAkB,CAAC;AACzB,eAAW,QAAQ,UAAQ;AACvB,YAAM,OAAO,gBAAAD,QAAQ,UAAU,MAAM,IAAI,EAAE;AAC3C,sBAAgB,IAAI,IAAI,IAAI,SAAS,KAAK,MAAM,GAAG,IAAI;AAAA,IAC3D,CAAC;AACD,UAAM,qBAAqB,MAAM;AAC7B,YAAM,WAAW,gBAAgB;AACjC,UAAI,SAAS,OAAO;AAChB,cAAM,QAAQ,CAAC;AACf,iBAAS,MAAM,QAAQ,UAAQ;AAC3B,gBAAM,EAAE,OAAO,WAAW,IAAI;AAC9B,cAAI,OAAO;AACP,gBAAI,YAAY;AAChB,gBAAI,YAAY;AACZ,oBAAM,EAAE,YAAY,UAAU,aAAa,IAAI;AAC/C,kBAAI,gBAAAA,QAAQ,WAAW,YAAY,GAAG;AAClC,4BAAY,aAAa,EAAE,KAAK,CAAC;AAAA,cACrC,WACS,CAAC,gBAAAA,QAAQ,YAAY,YAAY,GAAG;AACzC,4BAAY;AAAA,cAChB;AACA,kBAAI,cAAc,UAAU;AACxB,gCAAAA,QAAQ,IAAI,OAAO,YAAY,IAAI;AACnC,gCAAAA,QAAQ,IAAI,OAAO,UAAU,IAAI;AAAA,cACrC;AAAA,YACJ;AACA,kBAAM,KAAK,IAAI;AAAA,UACnB;AAAA,QACJ,CAAC;AACD,eAAO;AAAA,MACX;AACA,aAAO,CAAC;AAAA,IACZ;AACA,UAAM,YAAY,MAAM;AACpB,YAAM,EAAE,aAAa,WAAW,IAAI;AACpC,YAAM,EAAE,YAAY,IAAI;AACxB,YAAM,YAAY,iBAAiB;AACnC,YAAM,WAAW,gBAAgB;AACjC,UAAI,eAAe,aAAa,SAAS,GAAG;AACxC,YAAI,cAAc,aAAa,QAAQ,KAAK,UAAU,QAAQ,SAAS,OAAO;AAC1E,oBAAU,WAAW,mBAAmB;AAAA,QAC5C;AACA,YAAI,CAAC,aAAa;AACd,oBAAU,cAAc;AACxB,cAAI,UAAU,aAAa,OAAO;AAC9B,qBAAS,EAAE,KAAK,MAAM,QAAQ,YAAY,SAAS,CAAC,EAAE,KAAK,CAAC,SAAS;AACjE,4BAAc,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,EAAE,UAAU,KAAK,CAAC,GAAG,IAAI,MAAM,SAAS,CAAC;AAAA,YACjH,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,2BAA2B,CAAC,SAAS;AACvC,YAAM,WAAW,gBAAgB;AACjC,YAAM,QAAQ,aAAa,OAAO,MAAM,kBAAkB,MAAM;AAChE,UAAI,SAAS,UAAU,UAAU,SAAS,eAAe,OAAO;AAC5D,gBAAQ,iBAAiB,IAAI;AAAA,MACjC;AAAA,IACJ;AACA,UAAM,gBAAgB,CAAC,MAAM,QAAQ,SAAS;AAC1C,WAAK,MAAM,YAAY,MAAM,EAAE,OAAO,SAAS,QAAQ,KAAK,GAAG,MAAM,CAAC;AAAA,IAC1E;AACA,UAAM,cAAc;AAAA,MAChB;AAAA,MACA,QAAQ;AACJ,eAAO,QAAQ;AAAA,MACnB;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,YAAY,gBAAgB,MAAM;AAC9B,cAAM,EAAE,aAAa,eAAe,aAAa,WAAW,YAAY,IAAI;AAC5E,cAAM,EAAE,UAAU,IAAI;AACtB,cAAM,cAAc,mBAAmB;AACvC,cAAM,YAAY,iBAAiB;AACnC,cAAM,YAAY,iBAAiB;AACnC,cAAM,YAAY,iBAAiB;AACnC,cAAM,cAAc,mBAAmB;AACvC,cAAM,EAAE,aAAa,YAAY,cAAc,aAAa,YAAY,WAAW,OAAO,CAAC,EAAE,IAAI;AACjG,cAAM,aAAa,UAAU,YAAY,UAAU,SAAS,CAAC;AAC7D,cAAM,WAAW,SAAS;AAC1B,YAAI,CAAC,UAAU;AACX,iBAAO,SAAS;AAAA,QACpB;AACA,YAAI,WAAW,YAAY;AAC3B,YAAI,SAAS;AACb,YAAI,OAAO;AACX,YAAI,gBAAAA,QAAQ,SAAS,WAAW,GAAG;AAC/B,gBAAM,EAAE,QAAQ,IAAI;AACpB,gBAAM,WAAW,iBAAiB,aAAa,WAAW,KAAK,UAAU,gBAAAA,QAAQ,SAAS,SAAS,CAAC,SAAS,KAAK,SAAS,aAAa,EAAE,UAAU,YAAY,CAAC,IAAI;AACrK,mBAAS,WAAW,SAAS,OAAO;AACpC,iBAAO;AAAA,QACX,OACK;AACD,mBAAS;AACT,iBAAO,OAAO;AAAA,QAClB;AACA,cAAM,YAAY,SAAS,OAAO,SAAS;AAC3C,gBAAQ,MAAM;AAAA,UACV,KAAK;AACD,mBAAO,SAAS,OAAO,CAAC,CAAC;AAAA,UAC7B,KAAK;AACD,mBAAO,SAAS,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,MAAM,SAAS,WAAW,KAAK,IAAI,CAAC;AAAA;AAAA,UAE/E,KAAK;AACD,mBAAO,SAAS,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,MAAM,SAAS,WAAW,KAAK,IAAI,CAAC;AAAA;AAAA,UAE/E,KAAK;AACD,gCAAoB,IAAI;AACxB;AAAA,UACJ,KAAK;AACD,mBAAO,gBAAgB,MAAM,+BAA+B,MAAM,SAAS,kBAAkB,CAAC;AAAA,UAClG,KAAK;AACD,qBAAS,WAAW,SAAS;AAC7B;AAAA,UACJ,KAAK;AACD,qBAAS,WAAW,SAAS;AAC7B;AAAA,UACJ,KAAK;AACD,qBAAS,WAAW,SAAS;AAC7B;AAAA,UACJ,KAAK;AACD,qBAAS,WAAW,SAAS;AAC7B;AAAA,UACJ,KAAK;AACD,mBAAO,SAAS,YAAY,IAAI;AAAA,UACpC,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK,SAAS;AACV,kBAAM,cAAc,KAAK;AACzB,kBAAM,sBAAsB,KAAK;AACjC,kBAAM,oBAAoB,KAAK;AAC/B,gBAAI,aAAa;AACb,oBAAM,WAAW,SAAS;AAC1B,oBAAM,WAAW,SAAS;AAC1B,kBAAI,CAAC,YAAY,UAAU,cAAc;AACrC,uBAAO,SAAS;AAAA,cACpB;AACA,kBAAI,WAAW,CAAC;AAChB,kBAAI,aAAa,CAAC;AAClB,kBAAI,aAAa,CAAC;AAClB,kBAAI,aAAa;AACb,oBAAI,YAAY,UAAU;AAEtB,4BAAU,cAAc;AAAA,gBAC5B;AACA,oBAAI,aAAa,SAAS,GAAG;AACzB,+BAAa,OAAO,OAAO,CAAC,GAAG,SAAS;AAAA,gBAC5C;AAAA,cACJ;AACA,kBAAI,UAAU;AAEV,oBAAI,eAAe,aAAa,SAAS,KAAK,UAAU,MAAM;AAC1D,6BAAW,mBAAmB;AAC9B,4BAAU,WAAW;AAAA,gBACzB;AACA,oBAAI,UAAU;AACV,wBAAM,oBAAoB,SAAS;AACnC,wBAAM,EAAE,iBAAiB,oBAAoB,IAAI;AACjD,wBAAM,EAAE,gBAAgB,IAAI,SAAS,eAAe;AACpD,wBAAM,WAAW,gBAAgB;AACjC,sBAAI,cAAc,SAAS;AAC3B,kCAAgB,QAAQ,CAAC,WAAW;AAChC,2BAAO,QAAQ;AAAA,kBACnB,CAAC;AAED,sBAAI,aAAa;AACb,wBAAI,CAAC,gBAAAA,QAAQ,QAAQ,WAAW,GAAG;AAC/B,oCAAc,CAAC,WAAW;AAAA,oBAC9B;AACA,+BAAW,YAAY,IAAI,CAAC,SAAS;AACjC,4BAAM,EAAE,OAAO,MAAM,IAAI;AACzB,4BAAM,UAAU,oBAAoB,KAAK;AACzC,0BAAI,SAAS;AACT,8BAAM,SAAS,QAAQ;AACvB,4BAAI,QAAQ;AACR,iCAAO,QAAQ;AAAA,wBACnB;AAAA,sBACJ;AACA,6BAAO;AAAA,wBACH;AAAA,wBACA,UAAU;AAAA,wBACV;AAAA,sBACJ;AAAA,oBACJ,CAAC;AAAA,kBACL;AACA,+BAAa,SAAS,kBAAkB;AAAA,gBAC5C;AAAA,cACJ,OACK;AACD,oBAAI,UAAU;AACV,sBAAI,UAAU;AACV,6BAAS,SAAS;AAAA,kBACtB,OACK;AACD,+BAAW,SAAS,eAAe;AACnC,iCAAa,SAAS,kBAAkB;AAAA,kBAC5C;AAAA,gBACJ;AAAA,cACJ;AACA,oBAAM,eAAe;AAAA,gBACjB,QAAQ;AAAA,gBACR,OAAO;AAAA,gBACP,QAAQ;AAAA,gBACR;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,MAAM;AAAA,gBACN,MAAM,SAAS,SAAS,SAAS,CAAC,IAAI,CAAC;AAAA,gBACvC,OAAO;AAAA,gBACP,SAAS;AAAA,gBACT,MAAM;AAAA,gBACN,SAAS;AAAA,cACb;AACA,wBAAU,WAAW;AACrB,wBAAU,aAAa;AACvB,wBAAU,eAAe;AACzB,qBAAO,QAAQ,SAAS,eAAe,aAAa,cAAc,GAAG,IAAI,CAAC,EACrE,KAAK,UAAQ;AACd,oBAAI,YAAY,CAAC;AACjB,0BAAU,eAAe;AACzB,oBAAI,MAAM;AACN,sBAAI,eAAe,aAAa,SAAS,GAAG;AACxC,0BAAM,YAAY,WAAW;AAC7B,0BAAM,SAAS,gBAAAA,QAAQ,WAAW,SAAS,IAAI,UAAU,EAAE,MAAM,MAAM,QAAQ,UAAU,OAAO,SAAS,QAAQ,KAAK,CAAC,IAAI,gBAAAA,QAAQ,IAAI,MAAM,aAAa,YAAY,MAAM;AAC5K,8BAAU,QAAQ,gBAAAA,QAAQ,SAAS,KAAK;AACxC,0BAAM,aAAa,WAAW;AAC9B,iCAAa,gBAAAA,QAAQ,WAAW,UAAU,IAAI,WAAW,EAAE,MAAM,MAAM,QAAQ,UAAU,OAAO,SAAS,QAAQ,KAAK,CAAC,IAAI,gBAAAA,QAAQ,IAAI,MAAM,cAAc,QAAQ,MAAM,CAAC;AAE1K,0BAAM,YAAY,KAAK,IAAI,KAAK,KAAK,QAAQ,UAAU,QAAQ,GAAG,CAAC;AACnE,wBAAI,UAAU,cAAc,WAAW;AACnC,gCAAU,cAAc;AAAA,oBAC5B;AAAA,kBACJ,OACK;AACD,0BAAM,WAAW,WAAW;AAC5B,iCAAa,WAAY,gBAAAA,QAAQ,WAAW,QAAQ,IAAI,SAAS,EAAE,MAAM,MAAM,QAAQ,UAAU,OAAO,SAAS,QAAQ,KAAK,CAAC,IAAI,gBAAAA,QAAQ,IAAI,MAAM,QAAQ,IAAK,SAAS,CAAC;AAAA,kBAChL;AAAA,gBACJ;AACA,oBAAI,UAAU;AACV,2BAAS,SAAS,SAAS;AAAA,gBAC/B,OACK;AACD,2BAAS,MAAM;AACX,wBAAI,UAAU;AACV,+BAAS,SAAS,SAAS;AAAA,oBAC/B;AAAA,kBACJ,CAAC;AAAA,gBACL;AACA,oBAAI,YAAY;AACZ,6BAAW,cAAc,GAAG,IAAI;AAAA,gBACpC;AACA,oBAAI,qBAAqB;AACrB,sCAAoB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG,EAAE,UAAU,KAAK,CAAC,CAAC;AAAA,gBAC1F;AACA,uBAAO,EAAE,QAAQ,KAAK;AAAA,cAC1B,CAAC,EAAE,MAAM,CAAC,SAAS;AACf,0BAAU,eAAe;AACzB,oBAAI,mBAAmB;AACnB,oCAAkB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG,EAAE,UAAU,KAAK,CAAC,CAAC;AAAA,gBACxF;AACA,uBAAO,EAAE,QAAQ,MAAM;AAAA,cAC3B,CAAC;AAAA,YACL,OACK;AACD,qBAAO,qBAAqB,CAAC,yBAAyB,CAAC;AAAA,YAC3D;AACA;AAAA,UACJ;AAAA,UACA,KAAK,UAAU;AACX,kBAAM,cAAc,KAAK;AACzB,kBAAM,uBAAuB,KAAK;AAClC,kBAAM,qBAAqB,KAAK;AAChC,gBAAI,aAAa;AACb,oBAAM,gBAAgB,QAAQ,mBAAmB;AACjD,oBAAM,gBAAgB,cAAc,OAAO,SAAO,CAAC,SAAS,cAAc,GAAG,CAAC;AAC9E,oBAAM,OAAO,EAAE,cAAc;AAC7B,oBAAM,eAAe;AAAA,gBACjB,QAAQ;AAAA,gBACR,OAAO;AAAA,gBACP,QAAQ;AAAA,gBACR;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,MAAM;AAAA,gBACN,SAAS;AAAA,cACb;AACA,kBAAI,cAAc,QAAQ;AACtB,uBAAO,gBAAgB,MAAM,+BAA+B,MAAM;AAC9D,sBAAI,CAAC,cAAc,QAAQ;AACvB,2BAAO,SAAS,OAAO,aAAa;AAAA,kBACxC;AACA,4BAAU,eAAe;AACzB,yBAAO,QAAQ,SAAS,gBAAgB,aAAa,cAAc,GAAG,IAAI,CAAC,EACtE,KAAK,UAAQ;AACd,8BAAU,eAAe;AACzB,6BAAS,cAAc,eAAe,KAAK;AAC3C,wBAAI,WAAW;AACX,0BAAI,MAAM,OAAO;AACb,8BAAM,MAAM,QAAQ,EAAE,SAAS,WAAW,MAAM,qBAAqB,GAAG,QAAQ,UAAU,CAAC;AAAA,sBAC/F;AAAA,oBACJ;AACA,wBAAI,aAAa;AACb,kCAAY,cAAc,GAAG,IAAI;AAAA,oBACrC,OACK;AACD,8BAAQ,YAAY,OAAO;AAAA,oBAC/B;AACA,wBAAI,sBAAsB;AACtB,2CAAqB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG,EAAE,UAAU,KAAK,CAAC,CAAC;AAAA,oBAC3F;AACA,2BAAO,EAAE,QAAQ,KAAK;AAAA,kBAC1B,CAAC,EACI,MAAM,UAAQ;AACf,8BAAU,eAAe;AACzB,wBAAI,WAAW;AACX,0BAAI,MAAM,OAAO;AACb,8BAAM,MAAM,QAAQ,EAAE,IAAI,MAAM,SAAS,WAAW,MAAM,oBAAoB,GAAG,QAAQ,QAAQ,CAAC;AAAA,sBACtG;AAAA,oBACJ;AACA,wBAAI,oBAAoB;AACpB,yCAAmB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG,EAAE,UAAU,KAAK,CAAC,CAAC;AAAA,oBACzF;AACA,2BAAO,EAAE,QAAQ,MAAM;AAAA,kBAC3B,CAAC;AAAA,gBACL,CAAC;AAAA,cACL,OACK;AACD,oBAAI,aAAa;AACb,sBAAI,MAAM,OAAO;AACb,0BAAM,MAAM,QAAQ,EAAE,IAAI,MAAM,SAAS,QAAQ,0BAA0B,GAAG,QAAQ,UAAU,CAAC;AAAA,kBACrG;AAAA,gBACJ;AAAA,cACJ;AAAA,YACJ,OACK;AACD,qBAAO,qBAAqB,CAAC,0BAA0B,CAAC;AAAA,YAC5D;AACA;AAAA,UACJ;AAAA,UACA,KAAK,QAAQ;AACT,kBAAM,cAAc,KAAK;AACzB,kBAAM,qBAAqB,KAAK;AAChC,kBAAM,mBAAmB,KAAK;AAC9B,gBAAI,aAAa;AACb,oBAAM,OAAO,SAAS,aAAa;AACnC,oBAAM,EAAE,eAAe,eAAe,eAAe,eAAe,IAAI;AACxE,oBAAM,eAAe;AAAA,gBACjB,QAAQ;AAAA,gBACR,OAAO;AAAA,gBACP,QAAQ;AAAA,gBACR;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,MAAM;AAAA,gBACN,SAAS;AAAA,cACb;AAEA,kBAAI,cAAc,QAAQ;AACtB,qBAAK,iBAAiB,eAAe,OAAO,CAAC,QAAQ,SAAS,eAAe,eAAe,GAAG,MAAM,EAAE;AAAA,cAC3G;AAEA,kBAAI,eAAe,QAAQ;AACvB,qBAAK,gBAAgB,cAAc,OAAO,CAAC,QAAQ,SAAS,eAAe,gBAAgB,GAAG,MAAM,EAAE;AAAA,cAC1G;AACA,kBAAI,cAAc,QAAQ,QAAQ;AAClC,kBAAI,WAAW;AAEX,8BAAc,SAAS,eAAe,YAAY,YAAY,SAAS,iBAAiB,UAAU,EAAE,KAAK,cAAc,OAAO,aAAa,CAAC;AAAA,cAChJ;AACA,qBAAO,YAAY,KAAK,CAAC,WAAW;AAChC,oBAAI,QAAQ;AAER;AAAA,gBACJ;AACA,oBAAI,KAAK,cAAc,UAAU,cAAc,UAAU,cAAc,UAAU,KAAK,eAAe,QAAQ;AACzG,4BAAU,eAAe;AACzB,yBAAO,QAAQ,SAAS,cAAc,aAAa,cAAc,GAAG,IAAI,CAAC,EACpE,KAAK,UAAQ;AACd,8BAAU,eAAe;AACzB,6BAAS,gBAAgB;AACzB,wBAAI,WAAW;AACX,0BAAI,MAAM,OAAO;AACb,8BAAM,MAAM,QAAQ,EAAE,SAAS,WAAW,MAAM,sBAAsB,GAAG,QAAQ,UAAU,CAAC;AAAA,sBAChG;AAAA,oBACJ;AACA,wBAAI,WAAW;AACX,gCAAU,cAAc,GAAG,IAAI;AAAA,oBACnC,OACK;AACD,8BAAQ,YAAY,OAAO;AAAA,oBAC/B;AACA,wBAAI,oBAAoB;AACpB,yCAAmB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG,EAAE,UAAU,KAAK,CAAC,CAAC;AAAA,oBACzF;AACA,2BAAO,EAAE,QAAQ,KAAK;AAAA,kBAC1B,CAAC,EACI,MAAM,UAAQ;AACf,8BAAU,eAAe;AACzB,wBAAI,WAAW;AACX,0BAAI,MAAM,OAAO;AACb,8BAAM,MAAM,QAAQ,EAAE,IAAI,MAAM,SAAS,WAAW,MAAM,oBAAoB,GAAG,QAAQ,QAAQ,CAAC;AAAA,sBACtG;AAAA,oBACJ;AACA,wBAAI,kBAAkB;AAClB,uCAAiB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG,EAAE,UAAU,KAAK,CAAC,CAAC;AAAA,oBACvF;AACA,2BAAO,EAAE,QAAQ,MAAM;AAAA,kBAC3B,CAAC;AAAA,gBACL,OACK;AACD,sBAAI,aAAa;AACb,wBAAI,MAAM,OAAO;AACb,4BAAM,MAAM,QAAQ,EAAE,IAAI,MAAM,SAAS,QAAQ,wBAAwB,GAAG,QAAQ,OAAO,CAAC;AAAA,oBAChG;AAAA,kBACJ;AAAA,gBACJ;AAAA,cACJ,CAAC;AAAA,YACL,OACK;AACD,qBAAO,qBAAqB,CAAC,wBAAwB,CAAC;AAAA,YAC1D;AACA;AAAA,UACJ;AAAA,UACA,SAAS;AACL,kBAAM,eAAe,SAAS,IAAI,IAAI;AACtC,gBAAI,cAAc;AACd,oBAAM,iBAAiB,aAAa,sBAAsB,aAAa;AACvE,kBAAI,gBAAgB;AAChB,+BAAe,EAAE,MAAM,QAAQ,OAAO,SAAS,QAAQ,UAAU,QAAQ,KAAK,GAAG,GAAG,IAAI;AAAA,cAC5F,OACK;AACD,uBAAO,yBAAyB,CAAC,IAAI,CAAC;AAAA,cAC1C;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AACA,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,YAAY;AACR,eAAO,MAAM;AAAA,MACjB;AAAA,MACA,OAAO;AACH,YAAI,UAAU,QAAQ;AAClB,iBAAO,QAAQ,OAAO;AAAA,QAC1B;AACA,eAAO,QAAQ,SAAS;AAAA,MAC5B;AAAA,MACA,cAAc;AACV,eAAO,UAAU;AAAA,MACrB;AAAA,MACA,WAAW;AACP,eAAO,WAAW,IAAI;AAAA,MAC1B;AAAA,MACA,SAAS;AACL,eAAO,WAAW;AAAA,MACtB;AAAA,MACA;AAAA,MACA,aAAa,WAAW;AACpB,cAAM,WAAW,gBAAgB;AACjC,cAAM,EAAE,WAAW,IAAI;AACvB,cAAM,EAAE,MAAM,IAAI;AAClB,cAAM,WAAW,CAAC;AAClB,wBAAAA,QAAQ,SAAS,cAAc,aAAa,QAAQ,KAAK,QAAQ,QAAQ,CAAC,GAAG,UAAQ;AACjF,mBAAS,KAAK,IAAI;AAAA,QACtB,GAAG,EAAE,UAAU,WAAW,CAAC;AAC3B,eAAO,gBAAAA,QAAQ,YAAY,SAAS,IAAI,WAAW,SAAS,SAAS;AAAA,MACzE;AAAA,MACA,YAAY;AACR,cAAM,QAAQ,QAAQ;AACtB,YAAI,OAAO;AACP,iBAAO,MAAM,MAAM;AAAA,QACvB;AACA,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,eAAe;AACX,cAAM,QAAQ,QAAQ;AACtB,YAAI,OAAO;AACP,iBAAO,MAAM,SAAS;AAAA,QAC1B;AACA,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,kBAAkB,OAAO;AACrB,cAAM,QAAQ,QAAQ;AACtB,YAAI,OAAO;AACP,iBAAO,MAAM,cAAc,KAAK;AAAA,QACpC;AACA,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,kBAAkB,OAAO;AACrB,cAAM,QAAQ,QAAQ;AACtB,YAAI,OAAO;AACP,iBAAO,MAAM,cAAc,KAAK;AAAA,QACpC;AACA,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,WAAW;AACP,cAAM,EAAE,UAAU,IAAI;AACtB,kBAAU,cAAc;AACxB,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,gBAAgB,MAAM;AAClB,cAAM,SAAS,SAAS;AACxB,YAAI,QAAQ;AACR,iBAAO,gBAAgB,IAAI;AAAA,QAC/B;AAAA,MACJ;AAAA,MACA,UAAU;AACN,cAAM,EAAE,UAAU,IAAI;AACtB,cAAM,YAAY,iBAAiB;AACnC,kBAAU,cAAc;AACxB,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,eAAe,MAAM;AACjB,cAAM,SAAS,SAAS;AACxB,YAAI,QAAQ;AACR,iBAAO,eAAe,IAAI;AAAA,QAC9B;AAAA,MACJ;AAAA,MACA,eAAe,aAAa;AACxB,cAAM,EAAE,UAAU,IAAI;AACtB,cAAM,YAAY,iBAAiB;AACnC,kBAAU,cAAc,KAAK,IAAI,WAAW,KAAK,IAAI,GAAG,gBAAAA,QAAQ,SAAS,WAAW,CAAC,CAAC;AACtF,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,sBAAsB,MAAM,aAAa;AACrC,cAAM,SAAS,SAAS;AACxB,YAAI,QAAQ;AACR,iBAAO,sBAAsB,MAAM,WAAW;AAAA,QAClD;AAAA,MACJ;AAAA,MACA,YAAY,UAAU;AAClB,cAAM,EAAE,UAAU,IAAI;AACtB,kBAAU,WAAW,KAAK,IAAI,GAAG,gBAAAA,QAAQ,SAAS,QAAQ,CAAC;AAC3D,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,mBAAmB,MAAM,UAAU;AAC/B,cAAM,SAAS,SAAS;AACxB,YAAI,QAAQ;AACR,iBAAO,mBAAmB,MAAM,QAAQ;AAAA,QAC5C;AAAA,MACJ;AAAA,MACA,eAAe;AACX,cAAM,WAAW,SAAS;AAC1B,YAAI,MAAM,aAAa;AACnB,gBAAM,EAAE,SAAS,IAAI;AACrB,iBAAO;AAAA,YACH,MAAM,WAAW,SAAS,YAAY,IAAI,CAAC;AAAA,YAC3C,QAAQ,UAAU;AAAA,YAClB,MAAM,YAAY;AAAA,YAClB,MAAM,SAAS,SAAS,SAAS,CAAC,IAAI,CAAC;AAAA,YACvC,OAAO;AAAA,YACP,OAAO,UAAU;AAAA,YACjB,gBAAgB,WAAW,SAAS,kBAAkB,IAAI,CAAC;AAAA,UAC/D;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAmBJ;AACA,UAAM,qBAAqB;AAAA,MACvB;AAAA,MACA,SAAS,UAAU,QAAQ;AACvB,YAAI,UAAU;AACV,cAAI,gBAAAA,QAAQ,SAAS,QAAQ,GAAG;AAC5B,uBAAW,MAAM,QAAQ,KAAK;AAAA,UAClC;AACA,cAAI,gBAAAA,QAAQ,WAAW,QAAQ,GAAG;AAC9B,mBAAO,WAAW,SAAS,MAAM,CAAC;AAAA,UACtC;AAAA,QACJ;AACA,eAAO,CAAC;AAAA,MACZ;AAAA;AAAA;AAAA;AAAA,MAIA,mBAAmB;AACf,cAAM,EAAE,OAAO,IAAI;AACnB,cAAM,KAAK,QAAQ;AACnB,YAAI,IAAI;AACJ,gBAAM,cAAc,eAAe;AACnC,gBAAM,iBAAiB,kBAAkB;AACzC,gBAAM,aAAa,cAAc;AACjC,gBAAM,gBAAgB,iBAAiB;AACvC,gBAAM,eAAe,gBAAgB;AACrC,gBAAM,WAAW,GAAG;AACpB,gBAAM,oBAAoB,SAAS,IAAK,WAAW,wBAAwB,QAAQ,IAAI;AACvF,iBAAO,oBAAoB,wBAAwB,EAAE,IAAI,gBAAgB,WAAW,IAAI,gBAAgB,cAAc,IAAI,gBAAgB,UAAU,IAAI,gBAAgB,aAAa,IAAI,gBAAgB,YAAY;AAAA,QACzN;AACA,eAAO;AAAA,MACX;AAAA,MACA,kBAAkB;AACd,cAAM,KAAK,QAAQ;AACnB,YAAI,IAAI;AACJ,gBAAM,WAAW,GAAG;AACpB,kBAAQ,UAAU,SAAS,WAAW,EAAE,gBAAiB,WAAW,gBAAAA,QAAQ,SAAS,iBAAiB,QAAQ,EAAE,MAAM,IAAI,KAAM,mBAAmB,iBAAiB;AAAA,QACxK;AACA,eAAO;AAAA,MACX;AAAA,MACA,0BAA0B,QAAQ,MAAM;AACpC,cAAM,EAAE,KAAK,IAAI;AACjB,eAAO,QAAQ,YAAY,QAAQ,IAAI,EAAE,KAAK,CAAC,SAAS;AACpD,cAAI,QAAQ,QAAQ,KAAK,UAAU,CAAC,SAAS,UAAU,UAAU,MAAM,EAAE,SAAS,IAAI,GAAG;AACrF,oBAAQ,cAAc,SAAS,YAAY,SAAS,SAAS,SAAS,IAAI,KAAK,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,EAAE,UAAU,SAAS,SAAS,CAAC,GAAG,IAAI;AAAA,UAC/K;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,MACA,uBAAuB,QAAQ,MAAM;AACjC,gBAAQ,0BAA0B,QAAQ,IAAI;AAC9C,gBAAQ,cAAc,wBAAwB,EAAE,MAAM,OAAO,MAAM,OAAO,GAAG,IAAI;AAAA,MACrF;AAAA,MACA,uBAAuB,MAAM,MAAM;AAC/B,gBAAQ,0BAA0B,MAAM,IAAI;AAC5C,gBAAQ,cAAc,sBAAsB,EAAE,MAAM,KAAK,MAAM,KAAK,GAAG,IAAI;AAAA,MAC/E;AAAA,MACA,iBAAiB,MAAM;AACnB,gBAAQ,KAAK;AACb,gBAAQ,cAAc,QAAQ,EAAE,MAAM,UAAU,SAAS,QAAQ,SAAS,GAAG,IAAI;AAAA,MACrF;AAAA,IACJ;AACA,WAAO,OAAO,SAAS,wBAAwB,aAAa,oBAAoB;AAAA;AAAA,MAE5E,WAAW,SAAS;AAChB,cAAM,WAAW,SAAS;AAC1B,wBAAAA,QAAQ,SAAS,SAAS,CAAC,WAAW;AAClC,cAAI,OAAO,OAAO;AACd,4BAAAA,QAAQ,KAAK,OAAO,OAAO,CAAC,SAAS;AACjC,kBAAI,CAAC,gBAAAA,QAAQ,WAAW,IAAI,GAAG;AAC3B,oBAAI,CAAC,MAAM,IAAI,GAAG;AACd,yBAAO,qBAAqB,CAAC,IAAI,CAAC;AAAA,gBACtC;AAAA,cACJ;AAAA,YACJ,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AACD,YAAI,UAAU;AACV,iBAAO,SAAS,WAAW,OAAO;AAAA,QACtC;AACA,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,aAAa,SAAS;AAClB,gBAAQ,SAAS;AACjB,eAAO,QAAQ,WAAW,OAAO;AAAA,MACrC;AAAA,IACJ,CAAC;AACD,UAAM,WAAW,MAAM;AACnB,YAAM,QAAQ,YAAY;AAC1B,YAAM,SAAS,cAAc;AAC7B,YAAM,YAAY,iBAAiB;AACnC,aAAO,EAAE,OAAO;AAAA,QACZ,KAAK;AAAA,QACL,OAAO,CAAC,YAAY;AAAA,UACZ,CAAC,SAAS,KAAK,EAAE,GAAG;AAAA,UACpB,cAAc,CAAC,CAAC,MAAM;AAAA,UACtB,aAAa,MAAM;AAAA,UACnB,gBAAgB,UAAU;AAAA,UAC1B,eAAe;AAAA,QACnB,CAAC;AAAA,QACL,OAAO;AAAA,MACX,GAAG,aAAa,CAAC;AAAA,IACrB;AACA,UAAM,aAAa,IAAI,CAAC;AACxB,UAAM,MAAM,MAAM,UAAU,MAAM,QAAQ,SAAS,IAAI,MAAM;AACzD,iBAAW;AAAA,IACf,CAAC;AACD,UAAM,MAAM,MAAM,SAAS,MAAM;AAC7B,iBAAW;AAAA,IACf,CAAC;AACD,UAAM,YAAY,MAAM;AACpB,eAAS,MAAM,QAAQ,WAAW,MAAM,WAAW,CAAC,CAAC,CAAC;AAAA,IAC1D,CAAC;AACD,UAAM,MAAM,MAAM,eAAe,MAAM;AACnC,kBAAY;AAAA,IAChB,CAAC;AACD,UAAM,8BAA8B,MAAM;AACtC,gBAAU,aAAa;AAAA,IAC3B,CAAC;AACD,UAAM,2BAA2B,MAAM;AACnC,gBAAU,UAAU;AAAA,IACxB,CAAC;AACD,UAAM,wBAAwB,MAAM;AAChC,gBAAU,OAAO;AAAA,IACrB,CAAC;AACD,UAAM,MAAM,MAAM,aAAa,MAAM;AACjC,gBAAU;AAAA,IACd,CAAC;AACD,UAAM,QAAQ,CAAC,YAAY;AACvB,YAAM,EAAE,UAAU,IAAI;AACtB,UAAI,WAAW;AACX,cAAM,WAAW,UAAU,OAAO;AAClC,YAAI,YAAY,gBAAAA,QAAQ,SAAS,QAAQ,GAAG;AACxC,iBAAO,OAAO,SAAS,QAAQ;AAAA,QACnC;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,cAAU;AACV,cAAU,MAAM;AACZ,eAAS,MAAM;AACX,cAAM,EAAE,QAAQ,IAAI;AACpB,cAAM,YAAY,iBAAiB;AACnC,YAAI,MAAM,YAAY;AAClB,cAAI,CAAC,oBAAoB;AACrB,mBAAO,qBAAqB,CAAC,UAAU,CAAC;AAAA,UAC5C;AAAA,QACJ;AACA,YAAI,MAAM,aAAa;AACnB,cAAI,CAAC,qBAAqB;AACtB,mBAAO,qBAAqB,CAAC,WAAW,CAAC;AAAA,UAC7C;AAAA,QACJ;AAMA,YAAI,UAAU,OAAO;AACjB,kBAAQ,qBAAqB,CAAC,sBAAsB,uBAAuB,CAAC;AAAA,QAChF;AACA,YAAI,WAAW,QAAQ,QAAQ;AAC3B,kBAAQ,WAAW,OAAO;AAAA,QAC9B;AACA,oBAAY;AACZ,kBAAU;AAAA,MACd,CAAC;AACD,mBAAa,GAAG,SAAS,WAAW,wBAAwB;AAAA,IAChE,CAAC;AACD,gBAAY,MAAM;AACd,mBAAa,IAAI,SAAS,SAAS;AACnC,sBAAAA,QAAQ,OAAO,cAAc,mBAAmB,CAAC;AAAA,IACrD,CAAC;AACD,YAAQ,WAAW;AACnB,YAAQ,WAAW,OAAO;AAC1B,YAAQ,YAAY,IAAI;AACxB,WAAO;AAAA,EACX;AAAA,EACA,SAAS;AACL,WAAO,KAAK,SAAS;AAAA,EACzB;AACJ,CAAC;;;AGv/CM,IAAM,UAAU,OAAO,OAAO,CAAC,GAAG,cAAkB;AAAA,EACvD,QAAQ,KAAK;AACT,QAAI,UAAU,aAAiB,MAAM,YAAgB;AAAA,EACzD;AACJ,CAAC;AACD,IAAI,MAAM,YAAY;AAClB,QAAM,WAAW,UAAU,aAAiB,MAAM,YAAgB;AACtE;AACA,MAAM,UAAU,YAAgB;AACzB,IAAM,OAAO;AACpB,IAAOE,gBAAQ;;;ACVf,IAAO,mBAAQC;", "names": ["getConfig", "XEUtils", "tableProps", "grid_default", "grid_default"]}