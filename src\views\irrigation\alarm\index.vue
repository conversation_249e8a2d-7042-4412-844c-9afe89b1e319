<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Modal,
  Form,
  FormItem,
  DatePicker,
  message,
  Tag,
  Badge,
  Statistic,
  Row,
  Col,
  Timeline,
  Alert,
  notification,
} from 'ant-design-vue';
import type { TableColumnsType } from 'ant-design-vue';
import type { EchartsUIType } from '@vben/plugins/echarts';
import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

defineOptions({ name: 'IrrigationAlarm' });

// 表单引用
const formRef = ref();

// 模态框控制
const modalVisible = ref(false);
const detailModalVisible = ref(false);
const selectedAlarm = ref<any>(null);

// 搜索条件
const searchForm = reactive({
  stationName: '',
  alarmType: '',
  status: '',
  dateRange: null,
});

// 处理表单数据
const handleForm = reactive({
  id: null,
  handleResult: '',
  handlePerson: '',
  handleTime: null,
  remark: '',
});

// 模拟报警数据
const alarmData = ref([
  {
    id: 1,
    stationName: '东风灌站',
    alarmType: '水泵故障',
    alarmLevel: 'high',
    alarmTime: '2024-01-15 14:30:25',
    description: '主水泵电流异常，疑似轴承故障',
    status: 'processing',
    handlePerson: '',
    handleTime: null,
    handleResult: '',
    remark: '',
  },
  {
    id: 2,
    stationName: '红旗灌站',
    alarmType: '电压异常',
    alarmLevel: 'medium',
    alarmTime: '2024-01-15 13:45:12',
    description: '供电电压不稳定，波动范围超过正常值',
    status: 'processing',
    handlePerson: '',
    handleTime: null,
    handleResult: '',
    remark: '',
  },
  {
    id: 3,
    stationName: '胜利灌站',
    alarmType: '流量异常',
    alarmLevel: 'low',
    alarmTime: '2024-01-15 12:20:08',
    description: '出水流量低于设定值，可能存在管道堵塞',
    status: 'resolved',
    handlePerson: '张三',
    handleTime: '2024-01-15 15:30:00',
    handleResult: '清理管道堵塞物，恢复正常流量',
    remark: '定期检查管道',
  },
  {
    id: 4,
    stationName: '新华灌站',
    alarmType: '水位异常',
    alarmLevel: 'high',
    alarmTime: '2024-01-15 11:15:30',
    description: '水位传感器显示异常低水位',
    status: 'resolved',
    handlePerson: '李四',
    handleTime: '2024-01-15 14:20:00',
    handleResult: '检查水位传感器，重新校准',
    remark: '传感器需要定期校准',
  },
]);

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '灌站名称',
    dataIndex: 'stationName',
    key: 'stationName',
    width: 120,
  },
  {
    title: '报警类型',
    dataIndex: 'alarmType',
    key: 'alarmType',
    width: 120,
  },
  {
    title: '报警等级',
    dataIndex: 'alarmLevel',
    key: 'alarmLevel',
    width: 100,
  },
  {
    title: '报警时间',
    dataIndex: 'alarmTime',
    key: 'alarmTime',
    width: 150,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '处理人',
    dataIndex: 'handlePerson',
    key: 'handlePerson',
    width: 100,
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right',
  },
];

// 筛选后的数据
const filteredData = computed(() => {
  return alarmData.value.filter(item => {
    const stationMatch = !searchForm.stationName || item.stationName.includes(searchForm.stationName);
    const typeMatch = !searchForm.alarmType || item.alarmType === searchForm.alarmType;
    const statusMatch = !searchForm.status || item.status === searchForm.status;
    return stationMatch && typeMatch && statusMatch;
  });
});

// 统计数据
const statistics = computed(() => {
  const total = alarmData.value.length;
  const processing = alarmData.value.filter(item => item.status === 'processing').length;
  const resolved = alarmData.value.filter(item => item.status === 'resolved').length;
  const high = alarmData.value.filter(item => item.alarmLevel === 'high').length;
  
  return {
    total,
    processing,
    resolved,
    high,
    resolveRate: total > 0 ? ((resolved / total) * 100).toFixed(1) : '0',
  };
});

// 获取报警等级标签
const getAlarmLevelTag = (level: string) => {
  switch (level) {
    case 'high':
      return { color: 'red', text: '高级' };
    case 'medium':
      return { color: 'orange', text: '中级' };
    case 'low':
      return { color: 'blue', text: '低级' };
    default:
      return { color: 'default', text: '未知' };
  }
};

// 获取状态标签
const getStatusTag = (status: string) => {
  switch (status) {
    case 'processing':
      return { color: 'processing', text: '处理中' };
    case 'resolved':
      return { color: 'success', text: '已解决' };
    case 'ignored':
      return { color: 'default', text: '已忽略' };
    default:
      return { color: 'default', text: '未知' };
  }
};

// 重置搜索表单
const resetSearch = () => {
  Object.assign(searchForm, {
    stationName: '',
    alarmType: '',
    status: '',
    dateRange: null,
  });
};

// 查看详情
const viewDetail = (record: any) => {
  selectedAlarm.value = record;
  detailModalVisible.value = true;
};

// 处理报警
const handleAlarm = (record: any) => {
  Object.assign(handleForm, {
    id: record.id,
    handleResult: '',
    handlePerson: '',
    handleTime: null,
    remark: '',
  });
  modalVisible.value = true;
};

// 提交处理结果
const submitHandle = async () => {
  try {
    await formRef.value?.validate();
    
    const index = alarmData.value.findIndex(item => item.id === handleForm.id);
    if (index > -1) {
      Object.assign(alarmData.value[index], {
        status: 'resolved',
        handlePerson: handleForm.handlePerson,
        handleTime: new Date().toLocaleString(),
        handleResult: handleForm.handleResult,
        remark: handleForm.remark,
      });
      message.success('处理成功');
    }
    
    modalVisible.value = false;
    resetHandleForm();
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

// 重置处理表单
const resetHandleForm = () => {
  Object.assign(handleForm, {
    id: null,
    handleResult: '',
    handlePerson: '',
    handleTime: null,
    remark: '',
  });
  formRef.value?.resetFields();
};

// 模拟实时报警
const simulateRealTimeAlarm = () => {
  const stations = ['东风灌站', '红旗灌站', '胜利灌站', '新华灌站', '建设灌站'];
  const types = ['水泵故障', '电压异常', '流量异常', '水位异常', '温度异常'];
  const levels = ['high', 'medium', 'low'];
  
  const newAlarm = {
    id: Date.now(),
    stationName: stations[Math.floor(Math.random() * stations.length)],
    alarmType: types[Math.floor(Math.random() * types.length)],
    alarmLevel: levels[Math.floor(Math.random() * levels.length)],
    alarmTime: new Date().toLocaleString(),
    description: '系统检测到异常，请及时处理',
    status: 'processing',
    handlePerson: '',
    handleTime: null,
    handleResult: '',
    remark: '',
  };
  
  alarmData.value.unshift(newAlarm);
  
  // 显示通知
  notification.warning({
    message: '新报警',
    description: `${newAlarm.stationName} 发生 ${newAlarm.alarmType}`,
    duration: 5,
  });
};

// 图表配置
const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

onMounted(() => {
  // 渲染报警趋势图
  renderEcharts({
    title: {
      text: '7天报警趋势',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: ['高级报警', '中级报警', '低级报警'],
      top: 30,
    },
    xAxis: {
      type: 'category',
      data: ['1/9', '1/10', '1/11', '1/12', '1/13', '1/14', '1/15'],
    },
    yAxis: {
      type: 'value',
      name: '报警数量',
    },
    series: [
      {
        name: '高级报警',
        type: 'line',
        data: [2, 3, 1, 4, 2, 3, 2],
        itemStyle: { color: '#ff4d4f' },
      },
      {
        name: '中级报警',
        type: 'line',
        data: [3, 2, 4, 3, 5, 2, 3],
        itemStyle: { color: '#faad14' },
      },
      {
        name: '低级报警',
        type: 'line',
        data: [1, 2, 2, 1, 3, 2, 1],
        itemStyle: { color: '#1890ff' },
      },
    ],
  });
  
  // 模拟实时报警（每30秒一次）
  setInterval(() => {
    if (Math.random() > 0.7) { // 30%概率产生新报警
      simulateRealTimeAlarm();
    }
  }, 30000);
});

// 表单验证规则
const rules = {
  handleResult: [{ required: true, message: '请输入处理结果' }],
  handlePerson: [{ required: true, message: '请输入处理人' }],
};
</script>

<template>
  <div class="p-5">
    <!-- 统计概览 -->
    <Row :gutter="16" class="mb-5">
      <Col :span="6">
        <Card>
          <Statistic
            title="总报警数"
            :value="statistics.total"
            :value-style="{ color: '#1890ff' }"
          />
        </Card>
      </Col>
      <Col :span="6">
        <Card>
          <Statistic
            title="处理中"
            :value="statistics.processing"
            :value-style="{ color: '#faad14' }"
          />
        </Card>
      </Col>
      <Col :span="6">
        <Card>
          <Statistic
            title="已解决"
            :value="statistics.resolved"
            :value-style="{ color: '#52c41a' }"
          />
        </Card>
      </Col>
      <Col :span="6">
        <Card>
          <Statistic
            title="解决率"
            :value="statistics.resolveRate"
            suffix="%"
            :value-style="{ color: '#722ed1' }"
          />
        </Card>
      </Col>
    </Row>

    <!-- 实时报警提示 -->
    <Alert
      v-if="statistics.processing > 0"
      :message="`当前有 ${statistics.processing} 个报警待处理`"
      type="warning"
      show-icon
      closable
      class="mb-5"
    />

    <!-- 报警趋势图 -->
    <Card title="报警趋势分析" class="mb-5">
      <EchartsUI ref="chartRef" style="height: 300px" />
    </Card>

    <!-- 搜索区域 -->
    <Card title="报警查询" class="mb-5">
      <Form layout="inline" :model="searchForm">
        <FormItem label="灌站名称">
          <Input
            v-model:value="searchForm.stationName"
            placeholder="请输入灌站名称"
            style="width: 200px"
          />
        </FormItem>
        <FormItem label="报警类型">
          <Select
            v-model:value="searchForm.alarmType"
            placeholder="请选择报警类型"
            style="width: 150px"
            allow-clear
          >
            <Select.Option value="水泵故障">水泵故障</Select.Option>
            <Select.Option value="电压异常">电压异常</Select.Option>
            <Select.Option value="流量异常">流量异常</Select.Option>
            <Select.Option value="水位异常">水位异常</Select.Option>
            <Select.Option value="温度异常">温度异常</Select.Option>
          </Select>
        </FormItem>
        <FormItem label="处理状态">
          <Select
            v-model:value="searchForm.status"
            placeholder="请选择状态"
            style="width: 120px"
            allow-clear
          >
            <Select.Option value="processing">处理中</Select.Option>
            <Select.Option value="resolved">已解决</Select.Option>
            <Select.Option value="ignored">已忽略</Select.Option>
          </Select>
        </FormItem>
        <FormItem>
          <Space>
            <Button type="primary">查询</Button>
            <Button @click="resetSearch">重置</Button>
            <Button type="dashed" @click="simulateRealTimeAlarm">模拟报警</Button>
          </Space>
        </FormItem>
      </Form>
    </Card>

    <!-- 报警列表 -->
    <Card title="报警列表">
      <Table
        :columns="columns"
        :data-source="filteredData"
        :scroll="{ x: 1000 }"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'alarmLevel'">
            <Tag :color="getAlarmLevelTag(record.alarmLevel).color">
              {{ getAlarmLevelTag(record.alarmLevel).text }}
            </Tag>
          </template>
          <template v-else-if="column.key === 'status'">
            <Badge
              :status="getStatusTag(record.status).color"
              :text="getStatusTag(record.status).text"
            />
          </template>
          <template v-else-if="column.key === 'action'">
            <Space>
              <Button type="link" size="small" @click="viewDetail(record)">
                详情
              </Button>
              <Button
                v-if="record.status === 'processing'"
                type="link"
                size="small"
                @click="handleAlarm(record)"
              >
                处理
              </Button>
            </Space>
          </template>
        </template>
      </Table>
    </Card>

    <!-- 报警详情模态框 -->
    <Modal
      v-model:open="detailModalVisible"
      title="报警详情"
      width="600px"
      :footer="null"
    >
      <div v-if="selectedAlarm">
        <div class="space-y-4">
          <div><strong>灌站名称：</strong>{{ selectedAlarm.stationName }}</div>
          <div><strong>报警类型：</strong>{{ selectedAlarm.alarmType }}</div>
          <div>
            <strong>报警等级：</strong>
            <Tag :color="getAlarmLevelTag(selectedAlarm.alarmLevel).color">
              {{ getAlarmLevelTag(selectedAlarm.alarmLevel).text }}
            </Tag>
          </div>
          <div><strong>报警时间：</strong>{{ selectedAlarm.alarmTime }}</div>
          <div><strong>报警描述：</strong>{{ selectedAlarm.description }}</div>
          <div>
            <strong>处理状态：</strong>
            <Badge
              :status="getStatusTag(selectedAlarm.status).color"
              :text="getStatusTag(selectedAlarm.status).text"
            />
          </div>
          <div v-if="selectedAlarm.handlePerson">
            <strong>处理人：</strong>{{ selectedAlarm.handlePerson }}
          </div>
          <div v-if="selectedAlarm.handleTime">
            <strong>处理时间：</strong>{{ selectedAlarm.handleTime }}
          </div>
          <div v-if="selectedAlarm.handleResult">
            <strong>处理结果：</strong>{{ selectedAlarm.handleResult }}
          </div>
          <div v-if="selectedAlarm.remark">
            <strong>备注：</strong>{{ selectedAlarm.remark }}
          </div>
        </div>
      </div>
    </Modal>

    <!-- 处理报警模态框 -->
    <Modal
      v-model:open="modalVisible"
      title="处理报警"
      width="600px"
      @ok="submitHandle"
      @cancel="resetHandleForm"
    >
      <Form
        ref="formRef"
        :model="handleForm"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <FormItem label="处理结果" name="handleResult">
          <Input.TextArea
            v-model:value="handleForm.handleResult"
            placeholder="请输入处理结果"
            :rows="3"
          />
        </FormItem>
        <FormItem label="处理人" name="handlePerson">
          <Input v-model:value="handleForm.handlePerson" placeholder="请输入处理人" />
        </FormItem>
        <FormItem label="备注">
          <Input.TextArea
            v-model:value="handleForm.remark"
            placeholder="请输入备注信息"
            :rows="2"
          />
        </FormItem>
      </Form>
    </Modal>
  </div>
</template>

<style scoped>
.space-y-4 > * + * {
  margin-top: 1rem;
}
</style>
