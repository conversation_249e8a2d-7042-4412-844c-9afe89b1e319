var Qe=Object.defineProperty,Ze=Object.defineProperties;var et=Object.getOwnPropertyDescriptors;var ne=Object.getOwnPropertySymbols;var ke=Object.prototype.hasOwnProperty,Be=Object.prototype.propertyIsEnumerable;var de=(s,t,o)=>t in s?Qe(s,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):s[t]=o,h=(s,t)=>{for(var o in t||(t={}))ke.call(t,o)&&de(s,o,t[o]);if(ne)for(var o of ne(t))Be.call(t,o)&&de(s,o,t[o]);return s},V=(s,t)=>Ze(s,et(t));var W=(s,t)=>{var o={};for(var a in s)ke.call(s,a)&&t.indexOf(a)<0&&(o[a]=s[a]);if(s!=null&&ne)for(var a of ne(s))t.indexOf(a)<0&&Be.call(s,a)&&(o[a]=s[a]);return o};var U=(s,t,o)=>de(s,typeof t!="symbol"?t+"":t,o);var J=(s,t,o)=>new Promise((a,l)=>{var i=m=>{try{k(o.next(m))}catch(d){l(d)}},p=m=>{try{k(o.throw(m))}catch(d){l(d)}},k=m=>m.done?a(m.value):Promise.resolve(m.value).then(i,p);k((o=o.apply(s,t)).next())});import{a$ as le,bv as Te,bQ as tt,bz as ot,Y as st,bR as at,bS as nt,bC as Ae,bT as lt,bU as rt,bH as it,bj as dt,bV as ct,bW as ut,bX as pt,bY as ft,bJ as mt,bM as ht,b2 as Me}from"./bootstrap-CFDAkNgp.js";import{c as N}from"./index-DpxZFE0y.js";import{a4 as T,aa as y,ab as f,a7 as e,af as yt,ag as vt,ac as v,a8 as C,R as $e,av as ue,J as z,P as x,ar as gt,x as X,aq as M,aw as bt,aV as F,aW as D,ad as pe,T as Ee,a9 as Ct,ao as kt,az as Bt,bu as ce,an as Mt,Y as wt,bK as _t,aB as Ot,ai as G,aj as Q,ah as we,n as fe,aF as Se,bn as xt,i as Dt,k as _e}from"../jse/index-index-B2UBupFX.js";import{_ as Tt,S as At,c as $t}from"./use-vben-form-DwBeC3z-.js";const Gt=le("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);const Et=le("expand",[["path",{d:"m15 15 6 6",key:"1s409w"}],["path",{d:"m15 9 6-6",key:"ko1vev"}],["path",{d:"M21 16v5h-5",key:"1ck2sf"}],["path",{d:"M21 8V3h-5",key:"1qoq8a"}],["path",{d:"M3 16v5h5",key:"1t08am"}],["path",{d:"m3 21 6-6",key:"wwnumi"}],["path",{d:"M3 8V3h5",key:"1ln10m"}],["path",{d:"M9 9 3 3",key:"v551iv"}]]);const St=le("shrink",[["path",{d:"m15 15 6 6m-6-6v4.8m0-4.8h4.8",key:"17vawe"}],["path",{d:"M9 19.8V15m0 0H4.2M9 15l-6 6",key:"chjx8e"}],["path",{d:"M15 4.2V9m0 0h4.8M15 9l6-6",key:"lav6yq"}],["path",{d:"M9 4.2V9m0 0H4.2M9 9 3 3",key:"1pxi2q"}]]);const It=le("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Lt=T({__name:"Dialog",props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(s,{emit:t}){const l=Te(s,t);return(i,p)=>(f(),y(e(tt),yt(vt(e(l))),{default:v(()=>[C(i.$slots,"default")]),_:3},16))}}),Pt=["data-dismissable-modal"],zt=T({__name:"DialogOverlay",setup(s){ot();const t=$e("DISMISSABLE_MODAL_ID");return(o,a)=>(f(),ue("div",{"data-dismissable-modal":e(t),class:"bg-overlay z-popup inset-0"},null,8,Pt))}}),Rt=T({__name:"DialogContent",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{},animationType:{default:"slide"},appendTo:{default:"body"},class:{},closeClass:{},closeDisabled:{type:Boolean,default:!1},modal:{type:Boolean},open:{type:Boolean},overlayBlur:{},showClose:{type:Boolean,default:!0},zIndex:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus","close","closed","opened"],setup(s,{expose:t,emit:o}){const a=s,l=o,i=z(()=>{const $=a,{class:r,modal:c,open:g,showClose:B,animationType:w}=$;return W($,["class","modal","open","showClose","animationType"])});function p(){return a.appendTo==="body"||a.appendTo===document.body||!a.appendTo}const k=z(()=>p()?"fixed":"absolute"),m=Te(i,l),d=x(null);function u(r){var c;r.target===((c=d.value)==null?void 0:c.$el)&&(a.open?l("opened"):l("closed"))}return t({getContentRef:()=>d.value}),(r,c)=>(f(),y(gt,{defer:"",to:r.appendTo},[X(st,{name:"fade"},{default:v(()=>[r.open&&r.modal?(f(),y(zt,{key:0,style:bt(V(h({},r.zIndex?{zIndex:r.zIndex}:{}),{position:k.value,backdropFilter:r.overlayBlur&&r.overlayBlur>0?`blur(${r.overlayBlur}px)`:"none"})),onClick:c[0]||(c[0]=()=>l("close"))},null,8,["style"])):M("",!0)]),_:1}),X(e(nt),pe({ref_key:"contentRef",ref:d,style:V(h({},r.zIndex?{zIndex:r.zIndex}:{}),{position:k.value}),onAnimationend:u},e(m),{class:e(D)("z-popup bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 w-full p-6 shadow-lg outline-none sm:rounded-xl",{"data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-top-[48%]":r.animationType==="slide"},a.class)}),{default:v(()=>[C(r.$slots,"default"),r.showClose?(f(),y(e(at),{key:0,disabled:r.closeDisabled,class:F(e(D)("data-[state=open]:bg-accent data-[state=open]:text-muted-foreground hover:bg-accent hover:text-accent-foreground text-foreground/80 flex-center absolute right-3 top-3 h-6 w-6 rounded-full px-1 text-lg opacity-70 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none",a.closeClass)),onClick:c[1]||(c[1]=()=>l("close"))},{default:v(()=>[X(e(It),{class:"h-4 w-4"})]),_:1},8,["disabled","class"])):M("",!0)]),_:3},16,["style","class"])],8,["to"]))}}),Oe=T({__name:"DialogDescription",props:{asChild:{type:Boolean},as:{},class:{}},setup(s){const t=s,o=z(()=>{const p=t,{class:l}=p;return W(p,["class"])}),a=Ae(o);return(l,i)=>(f(),y(e(lt),pe(e(a),{class:e(D)("text-muted-foreground text-sm",t.class)}),{default:v(()=>[C(l.$slots,"default")]),_:3},16,["class"]))}}),Vt=T({__name:"DialogFooter",props:{class:{}},setup(s){const t=s;return(o,a)=>(f(),ue("div",{class:F(e(D)("flex flex-row flex-col-reverse justify-end gap-x-2",t.class))},[C(o.$slots,"default")],2))}}),Ft=T({__name:"DialogHeader",props:{class:{}},setup(s){const t=s;return(o,a)=>(f(),ue("div",{class:F(e(D)("flex flex-col gap-y-1.5 text-center sm:text-left",t.class))},[C(o.$slots,"default")],2))}}),xe=T({__name:"DialogTitle",props:{asChild:{type:Boolean},as:{},class:{}},setup(s){const t=s,o=z(()=>{const p=t,{class:l}=p;return W(p,["class"])}),a=Ae(o);return(l,i)=>(f(),y(e(rt),pe(e(a),{class:e(D)("text-lg font-semibold leading-none tracking-tight",t.class)}),{default:v(()=>[C(l.$slots,"default")]),_:3},16,["class"]))}}),Qt=N("mdi:keyboard-esc"),Zt=N("mdi:wechat"),eo=N("mdi:github"),to=N("mdi:google"),oo=N("mdi:qqchat"),so=N("ri:dingding-fill");function Yt(s,t,o,a){const l=Ee({offsetX:0,offsetY:0}),i=x(!1),p=u=>{const r=u.clientX,c=u.clientY;if(!s.value)return;const g=s.value.getBoundingClientRect(),{offsetX:B,offsetY:w}=l,A=g.left,$=g.top,q=g.width,Z=g.height;let S=null;if(a!=null&&a.value){const _=document.querySelector(a.value);_&&(S=_.getBoundingClientRect())}let j,H,K,I;if(S)K=S.left-A+B,j=S.right-A-q+B,I=S.top-$+w,H=S.bottom-$-Z+w;else{const _=document.documentElement,L=_.clientWidth,O=_.clientHeight;K=-A+B,I=-$+w,j=L-A-q+B,H=O-$-Z+w}const ee=_=>{let L=B+_.clientX-r,O=w+_.clientY-c;L=Math.min(Math.max(L,K),j),O=Math.min(Math.max(O,I),H),l.offsetX=L,l.offsetY=O,s.value&&(s.value.style.transform=`translate(${L}px, ${O}px)`,i.value=!0)},te=()=>{i.value=!1,document.removeEventListener("mousemove",ee),document.removeEventListener("mouseup",te)};document.addEventListener("mousemove",ee),document.addEventListener("mouseup",te)},k=()=>{const u=ce(t);u&&s.value&&u.addEventListener("mousedown",p)},m=()=>{const u=ce(t);u&&s.value&&u.removeEventListener("mousedown",p)},d=()=>{l.offsetX=0,l.offsetY=0;const u=ce(s);u&&(u.style.transform="none")};return Ct(()=>{kt(()=>{o.value?k():m()})}),Bt(()=>{m()}),{dragging:i,resetPosition:d,transform:l}}const Xt=T({__name:"modal",props:{modalApi:{default:void 0},animationType:{},appendToMain:{type:Boolean,default:!1},bordered:{type:Boolean},cancelText:{},centered:{type:Boolean},class:{},closable:{type:Boolean},closeOnClickModal:{type:Boolean},closeOnPressEscape:{type:Boolean},confirmDisabled:{type:Boolean},confirmLoading:{type:Boolean},confirmText:{},contentClass:{},description:{},destroyOnClose:{type:Boolean,default:!1},draggable:{type:Boolean},footer:{type:Boolean},footerClass:{},fullscreen:{type:Boolean},fullscreenButton:{type:Boolean},header:{type:Boolean},headerClass:{},loading:{type:Boolean},modal:{type:Boolean},openAutoFocus:{type:Boolean},overlayBlur:{},showCancelButton:{type:Boolean},showConfirmButton:{type:Boolean},submitting:{type:Boolean},title:{},titleTooltip:{},zIndex:{}},setup(s){var ae,Ce;const t=s,o=it.getComponents(),a=x(),l=x(),i=x(),p=x(),k=x(),m=Mt();Se("DISMISSABLE_MODAL_ID",m);const{$t:d}=dt(),{isMobile:u}=ct(),r=(Ce=(ae=t.modalApi)==null?void 0:ae.useStore)==null?void 0:Ce.call(ae),{appendToMain:c,bordered:g,cancelText:B,centered:w,class:A,closable:$,closeOnClickModal:q,closeOnPressEscape:Z,confirmDisabled:S,confirmLoading:j,confirmText:H,contentClass:K,description:I,destroyOnClose:ee,draggable:te,footer:_,footerClass:L,fullscreen:O,fullscreenButton:Ie,header:me,headerClass:Le,loading:he,modal:Pe,openAutoFocus:ze,overlayBlur:Re,showCancelButton:Ve,showConfirmButton:Fe,submitting:E,title:oe,titleTooltip:ye,animationType:Ye,zIndex:Xe}=ut(t,r),se=z(()=>O.value||u.value),ve=z(()=>te.value&&!se.value&&me.value),ge=z(()=>c.value?`#${pt}>div:not(.absolute)>div`:void 0),{dragging:Ne,transform:qe}=Yt(i,p,ve,ge),re=x(!1),ie=x(!0);wt(()=>{var n;return(n=r==null?void 0:r.value)==null?void 0:n.isOpen},n=>J(null,null,function*(){if(n){if(ie.value=!1,re.value||(re.value=!0),yield fe(),!a.value)return;const b=a.value.getContentRef();i.value=b.$el;const{offsetX:Y,offsetY:R}=qe;i.value.style.transform=`translate(${Y}px, ${R}px)`}}),{immediate:!0}),_t(()=>{var n;c.value||(n=t.modalApi)==null||n.close()});function je(){var n;(n=t.modalApi)==null||n.setState(b=>V(h({},b),{fullscreen:!O.value}))}function He(n){(!q.value||E.value)&&(n.preventDefault(),n.stopPropagation())}function Ke(n){(!Z.value||E.value)&&n.preventDefault()}function We(n){ze.value||n==null||n.preventDefault()}function Ue(n){const b=n.target,Y=b==null?void 0:b.dataset.dismissableModal;(!q.value||Y!==m||E.value)&&(n.preventDefault(),n.stopPropagation())}function be(n){n.preventDefault(),n.stopPropagation()}const Je=z(()=>!e(ee)&&e(re));function Ge(){var n;ie.value=!0,(n=t.modalApi)==null||n.onClosed()}return(n,b)=>{var Y;return f(),y(e(Lt),{modal:!1,open:(Y=e(r))==null?void 0:Y.isOpen,"onUpdate:open":b[3]||(b[3]=()=>{var R;return e(E)||(R=n.modalApi)==null?void 0:R.close()})},{default:v(()=>{var R;return[X(e(Rt),{ref_key:"contentRef",ref:a,"append-to":ge.value,class:F(e(D)("left-0 right-0 top-[10vh] mx-auto flex max-h-[80%] w-[520px] flex-col p-0",se.value?"sm:rounded-none":"sm:rounded-[var(--radius)]",e(A),{"border-border border":e(g),"shadow-3xl":!e(g),"left-0 top-0 size-full max-h-full !translate-x-0 !translate-y-0":se.value,"top-1/2 !-translate-y-1/2":e(w)&&!se.value,"duration-300":!e(Ne),hidden:ie.value})),"force-mount":Je.value,modal:e(Pe),open:(R=e(r))==null?void 0:R.isOpen,"show-close":e($),"animation-type":e(Ye),"z-index":e(Xe),"overlay-blur":e(Re),"close-class":"top-3",onCloseAutoFocus:be,onClosed:Ge,"close-disabled":e(E),onEscapeKeyDown:Ke,onFocusOutside:be,onInteractOutside:He,onOpenAutoFocus:We,onOpened:b[2]||(b[2]=()=>{var P;return(P=n.modalApi)==null?void 0:P.onOpened()}),onPointerDownOutside:Ue},{default:v(()=>[X(e(Ft),{ref_key:"headerRef",ref:p,class:F(e(D)("px-5 py-4",{"border-b":e(g),hidden:!e(me),"cursor-move select-none":ve.value},e(Le)))},{default:v(()=>[e(oe)?(f(),y(e(xe),{key:0,class:"text-left"},{default:v(()=>[C(n.$slots,"title",{},()=>[G(Q(e(oe))+" ",1),e(ye)?C(n.$slots,"titleTooltip",{key:0},()=>[X(e(Tt),{"trigger-class":"pb-1"},{default:v(()=>[G(Q(e(ye)),1)]),_:1})]):M("",!0)])]),_:3})):M("",!0),e(I)?(f(),y(e(Oe),{key:1},{default:v(()=>[C(n.$slots,"description",{},()=>[G(Q(e(I)),1)])]),_:3})):M("",!0),!e(oe)||!e(I)?(f(),y(e(ft),{key:2},{default:v(()=>[e(oe)?M("",!0):(f(),y(e(xe),{key:0})),e(I)?M("",!0):(f(),y(e(Oe),{key:1}))]),_:1})):M("",!0)]),_:3},8,["class"]),Ot("div",{ref_key:"wrapperRef",ref:l,class:F(e(D)("relative min-h-40 flex-1 overflow-y-auto p-3",e(K),{"pointer-events-none":e(he)||e(E)}))},[C(n.$slots,"default")],2),e(he)||e(E)?(f(),y(e(mt),{key:0,spinning:""})):M("",!0),e(Ie)?(f(),y(e(ht),{key:1,class:"hover:bg-accent hover:text-accent-foreground text-foreground/80 flex-center absolute right-10 top-3 hidden size-6 rounded-full px-1 text-lg opacity-70 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none sm:block",onClick:je},{default:v(()=>[e(O)?(f(),y(e(St),{key:0,class:"size-3.5"})):(f(),y(e(Et),{key:1,class:"size-3.5"}))]),_:1})):M("",!0),e(_)?(f(),y(e(Vt),{key:2,ref_key:"footerRef",ref:k,class:F(e(D)("flex-row items-center justify-end p-2",{"border-t":e(g)},e(L)))},{default:v(()=>[C(n.$slots,"prepend-footer"),C(n.$slots,"footer",{},()=>[e(Ve)?(f(),y(we(e(o).DefaultButton||e(Me)),{key:0,variant:"ghost",disabled:e(E),onClick:b[0]||(b[0]=()=>{var P;return(P=n.modalApi)==null?void 0:P.onCancel()})},{default:v(()=>[C(n.$slots,"cancelText",{},()=>[G(Q(e(B)||e(d)("cancel")),1)])]),_:3},8,["disabled"])):M("",!0),C(n.$slots,"center-footer"),e(Fe)?(f(),y(we(e(o).PrimaryButton||e(Me)),{key:1,disabled:e(S),loading:e(j)||e(E),onClick:b[1]||(b[1]=()=>{var P;return(P=n.modalApi)==null?void 0:P.onConfirm()})},{default:v(()=>[C(n.$slots,"confirmText",{},()=>[G(Q(e(H)||e(d)("confirm")),1)])]),_:3},8,["disabled","loading"])):M("",!0)]),C(n.$slots,"append-footer")]),_:3},8,["class"])):M("",!0)]),_:3},8,["append-to","class","force-mount","modal","open","show-close","animation-type","z-index","overlay-blur","close-disabled"])]}),_:3},8,["open"])}}});class Nt{constructor(t={}){U(this,"sharedData",{payload:{}});U(this,"store");U(this,"api");U(this,"state");const r=t,{connectedComponent:o,onBeforeClose:a,onCancel:l,onClosed:i,onConfirm:p,onOpenChange:k,onOpened:m}=r,d=W(r,["connectedComponent","onBeforeClose","onCancel","onClosed","onConfirm","onOpenChange","onOpened"]),u={bordered:!0,centered:!1,class:"",closeOnClickModal:!0,closeOnPressEscape:!0,confirmDisabled:!1,confirmLoading:!1,contentClass:"",destroyOnClose:!0,draggable:!1,footer:!0,footerClass:"",fullscreen:!1,fullscreenButton:!0,header:!0,headerClass:"",isOpen:!1,loading:!1,modal:!0,openAutoFocus:!1,showCancelButton:!0,showConfirmButton:!0,title:"",animationType:"slide"};this.store=new At(h(h({},u),d),{onUpdate:()=>{var g,B,w;const c=this.store.state;(c==null?void 0:c.isOpen)===((g=this.state)==null?void 0:g.isOpen)?this.state=c:(this.state=c,(w=(B=this.api).onOpenChange)==null||w.call(B,!!(c!=null&&c.isOpen)))}}),this.state=this.store.state,this.api={onBeforeClose:a,onCancel:l,onClosed:i,onConfirm:p,onOpenChange:k,onOpened:m},xt(this)}close(){return J(this,null,function*(){var o,a,l;((l=yield(a=(o=this.api).onBeforeClose)==null?void 0:a.call(o))!=null?l:!0)&&this.store.setState(i=>V(h({},i),{isOpen:!1}))})}getData(){var t,o;return(o=(t=this.sharedData)==null?void 0:t.payload)!=null?o:{}}lock(t=!0){return this.setState({submitting:t})}onCancel(){var t,o;this.api.onCancel?(o=(t=this.api).onCancel)==null||o.call(t):this.close()}onClosed(){var t,o;this.state.isOpen||(o=(t=this.api).onClosed)==null||o.call(t)}onConfirm(){var t,o;(o=(t=this.api).onConfirm)==null||o.call(t)}onOpened(){var t,o;this.state.isOpen&&((o=(t=this.api).onOpened)==null||o.call(t))}open(){this.store.setState(t=>V(h({},t),{isOpen:!0,submitting:!1}))}setData(t){return this.sharedData.payload=t,this}setState(t){return Dt(t)?this.store.setState(t):this.store.setState(o=>h(h({},o),t)),this}unlock(){return this.lock(!1)}}const De=Symbol("VBEN_MODAL_INJECT"),qt={};function ao(s={}){var m;const{connectedComponent:t}=s;if(t){const d=Ee({}),u=x(!0);return[T((c,{attrs:g,slots:B})=>(Se(De,{extendApi(A){Object.setPrototypeOf(d,A)},options:s,reCreateModal(){return J(this,null,function*(){u.value=!1,yield fe(),u.value=!0})}}),jt(d,h(h(h({},c),g),B)),()=>_e(u.value?t:"div",h(h({},c),g),B)),{name:"VbenParentModal",inheritAttrs:!1}),d]}const o=$e(De,{}),a=h(h(h({},qt),o.options),s);a.onOpenChange=d=>{var u,r,c;(u=s.onOpenChange)==null||u.call(s,d),(c=(r=o.options)==null?void 0:r.onOpenChange)==null||c.call(r,d)};const l=a.onClosed;a.onClosed=()=>{var d;l==null||l(),a.destroyOnClose&&((d=o.reCreateModal)==null||d.call(o))};const i=new Nt(a),p=i;p.useStore=d=>$t(i.store,d);const k=T((d,{attrs:u,slots:r})=>()=>_e(Xt,V(h(h({},d),u),{modalApi:p}),r),{name:"VbenModal",inheritAttrs:!1});return(m=o.extendApi)==null||m.call(o,p),[k,p]}function jt(s,t){return J(this,null,function*(){var l;if(!t||Object.keys(t).length===0)return;yield fe();const o=(l=s==null?void 0:s.store)==null?void 0:l.state;if(!o)return;const a=new Set(Object.keys(o));for(const i of Object.keys(t))a.has(i)&&!["class"].includes(i)&&console.warn(`[Vben Modal]: When 'connectedComponent' exists, do not set props or slots '${i}', which will increase complexity. If you need to modify the props of Modal, please use useVbenModal or api.`)})}export{Gt as C,Zt as M,so as R,It as X,oo as a,eo as b,to as c,Qt as d,ao as u};
