var c=Object.defineProperty;var t=Object.getOwnPropertySymbols;var m=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable;var a=(e,n,o)=>n in e?c(e,n,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[n]=o,r=(e,n)=>{for(var o in n||(n={}))m.call(n,o)&&a(e,o,n[o]);if(t)for(var o of t(n))f.call(n,o)&&a(e,o,n[o]);return e};import{bZ as p}from"./bootstrap-CFDAkNgp.js";import{a4 as s,k as i}from"../jse/index-index-B2UBupFX.js";function d(e){return s({name:`Icon-${e}`,setup(n,{attrs:o}){return()=>i(p,r(r({icon:e},n),o))}})}export{d as c};
