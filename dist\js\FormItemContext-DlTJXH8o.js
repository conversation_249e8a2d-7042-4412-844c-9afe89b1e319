import{al as i}from"./bootstrap-DlHXJWd_.js";import{a4 as F,R as r,q as c,az as u,J as I,aF as a}from"../jse/index-index-DYNcUVMZ.js";const m=Symbol("ContextProps"),s=Symbol("InternalContextProps"),l={id:I(()=>{}),onFieldBlur:()=>{},onFieldChange:()=>{},clearValidate:()=>{}},d={addFormItemField:()=>{},removeFormItemField:()=>{}},f=()=>{const o=r(s,d),t=Symbol("FormItemFieldKey"),e=c();return o.addFormItemField(t,e.type),u(()=>{o.removeFormItemField(t)}),a(s,d),a(m,l),r(m,l)},C=i({}),v=F({name:"NoFormStatus",setup(o,t){let{slots:e}=t;return C.useProvide({}),()=>{var n;return(n=e.default)===null||n===void 0?void 0:n.call(e)}}});export{C as F,v as N,f as u};
