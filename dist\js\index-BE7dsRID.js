import{v as V,P as j,t as F,a as k,aK as J,j as K,_ as m,O as M,b as G,aL as x}from"./bootstrap-CFDAkNgp.js";import{u as W}from"./useFlexGapSupport-TvfJoknb.js";import{a4 as Y,J as s,P as I,Y as q,x as p,F as H}from"../jse/index-index-B2UBupFX.js";const Q={small:8,middle:16,large:24},U=()=>({prefixCls:String,size:{type:[String,Number,Array]},direction:j.oneOf(F("horizontal","vertical")).def("horizontal"),align:j.oneOf(F("start","end","center","baseline")),wrap:V()});function X(e){return typeof e=="string"?Q[e]:e||0}const d=Y({compatConfig:{MODE:3},name:"ASpace",inheritAttrs:!1,props:U(),slots:Object,setup(e,P){let{slots:o,attrs:f}=P;const{prefixCls:l,space:g,direction:z}=k("space",e),[B,D]=J(l),h=W(),n=s(()=>{var a,t,i;return(i=(a=e.size)!==null&&a!==void 0?a:(t=g==null?void 0:g.value)===null||t===void 0?void 0:t.size)!==null&&i!==void 0?i:"small"}),y=I(),r=I();q(n,()=>{[y.value,r.value]=(Array.isArray(n.value)?n.value:[n.value,n.value]).map(a=>X(a))},{immediate:!0});const b=s(()=>e.align===void 0&&e.direction==="horizontal"?"center":e.align),E=s(()=>K(l.value,D.value,`${l.value}-${e.direction}`,{[`${l.value}-rtl`]:z.value==="rtl",[`${l.value}-align-${b.value}`]:b.value})),L=s(()=>z.value==="rtl"?"marginLeft":"marginRight"),R=s(()=>{const a={};return h.value&&(a.columnGap=`${y.value}px`,a.rowGap=`${r.value}px`),m(m({},a),e.wrap&&{flexWrap:"wrap",marginBottom:`${-r.value}px`})});return()=>{var a,t;const{wrap:i,direction:T="horizontal"}=e,C=(a=o.default)===null||a===void 0?void 0:a.call(o),_=M(C),w=_.length;if(w===0)return null;const c=(t=o.split)===null||t===void 0?void 0:t.call(o),A=`${l.value}-item`,O=y.value,S=w-1;return p("div",G(G({},f),{},{class:[E.value,f.class],style:[R.value,f.style]}),[_.map((N,u)=>{let $=C.indexOf(N);$===-1&&($=`$$space-${u}`);let v={};return h.value||(T==="vertical"?u<S&&(v={marginBottom:`${O/(c?2:1)}px`}):v=m(m({},u<S&&{[L.value]:`${O/(c?2:1)}px`}),i&&{paddingBottom:`${r.value}px`})),B(p(H,{key:$},[p("div",{class:A,style:v},[N]),u<S&&c&&p("span",{class:`${A}-split`,style:v},[c])]))})])}}});d.Compact=x;d.install=function(e){return e.component(d.name,d),e.component(x.name,x),e};export{x as Compact,d as default,U as spaceProps};
