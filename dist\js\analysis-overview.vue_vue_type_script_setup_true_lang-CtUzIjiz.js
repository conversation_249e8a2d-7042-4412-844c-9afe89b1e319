var q=Object.defineProperty;var m=Object.getOwnPropertySymbols;var I=Object.prototype.hasOwnProperty,N=Object.prototype.propertyIsEnumerable;var F=(e,s,t)=>s in e?q(e,s,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[s]=t,P=(e,s)=>{for(var t in s||(s={}))I.call(s,t)&&F(e,t,s[t]);if(m)for(var t of m(s))N.call(s,t)&&F(e,t,s[t]);return e};var j=(e,s)=>{var t={};for(var a in e)I.call(e,a)&&s.indexOf(a)<0&&(t[a]=e[a]);if(e!=null&&m)for(var a of m(e))s.indexOf(a)<0&&N.call(e,a)&&(t[a]=e[a]);return t};import{c as b}from"./index-DpxZFE0y.js";import{bC as G,b$ as H,c0 as K}from"./bootstrap-CFDAkNgp.js";import{_ as U,a as X,b as Z}from"./TabsList.vue_vue_type_script_setup_true_lang-DM4no0zC.js";import{a4 as p,av as d,ab as r,aV as aa,a7 as l,aW as D,a8 as w,J as V,aa as v,ac as i,ad as ea,P as E,bQ as z,ao as sa,Y as ta,a9 as la,bR as na,bS as oa,aw as ra,aj as g,x as c,F as y,aC as $,ai as O,aB as ia}from"../jse/index-index-B2UBupFX.js";import{_ as ca,a as ua,b as da,c as fa}from"./CardTitle.vue_vue_type_script_setup_true_lang-DM2nXXp9.js";const pa=p({__name:"CardFooter",props:{class:{}},setup(e){const s=e;return(t,a)=>(r(),d("div",{class:aa(l(D)("flex items-center p-6 pt-0",s.class))},[w(t.$slots,"default")],2))}}),_a=p({__name:"TabsTrigger",props:{value:{},disabled:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(e){const s=e,t=V(()=>{const _=s,{class:o}=_;return j(_,["class"])}),a=G(t);return(o,n)=>(r(),v(l(H),ea(l(a),{class:l(D)("ring-offset-background focus-visible:ring-ring data-[state=active]:bg-background data-[state=active]:text-foreground inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow",s.class)}),{default:i(()=>[w(o.$slots,"default")]),_:3},16,["class"]))}}),A=p({name:"CountToAnimator",__name:"count-to-animator",props:{autoplay:{type:Boolean,default:!0},color:{default:""},decimal:{default:"."},decimals:{default:0},duration:{default:1500},endVal:{default:2021},prefix:{default:""},separator:{default:","},startVal:{default:0},suffix:{default:""},transition:{default:"linear"},useEasing:{type:Boolean,default:!0}},emits:["finished","onFinished","onStarted","started"],setup(e,{expose:s,emit:t}){const a=e,o=t,n=E(a.startVal),_=E(!1);let S=z(n);const J=V(()=>M(l(S)));sa(()=>{n.value=a.startVal}),ta([()=>a.startVal,()=>a.endVal],()=>{a.autoplay&&k()}),la(()=>{a.autoplay&&k()});function k(){C(),n.value=a.endVal}function L(){n.value=a.startVal,C()}function C(){S=z(n,P({disabled:_,duration:a.duration,onFinished:()=>{o("finished"),o("onFinished")},onStarted:()=>{o("started"),o("onStarted")}},a.useEasing?{transition:na[a.transition]}:{}))}function M(u){if(!u&&u!==0)return"";const{decimal:B,decimals:Q,prefix:R,separator:x,suffix:W}=a;u=Number(u).toFixed(Q),u+="";const h=u.split(".");let f=h[0];const Y=h.length>1?B+h[1]:"",T=/(\d+)(\d{3})/;if(x&&!oa(x)&&f)for(;T.test(f);)f=f.replace(T,`$1${x}$2`);return R+f+Y+W}return s({reset:L}),(u,B)=>(r(),d("span",{style:ra({color:u.color})},g(J.value),5))}}),wa=b("svg:download"),Va=b("svg:card"),Sa=b("svg:bell"),ka=b("svg:cake"),ma={class:"card-box w-full px-4 pb-5 pt-3"},Ca=p({name:"AnalysisChartsTabs",__name:"analysis-charts-tabs",props:{tabs:{default:()=>[]}},setup(e){const s=e,t=V(()=>{var a,o;return(o=(a=s.tabs)==null?void 0:a[0])==null?void 0:o.value});return(a,o)=>(r(),d("div",ma,[c(l(U),{"default-value":t.value},{default:i(()=>[c(l(X),null,{default:i(()=>[(r(!0),d(y,null,$(a.tabs,n=>(r(),v(l(_a),{key:n.label,value:n.value},{default:i(()=>[O(g(n.label),1)]),_:2},1032,["value"]))),128))]),_:1}),(r(!0),d(y,null,$(a.tabs,n=>(r(),v(l(Z),{key:n.label,value:n.value,class:"pt-4"},{default:i(()=>[w(a.$slots,n.value)]),_:2},1032,["value"]))),128))]),_:3},8,["default-value"])]))}}),va={class:"grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4"},Ba=p({name:"AnalysisOverview",__name:"analysis-overview",props:{items:{default:()=>[]}},setup(e){return(s,t)=>(r(),d("div",va,[(r(!0),d(y,null,$(s.items,a=>(r(),v(l(fa),{key:a.title,title:a.title,class:"w-full"},{default:i(()=>[c(l(ca),null,{default:i(()=>[c(l(ua),{class:"text-xl"},{default:i(()=>[O(g(a.title),1)]),_:2},1024)]),_:2},1024),c(l(da),{class:"flex items-center justify-between"},{default:i(()=>[c(l(A),{"end-val":a.value,"start-val":1,class:"text-xl",prefix:""},null,8,["end-val"]),c(l(K),{icon:a.icon,class:"size-8 flex-shrink-0"},null,8,["icon"])]),_:2},1024),c(l(pa),{class:"justify-between"},{default:i(()=>[ia("span",null,g(a.totalTitle),1),c(l(A),{"end-val":a.totalValue,"start-val":1,prefix:""},null,8,["end-val"])]),_:2},1024)]),_:2},1032,["title"]))),128))]))}});export{Va as S,Ba as _,Ca as a,ka as b,wa as c,Sa as d};
