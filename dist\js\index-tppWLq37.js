import m from"./index-CGqxGK2L.js";import{F as J,bu as L}from"./bootstrap-CFDAkNgp.js";import{C as o,a as S,T as X}from"./index-DOXLVRHg.js";import{R as c,C as n,S as y}from"./index-CHRdc_8y.js";import{u as v,_ as k}from"./use-echarts-Dor8gHz9.js";import{T as D}from"./index-BhH5F5SY.js";import{a4 as G,T as F,P as _,J as H,a9 as K,av as N,ab as x,x as e,ac as a,a7 as t,aB as s,ai as r,aa as E,aq as O,aj as p}from"../jse/index-index-B2UBupFX.js";import{T as B}from"./index-DEfsrzsO.js";import{P as T}from"./index-Ci1_yKfn.js";import"./index-B2Lu6Z2W.js";import"./Trigger-D2zZP_An.js";import"./ResizeObserver.es-CDE7jhPe.js";import"./BaseInput-Dslq5mxC.js";import"./Overflow--DaGju1E.js";import"./index-DQZjs6Lb.js";import"./isMobile-8sZ0LT6r.js";import"./DownOutlined-CVWF16Fu.js";import"./CheckOutlined-CZeS3E7o.js";import"./SearchOutlined-DqQ4RgbY.js";import"./move-IXaXzbNk.js";import"./slide-BhgK1D9k.js";import"./useMergedState-C4x1IDb9.js";import"./FormItemContext-CoieKSxA.js";import"./statusUtils-D62pPzYs.js";import"./index-1BiZfdtR.js";import"./shallowequal-CNCY1mYq.js";import"./_arrayIncludes-B8uzE354.js";import"./index-tPQFuBU-.js";import"./colors-KzMfSzFw.js";import"./collapseMotion-DiwOar_A.js";import"./useRefs-f0KzY-v7.js";import"./hasIn-Bt_d2Zq4.js";import"./isPlainObject-0t1li2J1.js";import"./Col-Bjak4A2I.js";import"./useFlexGapSupport-TvfJoknb.js";import"./index-DYfzhziO.js";import"./index-C5usYyko.js";import"./index-DfHX-m0D.js";import"./index-C5ScQeGh.js";import"./index-BDBY1qBK.js";import"./Checkbox-Dt0Z6J8a.js";import"./index-UTpExPeP.js";import"./index-BCzosP6o.js";import"./EyeOutlined-MQsW2UzL.js";import"./debounce-CesRCMoz.js";const Q={class:"p-5"},Y={key:0,class:"flex items-center"},Z={key:0,class:"font-medium"},tt={class:"text-center p-4"},et={class:"text-2xl font-bold text-blue-500"},at={class:"text-center p-4"},lt={class:"text-2xl font-bold text-green-500"},nt={class:"text-center p-4"},it={class:"text-2xl font-bold text-purple-500"},ot=G({name:"IrrigationStatistics",__name:"index",setup(st){const g=F({timeRange:"month",stationType:"all",dateRange:null}),R=_(),h=_(),I=_(),P=_(),{renderEcharts:M}=v(R),{renderEcharts:W}=v(h),{renderEcharts:V}=v(I),{renderEcharts:q}=v(P),i=F({totalStations:154,onlineStations:142,totalFlow:12856.8,totalPower:68245.6,totalIrrigationArea:125680,avgEfficiency:87.5,maintenanceRate:92.3,faultRate:2.1}),b=_([{stationName:"东风灌站",efficiency:95.2,runningTime:8.5,targetTime:9,status:"excellent"},{stationName:"红旗灌站",efficiency:78.5,runningTime:6.2,targetTime:8,status:"good"},{stationName:"胜利灌站",efficiency:92.1,runningTime:7.8,targetTime:8.5,status:"excellent"},{stationName:"新华灌站",efficiency:65.3,runningTime:5.1,targetTime:8,status:"poor"},{stationName:"建设灌站",efficiency:88.7,runningTime:7.5,targetTime:8.5,status:"good"}]),z=_([{id:1,stationName:"东风灌站",deviceName:"主水泵",maintenanceType:"定期保养",maintenanceDate:"2024-01-10",cost:2500,duration:4,result:"正常"},{id:2,stationName:"红旗灌站",deviceName:"变频器",maintenanceType:"故障维修",maintenanceDate:"2024-01-08",cost:8500,duration:12,result:"已修复"},{id:3,stationName:"胜利灌站",deviceName:"流量计",maintenanceType:"校准检测",maintenanceDate:"2024-01-05",cost:1200,duration:2,result:"正常"}]),U=[{title:"灌站名称",dataIndex:"stationName",key:"stationName"},{title:"运行效率(%)",dataIndex:"efficiency",key:"efficiency"},{title:"运行时间(h)",dataIndex:"runningTime",key:"runningTime"},{title:"目标时间(h)",dataIndex:"targetTime",key:"targetTime"},{title:"状态",dataIndex:"status",key:"status"}],$=[{title:"灌站名称",dataIndex:"stationName",key:"stationName"},{title:"设备名称",dataIndex:"deviceName",key:"deviceName"},{title:"维修类型",dataIndex:"maintenanceType",key:"maintenanceType"},{title:"维修日期",dataIndex:"maintenanceDate",key:"maintenanceDate"},{title:"费用(元)",dataIndex:"cost",key:"cost"},{title:"耗时(h)",dataIndex:"duration",key:"duration"},{title:"结果",dataIndex:"result",key:"result"}],C=u=>{switch(u){case"excellent":return{color:"green",text:"优秀"};case"good":return{color:"blue",text:"良好"};case"poor":return{color:"red",text:"较差"};default:return{color:"default",text:"未知"}}},w=H(()=>{const u=(i.onlineStations/i.totalStations*100).toFixed(1),l=(i.totalFlow/i.onlineStations).toFixed(1),f=(i.totalPower/i.onlineStations).toFixed(1);return{onlineRate:u,avgFlowPerStation:l,avgPowerPerStation:f}}),A=()=>{M({title:{text:"30天流量趋势",left:"center"},tooltip:{trigger:"axis",formatter:"{b}: {c} m³/h"},xAxis:{type:"category",data:Array.from({length:30}).map((u,l)=>`${l+1}日`)},yAxis:{type:"value",name:"流量(m³/h)"},series:[{name:"总流量",type:"line",smooth:!0,data:Array.from({length:30}).map(()=>1e4+Math.random()*5e3),areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(24, 144, 255, 0.6)"},{offset:1,color:"rgba(24, 144, 255, 0.1)"}]}}}]}),W({title:{text:"灌站功率分布",left:"center"},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c}座 ({d}%)"},series:[{name:"功率分布",type:"pie",radius:["40%","70%"],data:[{value:25,name:"大型灌站(>300kW)"},{value:45,name:"中型灌站(150-300kW)"},{value:58,name:"小型灌站(50-150kW)"},{value:26,name:"微型灌站(<50kW)"}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]}),V({title:{text:"灌站运行效率分析",left:"center"},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},xAxis:{type:"category",data:b.value.map(u=>u.stationName)},yAxis:{type:"value",name:"效率(%)",max:100},series:[{name:"运行效率",type:"bar",data:b.value.map(u=>({value:u.efficiency,itemStyle:{color:u.efficiency>=90?"#52c41a":u.efficiency>=80?"#1890ff":"#ff4d4f"}}))}]}),q({title:{text:"月度维修统计",left:"center"},tooltip:{trigger:"axis"},legend:{data:["维修次数","维修费用"],top:30},xAxis:{type:"category",data:["1月","2月","3月","4月","5月","6月"]},yAxis:[{type:"value",name:"维修次数",position:"left"},{type:"value",name:"费用(万元)",position:"right"}],series:[{name:"维修次数",type:"bar",data:[12,8,15,10,6,9],itemStyle:{color:"#1890ff"}},{name:"维修费用",type:"line",yAxisIndex:1,data:[8.5,6.2,12.3,7.8,4.5,6.9],itemStyle:{color:"#ff4d4f"}}]})},j=()=>{i.totalFlow+=(Math.random()-.5)*1e3,i.totalPower+=(Math.random()-.5)*5e3,i.avgEfficiency+=(Math.random()-.5)*2,A()};return K(()=>{A()}),(u,l)=>(x(),N("div",Q,[e(t(o),{title:"查询条件",class:"mb-5"},{default:a(()=>[e(t(c),{gutter:16,align:"middle"},{default:a(()=>[e(t(n),{span:4},{default:a(()=>[l[6]||(l[6]=s("span",{class:"mr-2"},"时间范围：",-1)),e(t(m),{value:g.timeRange,"onUpdate:value":l[0]||(l[0]=f=>g.timeRange=f),style:{width:"120px"}},{default:a(()=>[e(t(m).Option,{value:"week"},{default:a(()=>l[2]||(l[2]=[r("近一周")])),_:1,__:[2]}),e(t(m).Option,{value:"month"},{default:a(()=>l[3]||(l[3]=[r("近一月")])),_:1,__:[3]}),e(t(m).Option,{value:"quarter"},{default:a(()=>l[4]||(l[4]=[r("近一季")])),_:1,__:[4]}),e(t(m).Option,{value:"year"},{default:a(()=>l[5]||(l[5]=[r("近一年")])),_:1,__:[5]})]),_:1},8,["value"])]),_:1,__:[6]}),e(t(n),{span:4},{default:a(()=>[l[12]||(l[12]=s("span",{class:"mr-2"},"灌站类型：",-1)),e(t(m),{value:g.stationType,"onUpdate:value":l[1]||(l[1]=f=>g.stationType=f),style:{width:"120px"}},{default:a(()=>[e(t(m).Option,{value:"all"},{default:a(()=>l[7]||(l[7]=[r("全部")])),_:1,__:[7]}),e(t(m).Option,{value:"large"},{default:a(()=>l[8]||(l[8]=[r("大型")])),_:1,__:[8]}),e(t(m).Option,{value:"medium"},{default:a(()=>l[9]||(l[9]=[r("中型")])),_:1,__:[9]}),e(t(m).Option,{value:"small"},{default:a(()=>l[10]||(l[10]=[r("小型")])),_:1,__:[10]}),e(t(m).Option,{value:"micro"},{default:a(()=>l[11]||(l[11]=[r("微型")])),_:1,__:[11]})]),_:1},8,["value"])]),_:1,__:[12]}),e(t(n),{span:4},{default:a(()=>[e(t(J),{type:"primary",onClick:j},{default:a(()=>l[13]||(l[13]=[r("刷新数据")])),_:1,__:[13]})]),_:1})]),_:1})]),_:1}),e(t(c),{gutter:16,class:"mb-5"},{default:a(()=>[e(t(n),{span:4},{default:a(()=>[e(t(o),null,{default:a(()=>[e(t(y),{title:"总灌站数",value:i.totalStations,suffix:"座","value-style":{color:"#1890ff"}},null,8,["value"])]),_:1})]),_:1}),e(t(n),{span:4},{default:a(()=>[e(t(o),null,{default:a(()=>[e(t(y),{title:"在线率",value:w.value.onlineRate,suffix:"%","value-style":{color:"#52c41a"}},null,8,["value"])]),_:1})]),_:1}),e(t(n),{span:4},{default:a(()=>[e(t(o),null,{default:a(()=>[e(t(y),{title:"总流量",value:i.totalFlow,suffix:"m³/h",precision:1,"value-style":{color:"#722ed1"}},null,8,["value"])]),_:1})]),_:1}),e(t(n),{span:4},{default:a(()=>[e(t(o),null,{default:a(()=>[e(t(y),{title:"总功率",value:i.totalPower,suffix:"kW",precision:1,"value-style":{color:"#faad14"}},null,8,["value"])]),_:1})]),_:1}),e(t(n),{span:4},{default:a(()=>[e(t(o),null,{default:a(()=>[e(t(y),{title:"平均效率",value:i.avgEfficiency,suffix:"%",precision:1,"value-style":{color:"#13c2c2"}},null,8,["value"])]),_:1})]),_:1}),e(t(n),{span:4},{default:a(()=>[e(t(o),null,{default:a(()=>[e(t(y),{title:"故障率",value:i.faultRate,suffix:"%",precision:1,"value-style":{color:"#ff4d4f"}},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(t(X),{"default-active-key":"1",class:"mb-5"},{default:a(()=>[e(t(S),{key:"1",tab:"运行分析"},{default:a(()=>[e(t(c),{gutter:16},{default:a(()=>[e(t(n),{span:12},{default:a(()=>[e(t(o),{title:"流量趋势分析"},{default:a(()=>[e(t(k),{ref_key:"flowTrendRef",ref:R,style:{height:"350px"}},null,512)]),_:1})]),_:1}),e(t(n),{span:12},{default:a(()=>[e(t(o),{title:"功率分布统计"},{default:a(()=>[e(t(k),{ref_key:"powerDistributionRef",ref:h,style:{height:"350px"}},null,512)]),_:1})]),_:1})]),_:1})]),_:1}),e(t(S),{key:"2",tab:"效率分析"},{default:a(()=>[e(t(c),{gutter:16},{default:a(()=>[e(t(n),{span:12},{default:a(()=>[e(t(o),{title:"运行效率对比"},{default:a(()=>[e(t(k),{ref_key:"efficiencyAnalysisRef",ref:I,style:{height:"350px"}},null,512)]),_:1})]),_:1}),e(t(n),{span:12},{default:a(()=>[e(t(o),{title:"效率详细数据"},{default:a(()=>[e(t(B),{columns:U,"data-source":b.value,pagination:!1,size:"small"},{bodyCell:a(({column:f,record:d})=>[f.key==="efficiency"?(x(),N("div",Y,[e(t(T),{percent:d.efficiency,size:"small","stroke-color":d.efficiency>=90?"#52c41a":d.efficiency>=80?"#1890ff":"#ff4d4f",class:"mr-2",style:{width:"60px"}},null,8,["percent","stroke-color"]),s("span",null,p(d.efficiency)+"%",1)])):f.key==="status"?(x(),E(t(D),{key:1,color:C(d.status).color},{default:a(()=>[r(p(C(d.status).text),1)]),_:2},1032,["color"])):O("",!0)]),_:1},8,["data-source"])]),_:1})]),_:1})]),_:1})]),_:1}),e(t(S),{key:"3",tab:"维修分析"},{default:a(()=>[e(t(c),{gutter:16},{default:a(()=>[e(t(n),{span:12},{default:a(()=>[e(t(o),{title:"维修统计趋势"},{default:a(()=>[e(t(k),{ref_key:"maintenanceStatRef",ref:P,style:{height:"350px"}},null,512)]),_:1})]),_:1}),e(t(n),{span:12},{default:a(()=>[e(t(o),{title:"维修记录详情"},{default:a(()=>[e(t(B),{columns:$,"data-source":z.value,pagination:!1,size:"small"},{bodyCell:a(({column:f,record:d})=>[f.key==="cost"?(x(),N("span",Z,"¥"+p(d.cost.toLocaleString()),1)):f.key==="result"?(x(),E(t(D),{key:1,color:d.result==="正常"||d.result==="已修复"?"green":"red"},{default:a(()=>[r(p(d.result),1)]),_:2},1032,["color"])):O("",!0)]),_:1},8,["data-source"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),e(t(o),{title:"关键指标分析"},{default:a(()=>[e(t(c),{gutter:16},{default:a(()=>[e(t(n),{span:8},{default:a(()=>[s("div",tt,[l[14]||(l[14]=s("div",{class:"text-lg text-gray-600 mb-2"},"平均单站流量",-1)),s("div",et,p(w.value.avgFlowPerStation)+" m³/h ",1),e(t(T),{percent:75,"stroke-color":"#1890ff",class:"mt-2"})])]),_:1}),e(t(n),{span:8},{default:a(()=>[s("div",at,[l[15]||(l[15]=s("div",{class:"text-lg text-gray-600 mb-2"},"平均单站功率",-1)),s("div",lt,p(w.value.avgPowerPerStation)+" kW ",1),e(t(T),{percent:68,"stroke-color":"#52c41a",class:"mt-2"})])]),_:1}),e(t(n),{span:8},{default:a(()=>[s("div",nt,[l[16]||(l[16]=s("div",{class:"text-lg text-gray-600 mb-2"},"维修完成率",-1)),s("div",it,p(i.maintenanceRate)+"% ",1),e(t(T),{percent:i.maintenanceRate,"stroke-color":"#722ed1",class:"mt-2"},null,8,["percent"])])]),_:1})]),_:1})]),_:1})]))}}),Zt=L(ot,[["__scopeId","data-v-86a7ebd4"]]);export{Zt as default};
