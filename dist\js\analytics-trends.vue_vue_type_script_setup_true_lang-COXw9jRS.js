import{u as s,_ as o}from"./use-echarts-DVa43yCy.js";import{a4 as i,P as l,a9 as n,aa as c,ab as p,a7 as m}from"../jse/index-index-DYNcUVMZ.js";const h=i({__name:"analytics-trends",setup(f){const e=l(),{renderEcharts:a}=s(e);return n(()=>{a({grid:{bottom:0,containLabel:!0,left:"1%",right:"1%",top:"2 %"},series:[{areaStyle:{},data:[111,2e3,6e3,16e3,33333,55555,64e3,33333,18e3,36e3,7e4,42444,23222,13e3,8e3,4e3,1200,333,222,111],itemStyle:{color:"#5ab1ef"},smooth:!0,type:"line"},{areaStyle:{},data:[33,66,88,333,3333,6200,2e4,3e3,1200,13e3,22e3,11e3,2221,1201,390,198,60,30,22,11],itemStyle:{color:"#019680"},smooth:!0,type:"line"}],tooltip:{axisPointer:{lineStyle:{color:"#019680",width:1}},trigger:"axis"},xAxis:{axisTick:{show:!1},boundaryGap:!1,data:Array.from({length:18}).map((r,t)=>`${t+6}:00`),splitLine:{lineStyle:{type:"solid",width:1},show:!0},type:"category"},yAxis:[{axisTick:{show:!1},max:8e4,splitArea:{show:!0},splitNumber:4,type:"value"}]})}),(r,t)=>(p(),c(m(o),{ref_key:"chartRef",ref:e},null,512))}});export{h as _};
