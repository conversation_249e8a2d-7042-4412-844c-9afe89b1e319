import{bu as o}from"./bootstrap-CFDAkNgp.js";import{av as a,ab as r,aB as e,a8 as s}from"../jse/index-index-B2UBupFX.js";const c={},l={class:"mb-7 sm:mx-auto sm:w-full sm:max-w-md"},n={class:"text-foreground mb-3 text-3xl font-bold leading-9 tracking-tight lg:text-4xl"},d={class:"text-muted-foreground lg:text-md text-sm"};function m(t,i){return r(),a("div",l,[e("h2",n,[s(t.$slots,"default")]),e("p",d,[s(t.$slots,"desc")])])}const x=o(c,[["render",m]]);export{x as T};
