import{_ as t,s as U,v as c,x as D,b as d,o as O}from"./bootstrap-CFDAkNgp.js";import{g as H,c as N,d as M,r as V,a as W}from"./index-CPEaeSbW.js";import{u as B}from"./FormItemContext-CoieKSxA.js";import{a4 as w,P as R,x as S}from"../jse/index-index-B2UBupFX.js";import"./index-BhH5F5SY.js";import"./colors-KzMfSzFw.js";import"./useMergedState-C4x1IDb9.js";import"./Trigger-D2zZP_An.js";import"./ResizeObserver.es-CDE7jhPe.js";import"./move-IXaXzbNk.js";import"./shallowequal-CNCY1mYq.js";import"./statusUtils-D62pPzYs.js";import"./index-C5ScQeGh.js";import"./slide-BhgK1D9k.js";const x=()=>({format:String,showNow:c(),showHour:c(),showMinute:c(),showSecond:c(),use12Hours:c(),hourStep:Number,minuteStep:Number,secondStep:Number,hideDisabledOptions:c(),popupClassName:String,status:U()});function $(p){const _=H(p,t(t({},x()),{order:{type:Boolean,default:!0}})),{TimePicker:I,RangePicker:y}=_,j=w({name:"ATimePicker",inheritAttrs:!1,props:t(t(t(t({},N()),M()),x()),{addon:{type:Function}}),slots:Object,setup(m,g){let{slots:i,expose:C,emit:n,attrs:f}=g;const r=m,a=B();D(!(i.addon||r.addon),"TimePicker","`addon` is deprecated. Please use `v-slot:renderExtraFooter` instead.");const s=R();C({focus:()=>{var o;(o=s.value)===null||o===void 0||o.focus()},blur:()=>{var o;(o=s.value)===null||o===void 0||o.blur()}});const h=(o,F)=>{n("update:value",o),n("change",o,F),a.onFieldChange()},k=o=>{n("update:open",o),n("openChange",o)},P=o=>{n("focus",o)},v=o=>{n("blur",o),a.onFieldBlur()},b=o=>{n("ok",o)};return()=>{const{id:o=a.id.value}=r;return S(I,d(d(d({},f),O(r,["onUpdate:value","onUpdate:open"])),{},{id:o,dropdownClassName:r.popupClassName,mode:void 0,ref:s,renderExtraFooter:r.addon||i.addon||r.renderExtraFooter||i.renderExtraFooter,onChange:h,onOpenChange:k,onFocus:P,onBlur:v,onOk:b}),i)}}}),A=w({name:"ATimeRangePicker",inheritAttrs:!1,props:t(t(t(t({},N()),V()),x()),{order:{type:Boolean,default:!0}}),slots:Object,setup(m,g){let{slots:i,expose:C,emit:n,attrs:f}=g;const r=m,a=R(),s=B();C({focus:()=>{var e;(e=a.value)===null||e===void 0||e.focus()},blur:()=>{var e;(e=a.value)===null||e===void 0||e.blur()}});const h=(e,u)=>{n("update:value",e),n("change",e,u),s.onFieldChange()},k=e=>{n("update:open",e),n("openChange",e)},P=e=>{n("focus",e)},v=e=>{n("blur",e),s.onFieldBlur()},b=(e,u)=>{n("panelChange",e,u)},o=e=>{n("ok",e)},F=(e,u,E)=>{n("calendarChange",e,u,E)};return()=>{const{id:e=s.id.value}=r;return S(y,d(d(d({},f),O(r,["onUpdate:open","onUpdate:value"])),{},{id:e,dropdownClassName:r.popupClassName,picker:"time",mode:void 0,ref:a,onChange:h,onOpenChange:k,onFocus:P,onBlur:v,onPanelChange:b,onOk:o,onCalendarChange:F}),i)}}});return{TimePicker:j,TimeRangePicker:A}}const{TimePicker:l,TimeRangePicker:T}=$(W),re=t(l,{TimePicker:l,TimeRangePicker:T,install:p=>(p.component(l.name,l),p.component(T.name,T),p)});export{l as TimePicker,T as TimeRangePicker,re as default};
