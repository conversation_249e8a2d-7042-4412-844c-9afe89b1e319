import{_ as r,o as O,w as x,a as B,i as E,b as j,f as G}from"./bootstrap-CFDAkNgp.js";import _,{selectProps as M}from"./index-CGqxGK2L.js";import{a4 as P,P as F,x as f}from"../jse/index-index-B2UBupFX.js";import"./index-B2Lu6Z2W.js";import"./Trigger-D2zZP_An.js";import"./ResizeObserver.es-CDE7jhPe.js";import"./BaseInput-Dslq5mxC.js";import"./Overflow--DaGju1E.js";import"./index-DQZjs6Lb.js";import"./isMobile-8sZ0LT6r.js";import"./DownOutlined-CVWF16Fu.js";import"./CheckOutlined-CZeS3E7o.js";import"./SearchOutlined-DqQ4RgbY.js";import"./move-IXaXzbNk.js";import"./slide-BhgK1D9k.js";import"./useMergedState-C4x1IDb9.js";import"./FormItemContext-CoieKSxA.js";import"./statusUtils-D62pPzYs.js";const n=()=>null;n.isSelectOption=!0;n.displayName="AAutoCompleteOption";const u=()=>null;u.isSelectOptGroup=!0;u.displayName="AAutoCompleteOptGroup";function $(e){var s,o;return((s=e==null?void 0:e.type)===null||s===void 0?void 0:s.isSelectOption)||((o=e==null?void 0:e.type)===null||o===void 0?void 0:o.isSelectOptGroup)}const k=()=>r(r({},O(M(),["loading","mode","optionLabelProp","labelInValue"])),{dataSource:Array,dropdownMenuStyle:{type:Object,default:void 0},dropdownMatchSelectWidth:{type:[Number,Boolean],default:!0},prefixCls:String,showSearch:{type:Boolean,default:void 0},transitionName:String,choiceTransitionName:{type:String,default:"zoom"},autofocus:{type:Boolean,default:void 0},backfill:{type:Boolean,default:void 0},filterOption:{type:[Boolean,Function],default:!1},defaultActiveFirstOption:{type:Boolean,default:!0},status:String}),le=n,ne=u,v=P({compatConfig:{MODE:3},name:"AAutoComplete",inheritAttrs:!1,props:k(),slots:Object,setup(e,s){let{slots:o,attrs:C,expose:b}=s;x(!e.dropdownClassName);const d=F(),A=()=>{var t;const a=G((t=o.default)===null||t===void 0?void 0:t.call(o));return a.length?a[0]:void 0};b({focus:()=>{var t;(t=d.value)===null||t===void 0||t.focus()},blur:()=>{var t;(t=d.value)===null||t===void 0||t.blur()}});const{prefixCls:i}=B("select",e);return()=>{var t,a,p;const{size:S,dataSource:y,notFoundContent:N=(t=o.notFoundContent)===null||t===void 0?void 0:t.call(o)}=e;let c;const{class:g}=C,h={[g]:!!g,[`${i.value}-lg`]:S==="large",[`${i.value}-sm`]:S==="small",[`${i.value}-show-search`]:!0,[`${i.value}-auto-complete`]:!0};if(e.options===void 0){const m=((a=o.dataSource)===null||a===void 0?void 0:a.call(o))||((p=o.options)===null||p===void 0?void 0:p.call(o))||[];m.length&&$(m[0])?c=m:c=y?y.map(l=>{if(E(l))return l;switch(typeof l){case"string":return f(n,{key:l,value:l},{default:()=>[l]});case"object":return f(n,{key:l.value,value:l.value},{default:()=>[l.text]});default:throw new Error("AutoComplete[dataSource] only supports type `string[] | Object[]`.")}}):[]}const w=O(r(r(r({},e),C),{mode:_.SECRET_COMBOBOX_MODE_DO_NOT_USE,getInputElement:A,notFoundContent:N,class:h,popupClassName:e.popupClassName||e.dropdownClassName,ref:d}),["dataSource","loading"]);return f(_,w,j({default:()=>[c]},O(o,["default","dataSource","options"])))}}}),ae=r(v,{Option:n,OptGroup:u,install(e){return e.component(v.name,v),e.component(n.displayName,n),e.component(u.displayName,u),e}});export{ne as AutoCompleteOptGroup,le as AutoCompleteOption,k as autoCompleteProps,ae as default};
