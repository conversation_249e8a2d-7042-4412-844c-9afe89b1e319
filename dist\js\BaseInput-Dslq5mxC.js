import{P as s,b as f}from"./bootstrap-CFDAkNgp.js";import{a4 as O,a5 as w,x as h,P as b,Y as k,J as S}from"../jse/index-index-B2UBupFX.js";var z=function(n,u){var o={};for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&u.indexOf(t)<0&&(o[t]=n[t]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,t=Object.getOwnPropertySymbols(n);a<t.length;a++)u.indexOf(t[a])<0&&Object.prototype.propertyIsEnumerable.call(n,t[a])&&(o[t[a]]=n[t[a]]);return o};const K=O({compatConfig:{MODE:3},props:{disabled:s.looseBool,type:s.string,value:s.any,tag:{type:String,default:"input"},size:s.string,onChange:Function,onInput:Function,onBlur:Function,onFocus:Function,onKeydown:Function,onCompositionstart:Function,onCompositionend:Function,onKeyup:Function,onPaste:Function,onMousedown:Function},emits:["change","input","blur","keydown","focus","compositionstart","compositionend","keyup","paste","mousedown"],setup(n,u){let{expose:o}=u;const t=w(null);return o({focus:()=>{t.value&&t.value.focus()},blur:()=>{t.value&&t.value.blur()},input:t,setSelectionRange:(l,c,d)=>{var v;(v=t.value)===null||v===void 0||v.setSelectionRange(l,c,d)},select:()=>{var l;(l=t.value)===null||l===void 0||l.select()},getSelectionStart:()=>{var l;return(l=t.value)===null||l===void 0?void 0:l.selectionStart},getSelectionEnd:()=>{var l;return(l=t.value)===null||l===void 0?void 0:l.selectionEnd},getScrollTop:()=>{var l;return(l=t.value)===null||l===void 0?void 0:l.scrollTop}}),()=>{const{tag:l,value:c}=n,d=z(n,["tag","value"]);return h(l,f(f({},d),{},{ref:t,value:c}),null)}}});function $(n){const u=n.getBoundingClientRect(),o=document.documentElement;return{left:u.left+(window.scrollX||o.scrollLeft)-(o.clientLeft||document.body.clientLeft||0),top:u.top+(window.scrollY||o.scrollTop)-(o.clientTop||document.body.clientTop||0)}}function M(n){return Object.keys(n).reduce((u,o)=>{const t=n[o];return typeof t=="undefined"||t===null||(u+=`${o}: ${n[o]};`),u},"")}var L=function(n,u){var o={};for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&u.indexOf(t)<0&&(o[t]=n[t]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,t=Object.getOwnPropertySymbols(n);a<t.length;a++)u.indexOf(t[a])<0&&Object.prototype.propertyIsEnumerable.call(n,t[a])&&(o[t[a]]=n[t[a]]);return o};const Y=O({compatConfig:{MODE:3},inheritAttrs:!1,props:{disabled:s.looseBool,type:s.string,value:s.any,lazy:s.bool.def(!0),tag:{type:String,default:"input"},size:s.string,style:s.oneOfType([String,Object]),class:s.string},emits:["change","input","blur","keydown","focus","compositionstart","compositionend","keyup","paste","mousedown"],setup(n,u){let{emit:o,attrs:t,expose:a}=u;const r=w(null),y=b(),i=b(!1);k([()=>n.value,i],()=>{i.value||(y.value=n.value)},{immediate:!0});const l=e=>{o("change",e)},c=e=>{i.value=!0,e.target.composing=!0,o("compositionstart",e)},d=e=>{i.value=!1,e.target.composing=!1,o("compositionend",e);const p=document.createEvent("HTMLEvents");p.initEvent("input",!0,!0),e.target.dispatchEvent(p),l(e)},v=e=>{if(i.value&&n.lazy){y.value=e.target.value;return}o("input",e)},_=e=>{o("blur",e)},C=e=>{o("focus",e)},E=()=>{r.value&&r.value.focus()},P=()=>{r.value&&r.value.blur()},F=e=>{o("keydown",e)},j=e=>{o("keyup",e)},T=(e,p,g)=>{var m;(m=r.value)===null||m===void 0||m.setSelectionRange(e,p,g)},x=()=>{var e;(e=r.value)===null||e===void 0||e.select()};a({focus:E,blur:P,input:S(()=>{var e;return(e=r.value)===null||e===void 0?void 0:e.input}),setSelectionRange:T,select:x,getSelectionStart:()=>{var e;return(e=r.value)===null||e===void 0?void 0:e.getSelectionStart()},getSelectionEnd:()=>{var e;return(e=r.value)===null||e===void 0?void 0:e.getSelectionEnd()},getScrollTop:()=>{var e;return(e=r.value)===null||e===void 0?void 0:e.getScrollTop()}});const B=e=>{o("mousedown",e)},R=e=>{o("paste",e)},I=S(()=>n.style&&typeof n.style!="string"?M(n.style):n.style);return()=>{const{style:e,lazy:p}=n,g=L(n,["style","lazy"]);return h(K,f(f(f({},g),t),{},{style:I.value,onInput:v,onChange:l,onBlur:_,onFocus:C,ref:r,value:y.value,onCompositionstart:c,onCompositionend:d,onKeyup:j,onKeydown:F,onPaste:R,onMousedown:B}),null)}}});export{Y as B,$ as g};
