const i=o=>({[o.componentCls]:{[`${o.antCls}-motion-collapse-legacy`]:{overflow:"hidden","&-active":{transition:`height ${o.motionDurationMid} ${o.motionEaseInOut},
        opacity ${o.motionDurationMid} ${o.motionEaseInOut} !important`}},[`${o.antCls}-motion-collapse`]:{overflow:"hidden",transition:`height ${o.motionDurationMid} ${o.motionEaseInOut},
        opacity ${o.motionDurationMid} ${o.motionEaseInOut} !important`}}});export{i as g};
