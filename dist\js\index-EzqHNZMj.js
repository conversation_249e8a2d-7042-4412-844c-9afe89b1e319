import"./bootstrap-CFDAkNgp.js";import{_ as l,a as i,S as c,b as _,c as r,d as n}from"./analysis-overview.vue_vue_type_script_setup_true_lang-CtUzIjiz.js";import{_ as d}from"./analytics-trends.vue_vue_type_script_setup_true_lang-DM6R_TTc.js";import{_ as f}from"./analytics-visits-data.vue_vue_type_script_setup_true_lang-De2clNSs.js";import{_ as p}from"./analytics-visits-sales.vue_vue_type_script_setup_true_lang-CLNls_jS.js";import{_ as u}from"./analytics-visits-source.vue_vue_type_script_setup_true_lang-m6x3zL2i.js";import{_ as v}from"./analytics-visits.vue_vue_type_script_setup_true_lang-BkTwID9K.js";import{_ as s}from"./analysis-chart-card.vue_vue_type_script_setup_true_lang-DyyU6rkg.js";import{a4 as $,av as w,ab as b,x as t,aB as x,a7 as a,ac as e}from"../jse/index-index-B2UBupFX.js";import"./index-DpxZFE0y.js";import"./TabsList.vue_vue_type_script_setup_true_lang-DM4no0zC.js";import"./CardTitle.vue_vue_type_script_setup_true_lang-DM2nXXp9.js";import"./use-echarts-Dor8gHz9.js";const V={class:"p-5"},h={class:"mt-5 w-full md:flex"},G=$({__name:"index",setup(B){const o=[{icon:c,title:"用户量",totalTitle:"总用户量",totalValue:12e4,value:2e3},{icon:_,title:"访问量",totalTitle:"总访问量",totalValue:5e5,value:2e4},{icon:r,title:"下载量",totalTitle:"总下载量",totalValue:12e4,value:8e3},{icon:n,title:"使用量",totalTitle:"总使用量",totalValue:5e4,value:5e3}],m=[{label:"流量趋势",value:"trends"},{label:"月访问量",value:"visits"}];return(I,S)=>(b(),w("div",V,[t(a(l),{items:o}),t(a(i),{tabs:m,class:"mt-5"},{trends:e(()=>[t(d)]),visits:e(()=>[t(v)]),_:1}),x("div",h,[t(a(s),{class:"mt-5 md:mr-4 md:mt-0 md:w-1/3",title:"访问数量"},{default:e(()=>[t(f)]),_:1}),t(a(s),{class:"mt-5 md:mr-4 md:mt-0 md:w-1/3",title:"访问来源"},{default:e(()=>[t(u)]),_:1}),t(a(s),{class:"mt-5 md:mt-0 md:w-1/3",title:"访问来源"},{default:e(()=>[t(p)]),_:1})])]))}});export{G as default};
