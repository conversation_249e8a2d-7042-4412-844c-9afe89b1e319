{"version": 3, "sources": ["../../../../../node_modules/.pnpm/@vueuse+shared@13.1.0_vue@3.5.17_typescript@5.8.3_/node_modules/@vueuse/shared/index.mjs", "../../../../../node_modules/.pnpm/framesync@6.1.2/node_modules/framesync/dist/es/on-next-frame.mjs", "../../../../../node_modules/.pnpm/framesync@6.1.2/node_modules/framesync/dist/es/create-render-step.mjs", "../../../../../node_modules/.pnpm/framesync@6.1.2/node_modules/framesync/dist/es/index.mjs", "../../../../../node_modules/.pnpm/tslib@2.4.0/node_modules/tslib/tslib.es6.js", "../../../../../node_modules/.pnpm/hey-listen@1.0.8/node_modules/hey-listen/dist/hey-listen.es.js", "../../../../../node_modules/.pnpm/popmotion@11.0.5/node_modules/popmotion/dist/es/utils/clamp.mjs", "../../../../../node_modules/.pnpm/popmotion@11.0.5/node_modules/popmotion/dist/es/animations/utils/find-spring.mjs", "../../../../../node_modules/.pnpm/popmotion@11.0.5/node_modules/popmotion/dist/es/animations/generators/spring.mjs", "../../../../../node_modules/.pnpm/popmotion@11.0.5/node_modules/popmotion/dist/es/utils/progress.mjs", "../../../../../node_modules/.pnpm/popmotion@11.0.5/node_modules/popmotion/dist/es/utils/mix.mjs", "../../../../../node_modules/.pnpm/style-value-types@5.1.2/node_modules/style-value-types/dist/es/utils.mjs", "../../../../../node_modules/.pnpm/style-value-types@5.1.2/node_modules/style-value-types/dist/es/numbers/index.mjs", "../../../../../node_modules/.pnpm/style-value-types@5.1.2/node_modules/style-value-types/dist/es/numbers/units.mjs", "../../../../../node_modules/.pnpm/style-value-types@5.1.2/node_modules/style-value-types/dist/es/color/utils.mjs", "../../../../../node_modules/.pnpm/style-value-types@5.1.2/node_modules/style-value-types/dist/es/color/hsla.mjs", "../../../../../node_modules/.pnpm/style-value-types@5.1.2/node_modules/style-value-types/dist/es/color/rgba.mjs", "../../../../../node_modules/.pnpm/style-value-types@5.1.2/node_modules/style-value-types/dist/es/color/hex.mjs", "../../../../../node_modules/.pnpm/style-value-types@5.1.2/node_modules/style-value-types/dist/es/color/index.mjs", "../../../../../node_modules/.pnpm/style-value-types@5.1.2/node_modules/style-value-types/dist/es/complex/index.mjs", "../../../../../node_modules/.pnpm/style-value-types@5.1.2/node_modules/style-value-types/dist/es/complex/filter.mjs", "../../../../../node_modules/.pnpm/popmotion@11.0.5/node_modules/popmotion/dist/es/utils/hsla-to-rgba.mjs", "../../../../../node_modules/.pnpm/popmotion@11.0.5/node_modules/popmotion/dist/es/utils/mix-color.mjs", "../../../../../node_modules/.pnpm/popmotion@11.0.5/node_modules/popmotion/dist/es/utils/inc.mjs", "../../../../../node_modules/.pnpm/popmotion@11.0.5/node_modules/popmotion/dist/es/utils/pipe.mjs", "../../../../../node_modules/.pnpm/popmotion@11.0.5/node_modules/popmotion/dist/es/utils/mix-complex.mjs", "../../../../../node_modules/.pnpm/popmotion@11.0.5/node_modules/popmotion/dist/es/utils/interpolate.mjs", "../../../../../node_modules/.pnpm/popmotion@11.0.5/node_modules/popmotion/dist/es/easing/utils.mjs", "../../../../../node_modules/.pnpm/popmotion@11.0.5/node_modules/popmotion/dist/es/easing/index.mjs", "../../../../../node_modules/.pnpm/popmotion@11.0.5/node_modules/popmotion/dist/es/animations/generators/keyframes.mjs", "../../../../../node_modules/.pnpm/popmotion@11.0.5/node_modules/popmotion/dist/es/animations/generators/decay.mjs", "../../../../../node_modules/.pnpm/popmotion@11.0.5/node_modules/popmotion/dist/es/animations/utils/detect-animation-from-options.mjs", "../../../../../node_modules/.pnpm/popmotion@11.0.5/node_modules/popmotion/dist/es/animations/utils/elapsed.mjs", "../../../../../node_modules/.pnpm/popmotion@11.0.5/node_modules/popmotion/dist/es/animations/index.mjs", "../../../../../node_modules/.pnpm/popmotion@11.0.5/node_modules/popmotion/dist/es/utils/velocity-per-second.mjs", "../../../../../node_modules/.pnpm/popmotion@11.0.5/node_modules/popmotion/dist/es/animations/inertia.mjs", "../../../../../node_modules/.pnpm/popmotion@11.0.5/node_modules/popmotion/dist/es/utils/attract.mjs", "../../../../../node_modules/.pnpm/popmotion@11.0.5/node_modules/popmotion/dist/es/easing/cubic-bezier.mjs", "../../../../../node_modules/.pnpm/@vueuse+motion@3.0.3_magica_14bff79617cd63b7e33833d2b7c01092/node_modules/@vueuse/motion/dist/index.mjs"], "sourcesContent": ["import { shallowRef, watchEffect, readonly, watch, customRef, getCurrentScope, onScopeDispose, effectScope, getCurrentInstance, hasInjectionContext, inject, provide, ref, isRef, unref, toValue as toValue$1, computed, reactive, toRefs as toRefs$1, toRef as toRef$1, shallowReadonly, onBeforeMount, nextTick, onBeforeUnmount, onMounted, onUnmounted, isReactive } from 'vue';\n\nfunction computedEager(fn, options) {\n  var _a;\n  const result = shallowRef();\n  watchEffect(() => {\n    result.value = fn();\n  }, {\n    ...options,\n    flush: (_a = options == null ? void 0 : options.flush) != null ? _a : \"sync\"\n  });\n  return readonly(result);\n}\n\nfunction computedWithControl(source, fn) {\n  let v = void 0;\n  let track;\n  let trigger;\n  const dirty = shallowRef(true);\n  const update = () => {\n    dirty.value = true;\n    trigger();\n  };\n  watch(source, update, { flush: \"sync\" });\n  const get = typeof fn === \"function\" ? fn : fn.get;\n  const set = typeof fn === \"function\" ? void 0 : fn.set;\n  const result = customRef((_track, _trigger) => {\n    track = _track;\n    trigger = _trigger;\n    return {\n      get() {\n        if (dirty.value) {\n          v = get(v);\n          dirty.value = false;\n        }\n        track();\n        return v;\n      },\n      set(v2) {\n        set == null ? void 0 : set(v2);\n      }\n    };\n  });\n  if (Object.isExtensible(result))\n    result.trigger = update;\n  return result;\n}\n\nfunction tryOnScopeDispose(fn) {\n  if (getCurrentScope()) {\n    onScopeDispose(fn);\n    return true;\n  }\n  return false;\n}\n\nfunction createEventHook() {\n  const fns = /* @__PURE__ */ new Set();\n  const off = (fn) => {\n    fns.delete(fn);\n  };\n  const clear = () => {\n    fns.clear();\n  };\n  const on = (fn) => {\n    fns.add(fn);\n    const offFn = () => off(fn);\n    tryOnScopeDispose(offFn);\n    return {\n      off: offFn\n    };\n  };\n  const trigger = (...args) => {\n    return Promise.all(Array.from(fns).map((fn) => fn(...args)));\n  };\n  return {\n    on,\n    off,\n    trigger,\n    clear\n  };\n}\n\nfunction createGlobalState(stateFactory) {\n  let initialized = false;\n  let state;\n  const scope = effectScope(true);\n  return (...args) => {\n    if (!initialized) {\n      state = scope.run(() => stateFactory(...args));\n      initialized = true;\n    }\n    return state;\n  };\n}\n\nconst localProvidedStateMap = /* @__PURE__ */ new WeakMap();\n\nconst injectLocal = (...args) => {\n  var _a;\n  const key = args[0];\n  const instance = (_a = getCurrentInstance()) == null ? void 0 : _a.proxy;\n  if (instance == null && !hasInjectionContext())\n    throw new Error(\"injectLocal must be called in setup\");\n  if (instance && localProvidedStateMap.has(instance) && key in localProvidedStateMap.get(instance))\n    return localProvidedStateMap.get(instance)[key];\n  return inject(...args);\n};\n\nfunction provideLocal(key, value) {\n  var _a;\n  const instance = (_a = getCurrentInstance()) == null ? void 0 : _a.proxy;\n  if (instance == null)\n    throw new Error(\"provideLocal must be called in setup\");\n  if (!localProvidedStateMap.has(instance))\n    localProvidedStateMap.set(instance, /* @__PURE__ */ Object.create(null));\n  const localProvidedState = localProvidedStateMap.get(instance);\n  localProvidedState[key] = value;\n  return provide(key, value);\n}\n\nfunction createInjectionState(composable, options) {\n  const key = (options == null ? void 0 : options.injectionKey) || Symbol(composable.name || \"InjectionState\");\n  const defaultValue = options == null ? void 0 : options.defaultValue;\n  const useProvidingState = (...args) => {\n    const state = composable(...args);\n    provideLocal(key, state);\n    return state;\n  };\n  const useInjectedState = () => injectLocal(key, defaultValue);\n  return [useProvidingState, useInjectedState];\n}\n\nfunction createRef(value, deep) {\n  if (deep === true) {\n    return ref(value);\n  } else {\n    return shallowRef(value);\n  }\n}\n\nfunction createSharedComposable(composable) {\n  let subscribers = 0;\n  let state;\n  let scope;\n  const dispose = () => {\n    subscribers -= 1;\n    if (scope && subscribers <= 0) {\n      scope.stop();\n      state = void 0;\n      scope = void 0;\n    }\n  };\n  return (...args) => {\n    subscribers += 1;\n    if (!scope) {\n      scope = effectScope(true);\n      state = scope.run(() => composable(...args));\n    }\n    tryOnScopeDispose(dispose);\n    return state;\n  };\n}\n\nfunction extendRef(ref, extend, { enumerable = false, unwrap = true } = {}) {\n  for (const [key, value] of Object.entries(extend)) {\n    if (key === \"value\")\n      continue;\n    if (isRef(value) && unwrap) {\n      Object.defineProperty(ref, key, {\n        get() {\n          return value.value;\n        },\n        set(v) {\n          value.value = v;\n        },\n        enumerable\n      });\n    } else {\n      Object.defineProperty(ref, key, { value, enumerable });\n    }\n  }\n  return ref;\n}\n\nfunction get(obj, key) {\n  if (key == null)\n    return unref(obj);\n  return unref(obj)[key];\n}\n\nfunction isDefined(v) {\n  return unref(v) != null;\n}\n\nfunction makeDestructurable(obj, arr) {\n  if (typeof Symbol !== \"undefined\") {\n    const clone = { ...obj };\n    Object.defineProperty(clone, Symbol.iterator, {\n      enumerable: false,\n      value() {\n        let index = 0;\n        return {\n          next: () => ({\n            value: arr[index++],\n            done: index > arr.length\n          })\n        };\n      }\n    });\n    return clone;\n  } else {\n    return Object.assign([...arr], obj);\n  }\n}\n\nfunction reactify(fn, options) {\n  const unrefFn = (options == null ? void 0 : options.computedGetter) === false ? unref : toValue$1;\n  return function(...args) {\n    return computed(() => fn.apply(this, args.map((i) => unrefFn(i))));\n  };\n}\n\nfunction reactifyObject(obj, optionsOrKeys = {}) {\n  let keys = [];\n  let options;\n  if (Array.isArray(optionsOrKeys)) {\n    keys = optionsOrKeys;\n  } else {\n    options = optionsOrKeys;\n    const { includeOwnProperties = true } = optionsOrKeys;\n    keys.push(...Object.keys(obj));\n    if (includeOwnProperties)\n      keys.push(...Object.getOwnPropertyNames(obj));\n  }\n  return Object.fromEntries(\n    keys.map((key) => {\n      const value = obj[key];\n      return [\n        key,\n        typeof value === \"function\" ? reactify(value.bind(obj), options) : value\n      ];\n    })\n  );\n}\n\nfunction toReactive(objectRef) {\n  if (!isRef(objectRef))\n    return reactive(objectRef);\n  const proxy = new Proxy({}, {\n    get(_, p, receiver) {\n      return unref(Reflect.get(objectRef.value, p, receiver));\n    },\n    set(_, p, value) {\n      if (isRef(objectRef.value[p]) && !isRef(value))\n        objectRef.value[p].value = value;\n      else\n        objectRef.value[p] = value;\n      return true;\n    },\n    deleteProperty(_, p) {\n      return Reflect.deleteProperty(objectRef.value, p);\n    },\n    has(_, p) {\n      return Reflect.has(objectRef.value, p);\n    },\n    ownKeys() {\n      return Object.keys(objectRef.value);\n    },\n    getOwnPropertyDescriptor() {\n      return {\n        enumerable: true,\n        configurable: true\n      };\n    }\n  });\n  return reactive(proxy);\n}\n\nfunction reactiveComputed(fn) {\n  return toReactive(computed(fn));\n}\n\nfunction reactiveOmit(obj, ...keys) {\n  const flatKeys = keys.flat();\n  const predicate = flatKeys[0];\n  return reactiveComputed(() => typeof predicate === \"function\" ? Object.fromEntries(Object.entries(toRefs$1(obj)).filter(([k, v]) => !predicate(toValue$1(v), k))) : Object.fromEntries(Object.entries(toRefs$1(obj)).filter((e) => !flatKeys.includes(e[0]))));\n}\n\nconst isClient = typeof window !== \"undefined\" && typeof document !== \"undefined\";\nconst isWorker = typeof WorkerGlobalScope !== \"undefined\" && globalThis instanceof WorkerGlobalScope;\nconst isDef = (val) => typeof val !== \"undefined\";\nconst notNullish = (val) => val != null;\nconst assert = (condition, ...infos) => {\n  if (!condition)\n    console.warn(...infos);\n};\nconst toString = Object.prototype.toString;\nconst isObject = (val) => toString.call(val) === \"[object Object]\";\nconst now = () => Date.now();\nconst timestamp = () => +Date.now();\nconst clamp = (n, min, max) => Math.min(max, Math.max(min, n));\nconst noop = () => {\n};\nconst rand = (min, max) => {\n  min = Math.ceil(min);\n  max = Math.floor(max);\n  return Math.floor(Math.random() * (max - min + 1)) + min;\n};\nconst hasOwn = (val, key) => Object.prototype.hasOwnProperty.call(val, key);\nconst isIOS = /* @__PURE__ */ getIsIOS();\nfunction getIsIOS() {\n  var _a, _b;\n  return isClient && ((_a = window == null ? void 0 : window.navigator) == null ? void 0 : _a.userAgent) && (/iP(?:ad|hone|od)/.test(window.navigator.userAgent) || ((_b = window == null ? void 0 : window.navigator) == null ? void 0 : _b.maxTouchPoints) > 2 && /iPad|Macintosh/.test(window == null ? void 0 : window.navigator.userAgent));\n}\n\nfunction toRef(...args) {\n  if (args.length !== 1)\n    return toRef$1(...args);\n  const r = args[0];\n  return typeof r === \"function\" ? readonly(customRef(() => ({ get: r, set: noop }))) : ref(r);\n}\nconst resolveRef = toRef;\n\nfunction reactivePick(obj, ...keys) {\n  const flatKeys = keys.flat();\n  const predicate = flatKeys[0];\n  return reactiveComputed(() => typeof predicate === \"function\" ? Object.fromEntries(Object.entries(toRefs$1(obj)).filter(([k, v]) => predicate(toValue$1(v), k))) : Object.fromEntries(flatKeys.map((k) => [k, toRef(obj, k)])));\n}\n\nfunction refAutoReset(defaultValue, afterMs = 1e4) {\n  return customRef((track, trigger) => {\n    let value = toValue$1(defaultValue);\n    let timer;\n    const resetAfter = () => setTimeout(() => {\n      value = toValue$1(defaultValue);\n      trigger();\n    }, toValue$1(afterMs));\n    tryOnScopeDispose(() => {\n      clearTimeout(timer);\n    });\n    return {\n      get() {\n        track();\n        return value;\n      },\n      set(newValue) {\n        value = newValue;\n        trigger();\n        clearTimeout(timer);\n        timer = resetAfter();\n      }\n    };\n  });\n}\n\nfunction createFilterWrapper(filter, fn) {\n  function wrapper(...args) {\n    return new Promise((resolve, reject) => {\n      Promise.resolve(filter(() => fn.apply(this, args), { fn, thisArg: this, args })).then(resolve).catch(reject);\n    });\n  }\n  return wrapper;\n}\nconst bypassFilter = (invoke) => {\n  return invoke();\n};\nfunction debounceFilter(ms, options = {}) {\n  let timer;\n  let maxTimer;\n  let lastRejector = noop;\n  const _clearTimeout = (timer2) => {\n    clearTimeout(timer2);\n    lastRejector();\n    lastRejector = noop;\n  };\n  let lastInvoker;\n  const filter = (invoke) => {\n    const duration = toValue$1(ms);\n    const maxDuration = toValue$1(options.maxWait);\n    if (timer)\n      _clearTimeout(timer);\n    if (duration <= 0 || maxDuration !== void 0 && maxDuration <= 0) {\n      if (maxTimer) {\n        _clearTimeout(maxTimer);\n        maxTimer = null;\n      }\n      return Promise.resolve(invoke());\n    }\n    return new Promise((resolve, reject) => {\n      lastRejector = options.rejectOnCancel ? reject : resolve;\n      lastInvoker = invoke;\n      if (maxDuration && !maxTimer) {\n        maxTimer = setTimeout(() => {\n          if (timer)\n            _clearTimeout(timer);\n          maxTimer = null;\n          resolve(lastInvoker());\n        }, maxDuration);\n      }\n      timer = setTimeout(() => {\n        if (maxTimer)\n          _clearTimeout(maxTimer);\n        maxTimer = null;\n        resolve(invoke());\n      }, duration);\n    });\n  };\n  return filter;\n}\nfunction throttleFilter(...args) {\n  let lastExec = 0;\n  let timer;\n  let isLeading = true;\n  let lastRejector = noop;\n  let lastValue;\n  let ms;\n  let trailing;\n  let leading;\n  let rejectOnCancel;\n  if (!isRef(args[0]) && typeof args[0] === \"object\")\n    ({ delay: ms, trailing = true, leading = true, rejectOnCancel = false } = args[0]);\n  else\n    [ms, trailing = true, leading = true, rejectOnCancel = false] = args;\n  const clear = () => {\n    if (timer) {\n      clearTimeout(timer);\n      timer = void 0;\n      lastRejector();\n      lastRejector = noop;\n    }\n  };\n  const filter = (_invoke) => {\n    const duration = toValue$1(ms);\n    const elapsed = Date.now() - lastExec;\n    const invoke = () => {\n      return lastValue = _invoke();\n    };\n    clear();\n    if (duration <= 0) {\n      lastExec = Date.now();\n      return invoke();\n    }\n    if (elapsed > duration && (leading || !isLeading)) {\n      lastExec = Date.now();\n      invoke();\n    } else if (trailing) {\n      lastValue = new Promise((resolve, reject) => {\n        lastRejector = rejectOnCancel ? reject : resolve;\n        timer = setTimeout(() => {\n          lastExec = Date.now();\n          isLeading = true;\n          resolve(invoke());\n          clear();\n        }, Math.max(0, duration - elapsed));\n      });\n    }\n    if (!leading && !timer)\n      timer = setTimeout(() => isLeading = true, duration);\n    isLeading = false;\n    return lastValue;\n  };\n  return filter;\n}\nfunction pausableFilter(extendFilter = bypassFilter, options = {}) {\n  const {\n    initialState = \"active\"\n  } = options;\n  const isActive = toRef(initialState === \"active\");\n  function pause() {\n    isActive.value = false;\n  }\n  function resume() {\n    isActive.value = true;\n  }\n  const eventFilter = (...args) => {\n    if (isActive.value)\n      extendFilter(...args);\n  };\n  return { isActive: readonly(isActive), pause, resume, eventFilter };\n}\n\nfunction promiseTimeout(ms, throwOnTimeout = false, reason = \"Timeout\") {\n  return new Promise((resolve, reject) => {\n    if (throwOnTimeout)\n      setTimeout(() => reject(reason), ms);\n    else\n      setTimeout(resolve, ms);\n  });\n}\nfunction identity(arg) {\n  return arg;\n}\nfunction createSingletonPromise(fn) {\n  let _promise;\n  function wrapper() {\n    if (!_promise)\n      _promise = fn();\n    return _promise;\n  }\n  wrapper.reset = async () => {\n    const _prev = _promise;\n    _promise = void 0;\n    if (_prev)\n      await _prev;\n  };\n  return wrapper;\n}\nfunction invoke(fn) {\n  return fn();\n}\nfunction containsProp(obj, ...props) {\n  return props.some((k) => k in obj);\n}\nfunction increaseWithUnit(target, delta) {\n  var _a;\n  if (typeof target === \"number\")\n    return target + delta;\n  const value = ((_a = target.match(/^-?\\d+\\.?\\d*/)) == null ? void 0 : _a[0]) || \"\";\n  const unit = target.slice(value.length);\n  const result = Number.parseFloat(value) + delta;\n  if (Number.isNaN(result))\n    return target;\n  return result + unit;\n}\nfunction pxValue(px) {\n  return px.endsWith(\"rem\") ? Number.parseFloat(px) * 16 : Number.parseFloat(px);\n}\nfunction objectPick(obj, keys, omitUndefined = false) {\n  return keys.reduce((n, k) => {\n    if (k in obj) {\n      if (!omitUndefined || obj[k] !== void 0)\n        n[k] = obj[k];\n    }\n    return n;\n  }, {});\n}\nfunction objectOmit(obj, keys, omitUndefined = false) {\n  return Object.fromEntries(Object.entries(obj).filter(([key, value]) => {\n    return (!omitUndefined || value !== void 0) && !keys.includes(key);\n  }));\n}\nfunction objectEntries(obj) {\n  return Object.entries(obj);\n}\nfunction toArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\n\nfunction cacheStringFunction(fn) {\n  const cache = /* @__PURE__ */ Object.create(null);\n  return (str) => {\n    const hit = cache[str];\n    return hit || (cache[str] = fn(str));\n  };\n}\nconst hyphenateRE = /\\B([A-Z])/g;\nconst hyphenate = cacheStringFunction((str) => str.replace(hyphenateRE, \"-$1\").toLowerCase());\nconst camelizeRE = /-(\\w)/g;\nconst camelize = cacheStringFunction((str) => {\n  return str.replace(camelizeRE, (_, c) => c ? c.toUpperCase() : \"\");\n});\n\nfunction getLifeCycleTarget(target) {\n  return target || getCurrentInstance();\n}\n\nfunction useDebounceFn(fn, ms = 200, options = {}) {\n  return createFilterWrapper(\n    debounceFilter(ms, options),\n    fn\n  );\n}\n\nfunction refDebounced(value, ms = 200, options = {}) {\n  const debounced = ref(toValue$1(value));\n  const updater = useDebounceFn(() => {\n    debounced.value = value.value;\n  }, ms, options);\n  watch(value, () => updater());\n  return shallowReadonly(debounced);\n}\n\nfunction refDefault(source, defaultValue) {\n  return computed({\n    get() {\n      var _a;\n      return (_a = source.value) != null ? _a : defaultValue;\n    },\n    set(value) {\n      source.value = value;\n    }\n  });\n}\n\nfunction useThrottleFn(fn, ms = 200, trailing = false, leading = true, rejectOnCancel = false) {\n  return createFilterWrapper(\n    throttleFilter(ms, trailing, leading, rejectOnCancel),\n    fn\n  );\n}\n\nfunction refThrottled(value, delay = 200, trailing = true, leading = true) {\n  if (delay <= 0)\n    return value;\n  const throttled = ref(toValue$1(value));\n  const updater = useThrottleFn(() => {\n    throttled.value = value.value;\n  }, delay, trailing, leading);\n  watch(value, () => updater());\n  return throttled;\n}\n\nfunction refWithControl(initial, options = {}) {\n  let source = initial;\n  let track;\n  let trigger;\n  const ref = customRef((_track, _trigger) => {\n    track = _track;\n    trigger = _trigger;\n    return {\n      get() {\n        return get();\n      },\n      set(v) {\n        set(v);\n      }\n    };\n  });\n  function get(tracking = true) {\n    if (tracking)\n      track();\n    return source;\n  }\n  function set(value, triggering = true) {\n    var _a, _b;\n    if (value === source)\n      return;\n    const old = source;\n    if (((_a = options.onBeforeChange) == null ? void 0 : _a.call(options, value, old)) === false)\n      return;\n    source = value;\n    (_b = options.onChanged) == null ? void 0 : _b.call(options, value, old);\n    if (triggering)\n      trigger();\n  }\n  const untrackedGet = () => get(false);\n  const silentSet = (v) => set(v, false);\n  const peek = () => get(false);\n  const lay = (v) => set(v, false);\n  return extendRef(\n    ref,\n    {\n      get,\n      set,\n      untrackedGet,\n      silentSet,\n      peek,\n      lay\n    },\n    { enumerable: true }\n  );\n}\nconst controlledRef = refWithControl;\n\nfunction set(...args) {\n  if (args.length === 2) {\n    const [ref, value] = args;\n    ref.value = value;\n  }\n  if (args.length === 3) {\n    const [target, key, value] = args;\n    target[key] = value;\n  }\n}\n\nfunction watchWithFilter(source, cb, options = {}) {\n  const {\n    eventFilter = bypassFilter,\n    ...watchOptions\n  } = options;\n  return watch(\n    source,\n    createFilterWrapper(\n      eventFilter,\n      cb\n    ),\n    watchOptions\n  );\n}\n\nfunction watchPausable(source, cb, options = {}) {\n  const {\n    eventFilter: filter,\n    initialState = \"active\",\n    ...watchOptions\n  } = options;\n  const { eventFilter, pause, resume, isActive } = pausableFilter(filter, { initialState });\n  const stop = watchWithFilter(\n    source,\n    cb,\n    {\n      ...watchOptions,\n      eventFilter\n    }\n  );\n  return { stop, pause, resume, isActive };\n}\n\nfunction syncRef(left, right, ...[options]) {\n  const {\n    flush = \"sync\",\n    deep = false,\n    immediate = true,\n    direction = \"both\",\n    transform = {}\n  } = options || {};\n  const watchers = [];\n  const transformLTR = \"ltr\" in transform && transform.ltr || ((v) => v);\n  const transformRTL = \"rtl\" in transform && transform.rtl || ((v) => v);\n  if (direction === \"both\" || direction === \"ltr\") {\n    watchers.push(watchPausable(\n      left,\n      (newValue) => {\n        watchers.forEach((w) => w.pause());\n        right.value = transformLTR(newValue);\n        watchers.forEach((w) => w.resume());\n      },\n      { flush, deep, immediate }\n    ));\n  }\n  if (direction === \"both\" || direction === \"rtl\") {\n    watchers.push(watchPausable(\n      right,\n      (newValue) => {\n        watchers.forEach((w) => w.pause());\n        left.value = transformRTL(newValue);\n        watchers.forEach((w) => w.resume());\n      },\n      { flush, deep, immediate }\n    ));\n  }\n  const stop = () => {\n    watchers.forEach((w) => w.stop());\n  };\n  return stop;\n}\n\nfunction syncRefs(source, targets, options = {}) {\n  const {\n    flush = \"sync\",\n    deep = false,\n    immediate = true\n  } = options;\n  const targetsArray = toArray(targets);\n  return watch(\n    source,\n    (newValue) => targetsArray.forEach((target) => target.value = newValue),\n    { flush, deep, immediate }\n  );\n}\n\nfunction toRefs(objectRef, options = {}) {\n  if (!isRef(objectRef))\n    return toRefs$1(objectRef);\n  const result = Array.isArray(objectRef.value) ? Array.from({ length: objectRef.value.length }) : {};\n  for (const key in objectRef.value) {\n    result[key] = customRef(() => ({\n      get() {\n        return objectRef.value[key];\n      },\n      set(v) {\n        var _a;\n        const replaceRef = (_a = toValue$1(options.replaceRef)) != null ? _a : true;\n        if (replaceRef) {\n          if (Array.isArray(objectRef.value)) {\n            const copy = [...objectRef.value];\n            copy[key] = v;\n            objectRef.value = copy;\n          } else {\n            const newObject = { ...objectRef.value, [key]: v };\n            Object.setPrototypeOf(newObject, Object.getPrototypeOf(objectRef.value));\n            objectRef.value = newObject;\n          }\n        } else {\n          objectRef.value[key] = v;\n        }\n      }\n    }));\n  }\n  return result;\n}\n\nconst toValue = toValue$1;\nconst resolveUnref = toValue$1;\n\nfunction tryOnBeforeMount(fn, sync = true, target) {\n  const instance = getLifeCycleTarget(target);\n  if (instance)\n    onBeforeMount(fn, target);\n  else if (sync)\n    fn();\n  else\n    nextTick(fn);\n}\n\nfunction tryOnBeforeUnmount(fn, target) {\n  const instance = getLifeCycleTarget(target);\n  if (instance)\n    onBeforeUnmount(fn, target);\n}\n\nfunction tryOnMounted(fn, sync = true, target) {\n  const instance = getLifeCycleTarget(target);\n  if (instance)\n    onMounted(fn, target);\n  else if (sync)\n    fn();\n  else\n    nextTick(fn);\n}\n\nfunction tryOnUnmounted(fn, target) {\n  const instance = getLifeCycleTarget(target);\n  if (instance)\n    onUnmounted(fn, target);\n}\n\nfunction createUntil(r, isNot = false) {\n  function toMatch(condition, { flush = \"sync\", deep = false, timeout, throwOnTimeout } = {}) {\n    let stop = null;\n    const watcher = new Promise((resolve) => {\n      stop = watch(\n        r,\n        (v) => {\n          if (condition(v) !== isNot) {\n            if (stop)\n              stop();\n            else\n              nextTick(() => stop == null ? void 0 : stop());\n            resolve(v);\n          }\n        },\n        {\n          flush,\n          deep,\n          immediate: true\n        }\n      );\n    });\n    const promises = [watcher];\n    if (timeout != null) {\n      promises.push(\n        promiseTimeout(timeout, throwOnTimeout).then(() => toValue$1(r)).finally(() => stop == null ? void 0 : stop())\n      );\n    }\n    return Promise.race(promises);\n  }\n  function toBe(value, options) {\n    if (!isRef(value))\n      return toMatch((v) => v === value, options);\n    const { flush = \"sync\", deep = false, timeout, throwOnTimeout } = options != null ? options : {};\n    let stop = null;\n    const watcher = new Promise((resolve) => {\n      stop = watch(\n        [r, value],\n        ([v1, v2]) => {\n          if (isNot !== (v1 === v2)) {\n            if (stop)\n              stop();\n            else\n              nextTick(() => stop == null ? void 0 : stop());\n            resolve(v1);\n          }\n        },\n        {\n          flush,\n          deep,\n          immediate: true\n        }\n      );\n    });\n    const promises = [watcher];\n    if (timeout != null) {\n      promises.push(\n        promiseTimeout(timeout, throwOnTimeout).then(() => toValue$1(r)).finally(() => {\n          stop == null ? void 0 : stop();\n          return toValue$1(r);\n        })\n      );\n    }\n    return Promise.race(promises);\n  }\n  function toBeTruthy(options) {\n    return toMatch((v) => Boolean(v), options);\n  }\n  function toBeNull(options) {\n    return toBe(null, options);\n  }\n  function toBeUndefined(options) {\n    return toBe(void 0, options);\n  }\n  function toBeNaN(options) {\n    return toMatch(Number.isNaN, options);\n  }\n  function toContains(value, options) {\n    return toMatch((v) => {\n      const array = Array.from(v);\n      return array.includes(value) || array.includes(toValue$1(value));\n    }, options);\n  }\n  function changed(options) {\n    return changedTimes(1, options);\n  }\n  function changedTimes(n = 1, options) {\n    let count = -1;\n    return toMatch(() => {\n      count += 1;\n      return count >= n;\n    }, options);\n  }\n  if (Array.isArray(toValue$1(r))) {\n    const instance = {\n      toMatch,\n      toContains,\n      changed,\n      changedTimes,\n      get not() {\n        return createUntil(r, !isNot);\n      }\n    };\n    return instance;\n  } else {\n    const instance = {\n      toMatch,\n      toBe,\n      toBeTruthy,\n      toBeNull,\n      toBeNaN,\n      toBeUndefined,\n      changed,\n      changedTimes,\n      get not() {\n        return createUntil(r, !isNot);\n      }\n    };\n    return instance;\n  }\n}\nfunction until(r) {\n  return createUntil(r);\n}\n\nfunction defaultComparator(value, othVal) {\n  return value === othVal;\n}\nfunction useArrayDifference(...args) {\n  var _a, _b;\n  const list = args[0];\n  const values = args[1];\n  let compareFn = (_a = args[2]) != null ? _a : defaultComparator;\n  const {\n    symmetric = false\n  } = (_b = args[3]) != null ? _b : {};\n  if (typeof compareFn === \"string\") {\n    const key = compareFn;\n    compareFn = (value, othVal) => value[key] === othVal[key];\n  }\n  const diff1 = computed(() => toValue$1(list).filter((x) => toValue$1(values).findIndex((y) => compareFn(x, y)) === -1));\n  if (symmetric) {\n    const diff2 = computed(() => toValue$1(values).filter((x) => toValue$1(list).findIndex((y) => compareFn(x, y)) === -1));\n    return computed(() => symmetric ? [...toValue$1(diff1), ...toValue$1(diff2)] : toValue$1(diff1));\n  } else {\n    return diff1;\n  }\n}\n\nfunction useArrayEvery(list, fn) {\n  return computed(() => toValue$1(list).every((element, index, array) => fn(toValue$1(element), index, array)));\n}\n\nfunction useArrayFilter(list, fn) {\n  return computed(() => toValue$1(list).map((i) => toValue$1(i)).filter(fn));\n}\n\nfunction useArrayFind(list, fn) {\n  return computed(() => toValue$1(\n    toValue$1(list).find((element, index, array) => fn(toValue$1(element), index, array))\n  ));\n}\n\nfunction useArrayFindIndex(list, fn) {\n  return computed(() => toValue$1(list).findIndex((element, index, array) => fn(toValue$1(element), index, array)));\n}\n\nfunction findLast(arr, cb) {\n  let index = arr.length;\n  while (index-- > 0) {\n    if (cb(arr[index], index, arr))\n      return arr[index];\n  }\n  return void 0;\n}\nfunction useArrayFindLast(list, fn) {\n  return computed(() => toValue$1(\n    !Array.prototype.findLast ? findLast(toValue$1(list), (element, index, array) => fn(toValue$1(element), index, array)) : toValue$1(list).findLast((element, index, array) => fn(toValue$1(element), index, array))\n  ));\n}\n\nfunction isArrayIncludesOptions(obj) {\n  return isObject(obj) && containsProp(obj, \"formIndex\", \"comparator\");\n}\nfunction useArrayIncludes(...args) {\n  var _a;\n  const list = args[0];\n  const value = args[1];\n  let comparator = args[2];\n  let formIndex = 0;\n  if (isArrayIncludesOptions(comparator)) {\n    formIndex = (_a = comparator.fromIndex) != null ? _a : 0;\n    comparator = comparator.comparator;\n  }\n  if (typeof comparator === \"string\") {\n    const key = comparator;\n    comparator = (element, value2) => element[key] === toValue$1(value2);\n  }\n  comparator = comparator != null ? comparator : (element, value2) => element === toValue$1(value2);\n  return computed(() => toValue$1(list).slice(formIndex).some((element, index, array) => comparator(\n    toValue$1(element),\n    toValue$1(value),\n    index,\n    toValue$1(array)\n  )));\n}\n\nfunction useArrayJoin(list, separator) {\n  return computed(() => toValue$1(list).map((i) => toValue$1(i)).join(toValue$1(separator)));\n}\n\nfunction useArrayMap(list, fn) {\n  return computed(() => toValue$1(list).map((i) => toValue$1(i)).map(fn));\n}\n\nfunction useArrayReduce(list, reducer, ...args) {\n  const reduceCallback = (sum, value, index) => reducer(toValue$1(sum), toValue$1(value), index);\n  return computed(() => {\n    const resolved = toValue$1(list);\n    return args.length ? resolved.reduce(reduceCallback, typeof args[0] === \"function\" ? toValue$1(args[0]()) : toValue$1(args[0])) : resolved.reduce(reduceCallback);\n  });\n}\n\nfunction useArraySome(list, fn) {\n  return computed(() => toValue$1(list).some((element, index, array) => fn(toValue$1(element), index, array)));\n}\n\nfunction uniq(array) {\n  return Array.from(new Set(array));\n}\nfunction uniqueElementsBy(array, fn) {\n  return array.reduce((acc, v) => {\n    if (!acc.some((x) => fn(v, x, array)))\n      acc.push(v);\n    return acc;\n  }, []);\n}\nfunction useArrayUnique(list, compareFn) {\n  return computed(() => {\n    const resolvedList = toValue$1(list).map((element) => toValue$1(element));\n    return compareFn ? uniqueElementsBy(resolvedList, compareFn) : uniq(resolvedList);\n  });\n}\n\nfunction useCounter(initialValue = 0, options = {}) {\n  let _initialValue = unref(initialValue);\n  const count = shallowRef(initialValue);\n  const {\n    max = Number.POSITIVE_INFINITY,\n    min = Number.NEGATIVE_INFINITY\n  } = options;\n  const inc = (delta = 1) => count.value = Math.max(Math.min(max, count.value + delta), min);\n  const dec = (delta = 1) => count.value = Math.min(Math.max(min, count.value - delta), max);\n  const get = () => count.value;\n  const set = (val) => count.value = Math.max(min, Math.min(max, val));\n  const reset = (val = _initialValue) => {\n    _initialValue = val;\n    return set(val);\n  };\n  return { count: shallowReadonly(count), inc, dec, get, set, reset };\n}\n\nconst REGEX_PARSE = /^(\\d{4})[-/]?(\\d{1,2})?[-/]?(\\d{0,2})[T\\s]*(\\d{1,2})?:?(\\d{1,2})?:?(\\d{1,2})?[.:]?(\\d+)?$/i;\nconst REGEX_FORMAT = /[YMDHhms]o|\\[([^\\]]+)\\]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a{1,2}|A{1,2}|m{1,2}|s{1,2}|Z{1,2}|z{1,4}|SSS/g;\nfunction defaultMeridiem(hours, minutes, isLowercase, hasPeriod) {\n  let m = hours < 12 ? \"AM\" : \"PM\";\n  if (hasPeriod)\n    m = m.split(\"\").reduce((acc, curr) => acc += `${curr}.`, \"\");\n  return isLowercase ? m.toLowerCase() : m;\n}\nfunction formatOrdinal(num) {\n  const suffixes = [\"th\", \"st\", \"nd\", \"rd\"];\n  const v = num % 100;\n  return num + (suffixes[(v - 20) % 10] || suffixes[v] || suffixes[0]);\n}\nfunction formatDate(date, formatStr, options = {}) {\n  var _a;\n  const years = date.getFullYear();\n  const month = date.getMonth();\n  const days = date.getDate();\n  const hours = date.getHours();\n  const minutes = date.getMinutes();\n  const seconds = date.getSeconds();\n  const milliseconds = date.getMilliseconds();\n  const day = date.getDay();\n  const meridiem = (_a = options.customMeridiem) != null ? _a : defaultMeridiem;\n  const stripTimeZone = (dateString) => {\n    var _a2;\n    return (_a2 = dateString.split(\" \")[1]) != null ? _a2 : \"\";\n  };\n  const matches = {\n    Yo: () => formatOrdinal(years),\n    YY: () => String(years).slice(-2),\n    YYYY: () => years,\n    M: () => month + 1,\n    Mo: () => formatOrdinal(month + 1),\n    MM: () => `${month + 1}`.padStart(2, \"0\"),\n    MMM: () => date.toLocaleDateString(toValue$1(options.locales), { month: \"short\" }),\n    MMMM: () => date.toLocaleDateString(toValue$1(options.locales), { month: \"long\" }),\n    D: () => String(days),\n    Do: () => formatOrdinal(days),\n    DD: () => `${days}`.padStart(2, \"0\"),\n    H: () => String(hours),\n    Ho: () => formatOrdinal(hours),\n    HH: () => `${hours}`.padStart(2, \"0\"),\n    h: () => `${hours % 12 || 12}`.padStart(1, \"0\"),\n    ho: () => formatOrdinal(hours % 12 || 12),\n    hh: () => `${hours % 12 || 12}`.padStart(2, \"0\"),\n    m: () => String(minutes),\n    mo: () => formatOrdinal(minutes),\n    mm: () => `${minutes}`.padStart(2, \"0\"),\n    s: () => String(seconds),\n    so: () => formatOrdinal(seconds),\n    ss: () => `${seconds}`.padStart(2, \"0\"),\n    SSS: () => `${milliseconds}`.padStart(3, \"0\"),\n    d: () => day,\n    dd: () => date.toLocaleDateString(toValue$1(options.locales), { weekday: \"narrow\" }),\n    ddd: () => date.toLocaleDateString(toValue$1(options.locales), { weekday: \"short\" }),\n    dddd: () => date.toLocaleDateString(toValue$1(options.locales), { weekday: \"long\" }),\n    A: () => meridiem(hours, minutes),\n    AA: () => meridiem(hours, minutes, false, true),\n    a: () => meridiem(hours, minutes, true),\n    aa: () => meridiem(hours, minutes, true, true),\n    z: () => stripTimeZone(date.toLocaleDateString(toValue$1(options.locales), { timeZoneName: \"shortOffset\" })),\n    zz: () => stripTimeZone(date.toLocaleDateString(toValue$1(options.locales), { timeZoneName: \"shortOffset\" })),\n    zzz: () => stripTimeZone(date.toLocaleDateString(toValue$1(options.locales), { timeZoneName: \"shortOffset\" })),\n    zzzz: () => stripTimeZone(date.toLocaleDateString(toValue$1(options.locales), { timeZoneName: \"longOffset\" }))\n  };\n  return formatStr.replace(REGEX_FORMAT, (match, $1) => {\n    var _a2, _b;\n    return (_b = $1 != null ? $1 : (_a2 = matches[match]) == null ? void 0 : _a2.call(matches)) != null ? _b : match;\n  });\n}\nfunction normalizeDate(date) {\n  if (date === null)\n    return new Date(Number.NaN);\n  if (date === void 0)\n    return /* @__PURE__ */ new Date();\n  if (date instanceof Date)\n    return new Date(date);\n  if (typeof date === \"string\" && !/Z$/i.test(date)) {\n    const d = date.match(REGEX_PARSE);\n    if (d) {\n      const m = d[2] - 1 || 0;\n      const ms = (d[7] || \"0\").substring(0, 3);\n      return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);\n    }\n  }\n  return new Date(date);\n}\nfunction useDateFormat(date, formatStr = \"HH:mm:ss\", options = {}) {\n  return computed(() => formatDate(normalizeDate(toValue$1(date)), toValue$1(formatStr), options));\n}\n\nfunction useIntervalFn(cb, interval = 1e3, options = {}) {\n  const {\n    immediate = true,\n    immediateCallback = false\n  } = options;\n  let timer = null;\n  const isActive = shallowRef(false);\n  function clean() {\n    if (timer) {\n      clearInterval(timer);\n      timer = null;\n    }\n  }\n  function pause() {\n    isActive.value = false;\n    clean();\n  }\n  function resume() {\n    const intervalValue = toValue$1(interval);\n    if (intervalValue <= 0)\n      return;\n    isActive.value = true;\n    if (immediateCallback)\n      cb();\n    clean();\n    if (isActive.value)\n      timer = setInterval(cb, intervalValue);\n  }\n  if (immediate && isClient)\n    resume();\n  if (isRef(interval) || typeof interval === \"function\") {\n    const stopWatch = watch(interval, () => {\n      if (isActive.value && isClient)\n        resume();\n    });\n    tryOnScopeDispose(stopWatch);\n  }\n  tryOnScopeDispose(pause);\n  return {\n    isActive: shallowReadonly(isActive),\n    pause,\n    resume\n  };\n}\n\nfunction useInterval(interval = 1e3, options = {}) {\n  const {\n    controls: exposeControls = false,\n    immediate = true,\n    callback\n  } = options;\n  const counter = shallowRef(0);\n  const update = () => counter.value += 1;\n  const reset = () => {\n    counter.value = 0;\n  };\n  const controls = useIntervalFn(\n    callback ? () => {\n      update();\n      callback(counter.value);\n    } : update,\n    interval,\n    { immediate }\n  );\n  if (exposeControls) {\n    return {\n      counter: shallowReadonly(counter),\n      reset,\n      ...controls\n    };\n  } else {\n    return shallowReadonly(counter);\n  }\n}\n\nfunction useLastChanged(source, options = {}) {\n  var _a;\n  const ms = shallowRef((_a = options.initialValue) != null ? _a : null);\n  watch(\n    source,\n    () => ms.value = timestamp(),\n    options\n  );\n  return shallowReadonly(ms);\n}\n\nfunction useTimeoutFn(cb, interval, options = {}) {\n  const {\n    immediate = true,\n    immediateCallback = false\n  } = options;\n  const isPending = shallowRef(false);\n  let timer = null;\n  function clear() {\n    if (timer) {\n      clearTimeout(timer);\n      timer = null;\n    }\n  }\n  function stop() {\n    isPending.value = false;\n    clear();\n  }\n  function start(...args) {\n    if (immediateCallback)\n      cb();\n    clear();\n    isPending.value = true;\n    timer = setTimeout(() => {\n      isPending.value = false;\n      timer = null;\n      cb(...args);\n    }, toValue$1(interval));\n  }\n  if (immediate) {\n    isPending.value = true;\n    if (isClient)\n      start();\n  }\n  tryOnScopeDispose(stop);\n  return {\n    isPending: shallowReadonly(isPending),\n    start,\n    stop\n  };\n}\n\nfunction useTimeout(interval = 1e3, options = {}) {\n  const {\n    controls: exposeControls = false,\n    callback\n  } = options;\n  const controls = useTimeoutFn(\n    callback != null ? callback : noop,\n    interval,\n    options\n  );\n  const ready = computed(() => !controls.isPending.value);\n  if (exposeControls) {\n    return {\n      ready,\n      ...controls\n    };\n  } else {\n    return ready;\n  }\n}\n\nfunction useToNumber(value, options = {}) {\n  const {\n    method = \"parseFloat\",\n    radix,\n    nanToZero\n  } = options;\n  return computed(() => {\n    let resolved = toValue$1(value);\n    if (typeof method === \"function\")\n      resolved = method(resolved);\n    else if (typeof resolved === \"string\")\n      resolved = Number[method](resolved, radix);\n    if (nanToZero && Number.isNaN(resolved))\n      resolved = 0;\n    return resolved;\n  });\n}\n\nfunction useToString(value) {\n  return computed(() => `${toValue$1(value)}`);\n}\n\nfunction useToggle(initialValue = false, options = {}) {\n  const {\n    truthyValue = true,\n    falsyValue = false\n  } = options;\n  const valueIsRef = isRef(initialValue);\n  const _value = shallowRef(initialValue);\n  function toggle(value) {\n    if (arguments.length) {\n      _value.value = value;\n      return _value.value;\n    } else {\n      const truthy = toValue$1(truthyValue);\n      _value.value = _value.value === truthy ? toValue$1(falsyValue) : truthy;\n      return _value.value;\n    }\n  }\n  if (valueIsRef)\n    return toggle;\n  else\n    return [_value, toggle];\n}\n\nfunction watchArray(source, cb, options) {\n  let oldList = (options == null ? void 0 : options.immediate) ? [] : [...typeof source === \"function\" ? source() : Array.isArray(source) ? source : toValue$1(source)];\n  return watch(source, (newList, _, onCleanup) => {\n    const oldListRemains = Array.from({ length: oldList.length });\n    const added = [];\n    for (const obj of newList) {\n      let found = false;\n      for (let i = 0; i < oldList.length; i++) {\n        if (!oldListRemains[i] && obj === oldList[i]) {\n          oldListRemains[i] = true;\n          found = true;\n          break;\n        }\n      }\n      if (!found)\n        added.push(obj);\n    }\n    const removed = oldList.filter((_2, i) => !oldListRemains[i]);\n    cb(newList, oldList, added, removed, onCleanup);\n    oldList = [...newList];\n  }, options);\n}\n\nfunction watchAtMost(source, cb, options) {\n  const {\n    count,\n    ...watchOptions\n  } = options;\n  const current = shallowRef(0);\n  const stop = watchWithFilter(\n    source,\n    (...args) => {\n      current.value += 1;\n      if (current.value >= toValue$1(count))\n        nextTick(() => stop());\n      cb(...args);\n    },\n    watchOptions\n  );\n  return { count: current, stop };\n}\n\nfunction watchDebounced(source, cb, options = {}) {\n  const {\n    debounce = 0,\n    maxWait = void 0,\n    ...watchOptions\n  } = options;\n  return watchWithFilter(\n    source,\n    cb,\n    {\n      ...watchOptions,\n      eventFilter: debounceFilter(debounce, { maxWait })\n    }\n  );\n}\n\nfunction watchDeep(source, cb, options) {\n  return watch(\n    source,\n    cb,\n    {\n      ...options,\n      deep: true\n    }\n  );\n}\n\nfunction watchIgnorable(source, cb, options = {}) {\n  const {\n    eventFilter = bypassFilter,\n    ...watchOptions\n  } = options;\n  const filteredCb = createFilterWrapper(\n    eventFilter,\n    cb\n  );\n  let ignoreUpdates;\n  let ignorePrevAsyncUpdates;\n  let stop;\n  if (watchOptions.flush === \"sync\") {\n    const ignore = shallowRef(false);\n    ignorePrevAsyncUpdates = () => {\n    };\n    ignoreUpdates = (updater) => {\n      ignore.value = true;\n      updater();\n      ignore.value = false;\n    };\n    stop = watch(\n      source,\n      (...args) => {\n        if (!ignore.value)\n          filteredCb(...args);\n      },\n      watchOptions\n    );\n  } else {\n    const disposables = [];\n    const ignoreCounter = shallowRef(0);\n    const syncCounter = shallowRef(0);\n    ignorePrevAsyncUpdates = () => {\n      ignoreCounter.value = syncCounter.value;\n    };\n    disposables.push(\n      watch(\n        source,\n        () => {\n          syncCounter.value++;\n        },\n        { ...watchOptions, flush: \"sync\" }\n      )\n    );\n    ignoreUpdates = (updater) => {\n      const syncCounterPrev = syncCounter.value;\n      updater();\n      ignoreCounter.value += syncCounter.value - syncCounterPrev;\n    };\n    disposables.push(\n      watch(\n        source,\n        (...args) => {\n          const ignore = ignoreCounter.value > 0 && ignoreCounter.value === syncCounter.value;\n          ignoreCounter.value = 0;\n          syncCounter.value = 0;\n          if (ignore)\n            return;\n          filteredCb(...args);\n        },\n        watchOptions\n      )\n    );\n    stop = () => {\n      disposables.forEach((fn) => fn());\n    };\n  }\n  return { stop, ignoreUpdates, ignorePrevAsyncUpdates };\n}\n\nfunction watchImmediate(source, cb, options) {\n  return watch(\n    source,\n    cb,\n    {\n      ...options,\n      immediate: true\n    }\n  );\n}\n\nfunction watchOnce(source, cb, options) {\n  const stop = watch(source, (...args) => {\n    nextTick(() => stop());\n    return cb(...args);\n  }, options);\n  return stop;\n}\n\nfunction watchThrottled(source, cb, options = {}) {\n  const {\n    throttle = 0,\n    trailing = true,\n    leading = true,\n    ...watchOptions\n  } = options;\n  return watchWithFilter(\n    source,\n    cb,\n    {\n      ...watchOptions,\n      eventFilter: throttleFilter(throttle, trailing, leading)\n    }\n  );\n}\n\nfunction watchTriggerable(source, cb, options = {}) {\n  let cleanupFn;\n  function onEffect() {\n    if (!cleanupFn)\n      return;\n    const fn = cleanupFn;\n    cleanupFn = void 0;\n    fn();\n  }\n  function onCleanup(callback) {\n    cleanupFn = callback;\n  }\n  const _cb = (value, oldValue) => {\n    onEffect();\n    return cb(value, oldValue, onCleanup);\n  };\n  const res = watchIgnorable(source, _cb, options);\n  const { ignoreUpdates } = res;\n  const trigger = () => {\n    let res2;\n    ignoreUpdates(() => {\n      res2 = _cb(getWatchSources(source), getOldValue(source));\n    });\n    return res2;\n  };\n  return {\n    ...res,\n    trigger\n  };\n}\nfunction getWatchSources(sources) {\n  if (isReactive(sources))\n    return sources;\n  if (Array.isArray(sources))\n    return sources.map((item) => toValue$1(item));\n  return toValue$1(sources);\n}\nfunction getOldValue(source) {\n  return Array.isArray(source) ? source.map(() => void 0) : void 0;\n}\n\nfunction whenever(source, cb, options) {\n  const stop = watch(\n    source,\n    (v, ov, onInvalidate) => {\n      if (v) {\n        if (options == null ? void 0 : options.once)\n          nextTick(() => stop());\n        cb(v, ov, onInvalidate);\n      }\n    },\n    {\n      ...options,\n      once: false\n    }\n  );\n  return stop;\n}\n\nexport { assert, refAutoReset as autoResetRef, bypassFilter, camelize, clamp, computedEager, computedWithControl, containsProp, computedWithControl as controlledComputed, controlledRef, createEventHook, createFilterWrapper, createGlobalState, createInjectionState, reactify as createReactiveFn, createRef, createSharedComposable, createSingletonPromise, debounceFilter, refDebounced as debouncedRef, watchDebounced as debouncedWatch, computedEager as eagerComputed, extendRef, formatDate, get, getLifeCycleTarget, hasOwn, hyphenate, identity, watchIgnorable as ignorableWatch, increaseWithUnit, injectLocal, invoke, isClient, isDef, isDefined, isIOS, isObject, isWorker, makeDestructurable, noop, normalizeDate, notNullish, now, objectEntries, objectOmit, objectPick, pausableFilter, watchPausable as pausableWatch, promiseTimeout, provideLocal, pxValue, rand, reactify, reactifyObject, reactiveComputed, reactiveOmit, reactivePick, refAutoReset, refDebounced, refDefault, refThrottled, refWithControl, resolveRef, resolveUnref, set, syncRef, syncRefs, throttleFilter, refThrottled as throttledRef, watchThrottled as throttledWatch, timestamp, toArray, toReactive, toRef, toRefs, toValue, tryOnBeforeMount, tryOnBeforeUnmount, tryOnMounted, tryOnScopeDispose, tryOnUnmounted, until, useArrayDifference, useArrayEvery, useArrayFilter, useArrayFind, useArrayFindIndex, useArrayFindLast, useArrayIncludes, useArrayJoin, useArrayMap, useArrayReduce, useArraySome, useArrayUnique, useCounter, useDateFormat, refDebounced as useDebounce, useDebounceFn, useInterval, useIntervalFn, useLastChanged, refThrottled as useThrottle, useThrottleFn, useTimeout, useTimeoutFn, useToNumber, useToString, useToggle, watchArray, watchAtMost, watchDebounced, watchDeep, watchIgnorable, watchImmediate, watchOnce, watchPausable, watchThrottled, watchTriggerable, watchWithFilter, whenever };\n", "const defaultTimestep = (1 / 60) * 1000;\nconst getCurrentTime = typeof performance !== \"undefined\"\n    ? () => performance.now()\n    : () => Date.now();\nconst onNextFrame = typeof window !== \"undefined\"\n    ? (callback) => window.requestAnimationFrame(callback)\n    : (callback) => setTimeout(() => callback(getCurrentTime()), defaultTimestep);\n\nexport { defaultTimestep, onNextFrame };\n", "function createRenderStep(runNextFrame) {\n    let toRun = [];\n    let toRunNextFrame = [];\n    let numToRun = 0;\n    let isProcessing = false;\n    let flushNextFrame = false;\n    const toKeepAlive = new WeakSet();\n    const step = {\n        schedule: (callback, keepAlive = false, immediate = false) => {\n            const addToCurrentFrame = immediate && isProcessing;\n            const buffer = addToCurrentFrame ? toRun : toRunNextFrame;\n            if (keepAlive)\n                toKeepAlive.add(callback);\n            if (buffer.indexOf(callback) === -1) {\n                buffer.push(callback);\n                if (addToCurrentFrame && isProcessing)\n                    numToRun = toRun.length;\n            }\n            return callback;\n        },\n        cancel: (callback) => {\n            const index = toRunNextFrame.indexOf(callback);\n            if (index !== -1)\n                toRunNextFrame.splice(index, 1);\n            toKeepAlive.delete(callback);\n        },\n        process: (frameData) => {\n            if (isProcessing) {\n                flushNextFrame = true;\n                return;\n            }\n            isProcessing = true;\n            [toRun, toRunNextFrame] = [toRunNextFrame, toRun];\n            toRunNextFrame.length = 0;\n            numToRun = toRun.length;\n            if (numToRun) {\n                for (let i = 0; i < numToRun; i++) {\n                    const callback = toRun[i];\n                    callback(frameData);\n                    if (toKeepAlive.has(callback)) {\n                        step.schedule(callback);\n                        runNextFrame();\n                    }\n                }\n            }\n            isProcessing = false;\n            if (flushNextFrame) {\n                flushNextFrame = false;\n                step.process(frameData);\n            }\n        },\n    };\n    return step;\n}\n\nexport { createRenderStep };\n", "import { onNextFrame, defaultTimestep } from './on-next-frame.mjs';\nimport { createRenderStep } from './create-render-step.mjs';\n\nconst maxElapsed = 40;\nlet useDefaultElapsed = true;\nlet runNextFrame = false;\nlet isProcessing = false;\nconst frame = {\n    delta: 0,\n    timestamp: 0,\n};\nconst stepsOrder = [\n    \"read\",\n    \"update\",\n    \"preRender\",\n    \"render\",\n    \"postRender\",\n];\nconst steps = stepsOrder.reduce((acc, key) => {\n    acc[key] = createRenderStep(() => (runNextFrame = true));\n    return acc;\n}, {});\nconst sync = stepsOrder.reduce((acc, key) => {\n    const step = steps[key];\n    acc[key] = (process, keepAlive = false, immediate = false) => {\n        if (!runNextFrame)\n            startLoop();\n        return step.schedule(process, keepAlive, immediate);\n    };\n    return acc;\n}, {});\nconst cancelSync = stepsOrder.reduce((acc, key) => {\n    acc[key] = steps[key].cancel;\n    return acc;\n}, {});\nconst flushSync = stepsOrder.reduce((acc, key) => {\n    acc[key] = () => steps[key].process(frame);\n    return acc;\n}, {});\nconst processStep = (stepId) => steps[stepId].process(frame);\nconst processFrame = (timestamp) => {\n    runNextFrame = false;\n    frame.delta = useDefaultElapsed\n        ? defaultTimestep\n        : Math.max(Math.min(timestamp - frame.timestamp, maxElapsed), 1);\n    frame.timestamp = timestamp;\n    isProcessing = true;\n    stepsOrder.forEach(processStep);\n    isProcessing = false;\n    if (runNextFrame) {\n        useDefaultElapsed = false;\n        onNextFrame(processFrame);\n    }\n};\nconst startLoop = () => {\n    runNextFrame = true;\n    useDefaultElapsed = true;\n    if (!isProcessing)\n        onNextFrame(processFrame);\n};\nconst getFrameData = () => frame;\n\nexport default sync;\nexport { cancelSync, flushSync, getFrameData };\n", "/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n", "var warning = function () { };\r\nvar invariant = function () { };\r\nif (process.env.NODE_ENV !== 'production') {\r\n    warning = function (check, message) {\r\n        if (!check && typeof console !== 'undefined') {\r\n            console.warn(message);\r\n        }\r\n    };\r\n    invariant = function (check, message) {\r\n        if (!check) {\r\n            throw new Error(message);\r\n        }\r\n    };\r\n}\n\nexport { invariant, warning };\n", "const clamp = (min, max, v) => Math.min(Math.max(v, min), max);\n\nexport { clamp };\n", "import { warning } from 'hey-listen';\nimport { clamp } from '../../utils/clamp.mjs';\n\nconst safeMin = 0.001;\nconst minDuration = 0.01;\nconst maxDuration = 10.0;\nconst minDamping = 0.05;\nconst maxDamping = 1;\nfunction findSpring({ duration = 800, bounce = 0.25, velocity = 0, mass = 1, }) {\n    let envelope;\n    let derivative;\n    warning(duration <= maxDuration * 1000, \"Spring duration must be 10 seconds or less\");\n    let dampingRatio = 1 - bounce;\n    dampingRatio = clamp(minDamping, maxDamping, dampingRatio);\n    duration = clamp(minDuration, maxDuration, duration / 1000);\n    if (dampingRatio < 1) {\n        envelope = (undampedFreq) => {\n            const exponentialDecay = undampedFreq * dampingRatio;\n            const delta = exponentialDecay * duration;\n            const a = exponentialDecay - velocity;\n            const b = calcAngularFreq(undampedFreq, dampingRatio);\n            const c = Math.exp(-delta);\n            return safeMin - (a / b) * c;\n        };\n        derivative = (undampedFreq) => {\n            const exponentialDecay = undampedFreq * dampingRatio;\n            const delta = exponentialDecay * duration;\n            const d = delta * velocity + velocity;\n            const e = Math.pow(dampingRatio, 2) * Math.pow(undampedFreq, 2) * duration;\n            const f = Math.exp(-delta);\n            const g = calcAngularFreq(Math.pow(undampedFreq, 2), dampingRatio);\n            const factor = -envelope(undampedFreq) + safeMin > 0 ? -1 : 1;\n            return (factor * ((d - e) * f)) / g;\n        };\n    }\n    else {\n        envelope = (undampedFreq) => {\n            const a = Math.exp(-undampedFreq * duration);\n            const b = (undampedFreq - velocity) * duration + 1;\n            return -safeMin + a * b;\n        };\n        derivative = (undampedFreq) => {\n            const a = Math.exp(-undampedFreq * duration);\n            const b = (velocity - undampedFreq) * (duration * duration);\n            return a * b;\n        };\n    }\n    const initialGuess = 5 / duration;\n    const undampedFreq = approximateRoot(envelope, derivative, initialGuess);\n    duration = duration * 1000;\n    if (isNaN(undampedFreq)) {\n        return {\n            stiffness: 100,\n            damping: 10,\n            duration,\n        };\n    }\n    else {\n        const stiffness = Math.pow(undampedFreq, 2) * mass;\n        return {\n            stiffness,\n            damping: dampingRatio * 2 * Math.sqrt(mass * stiffness),\n            duration,\n        };\n    }\n}\nconst rootIterations = 12;\nfunction approximateRoot(envelope, derivative, initialGuess) {\n    let result = initialGuess;\n    for (let i = 1; i < rootIterations; i++) {\n        result = result - envelope(result) / derivative(result);\n    }\n    return result;\n}\nfunction calcAngularFreq(undampedFreq, dampingRatio) {\n    return undampedFreq * Math.sqrt(1 - dampingRatio * dampingRatio);\n}\n\nexport { calcAngularFreq, findSpring, maxDamping, maxDuration, minDamping, minDuration };\n", "import { __rest } from 'tslib';\nimport { findSpring, calcAngularFreq } from '../utils/find-spring.mjs';\n\nconst durationKeys = [\"duration\", \"bounce\"];\nconst physicsKeys = [\"stiffness\", \"damping\", \"mass\"];\nfunction isSpringType(options, keys) {\n    return keys.some((key) => options[key] !== undefined);\n}\nfunction getSpringOptions(options) {\n    let springOptions = Object.assign({ velocity: 0.0, stiffness: 100, damping: 10, mass: 1.0, isResolvedFromDuration: false }, options);\n    if (!isSpringType(options, physicsKeys) &&\n        isSpringType(options, durationKeys)) {\n        const derived = findSpring(options);\n        springOptions = Object.assign(Object.assign(Object.assign({}, springOptions), derived), { velocity: 0.0, mass: 1.0 });\n        springOptions.isResolvedFromDuration = true;\n    }\n    return springOptions;\n}\nfunction spring(_a) {\n    var { from = 0.0, to = 1.0, restSpeed = 2, restDelta } = _a, options = __rest(_a, [\"from\", \"to\", \"restSpeed\", \"restDelta\"]);\n    const state = { done: false, value: from };\n    let { stiffness, damping, mass, velocity, duration, isResolvedFromDuration, } = getSpringOptions(options);\n    let resolveSpring = zero;\n    let resolveVelocity = zero;\n    function createSpring() {\n        const initialVelocity = velocity ? -(velocity / 1000) : 0.0;\n        const initialDelta = to - from;\n        const dampingRatio = damping / (2 * Math.sqrt(stiffness * mass));\n        const undampedAngularFreq = Math.sqrt(stiffness / mass) / 1000;\n        if (restDelta === undefined) {\n            restDelta = Math.min(Math.abs(to - from) / 100, 0.4);\n        }\n        if (dampingRatio < 1) {\n            const angularFreq = calcAngularFreq(undampedAngularFreq, dampingRatio);\n            resolveSpring = (t) => {\n                const envelope = Math.exp(-dampingRatio * undampedAngularFreq * t);\n                return (to -\n                    envelope *\n                        (((initialVelocity +\n                            dampingRatio * undampedAngularFreq * initialDelta) /\n                            angularFreq) *\n                            Math.sin(angularFreq * t) +\n                            initialDelta * Math.cos(angularFreq * t)));\n            };\n            resolveVelocity = (t) => {\n                const envelope = Math.exp(-dampingRatio * undampedAngularFreq * t);\n                return (dampingRatio *\n                    undampedAngularFreq *\n                    envelope *\n                    ((Math.sin(angularFreq * t) *\n                        (initialVelocity +\n                            dampingRatio *\n                                undampedAngularFreq *\n                                initialDelta)) /\n                        angularFreq +\n                        initialDelta * Math.cos(angularFreq * t)) -\n                    envelope *\n                        (Math.cos(angularFreq * t) *\n                            (initialVelocity +\n                                dampingRatio *\n                                    undampedAngularFreq *\n                                    initialDelta) -\n                            angularFreq *\n                                initialDelta *\n                                Math.sin(angularFreq * t)));\n            };\n        }\n        else if (dampingRatio === 1) {\n            resolveSpring = (t) => to -\n                Math.exp(-undampedAngularFreq * t) *\n                    (initialDelta +\n                        (initialVelocity + undampedAngularFreq * initialDelta) *\n                            t);\n        }\n        else {\n            const dampedAngularFreq = undampedAngularFreq * Math.sqrt(dampingRatio * dampingRatio - 1);\n            resolveSpring = (t) => {\n                const envelope = Math.exp(-dampingRatio * undampedAngularFreq * t);\n                const freqForT = Math.min(dampedAngularFreq * t, 300);\n                return (to -\n                    (envelope *\n                        ((initialVelocity +\n                            dampingRatio * undampedAngularFreq * initialDelta) *\n                            Math.sinh(freqForT) +\n                            dampedAngularFreq *\n                                initialDelta *\n                                Math.cosh(freqForT))) /\n                        dampedAngularFreq);\n            };\n        }\n    }\n    createSpring();\n    return {\n        next: (t) => {\n            const current = resolveSpring(t);\n            if (!isResolvedFromDuration) {\n                const currentVelocity = resolveVelocity(t) * 1000;\n                const isBelowVelocityThreshold = Math.abs(currentVelocity) <= restSpeed;\n                const isBelowDisplacementThreshold = Math.abs(to - current) <= restDelta;\n                state.done =\n                    isBelowVelocityThreshold && isBelowDisplacementThreshold;\n            }\n            else {\n                state.done = t >= duration;\n            }\n            state.value = state.done ? to : current;\n            return state;\n        },\n        flipTarget: () => {\n            velocity = -velocity;\n            [from, to] = [to, from];\n            createSpring();\n        },\n    };\n}\nspring.needsInterpolation = (a, b) => typeof a === \"string\" || typeof b === \"string\";\nconst zero = (_t) => 0;\n\nexport { spring };\n", "const progress = (from, to, value) => {\n    const toFromDifference = to - from;\n    return toFromDifference === 0 ? 1 : (value - from) / toFromDifference;\n};\n\nexport { progress };\n", "const mix = (from, to, progress) => -progress * from + progress * to + from;\n\nexport { mix };\n", "const clamp = (min, max) => (v) => Math.max(Math.min(v, max), min);\nconst sanitize = (v) => (v % 1 ? Number(v.toFixed(5)) : v);\nconst floatRegex = /(-)?([\\d]*\\.?[\\d])+/g;\nconst colorRegex = /(#[0-9a-f]{6}|#[0-9a-f]{3}|#(?:[0-9a-f]{2}){2,4}|(rgb|hsl)a?\\((-?[\\d\\.]+%?[,\\s]+){2}(-?[\\d\\.]+%?)\\s*[\\,\\/]?\\s*[\\d\\.]*%?\\))/gi;\nconst singleColorRegex = /^(#[0-9a-f]{3}|#(?:[0-9a-f]{2}){2,4}|(rgb|hsl)a?\\((-?[\\d\\.]+%?[,\\s]+){2}(-?[\\d\\.]+%?)\\s*[\\,\\/]?\\s*[\\d\\.]*%?\\))$/i;\nfunction isString(v) {\n    return typeof v === 'string';\n}\n\nexport { clamp, colorRegex, floatRegex, isString, sanitize, singleColorRegex };\n", "import { clamp } from '../utils.mjs';\n\nconst number = {\n    test: (v) => typeof v === 'number',\n    parse: parseFloat,\n    transform: (v) => v,\n};\nconst alpha = Object.assign(Object.assign({}, number), { transform: clamp(0, 1) });\nconst scale = Object.assign(Object.assign({}, number), { default: 1 });\n\nexport { alpha, number, scale };\n", "import { isString } from '../utils.mjs';\n\nconst createUnitType = (unit) => ({\n    test: (v) => isString(v) && v.endsWith(unit) && v.split(' ').length === 1,\n    parse: parseFloat,\n    transform: (v) => `${v}${unit}`,\n});\nconst degrees = createUnitType('deg');\nconst percent = createUnitType('%');\nconst px = createUnitType('px');\nconst vh = createUnitType('vh');\nconst vw = createUnitType('vw');\nconst progressPercentage = Object.assign(Object.assign({}, percent), { parse: (v) => percent.parse(v) / 100, transform: (v) => percent.transform(v * 100) });\n\nexport { degrees, percent, progressPercentage, px, vh, vw };\n", "import { isString, singleColorRegex, floatRegex } from '../utils.mjs';\n\nconst isColorString = (type, testProp) => (v) => {\n    return Boolean((isString(v) && singleColorRegex.test(v) && v.startsWith(type)) ||\n        (testProp && Object.prototype.hasOwnProperty.call(v, testProp)));\n};\nconst splitColor = (aName, bName, cName) => (v) => {\n    if (!isString(v))\n        return v;\n    const [a, b, c, alpha] = v.match(floatRegex);\n    return {\n        [aName]: parseFloat(a),\n        [bName]: parseFloat(b),\n        [cName]: parseFloat(c),\n        alpha: alpha !== undefined ? parseFloat(alpha) : 1,\n    };\n};\n\nexport { isColorString, splitColor };\n", "import { alpha } from '../numbers/index.mjs';\nimport { percent } from '../numbers/units.mjs';\nimport { sanitize } from '../utils.mjs';\nimport { isColorString, splitColor } from './utils.mjs';\n\nconst hsla = {\n    test: isColorString('hsl', 'hue'),\n    parse: splitColor('hue', 'saturation', 'lightness'),\n    transform: ({ hue, saturation, lightness, alpha: alpha$1 = 1 }) => {\n        return ('hsla(' +\n            Math.round(hue) +\n            ', ' +\n            percent.transform(sanitize(saturation)) +\n            ', ' +\n            percent.transform(sanitize(lightness)) +\n            ', ' +\n            sanitize(alpha.transform(alpha$1)) +\n            ')');\n    },\n};\n\nexport { hsla };\n", "import { number, alpha } from '../numbers/index.mjs';\nimport { sanitize, clamp } from '../utils.mjs';\nimport { isColorString, splitColor } from './utils.mjs';\n\nconst clampRgbUnit = clamp(0, 255);\nconst rgbUnit = Object.assign(Object.assign({}, number), { transform: (v) => Math.round(clampRgbUnit(v)) });\nconst rgba = {\n    test: isColorString('rgb', 'red'),\n    parse: splitColor('red', 'green', 'blue'),\n    transform: ({ red, green, blue, alpha: alpha$1 = 1 }) => 'rgba(' +\n        rgbUnit.transform(red) +\n        ', ' +\n        rgbUnit.transform(green) +\n        ', ' +\n        rgbUnit.transform(blue) +\n        ', ' +\n        sanitize(alpha.transform(alpha$1)) +\n        ')',\n};\n\nexport { rgbUnit, rgba };\n", "import { rgba } from './rgba.mjs';\nimport { isColorString } from './utils.mjs';\n\nfunction parseHex(v) {\n    let r = '';\n    let g = '';\n    let b = '';\n    let a = '';\n    if (v.length > 5) {\n        r = v.substr(1, 2);\n        g = v.substr(3, 2);\n        b = v.substr(5, 2);\n        a = v.substr(7, 2);\n    }\n    else {\n        r = v.substr(1, 1);\n        g = v.substr(2, 1);\n        b = v.substr(3, 1);\n        a = v.substr(4, 1);\n        r += r;\n        g += g;\n        b += b;\n        a += a;\n    }\n    return {\n        red: parseInt(r, 16),\n        green: parseInt(g, 16),\n        blue: parseInt(b, 16),\n        alpha: a ? parseInt(a, 16) / 255 : 1,\n    };\n}\nconst hex = {\n    test: isColorString('#'),\n    parse: parseHex,\n    transform: rgba.transform,\n};\n\nexport { hex };\n", "import { isString } from '../utils.mjs';\nimport { hex } from './hex.mjs';\nimport { hsla } from './hsla.mjs';\nimport { rgba } from './rgba.mjs';\n\nconst color = {\n    test: (v) => rgba.test(v) || hex.test(v) || hsla.test(v),\n    parse: (v) => {\n        if (rgba.test(v)) {\n            return rgba.parse(v);\n        }\n        else if (hsla.test(v)) {\n            return hsla.parse(v);\n        }\n        else {\n            return hex.parse(v);\n        }\n    },\n    transform: (v) => {\n        return isString(v)\n            ? v\n            : v.hasOwnProperty('red')\n                ? rgba.transform(v)\n                : hsla.transform(v);\n    },\n};\n\nexport { color };\n", "import { color } from '../color/index.mjs';\nimport { number } from '../numbers/index.mjs';\nimport { isString, floatRegex, colorRegex, sanitize } from '../utils.mjs';\n\nconst colorToken = '${c}';\nconst numberToken = '${n}';\nfunction test(v) {\n    var _a, _b, _c, _d;\n    return (isNaN(v) &&\n        isString(v) &&\n        ((_b = (_a = v.match(floatRegex)) === null || _a === void 0 ? void 0 : _a.length) !== null && _b !== void 0 ? _b : 0) + ((_d = (_c = v.match(colorRegex)) === null || _c === void 0 ? void 0 : _c.length) !== null && _d !== void 0 ? _d : 0) > 0);\n}\nfunction analyse(v) {\n    if (typeof v === 'number')\n        v = `${v}`;\n    const values = [];\n    let numColors = 0;\n    const colors = v.match(colorRegex);\n    if (colors) {\n        numColors = colors.length;\n        v = v.replace(colorRegex, colorToken);\n        values.push(...colors.map(color.parse));\n    }\n    const numbers = v.match(floatRegex);\n    if (numbers) {\n        v = v.replace(floatRegex, numberToken);\n        values.push(...numbers.map(number.parse));\n    }\n    return { values, numColors, tokenised: v };\n}\nfunction parse(v) {\n    return analyse(v).values;\n}\nfunction createTransformer(v) {\n    const { values, numColors, tokenised } = analyse(v);\n    const numValues = values.length;\n    return (v) => {\n        let output = tokenised;\n        for (let i = 0; i < numValues; i++) {\n            output = output.replace(i < numColors ? colorToken : numberToken, i < numColors ? color.transform(v[i]) : sanitize(v[i]));\n        }\n        return output;\n    };\n}\nconst convertNumbersToZero = (v) => typeof v === 'number' ? 0 : v;\nfunction getAnimatableNone(v) {\n    const parsed = parse(v);\n    const transformer = createTransformer(v);\n    return transformer(parsed.map(convertNumbersToZero));\n}\nconst complex = { test, parse, createTransformer, getAnimatableNone };\n\nexport { complex };\n", "import { complex } from './index.mjs';\nimport { floatRegex } from '../utils.mjs';\n\nconst maxDefaults = new Set(['brightness', 'contrast', 'saturate', 'opacity']);\nfunction applyDefaultFilter(v) {\n    let [name, value] = v.slice(0, -1).split('(');\n    if (name === 'drop-shadow')\n        return v;\n    const [number] = value.match(floatRegex) || [];\n    if (!number)\n        return v;\n    const unit = value.replace(number, '');\n    let defaultValue = maxDefaults.has(name) ? 1 : 0;\n    if (number !== value)\n        defaultValue *= 100;\n    return name + '(' + defaultValue + unit + ')';\n}\nconst functionRegex = /([a-z-]*)\\(.*?\\)/g;\nconst filter = Object.assign(Object.assign({}, complex), { getAnimatableNone: (v) => {\n        const functions = v.match(functionRegex);\n        return functions ? functions.map(applyDefaultFilter).join(' ') : v;\n    } });\n\nexport { filter };\n", "function hueToRgb(p, q, t) {\n    if (t < 0)\n        t += 1;\n    if (t > 1)\n        t -= 1;\n    if (t < 1 / 6)\n        return p + (q - p) * 6 * t;\n    if (t < 1 / 2)\n        return q;\n    if (t < 2 / 3)\n        return p + (q - p) * (2 / 3 - t) * 6;\n    return p;\n}\nfunction hslaToRgba({ hue, saturation, lightness, alpha }) {\n    hue /= 360;\n    saturation /= 100;\n    lightness /= 100;\n    let red = 0;\n    let green = 0;\n    let blue = 0;\n    if (!saturation) {\n        red = green = blue = lightness;\n    }\n    else {\n        const q = lightness < 0.5\n            ? lightness * (1 + saturation)\n            : lightness + saturation - lightness * saturation;\n        const p = 2 * lightness - q;\n        red = hueToRgb(p, q, hue + 1 / 3);\n        green = hueToRgb(p, q, hue);\n        blue = hueToRgb(p, q, hue - 1 / 3);\n    }\n    return {\n        red: Math.round(red * 255),\n        green: Math.round(green * 255),\n        blue: Math.round(blue * 255),\n        alpha,\n    };\n}\n\nexport { hslaToRgba };\n", "import { mix } from './mix.mjs';\nimport { hsla, rgba, hex } from 'style-value-types';\nimport { invariant } from 'hey-listen';\nimport { hslaToRgba } from './hsla-to-rgba.mjs';\n\nconst mixLinearColor = (from, to, v) => {\n    const fromExpo = from * from;\n    const toExpo = to * to;\n    return Math.sqrt(Math.max(0, v * (toExpo - fromExpo) + fromExpo));\n};\nconst colorTypes = [hex, rgba, hsla];\nconst getColorType = (v) => colorTypes.find((type) => type.test(v));\nconst notAnimatable = (color) => `'${color}' is not an animatable color. Use the equivalent color code instead.`;\nconst mixColor = (from, to) => {\n    let fromColorType = getColorType(from);\n    let toColorType = getColorType(to);\n    invariant(!!fromColorType, notAnimatable(from));\n    invariant(!!toColorType, notAnimatable(to));\n    let fromColor = fromColorType.parse(from);\n    let toColor = toColorType.parse(to);\n    if (fromColorType === hsla) {\n        fromColor = hslaToRgba(fromColor);\n        fromColorType = rgba;\n    }\n    if (toColorType === hsla) {\n        toColor = hslaToRgba(toColor);\n        toColorType = rgba;\n    }\n    const blended = Object.assign({}, fromColor);\n    return (v) => {\n        for (const key in blended) {\n            if (key !== \"alpha\") {\n                blended[key] = mixLinearColor(fromColor[key], toColor[key], v);\n            }\n        }\n        blended.alpha = mix(fromColor.alpha, toColor.alpha, v);\n        return fromColorType.transform(blended);\n    };\n};\n\nexport { mixColor, mixLinearColor };\n", "const zeroPoint = {\n    x: 0,\n    y: 0,\n    z: 0\n};\nconst isNum = (v) => typeof v === 'number';\n\nexport { isNum, zeroPoint };\n", "const combineFunctions = (a, b) => (v) => b(a(v));\nconst pipe = (...transformers) => transformers.reduce(combineFunctions);\n\nexport { pipe };\n", "import { complex, color } from 'style-value-types';\nimport { mix } from './mix.mjs';\nimport { mixColor } from './mix-color.mjs';\nimport { isNum } from './inc.mjs';\nimport { pipe } from './pipe.mjs';\nimport { warning } from 'hey-listen';\n\nfunction getMixer(origin, target) {\n    if (isNum(origin)) {\n        return (v) => mix(origin, target, v);\n    }\n    else if (color.test(origin)) {\n        return mixColor(origin, target);\n    }\n    else {\n        return mixComplex(origin, target);\n    }\n}\nconst mixArray = (from, to) => {\n    const output = [...from];\n    const numValues = output.length;\n    const blendValue = from.map((fromThis, i) => getMixer(fromThis, to[i]));\n    return (v) => {\n        for (let i = 0; i < numValues; i++) {\n            output[i] = blendValue[i](v);\n        }\n        return output;\n    };\n};\nconst mixObject = (origin, target) => {\n    const output = Object.assign(Object.assign({}, origin), target);\n    const blendValue = {};\n    for (const key in output) {\n        if (origin[key] !== undefined && target[key] !== undefined) {\n            blendValue[key] = getMixer(origin[key], target[key]);\n        }\n    }\n    return (v) => {\n        for (const key in blendValue) {\n            output[key] = blendValue[key](v);\n        }\n        return output;\n    };\n};\nfunction analyse(value) {\n    const parsed = complex.parse(value);\n    const numValues = parsed.length;\n    let numNumbers = 0;\n    let numRGB = 0;\n    let numHSL = 0;\n    for (let i = 0; i < numValues; i++) {\n        if (numNumbers || typeof parsed[i] === \"number\") {\n            numNumbers++;\n        }\n        else {\n            if (parsed[i].hue !== undefined) {\n                numHSL++;\n            }\n            else {\n                numRGB++;\n            }\n        }\n    }\n    return { parsed, numNumbers, numRGB, numHSL };\n}\nconst mixComplex = (origin, target) => {\n    const template = complex.createTransformer(target);\n    const originStats = analyse(origin);\n    const targetStats = analyse(target);\n    const canInterpolate = originStats.numHSL === targetStats.numHSL &&\n        originStats.numRGB === targetStats.numRGB &&\n        originStats.numNumbers >= targetStats.numNumbers;\n    if (canInterpolate) {\n        return pipe(mixArray(originStats.parsed, targetStats.parsed), template);\n    }\n    else {\n        warning(true, `Complex values '${origin}' and '${target}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`);\n        return (p) => `${p > 0 ? target : origin}`;\n    }\n};\n\nexport { mixArray, mixComplex, mixObject };\n", "import { progress } from './progress.mjs';\nimport { mix } from './mix.mjs';\nimport { mixColor } from './mix-color.mjs';\nimport { mixComplex, mixArray, mixObject } from './mix-complex.mjs';\nimport { color } from 'style-value-types';\nimport { clamp } from './clamp.mjs';\nimport { pipe } from './pipe.mjs';\nimport { invariant } from 'hey-listen';\n\nconst mixNumber = (from, to) => (p) => mix(from, to, p);\nfunction detectMixerFactory(v) {\n    if (typeof v === 'number') {\n        return mixNumber;\n    }\n    else if (typeof v === 'string') {\n        if (color.test(v)) {\n            return mixColor;\n        }\n        else {\n            return mixComplex;\n        }\n    }\n    else if (Array.isArray(v)) {\n        return mixArray;\n    }\n    else if (typeof v === 'object') {\n        return mixObject;\n    }\n}\nfunction createMixers(output, ease, customMixer) {\n    const mixers = [];\n    const mixerFactory = customMixer || detectMixerFactory(output[0]);\n    const numMixers = output.length - 1;\n    for (let i = 0; i < numMixers; i++) {\n        let mixer = mixerFactory(output[i], output[i + 1]);\n        if (ease) {\n            const easingFunction = Array.isArray(ease) ? ease[i] : ease;\n            mixer = pipe(easingFunction, mixer);\n        }\n        mixers.push(mixer);\n    }\n    return mixers;\n}\nfunction fastInterpolate([from, to], [mixer]) {\n    return (v) => mixer(progress(from, to, v));\n}\nfunction slowInterpolate(input, mixers) {\n    const inputLength = input.length;\n    const lastInputIndex = inputLength - 1;\n    return (v) => {\n        let mixerIndex = 0;\n        let foundMixerIndex = false;\n        if (v <= input[0]) {\n            foundMixerIndex = true;\n        }\n        else if (v >= input[lastInputIndex]) {\n            mixerIndex = lastInputIndex - 1;\n            foundMixerIndex = true;\n        }\n        if (!foundMixerIndex) {\n            let i = 1;\n            for (; i < inputLength; i++) {\n                if (input[i] > v || i === lastInputIndex) {\n                    break;\n                }\n            }\n            mixerIndex = i - 1;\n        }\n        const progressInRange = progress(input[mixerIndex], input[mixerIndex + 1], v);\n        return mixers[mixerIndex](progressInRange);\n    };\n}\nfunction interpolate(input, output, { clamp: isClamp = true, ease, mixer } = {}) {\n    const inputLength = input.length;\n    invariant(inputLength === output.length, 'Both input and output ranges must be the same length');\n    invariant(!ease || !Array.isArray(ease) || ease.length === inputLength - 1, 'Array of easing functions must be of length `input.length - 1`, as it applies to the transitions **between** the defined values.');\n    if (input[0] > input[inputLength - 1]) {\n        input = [].concat(input);\n        output = [].concat(output);\n        input.reverse();\n        output.reverse();\n    }\n    const mixers = createMixers(output, ease, mixer);\n    const interpolator = inputLength === 2\n        ? fastInterpolate(input, mixers)\n        : slowInterpolate(input, mixers);\n    return isClamp\n        ? (v) => interpolator(clamp(input[0], input[inputLength - 1], v))\n        : interpolator;\n}\n\nexport { interpolate };\n", "const reverseEasing = easing => p => 1 - easing(1 - p);\nconst mirrorEasing = easing => p => p <= 0.5 ? easing(2 * p) / 2 : (2 - easing(2 * (1 - p))) / 2;\nconst createExpoIn = (power) => p => Math.pow(p, power);\nconst createBackIn = (power) => p => p * p * ((power + 1) * p - power);\nconst createAnticipate = (power) => {\n    const backEasing = createBackIn(power);\n    return p => (p *= 2) < 1\n        ? 0.5 * backEasing(p)\n        : 0.5 * (2 - Math.pow(2, -10 * (p - 1)));\n};\n\nexport { createAnticipate, createBackIn, createExpoIn, mirrorEasing, reverseEasing };\n", "import { createExpoIn, reverseEasing, mirrorEasing, createBackIn, createAnticipate } from './utils.mjs';\n\nconst DEFAULT_OVERSHOOT_STRENGTH = 1.525;\nconst BOUNCE_FIRST_THRESHOLD = 4.0 / 11.0;\nconst BOUNCE_SECOND_THRESHOLD = 8.0 / 11.0;\nconst BOUNCE_THIRD_THRESHOLD = 9.0 / 10.0;\nconst linear = p => p;\nconst easeIn = createExpoIn(2);\nconst easeOut = reverseEasing(easeIn);\nconst easeInOut = mirrorEasing(easeIn);\nconst circIn = p => 1 - Math.sin(Math.acos(p));\nconst circOut = reverseEasing(circIn);\nconst circInOut = mirrorEasing(circOut);\nconst backIn = createBackIn(DEFAULT_OVERSHOOT_STRENGTH);\nconst backOut = reverseEasing(backIn);\nconst backInOut = mirrorEasing(backIn);\nconst anticipate = createAnticipate(DEFAULT_OVERSHOOT_STRENGTH);\nconst ca = 4356.0 / 361.0;\nconst cb = 35442.0 / 1805.0;\nconst cc = 16061.0 / 1805.0;\nconst bounceOut = (p) => {\n    if (p === 1 || p === 0)\n        return p;\n    const p2 = p * p;\n    return p < BOUNCE_FIRST_THRESHOLD\n        ? 7.5625 * p2\n        : p < BOUNCE_SECOND_THRESHOLD\n            ? 9.075 * p2 - 9.9 * p + 3.4\n            : p < BOUNCE_THIRD_THRESHOLD\n                ? ca * p2 - cb * p + cc\n                : 10.8 * p * p - 20.52 * p + 10.72;\n};\nconst bounceIn = reverseEasing(bounceOut);\nconst bounceInOut = (p) => p < 0.5\n    ? 0.5 * (1.0 - bounceOut(1.0 - p * 2.0))\n    : 0.5 * bounceOut(p * 2.0 - 1.0) + 0.5;\n\nexport { anticipate, backIn, backInOut, backOut, bounceIn, bounceInOut, bounceOut, circIn, circInOut, circOut, easeIn, easeInOut, easeOut, linear };\n", "import { interpolate } from '../../utils/interpolate.mjs';\nimport { easeInOut } from '../../easing/index.mjs';\n\nfunction defaultEasing(values, easing) {\n    return values.map(() => easing || easeInOut).splice(0, values.length - 1);\n}\nfunction defaultOffset(values) {\n    const numValues = values.length;\n    return values.map((_value, i) => i !== 0 ? i / (numValues - 1) : 0);\n}\nfunction convertOffsetToTimes(offset, duration) {\n    return offset.map((o) => o * duration);\n}\nfunction keyframes({ from = 0, to = 1, ease, offset, duration = 300, }) {\n    const state = { done: false, value: from };\n    const values = Array.isArray(to) ? to : [from, to];\n    const times = convertOffsetToTimes(offset && offset.length === values.length\n        ? offset\n        : defaultOffset(values), duration);\n    function createInterpolator() {\n        return interpolate(times, values, {\n            ease: Array.isArray(ease) ? ease : defaultEasing(values, ease),\n        });\n    }\n    let interpolator = createInterpolator();\n    return {\n        next: (t) => {\n            state.value = interpolator(t);\n            state.done = t >= duration;\n            return state;\n        },\n        flipTarget: () => {\n            values.reverse();\n            interpolator = createInterpolator();\n        },\n    };\n}\n\nexport { convertOffsetToTimes, defaultEasing, defaultOffset, keyframes };\n", "function decay({ velocity = 0, from = 0, power = 0.8, timeConstant = 350, restDelta = 0.5, modifyTarget, }) {\n    const state = { done: false, value: from };\n    let amplitude = power * velocity;\n    const ideal = from + amplitude;\n    const target = modifyTarget === undefined ? ideal : modifyTarget(ideal);\n    if (target !== ideal)\n        amplitude = target - from;\n    return {\n        next: (t) => {\n            const delta = -amplitude * Math.exp(-t / timeConstant);\n            state.done = !(delta > restDelta || delta < -restDelta);\n            state.value = state.done ? target : target + delta;\n            return state;\n        },\n        flipTarget: () => { },\n    };\n}\n\nexport { decay };\n", "import { spring } from '../generators/spring.mjs';\nimport { keyframes } from '../generators/keyframes.mjs';\nimport { decay } from '../generators/decay.mjs';\n\nconst types = { keyframes, spring, decay };\nfunction detectAnimationFromOptions(config) {\n    if (Array.isArray(config.to)) {\n        return keyframes;\n    }\n    else if (types[config.type]) {\n        return types[config.type];\n    }\n    const keys = new Set(Object.keys(config));\n    if (keys.has(\"ease\") ||\n        (keys.has(\"duration\") && !keys.has(\"dampingRatio\"))) {\n        return keyframes;\n    }\n    else if (keys.has(\"dampingRatio\") ||\n        keys.has(\"stiffness\") ||\n        keys.has(\"mass\") ||\n        keys.has(\"damping\") ||\n        keys.has(\"restSpeed\") ||\n        keys.has(\"restDelta\")) {\n        return spring;\n    }\n    return keyframes;\n}\n\nexport { detectAnimationFromOptions };\n", "function loopElapsed(elapsed, duration, delay = 0) {\n    return elapsed - duration - delay;\n}\nfunction reverseElapsed(elapsed, duration, delay = 0, isForwardPlayback = true) {\n    return isForwardPlayback\n        ? loopElapsed(duration + -elapsed, duration, delay)\n        : duration - (elapsed - duration) + delay;\n}\nfunction hasRepeatDelayElapsed(elapsed, duration, delay, isForwardPlayback) {\n    return isForwardPlayback ? elapsed >= duration + delay : elapsed <= -delay;\n}\n\nexport { hasRepeatDelayElapsed, loopElapsed, reverseElapsed };\n", "import { __rest } from 'tslib';\nimport { detectAnimationFromOptions } from './utils/detect-animation-from-options.mjs';\nimport sync, { cancelSync } from 'framesync';\nimport { interpolate } from '../utils/interpolate.mjs';\nimport { hasRepeatDelayElapsed, reverseElapsed, loopElapsed } from './utils/elapsed.mjs';\n\nconst framesync = (update) => {\n    const passTimestamp = ({ delta }) => update(delta);\n    return {\n        start: () => sync.update(passTimestamp, true),\n        stop: () => cancelSync.update(passTimestamp),\n    };\n};\nfunction animate(_a) {\n    var _b, _c;\n    var { from, autoplay = true, driver = framesync, elapsed = 0, repeat: repeatMax = 0, repeatType = \"loop\", repeatDelay = 0, onPlay, onStop, onComplete, onRepeat, onUpdate } = _a, options = __rest(_a, [\"from\", \"autoplay\", \"driver\", \"elapsed\", \"repeat\", \"repeatType\", \"repeatDelay\", \"onPlay\", \"onStop\", \"onComplete\", \"onRepeat\", \"onUpdate\"]);\n    let { to } = options;\n    let driverControls;\n    let repeatCount = 0;\n    let computedDuration = options.duration;\n    let latest;\n    let isComplete = false;\n    let isForwardPlayback = true;\n    let interpolateFromNumber;\n    const animator = detectAnimationFromOptions(options);\n    if ((_c = (_b = animator).needsInterpolation) === null || _c === void 0 ? void 0 : _c.call(_b, from, to)) {\n        interpolateFromNumber = interpolate([0, 100], [from, to], {\n            clamp: false,\n        });\n        from = 0;\n        to = 100;\n    }\n    const animation = animator(Object.assign(Object.assign({}, options), { from, to }));\n    function repeat() {\n        repeatCount++;\n        if (repeatType === \"reverse\") {\n            isForwardPlayback = repeatCount % 2 === 0;\n            elapsed = reverseElapsed(elapsed, computedDuration, repeatDelay, isForwardPlayback);\n        }\n        else {\n            elapsed = loopElapsed(elapsed, computedDuration, repeatDelay);\n            if (repeatType === \"mirror\")\n                animation.flipTarget();\n        }\n        isComplete = false;\n        onRepeat && onRepeat();\n    }\n    function complete() {\n        driverControls.stop();\n        onComplete && onComplete();\n    }\n    function update(delta) {\n        if (!isForwardPlayback)\n            delta = -delta;\n        elapsed += delta;\n        if (!isComplete) {\n            const state = animation.next(Math.max(0, elapsed));\n            latest = state.value;\n            if (interpolateFromNumber)\n                latest = interpolateFromNumber(latest);\n            isComplete = isForwardPlayback ? state.done : elapsed <= 0;\n        }\n        onUpdate === null || onUpdate === void 0 ? void 0 : onUpdate(latest);\n        if (isComplete) {\n            if (repeatCount === 0)\n                computedDuration !== null && computedDuration !== void 0 ? computedDuration : (computedDuration = elapsed);\n            if (repeatCount < repeatMax) {\n                hasRepeatDelayElapsed(elapsed, computedDuration, repeatDelay, isForwardPlayback) && repeat();\n            }\n            else {\n                complete();\n            }\n        }\n    }\n    function play() {\n        onPlay === null || onPlay === void 0 ? void 0 : onPlay();\n        driverControls = driver(update);\n        driverControls.start();\n    }\n    autoplay && play();\n    return {\n        stop: () => {\n            onStop === null || onStop === void 0 ? void 0 : onStop();\n            driverControls.stop();\n        },\n    };\n}\n\nexport { animate };\n", "function velocityPerSecond(velocity, frameDuration) {\n    return frameDuration ? velocity * (1000 / frameDuration) : 0;\n}\n\nexport { velocityPerSecond };\n", "import { animate } from './index.mjs';\nimport { velocityPerSecond } from '../utils/velocity-per-second.mjs';\nimport { getFrameData } from 'framesync';\n\nfunction inertia({ from = 0, velocity = 0, min, max, power = 0.8, timeConstant = 750, bounceStiffness = 500, bounceDamping = 10, restDelta = 1, modifyTarget, driver, onUpdate, onComplete, onStop, }) {\n    let currentAnimation;\n    function isOutOfBounds(v) {\n        return (min !== undefined && v < min) || (max !== undefined && v > max);\n    }\n    function boundaryNearest(v) {\n        if (min === undefined)\n            return max;\n        if (max === undefined)\n            return min;\n        return Math.abs(min - v) < Math.abs(max - v) ? min : max;\n    }\n    function startAnimation(options) {\n        currentAnimation === null || currentAnimation === void 0 ? void 0 : currentAnimation.stop();\n        currentAnimation = animate(Object.assign(Object.assign({}, options), { driver, onUpdate: (v) => {\n                var _a;\n                onUpdate === null || onUpdate === void 0 ? void 0 : onUpdate(v);\n                (_a = options.onUpdate) === null || _a === void 0 ? void 0 : _a.call(options, v);\n            }, onComplete,\n            onStop }));\n    }\n    function startSpring(options) {\n        startAnimation(Object.assign({ type: \"spring\", stiffness: bounceStiffness, damping: bounceDamping, restDelta }, options));\n    }\n    if (isOutOfBounds(from)) {\n        startSpring({ from, velocity, to: boundaryNearest(from) });\n    }\n    else {\n        let target = power * velocity + from;\n        if (typeof modifyTarget !== \"undefined\")\n            target = modifyTarget(target);\n        const boundary = boundaryNearest(target);\n        const heading = boundary === min ? -1 : 1;\n        let prev;\n        let current;\n        const checkBoundary = (v) => {\n            prev = current;\n            current = v;\n            velocity = velocityPerSecond(v - prev, getFrameData().delta);\n            if ((heading === 1 && v > boundary) ||\n                (heading === -1 && v < boundary)) {\n                startSpring({ from: v, to: boundary, velocity });\n            }\n        };\n        startAnimation({\n            type: \"decay\",\n            from,\n            velocity,\n            timeConstant,\n            power,\n            restDelta,\n            modifyTarget,\n            onUpdate: isOutOfBounds(target) ? checkBoundary : undefined,\n        });\n    }\n    return {\n        stop: () => currentAnimation === null || currentAnimation === void 0 ? void 0 : currentAnimation.stop(),\n    };\n}\n\nexport { inertia };\n", "const identity = (v) => v;\nconst createAttractor = (alterDisplacement = identity) => (constant, origin, v) => {\n    const displacement = origin - v;\n    const springModifiedDisplacement = -(0 - constant + 1) * (0 - alterDisplacement(Math.abs(displacement)));\n    return displacement <= 0\n        ? origin + springModifiedDisplacement\n        : origin - springModifiedDisplacement;\n};\nconst attract = createAttractor();\nconst attractExpo = createAttractor(Math.sqrt);\n\nexport { attract, attractExpo, createAttractor };\n", "import { linear } from './index.mjs';\n\nconst a = (a1, a2) => 1.0 - 3.0 * a2 + 3.0 * a1;\nconst b = (a1, a2) => 3.0 * a2 - 6.0 * a1;\nconst c = (a1) => 3.0 * a1;\nconst calcBezier = (t, a1, a2) => ((a(a1, a2) * t + b(a1, a2)) * t + c(a1)) * t;\nconst getSlope = (t, a1, a2) => 3.0 * a(a1, a2) * t * t + 2.0 * b(a1, a2) * t + c(a1);\nconst subdivisionPrecision = 0.0000001;\nconst subdivisionMaxIterations = 10;\nfunction binarySubdivide(aX, aA, aB, mX1, mX2) {\n    let currentX;\n    let currentT;\n    let i = 0;\n    do {\n        currentT = aA + (aB - aA) / 2.0;\n        currentX = calcBezier(currentT, mX1, mX2) - aX;\n        if (currentX > 0.0) {\n            aB = currentT;\n        }\n        else {\n            aA = currentT;\n        }\n    } while (Math.abs(currentX) > subdivisionPrecision &&\n        ++i < subdivisionMaxIterations);\n    return currentT;\n}\nconst newtonIterations = 8;\nconst newtonMinSlope = 0.001;\nfunction newtonRaphsonIterate(aX, aGuessT, mX1, mX2) {\n    for (let i = 0; i < newtonIterations; ++i) {\n        const currentSlope = getSlope(aGuessT, mX1, mX2);\n        if (currentSlope === 0.0) {\n            return aGuessT;\n        }\n        const currentX = calcBezier(aGuessT, mX1, mX2) - aX;\n        aGuessT -= currentX / currentSlope;\n    }\n    return aGuessT;\n}\nconst kSplineTableSize = 11;\nconst kSampleStepSize = 1.0 / (kSplineTableSize - 1.0);\nfunction cubicBezier(mX1, mY1, mX2, mY2) {\n    if (mX1 === mY1 && mX2 === mY2)\n        return linear;\n    const sampleValues = new Float32Array(kSplineTableSize);\n    for (let i = 0; i < kSplineTableSize; ++i) {\n        sampleValues[i] = calcBezier(i * kSampleStepSize, mX1, mX2);\n    }\n    function getTForX(aX) {\n        let intervalStart = 0.0;\n        let currentSample = 1;\n        const lastSample = kSplineTableSize - 1;\n        for (; currentSample !== lastSample && sampleValues[currentSample] <= aX; ++currentSample) {\n            intervalStart += kSampleStepSize;\n        }\n        --currentSample;\n        const dist = (aX - sampleValues[currentSample]) /\n            (sampleValues[currentSample + 1] - sampleValues[currentSample]);\n        const guessForT = intervalStart + dist * kSampleStepSize;\n        const initialSlope = getSlope(guessForT, mX1, mX2);\n        if (initialSlope >= newtonMinSlope) {\n            return newtonRaphsonIterate(aX, guessForT, mX1, mX2);\n        }\n        else if (initialSlope === 0.0) {\n            return guessForT;\n        }\n        else {\n            return binarySubdivide(aX, intervalStart, intervalStart + kSampleStepSize, mX1, mX2);\n        }\n    }\n    return (t) => t === 0 || t === 1 ? t : calcBezier(getTForX(t), mY1, mY2);\n}\n\nexport { cubicBezier };\n", "import defu from 'defu';\nimport { ref, unref, watch, computed, reactive, toRaw, inject, onUpdated, nextTick, defineComponent, useSlots, h, Fragment, isRef } from 'vue';\nimport { isObject as isObject$1, useEventListener, useIntersectionObserver, unrefElement, useMediaQuery } from '@vueuse/core';\nimport { tryOnUnmounted } from '@vueuse/shared';\nimport sync, { getFrameData } from 'framesync';\nimport { velocityPerSecond, inertia, animate, cubicBezier, bounceOut, bounceInOut, bounceIn, anticipate, backOut, backInOut, backIn, circOut, circInOut, circIn, easeOut, easeInOut, easeIn, linear } from 'popmotion';\nimport { number, alpha, filter, px, progressPercentage, degrees, scale, color, complex } from 'style-value-types';\n\nconst motionState = {};\n\nclass SubscriptionManager {\n  subscriptions = /* @__PURE__ */ new Set();\n  add(handler) {\n    this.subscriptions.add(handler);\n    return () => this.subscriptions.delete(handler);\n  }\n  notify(a, b, c) {\n    if (!this.subscriptions.size)\n      return;\n    for (const handler of this.subscriptions) handler(a, b, c);\n  }\n  clear() {\n    this.subscriptions.clear();\n  }\n}\n\nfunction isFloat(value) {\n  return !Number.isNaN(Number.parseFloat(value));\n}\nclass MotionValue {\n  /**\n   * The current state of the `MotionValue`.\n   */\n  current;\n  /**\n   * The previous state of the `MotionValue`.\n   */\n  prev;\n  /**\n   * Duration, in milliseconds, since last updating frame.\n   */\n  timeDelta = 0;\n  /**\n   * Timestamp of the last time this `MotionValue` was updated.\n   */\n  lastUpdated = 0;\n  /**\n   * Functions to notify when the `MotionValue` updates.\n   */\n  updateSubscribers = new SubscriptionManager();\n  /**\n   * A reference to the currently-controlling Popmotion animation\n   */\n  stopAnimation;\n  /**\n   * Tracks whether this value can output a velocity.\n   */\n  canTrackVelocity = false;\n  /**\n   * init - The initiating value\n   * config - Optional configuration options\n   */\n  constructor(init) {\n    this.prev = this.current = init;\n    this.canTrackVelocity = isFloat(this.current);\n  }\n  /**\n   * Adds a function that will be notified when the `MotionValue` is updated.\n   *\n   * It returns a function that, when called, will cancel the subscription.\n   */\n  onChange(subscription) {\n    return this.updateSubscribers.add(subscription);\n  }\n  clearListeners() {\n    this.updateSubscribers.clear();\n  }\n  /**\n   * Sets the state of the `MotionValue`.\n   *\n   * @param v\n   * @param render\n   */\n  set(v) {\n    this.updateAndNotify(v);\n  }\n  /**\n   * Update and notify `MotionValue` subscribers.\n   *\n   * @param v\n   * @param render\n   */\n  updateAndNotify = (v) => {\n    this.prev = this.current;\n    this.current = v;\n    const { delta, timestamp } = getFrameData();\n    if (this.lastUpdated !== timestamp) {\n      this.timeDelta = delta;\n      this.lastUpdated = timestamp;\n    }\n    sync.postRender(this.scheduleVelocityCheck);\n    this.updateSubscribers.notify(this.current);\n  };\n  /**\n   * Returns the latest state of `MotionValue`\n   *\n   * @returns - The latest state of `MotionValue`\n   */\n  get() {\n    return this.current;\n  }\n  /**\n   * Get previous value.\n   *\n   * @returns - The previous latest state of `MotionValue`\n   */\n  getPrevious() {\n    return this.prev;\n  }\n  /**\n   * Returns the latest velocity of `MotionValue`\n   *\n   * @returns - The latest velocity of `MotionValue`. Returns `0` if the state is non-numerical.\n   */\n  getVelocity() {\n    return this.canTrackVelocity ? velocityPerSecond(Number.parseFloat(this.current) - Number.parseFloat(this.prev), this.timeDelta) : 0;\n  }\n  /**\n   * Schedule a velocity check for the next frame.\n   */\n  scheduleVelocityCheck = () => sync.postRender(this.velocityCheck);\n  /**\n   * Updates `prev` with `current` if the value hasn't been updated this frame.\n   * This ensures velocity calculations return `0`.\n   */\n  velocityCheck = ({ timestamp }) => {\n    if (!this.canTrackVelocity)\n      this.canTrackVelocity = isFloat(this.current);\n    if (timestamp !== this.lastUpdated)\n      this.prev = this.current;\n  };\n  /**\n   * Registers a new animation to control this `MotionValue`. Only one\n   * animation can drive a `MotionValue` at one time.\n   */\n  start(animation) {\n    this.stop();\n    return new Promise((resolve) => {\n      const { stop } = animation(resolve);\n      this.stopAnimation = stop;\n    }).then(() => this.clearAnimation());\n  }\n  /**\n   * Stop the currently active animation.\n   */\n  stop() {\n    if (this.stopAnimation)\n      this.stopAnimation();\n    this.clearAnimation();\n  }\n  /**\n   * Returns `true` if this value is currently animating.\n   */\n  isAnimating() {\n    return !!this.stopAnimation;\n  }\n  /**\n   * Clear the current animation reference.\n   */\n  clearAnimation() {\n    this.stopAnimation = null;\n  }\n  /**\n   * Destroy and clean up subscribers to this `MotionValue`.\n   */\n  destroy() {\n    this.updateSubscribers.clear();\n    this.stop();\n  }\n}\nfunction getMotionValue(init) {\n  return new MotionValue(init);\n}\n\nconst { isArray } = Array;\nfunction useMotionValues() {\n  const motionValues = ref({});\n  const stop = (keys) => {\n    const destroyKey = (key) => {\n      if (!motionValues.value[key])\n        return;\n      motionValues.value[key].stop();\n      motionValues.value[key].destroy();\n      delete motionValues.value[key];\n    };\n    if (keys) {\n      if (isArray(keys)) {\n        keys.forEach(destroyKey);\n      } else {\n        destroyKey(keys);\n      }\n    } else {\n      Object.keys(motionValues.value).forEach(destroyKey);\n    }\n  };\n  const get = (key, from, target) => {\n    if (motionValues.value[key])\n      return motionValues.value[key];\n    const motionValue = getMotionValue(from);\n    motionValue.onChange((v) => target[key] = v);\n    motionValues.value[key] = motionValue;\n    return motionValue;\n  };\n  tryOnUnmounted(stop);\n  return {\n    motionValues,\n    get,\n    stop\n  };\n}\n\nfunction isKeyframesTarget(v) {\n  return Array.isArray(v);\n}\nfunction underDampedSpring() {\n  return {\n    type: \"spring\",\n    stiffness: 500,\n    damping: 25,\n    restDelta: 0.5,\n    restSpeed: 10\n  };\n}\nfunction criticallyDampedSpring(to) {\n  return {\n    type: \"spring\",\n    stiffness: 550,\n    damping: to === 0 ? 2 * Math.sqrt(550) : 30,\n    restDelta: 0.01,\n    restSpeed: 10\n  };\n}\nfunction overDampedSpring(to) {\n  return {\n    type: \"spring\",\n    stiffness: 550,\n    damping: to === 0 ? 100 : 30,\n    restDelta: 0.01,\n    restSpeed: 10\n  };\n}\nfunction linearTween() {\n  return {\n    type: \"keyframes\",\n    ease: \"linear\",\n    duration: 300\n  };\n}\nfunction keyframes(values) {\n  return {\n    type: \"keyframes\",\n    duration: 800,\n    values\n  };\n}\nconst defaultTransitions = {\n  default: overDampedSpring,\n  x: underDampedSpring,\n  y: underDampedSpring,\n  z: underDampedSpring,\n  rotate: underDampedSpring,\n  rotateX: underDampedSpring,\n  rotateY: underDampedSpring,\n  rotateZ: underDampedSpring,\n  scaleX: criticallyDampedSpring,\n  scaleY: criticallyDampedSpring,\n  scale: criticallyDampedSpring,\n  backgroundColor: linearTween,\n  color: linearTween,\n  opacity: linearTween\n};\nfunction getDefaultTransition(valueKey, to) {\n  let transitionFactory;\n  if (isKeyframesTarget(to)) {\n    transitionFactory = keyframes;\n  } else {\n    transitionFactory = defaultTransitions[valueKey] || defaultTransitions.default;\n  }\n  return { to, ...transitionFactory(to) };\n}\n\nconst int = {\n  ...number,\n  transform: Math.round\n};\nconst valueTypes = {\n  // Color props\n  color,\n  backgroundColor: color,\n  outlineColor: color,\n  fill: color,\n  stroke: color,\n  // Border props\n  borderColor: color,\n  borderTopColor: color,\n  borderRightColor: color,\n  borderBottomColor: color,\n  borderLeftColor: color,\n  borderWidth: px,\n  borderTopWidth: px,\n  borderRightWidth: px,\n  borderBottomWidth: px,\n  borderLeftWidth: px,\n  borderRadius: px,\n  radius: px,\n  borderTopLeftRadius: px,\n  borderTopRightRadius: px,\n  borderBottomRightRadius: px,\n  borderBottomLeftRadius: px,\n  // Positioning props\n  width: px,\n  maxWidth: px,\n  height: px,\n  maxHeight: px,\n  size: px,\n  top: px,\n  right: px,\n  bottom: px,\n  left: px,\n  // Spacing props\n  padding: px,\n  paddingTop: px,\n  paddingRight: px,\n  paddingBottom: px,\n  paddingLeft: px,\n  margin: px,\n  marginTop: px,\n  marginRight: px,\n  marginBottom: px,\n  marginLeft: px,\n  // Transform props\n  rotate: degrees,\n  rotateX: degrees,\n  rotateY: degrees,\n  rotateZ: degrees,\n  scale,\n  scaleX: scale,\n  scaleY: scale,\n  scaleZ: scale,\n  skew: degrees,\n  skewX: degrees,\n  skewY: degrees,\n  distance: px,\n  translateX: px,\n  translateY: px,\n  translateZ: px,\n  x: px,\n  y: px,\n  z: px,\n  perspective: px,\n  transformPerspective: px,\n  opacity: alpha,\n  originX: progressPercentage,\n  originY: progressPercentage,\n  originZ: px,\n  // Misc\n  zIndex: int,\n  filter,\n  WebkitFilter: filter,\n  // SVG\n  fillOpacity: alpha,\n  strokeOpacity: alpha,\n  numOctaves: int\n};\nconst getValueType = (key) => valueTypes[key];\nfunction getValueAsType(value, type) {\n  return type && typeof value === \"number\" && type.transform ? type.transform(value) : value;\n}\nfunction getAnimatableNone(key, value) {\n  let defaultValueType = getValueType(key);\n  if (defaultValueType !== filter)\n    defaultValueType = complex;\n  return defaultValueType.getAnimatableNone ? defaultValueType.getAnimatableNone(value) : void 0;\n}\n\nconst easingLookup = {\n  linear,\n  easeIn,\n  easeInOut,\n  easeOut,\n  circIn,\n  circInOut,\n  circOut,\n  backIn,\n  backInOut,\n  backOut,\n  anticipate,\n  bounceIn,\n  bounceInOut,\n  bounceOut\n};\nfunction easingDefinitionToFunction(definition) {\n  if (Array.isArray(definition)) {\n    const [x1, y1, x2, y2] = definition;\n    return cubicBezier(x1, y1, x2, y2);\n  } else if (typeof definition === \"string\") {\n    return easingLookup[definition];\n  }\n  return definition;\n}\nfunction isEasingArray(ease) {\n  return Array.isArray(ease) && typeof ease[0] !== \"number\";\n}\nfunction isAnimatable(key, value) {\n  if (key === \"zIndex\")\n    return false;\n  if (typeof value === \"number\" || Array.isArray(value))\n    return true;\n  if (typeof value === \"string\" && complex.test(value) && !value.startsWith(\"url(\")) {\n    return true;\n  }\n  return false;\n}\nfunction hydrateKeyframes(options) {\n  if (Array.isArray(options.to) && options.to[0] === null) {\n    options.to = [...options.to];\n    options.to[0] = options.from;\n  }\n  return options;\n}\nfunction convertTransitionToAnimationOptions({ ease, times, delay, ...transition }) {\n  const options = { ...transition };\n  if (times)\n    options.offset = times;\n  if (ease) {\n    options.ease = isEasingArray(ease) ? ease.map(easingDefinitionToFunction) : easingDefinitionToFunction(ease);\n  }\n  if (delay)\n    options.elapsed = -delay;\n  return options;\n}\nfunction getPopmotionAnimationOptions(transition, options, key) {\n  if (Array.isArray(options.to)) {\n    if (!transition.duration)\n      transition.duration = 800;\n  }\n  hydrateKeyframes(options);\n  if (!isTransitionDefined(transition)) {\n    transition = {\n      ...transition,\n      ...getDefaultTransition(key, options.to)\n    };\n  }\n  return {\n    ...options,\n    ...convertTransitionToAnimationOptions(transition)\n  };\n}\nfunction isTransitionDefined({ delay, repeat, repeatType, repeatDelay, from, ...transition }) {\n  return !!Object.keys(transition).length;\n}\nfunction getValueTransition(transition, key) {\n  return transition[key] || transition.default || transition;\n}\nfunction getAnimation(key, value, target, transition, onComplete) {\n  const valueTransition = getValueTransition(transition, key);\n  let origin = valueTransition.from === null || valueTransition.from === void 0 ? value.get() : valueTransition.from;\n  const isTargetAnimatable = isAnimatable(key, target);\n  if (origin === \"none\" && isTargetAnimatable && typeof target === \"string\")\n    origin = getAnimatableNone(key, target);\n  const isOriginAnimatable = isAnimatable(key, origin);\n  function start(complete) {\n    const options = {\n      from: origin,\n      to: target,\n      velocity: transition.velocity ? transition.velocity : value.getVelocity(),\n      onUpdate: (v) => value.set(v)\n    };\n    return valueTransition.type === \"inertia\" || valueTransition.type === \"decay\" ? inertia({ ...options, ...valueTransition }) : animate({\n      ...getPopmotionAnimationOptions(valueTransition, options, key),\n      onUpdate: (v) => {\n        options.onUpdate(v);\n        if (valueTransition.onUpdate)\n          valueTransition.onUpdate(v);\n      },\n      onComplete: () => {\n        if (onComplete)\n          onComplete();\n        if (complete)\n          complete();\n      }\n    });\n  }\n  function set(complete) {\n    value.set(target);\n    if (onComplete)\n      onComplete();\n    if (complete)\n      complete();\n    return { stop: () => {\n    } };\n  }\n  return !isOriginAnimatable || !isTargetAnimatable || valueTransition.type === false ? set : start;\n}\n\nfunction useMotionTransitions() {\n  const { motionValues, stop, get } = useMotionValues();\n  const push = (key, value, target, transition = {}, onComplete) => {\n    const from = target[key];\n    const motionValue = get(key, from, target);\n    if (transition && transition.immediate) {\n      motionValue.set(value);\n      return;\n    }\n    const animation = getAnimation(key, motionValue, value, transition, onComplete);\n    motionValue.start(animation);\n  };\n  return { motionValues, stop, push };\n}\n\nfunction useMotionControls(motionProperties, variants = {}, { motionValues, push, stop } = useMotionTransitions()) {\n  const _variants = unref(variants);\n  const isAnimating = ref(false);\n  watch(\n    motionValues,\n    (newVal) => {\n      isAnimating.value = Object.values(newVal).filter((value) => value.isAnimating()).length > 0;\n    },\n    {\n      immediate: true,\n      deep: true\n    }\n  );\n  const getVariantFromKey = (variant) => {\n    if (!_variants || !_variants[variant])\n      throw new Error(`The variant ${variant} does not exist.`);\n    return _variants[variant];\n  };\n  const apply = (variant) => {\n    if (typeof variant === \"string\")\n      variant = getVariantFromKey(variant);\n    const animations = Object.entries(variant).map(([key, value]) => {\n      if (key === \"transition\")\n        return void 0;\n      return new Promise(\n        (resolve) => (\n          // @ts-expect-error - Fix errors later for typescript 5\n          push(key, value, motionProperties, variant.transition || getDefaultTransition(key, variant[key]), resolve)\n        )\n      );\n    }).filter(Boolean);\n    async function waitForComplete() {\n      await Promise.all(animations);\n      variant.transition?.onComplete?.();\n    }\n    return Promise.all([waitForComplete()]);\n  };\n  const set = (variant) => {\n    const variantData = isObject$1(variant) ? variant : getVariantFromKey(variant);\n    Object.entries(variantData).forEach(([key, value]) => {\n      if (key === \"transition\")\n        return;\n      push(key, value, motionProperties, {\n        immediate: true\n      });\n    });\n  };\n  const leave = async (done) => {\n    let leaveVariant;\n    if (_variants) {\n      if (_variants.leave)\n        leaveVariant = _variants.leave;\n      if (!_variants.leave && _variants.initial)\n        leaveVariant = _variants.initial;\n    }\n    if (!leaveVariant) {\n      done();\n      return;\n    }\n    await apply(leaveVariant);\n    done();\n  };\n  return {\n    isAnimating,\n    apply,\n    set,\n    leave,\n    stop\n  };\n}\n\nconst isBrowser = typeof window !== \"undefined\";\nconst supportsPointerEvents = () => isBrowser && (window.onpointerdown === null || import.meta.env?.TEST);\nconst supportsTouchEvents = () => isBrowser && (window.ontouchstart === null || import.meta.env?.TEST);\nconst supportsMouseEvents = () => isBrowser && (window.onmousedown === null || import.meta.env?.TEST);\n\nfunction registerEventListeners({ target, state, variants, apply }) {\n  const _variants = unref(variants);\n  const hovered = ref(false);\n  const tapped = ref(false);\n  const focused = ref(false);\n  const mutableKeys = computed(() => {\n    let result = [...Object.keys(state.value || {})];\n    if (!_variants)\n      return result;\n    if (_variants.hovered)\n      result = [...result, ...Object.keys(_variants.hovered)];\n    if (_variants.tapped)\n      result = [...result, ...Object.keys(_variants.tapped)];\n    if (_variants.focused)\n      result = [...result, ...Object.keys(_variants.focused)];\n    return result;\n  });\n  const computedProperties = computed(() => {\n    const result = {};\n    Object.assign(result, state.value);\n    if (hovered.value && _variants.hovered)\n      Object.assign(result, _variants.hovered);\n    if (tapped.value && _variants.tapped)\n      Object.assign(result, _variants.tapped);\n    if (focused.value && _variants.focused)\n      Object.assign(result, _variants.focused);\n    for (const key in result) {\n      if (!mutableKeys.value.includes(key))\n        delete result[key];\n    }\n    return result;\n  });\n  if (_variants.hovered) {\n    useEventListener(target, \"mouseenter\", () => hovered.value = true);\n    useEventListener(target, \"mouseleave\", () => {\n      hovered.value = false;\n      tapped.value = false;\n    });\n  }\n  if (_variants.tapped) {\n    if (supportsMouseEvents()) {\n      useEventListener(target, \"mousedown\", () => tapped.value = true);\n      useEventListener(target, \"mouseup\", () => tapped.value = false);\n    }\n    if (supportsPointerEvents()) {\n      useEventListener(target, \"pointerdown\", () => tapped.value = true);\n      useEventListener(target, \"pointerup\", () => tapped.value = false);\n    }\n    if (supportsTouchEvents()) {\n      useEventListener(target, \"touchstart\", () => tapped.value = true);\n      useEventListener(target, \"touchend\", () => tapped.value = false);\n    }\n  }\n  if (_variants.focused) {\n    useEventListener(target, \"focus\", () => focused.value = true);\n    useEventListener(target, \"blur\", () => focused.value = false);\n  }\n  watch([hovered, tapped, focused], () => {\n    apply(computedProperties.value);\n  });\n}\n\nfunction registerLifeCycleHooks({ set, target, variants, variant }) {\n  const _variants = unref(variants);\n  watch(\n    () => target,\n    () => {\n      if (!_variants)\n        return;\n      if (_variants.initial) {\n        set(\"initial\");\n        variant.value = \"initial\";\n      }\n      if (_variants.enter)\n        variant.value = \"enter\";\n    },\n    {\n      immediate: true,\n      flush: \"pre\"\n    }\n  );\n}\n\nfunction registerVariantsSync({ state, apply }) {\n  watch(\n    state,\n    (newVal) => {\n      if (newVal)\n        apply(newVal);\n    },\n    {\n      immediate: true\n    }\n  );\n}\n\nfunction registerVisibilityHooks({ target, variants, variant }) {\n  const _variants = unref(variants);\n  if (_variants && (_variants.visible || _variants.visibleOnce)) {\n    useIntersectionObserver(target, ([{ isIntersecting }]) => {\n      if (_variants.visible) {\n        if (isIntersecting)\n          variant.value = \"visible\";\n        else variant.value = \"initial\";\n      } else if (_variants.visibleOnce) {\n        if (isIntersecting && variant.value !== \"visibleOnce\")\n          variant.value = \"visibleOnce\";\n        else if (!variant.value)\n          variant.value = \"initial\";\n      }\n    });\n  }\n}\n\nfunction useMotionFeatures(instance, options = {\n  syncVariants: true,\n  lifeCycleHooks: true,\n  visibilityHooks: true,\n  eventListeners: true\n}) {\n  if (options.lifeCycleHooks)\n    registerLifeCycleHooks(instance);\n  if (options.syncVariants)\n    registerVariantsSync(instance);\n  if (options.visibilityHooks)\n    registerVisibilityHooks(instance);\n  if (options.eventListeners)\n    registerEventListeners(instance);\n}\n\nfunction reactiveStyle(props = {}) {\n  const state = reactive({\n    ...props\n  });\n  const style = ref({});\n  watch(\n    state,\n    () => {\n      const result = {};\n      for (const [key, value] of Object.entries(state)) {\n        const valueType = getValueType(key);\n        const valueAsType = getValueAsType(value, valueType);\n        result[key] = valueAsType;\n      }\n      style.value = result;\n    },\n    {\n      immediate: true,\n      deep: true\n    }\n  );\n  return {\n    state,\n    style\n  };\n}\n\nfunction usePermissiveTarget(target, onTarget) {\n  watch(\n    () => unrefElement(target),\n    (el) => {\n      if (!el)\n        return;\n      onTarget(el);\n    },\n    {\n      immediate: true\n    }\n  );\n}\n\nconst translateAlias = {\n  x: \"translateX\",\n  y: \"translateY\",\n  z: \"translateZ\"\n};\nfunction reactiveTransform(props = {}, enableHardwareAcceleration = true) {\n  const state = reactive({ ...props });\n  const transform = ref(\"\");\n  watch(\n    state,\n    (newVal) => {\n      let result = \"\";\n      let hasHardwareAcceleration = false;\n      if (enableHardwareAcceleration && (newVal.x || newVal.y || newVal.z)) {\n        const str = [newVal.x || 0, newVal.y || 0, newVal.z || 0].map((val) => getValueAsType(val, px)).join(\",\");\n        result += `translate3d(${str}) `;\n        hasHardwareAcceleration = true;\n      }\n      for (const [key, value] of Object.entries(newVal)) {\n        if (enableHardwareAcceleration && (key === \"x\" || key === \"y\" || key === \"z\"))\n          continue;\n        const valueType = getValueType(key);\n        const valueAsType = getValueAsType(value, valueType);\n        result += `${translateAlias[key] || key}(${valueAsType}) `;\n      }\n      if (enableHardwareAcceleration && !hasHardwareAcceleration)\n        result += \"translateZ(0px) \";\n      transform.value = result.trim();\n    },\n    {\n      immediate: true,\n      deep: true\n    }\n  );\n  return {\n    state,\n    transform\n  };\n}\n\nconst transformAxes = [\"\", \"X\", \"Y\", \"Z\"];\nconst order = [\"perspective\", \"translate\", \"scale\", \"rotate\", \"skew\"];\nconst transformProps = [\"transformPerspective\", \"x\", \"y\", \"z\"];\norder.forEach((operationKey) => {\n  transformAxes.forEach((axesKey) => {\n    const key = operationKey + axesKey;\n    transformProps.push(key);\n  });\n});\nconst transformPropSet = new Set(transformProps);\nfunction isTransformProp(key) {\n  return transformPropSet.has(key);\n}\nconst transformOriginProps = /* @__PURE__ */ new Set([\"originX\", \"originY\", \"originZ\"]);\nfunction isTransformOriginProp(key) {\n  return transformOriginProps.has(key);\n}\nfunction splitValues(variant) {\n  const transform = {};\n  const style = {};\n  Object.entries(variant).forEach(([key, value]) => {\n    if (isTransformProp(key) || isTransformOriginProp(key))\n      transform[key] = value;\n    else style[key] = value;\n  });\n  return { transform, style };\n}\nfunction variantToStyle(variant) {\n  const { transform: _transform, style: _style } = splitValues(variant);\n  const { transform } = reactiveTransform(_transform);\n  const { style } = reactiveStyle(_style);\n  if (transform.value)\n    style.value.transform = transform.value;\n  return style.value;\n}\n\nfunction useElementStyle(target, onInit) {\n  let _cache;\n  let _target;\n  const { state, style } = reactiveStyle();\n  usePermissiveTarget(target, (el) => {\n    _target = el;\n    for (const key of Object.keys(valueTypes)) {\n      if (el.style[key] === null || el.style[key] === \"\" || isTransformProp(key) || isTransformOriginProp(key))\n        continue;\n      state[key] = el.style[key];\n    }\n    if (_cache) {\n      Object.entries(_cache).forEach(([key, value]) => el.style[key] = value);\n    }\n    if (onInit)\n      onInit(state);\n  });\n  watch(\n    style,\n    (newVal) => {\n      if (!_target) {\n        _cache = newVal;\n        return;\n      }\n      for (const key in newVal) _target.style[key] = newVal[key];\n    },\n    {\n      immediate: true\n    }\n  );\n  return {\n    style: state\n  };\n}\n\nfunction parseTransform(transform) {\n  const transforms = transform.trim().split(/\\) |\\)/);\n  if (transforms.length === 1)\n    return {};\n  const parseValues = (value) => {\n    if (value.endsWith(\"px\") || value.endsWith(\"deg\"))\n      return Number.parseFloat(value);\n    if (Number.isNaN(Number(value)))\n      return Number(value);\n    return value;\n  };\n  return transforms.reduce((acc, transform2) => {\n    if (!transform2)\n      return acc;\n    const [name, transformValue] = transform2.split(\"(\");\n    const valueArray = transformValue.split(\",\");\n    const values = valueArray.map((val) => {\n      return parseValues(val.endsWith(\")\") ? val.replace(\")\", \"\") : val.trim());\n    });\n    const value = values.length === 1 ? values[0] : values;\n    return {\n      ...acc,\n      [name]: value\n    };\n  }, {});\n}\nfunction stateFromTransform(state, transform) {\n  Object.entries(parseTransform(transform)).forEach(([key, value]) => {\n    const axes = [\"x\", \"y\", \"z\"];\n    if (key === \"translate3d\") {\n      if (value === 0) {\n        axes.forEach((axis) => state[axis] = 0);\n        return;\n      }\n      value.forEach((axisValue, index) => state[axes[index]] = axisValue);\n      return;\n    }\n    value = Number.parseFloat(`${value}`);\n    if (key === \"translateX\") {\n      state.x = value;\n      return;\n    }\n    if (key === \"translateY\") {\n      state.y = value;\n      return;\n    }\n    if (key === \"translateZ\") {\n      state.z = value;\n      return;\n    }\n    state[key] = value;\n  });\n}\n\nfunction useElementTransform(target, onInit) {\n  let _cache;\n  let _target;\n  const { state, transform } = reactiveTransform();\n  usePermissiveTarget(target, (el) => {\n    _target = el;\n    if (el.style.transform)\n      stateFromTransform(state, el.style.transform);\n    if (_cache)\n      el.style.transform = _cache;\n    if (onInit)\n      onInit(state);\n  });\n  watch(\n    transform,\n    (newValue) => {\n      if (!_target) {\n        _cache = newValue;\n        return;\n      }\n      _target.style.transform = newValue;\n    },\n    {\n      immediate: true\n    }\n  );\n  return {\n    transform: state\n  };\n}\n\nfunction objectEntries(obj) {\n  return Object.entries(obj);\n}\n\nfunction useMotionProperties(target, defaultValues) {\n  const motionProperties = reactive({});\n  const apply = (values) => Object.entries(values).forEach(([key, value]) => motionProperties[key] = value);\n  const { style } = useElementStyle(target, apply);\n  const { transform } = useElementTransform(target, apply);\n  watch(\n    motionProperties,\n    (newVal) => {\n      objectEntries(newVal).forEach(([key, value]) => {\n        const target2 = isTransformProp(key) ? transform : style;\n        if (target2[key] && target2[key] === value)\n          return;\n        target2[key] = value;\n      });\n    },\n    {\n      immediate: true,\n      deep: true\n    }\n  );\n  usePermissiveTarget(target, () => defaultValues && apply(defaultValues));\n  return {\n    motionProperties,\n    style,\n    transform\n  };\n}\n\nfunction useMotionVariants(variants = {}) {\n  const _variants = unref(variants);\n  const variant = ref();\n  const state = computed(() => {\n    if (!variant.value)\n      return;\n    return _variants[variant.value];\n  });\n  return {\n    state,\n    variant\n  };\n}\n\nfunction useMotion(target, variants = {}, options) {\n  const { motionProperties } = useMotionProperties(target);\n  const { variant, state } = useMotionVariants(variants);\n  const controls = useMotionControls(motionProperties, variants);\n  const instance = {\n    target,\n    variant,\n    variants,\n    state,\n    motionProperties,\n    ...controls\n  };\n  useMotionFeatures(instance, options);\n  return instance;\n}\n\nconst transitionKeys = [\"delay\", \"duration\"];\nconst directivePropsKeys = [\"initial\", \"enter\", \"leave\", \"visible\", \"visible-once\", \"visibleOnce\", \"hovered\", \"tapped\", \"focused\", ...transitionKeys];\nfunction isTransitionKey(val) {\n  return transitionKeys.includes(val);\n}\nfunction resolveVariants(node, variantsRef) {\n  const target = node.props ? node.props : node.data && node.data.attrs ? node.data.attrs : {};\n  if (target) {\n    if (target.variants && isObject$1(target.variants)) {\n      variantsRef.value = {\n        ...variantsRef.value,\n        ...target.variants\n      };\n    }\n    for (let key of directivePropsKeys) {\n      if (!target || !target[key])\n        continue;\n      if (isTransitionKey(key) && typeof target[key] === \"number\") {\n        for (const variantKey of [\"enter\", \"visible\", \"visibleOnce\"]) {\n          const variantConfig = variantsRef.value[variantKey];\n          if (variantConfig == null)\n            continue;\n          variantConfig.transition ??= {};\n          variantConfig.transition[key] = target[key];\n        }\n        continue;\n      }\n      if (isObject$1(target[key])) {\n        const prop = target[key];\n        if (key === \"visible-once\")\n          key = \"visibleOnce\";\n        variantsRef.value[key] = prop;\n      }\n    }\n  }\n}\n\nfunction directive(variants, isPreset = false) {\n  const register = (el, binding, node) => {\n    const key = binding.value && typeof binding.value === \"string\" ? binding.value : node.key;\n    if (key && motionState[key])\n      motionState[key].stop();\n    const variantsObject = isPreset ? structuredClone(toRaw(variants) || {}) : variants || {};\n    const variantsRef = ref(variantsObject);\n    if (typeof binding.value === \"object\")\n      variantsRef.value = binding.value;\n    resolveVariants(node, variantsRef);\n    const motionOptions = { eventListeners: true, lifeCycleHooks: true, syncVariants: true, visibilityHooks: false };\n    const motionInstance = useMotion(\n      el,\n      variantsRef,\n      motionOptions\n    );\n    el.motionInstance = motionInstance;\n    if (key)\n      motionState[key] = motionInstance;\n  };\n  const mounted = (el, _binding, _node) => {\n    el.motionInstance && registerVisibilityHooks(el.motionInstance);\n  };\n  return {\n    created: register,\n    mounted,\n    getSSRProps(binding, node) {\n      let { initial: bindingInitial } = binding.value || node && node?.props || {};\n      bindingInitial = unref(bindingInitial);\n      const initial = defu({}, variants?.initial || {}, bindingInitial || {});\n      if (!initial || Object.keys(initial).length === 0)\n        return;\n      const style = variantToStyle(initial);\n      return {\n        style\n      };\n    }\n  };\n}\n\nconst fade = {\n  initial: {\n    opacity: 0\n  },\n  enter: {\n    opacity: 1\n  }\n};\nconst fadeVisible = {\n  initial: {\n    opacity: 0\n  },\n  visible: {\n    opacity: 1\n  }\n};\nconst fadeVisibleOnce = {\n  initial: {\n    opacity: 0\n  },\n  visibleOnce: {\n    opacity: 1\n  }\n};\n\nconst pop = {\n  initial: {\n    scale: 0,\n    opacity: 0\n  },\n  enter: {\n    scale: 1,\n    opacity: 1\n  }\n};\nconst popVisible = {\n  initial: {\n    scale: 0,\n    opacity: 0\n  },\n  visible: {\n    scale: 1,\n    opacity: 1\n  }\n};\nconst popVisibleOnce = {\n  initial: {\n    scale: 0,\n    opacity: 0\n  },\n  visibleOnce: {\n    scale: 1,\n    opacity: 1\n  }\n};\n\nconst rollLeft = {\n  initial: {\n    x: -100,\n    rotate: 90,\n    opacity: 0\n  },\n  enter: {\n    x: 0,\n    rotate: 0,\n    opacity: 1\n  }\n};\nconst rollVisibleLeft = {\n  initial: {\n    x: -100,\n    rotate: 90,\n    opacity: 0\n  },\n  visible: {\n    x: 0,\n    rotate: 0,\n    opacity: 1\n  }\n};\nconst rollVisibleOnceLeft = {\n  initial: {\n    x: -100,\n    rotate: 90,\n    opacity: 0\n  },\n  visibleOnce: {\n    x: 0,\n    rotate: 0,\n    opacity: 1\n  }\n};\nconst rollRight = {\n  initial: {\n    x: 100,\n    rotate: -90,\n    opacity: 0\n  },\n  enter: {\n    x: 0,\n    rotate: 0,\n    opacity: 1\n  }\n};\nconst rollVisibleRight = {\n  initial: {\n    x: 100,\n    rotate: -90,\n    opacity: 0\n  },\n  visible: {\n    x: 0,\n    rotate: 0,\n    opacity: 1\n  }\n};\nconst rollVisibleOnceRight = {\n  initial: {\n    x: 100,\n    rotate: -90,\n    opacity: 0\n  },\n  visibleOnce: {\n    x: 0,\n    rotate: 0,\n    opacity: 1\n  }\n};\nconst rollTop = {\n  initial: {\n    y: -100,\n    rotate: -90,\n    opacity: 0\n  },\n  enter: {\n    y: 0,\n    rotate: 0,\n    opacity: 1\n  }\n};\nconst rollVisibleTop = {\n  initial: {\n    y: -100,\n    rotate: -90,\n    opacity: 0\n  },\n  visible: {\n    y: 0,\n    rotate: 0,\n    opacity: 1\n  }\n};\nconst rollVisibleOnceTop = {\n  initial: {\n    y: -100,\n    rotate: -90,\n    opacity: 0\n  },\n  visibleOnce: {\n    y: 0,\n    rotate: 0,\n    opacity: 1\n  }\n};\nconst rollBottom = {\n  initial: {\n    y: 100,\n    rotate: 90,\n    opacity: 0\n  },\n  enter: {\n    y: 0,\n    rotate: 0,\n    opacity: 1\n  }\n};\nconst rollVisibleBottom = {\n  initial: {\n    y: 100,\n    rotate: 90,\n    opacity: 0\n  },\n  visible: {\n    y: 0,\n    rotate: 0,\n    opacity: 1\n  }\n};\nconst rollVisibleOnceBottom = {\n  initial: {\n    y: 100,\n    rotate: 90,\n    opacity: 0\n  },\n  visibleOnce: {\n    y: 0,\n    rotate: 0,\n    opacity: 1\n  }\n};\n\nconst slideLeft = {\n  initial: {\n    x: -100,\n    opacity: 0\n  },\n  enter: {\n    x: 0,\n    opacity: 1\n  }\n};\nconst slideVisibleLeft = {\n  initial: {\n    x: -100,\n    opacity: 0\n  },\n  visible: {\n    x: 0,\n    opacity: 1\n  }\n};\nconst slideVisibleOnceLeft = {\n  initial: {\n    x: -100,\n    opacity: 0\n  },\n  visibleOnce: {\n    x: 0,\n    opacity: 1\n  }\n};\nconst slideRight = {\n  initial: {\n    x: 100,\n    opacity: 0\n  },\n  enter: {\n    x: 0,\n    opacity: 1\n  }\n};\nconst slideVisibleRight = {\n  initial: {\n    x: 100,\n    opacity: 0\n  },\n  visible: {\n    x: 0,\n    opacity: 1\n  }\n};\nconst slideVisibleOnceRight = {\n  initial: {\n    x: 100,\n    opacity: 0\n  },\n  visibleOnce: {\n    x: 0,\n    opacity: 1\n  }\n};\nconst slideTop = {\n  initial: {\n    y: -100,\n    opacity: 0\n  },\n  enter: {\n    y: 0,\n    opacity: 1\n  }\n};\nconst slideVisibleTop = {\n  initial: {\n    y: -100,\n    opacity: 0\n  },\n  visible: {\n    y: 0,\n    opacity: 1\n  }\n};\nconst slideVisibleOnceTop = {\n  initial: {\n    y: -100,\n    opacity: 0\n  },\n  visibleOnce: {\n    y: 0,\n    opacity: 1\n  }\n};\nconst slideBottom = {\n  initial: {\n    y: 100,\n    opacity: 0\n  },\n  enter: {\n    y: 0,\n    opacity: 1\n  }\n};\nconst slideVisibleBottom = {\n  initial: {\n    y: 100,\n    opacity: 0\n  },\n  visible: {\n    y: 0,\n    opacity: 1\n  }\n};\nconst slideVisibleOnceBottom = {\n  initial: {\n    y: 100,\n    opacity: 0\n  },\n  visibleOnce: {\n    y: 0,\n    opacity: 1\n  }\n};\n\nconst presets = {\n  __proto__: null,\n  fade: fade,\n  fadeVisible: fadeVisible,\n  fadeVisibleOnce: fadeVisibleOnce,\n  pop: pop,\n  popVisible: popVisible,\n  popVisibleOnce: popVisibleOnce,\n  rollBottom: rollBottom,\n  rollLeft: rollLeft,\n  rollRight: rollRight,\n  rollTop: rollTop,\n  rollVisibleBottom: rollVisibleBottom,\n  rollVisibleLeft: rollVisibleLeft,\n  rollVisibleOnceBottom: rollVisibleOnceBottom,\n  rollVisibleOnceLeft: rollVisibleOnceLeft,\n  rollVisibleOnceRight: rollVisibleOnceRight,\n  rollVisibleOnceTop: rollVisibleOnceTop,\n  rollVisibleRight: rollVisibleRight,\n  rollVisibleTop: rollVisibleTop,\n  slideBottom: slideBottom,\n  slideLeft: slideLeft,\n  slideRight: slideRight,\n  slideTop: slideTop,\n  slideVisibleBottom: slideVisibleBottom,\n  slideVisibleLeft: slideVisibleLeft,\n  slideVisibleOnceBottom: slideVisibleOnceBottom,\n  slideVisibleOnceLeft: slideVisibleOnceLeft,\n  slideVisibleOnceRight: slideVisibleOnceRight,\n  slideVisibleOnceTop: slideVisibleOnceTop,\n  slideVisibleRight: slideVisibleRight,\n  slideVisibleTop: slideVisibleTop\n};\n\nfunction slugify(str) {\n  const a = \"\\xE0\\xE1\\xE2\\xE4\\xE6\\xE3\\xE5\\u0101\\u0103\\u0105\\xE7\\u0107\\u010D\\u0111\\u010F\\xE8\\xE9\\xEA\\xEB\\u0113\\u0117\\u0119\\u011B\\u011F\\u01F5\\u1E27\\xEE\\xEF\\xED\\u012B\\u012F\\xEC\\u0142\\u1E3F\\xF1\\u0144\\u01F9\\u0148\\xF4\\xF6\\xF2\\xF3\\u0153\\xF8\\u014D\\xF5\\u0151\\u1E55\\u0155\\u0159\\xDF\\u015B\\u0161\\u015F\\u0219\\u0165\\u021B\\xFB\\xFC\\xF9\\xFA\\u016B\\u01D8\\u016F\\u0171\\u0173\\u1E83\\u1E8D\\xFF\\xFD\\u017E\\u017A\\u017C\\xB7/_,:;\";\n  const b = \"aaaaaaaaaacccddeeeeeeeegghiiiiiilmnnnnoooooooooprrsssssttuuuuuuuuuwxyyzzz------\";\n  const p = new RegExp(a.split(\"\").join(\"|\"), \"g\");\n  return str.toString().replace(/[A-Z]/g, (s) => `-${s}`).toLowerCase().replace(/\\s+/g, \"-\").replace(p, (c) => b.charAt(a.indexOf(c))).replace(/&/g, \"-and-\").replace(/[^\\w\\-]+/g, \"\").replace(/-{2,}/g, \"-\").replace(/^-+/, \"\").replace(/-+$/, \"\");\n}\n\nconst CUSTOM_PRESETS = Symbol(\n  import.meta.env?.MODE === \"development\" ? \"motionCustomPresets\" : \"\"\n);\n\nconst MotionComponentProps = {\n  // Preset to be loaded\n  preset: {\n    type: String,\n    required: false\n  },\n  // Instance\n  instance: {\n    type: Object,\n    required: false\n  },\n  // Variants\n  variants: {\n    type: Object,\n    required: false\n  },\n  // Initial variant\n  initial: {\n    type: Object,\n    required: false\n  },\n  // Lifecycle hooks variants\n  enter: {\n    type: Object,\n    required: false\n  },\n  leave: {\n    type: Object,\n    required: false\n  },\n  // Intersection observer variants\n  visible: {\n    type: Object,\n    required: false\n  },\n  visibleOnce: {\n    type: Object,\n    required: false\n  },\n  // Event listeners variants\n  hovered: {\n    type: Object,\n    required: false\n  },\n  tapped: {\n    type: Object,\n    required: false\n  },\n  focused: {\n    type: Object,\n    required: false\n  },\n  // Helpers\n  delay: {\n    type: [Number, String],\n    required: false\n  },\n  duration: {\n    type: [Number, String],\n    required: false\n  }\n};\nfunction isObject(val) {\n  return Object.prototype.toString.call(val) === \"[object Object]\";\n}\nfunction clone(v) {\n  if (Array.isArray(v)) {\n    return v.map(clone);\n  }\n  if (isObject(v)) {\n    const res = {};\n    for (const key in v) {\n      res[key] = clone(v[key]);\n    }\n    return res;\n  }\n  return v;\n}\nfunction setupMotionComponent(props) {\n  const instances = reactive({});\n  const customPresets = inject(CUSTOM_PRESETS, {});\n  const preset = computed(() => {\n    if (props.preset == null) {\n      return {};\n    }\n    if (customPresets != null && props.preset in customPresets) {\n      return structuredClone(toRaw(customPresets)[props.preset]);\n    }\n    if (props.preset in presets) {\n      return structuredClone(presets[props.preset]);\n    }\n    return {};\n  });\n  const propsConfig = computed(() => ({\n    initial: props.initial,\n    enter: props.enter,\n    leave: props.leave,\n    visible: props.visible,\n    visibleOnce: props.visibleOnce,\n    hovered: props.hovered,\n    tapped: props.tapped,\n    focused: props.focused\n  }));\n  function applyTransitionHelpers(config, values) {\n    for (const transitionKey of [\"delay\", \"duration\"]) {\n      if (values[transitionKey] == null)\n        continue;\n      const transitionValueParsed = Number.parseInt(\n        values[transitionKey]\n      );\n      for (const variantKey of [\"enter\", \"visible\", \"visibleOnce\"]) {\n        const variantConfig = config[variantKey];\n        if (variantConfig == null)\n          continue;\n        variantConfig.transition ??= {};\n        variantConfig.transition[transitionKey] = transitionValueParsed;\n      }\n    }\n    return config;\n  }\n  const motionConfig = computed(() => {\n    const config = defu(\n      {},\n      propsConfig.value,\n      preset.value,\n      props.variants || {}\n    );\n    return applyTransitionHelpers({ ...config }, props);\n  });\n  if (import.meta.env?.MODE === \"development\") {\n    if (props.preset != null && presets?.[props.preset] == null && customPresets?.[props.preset] == null) {\n      console.warn(`[@vueuse/motion]: Preset \\`${props.preset}\\` not found.`);\n    }\n    const replayAnimation = (instance) => {\n      if (instance.variants?.initial) {\n        instance.set(\"initial\");\n      }\n      nextTick(() => {\n        if (instance.variants?.enter)\n          instance.apply(\"enter\");\n        if (instance.variants?.visible)\n          instance.apply(\"visible\");\n        if (instance.variants?.visibleOnce)\n          instance.apply(\"visibleOnce\");\n      });\n    };\n    onUpdated(() => {\n      for (const key in instances) {\n        replayAnimation(instances[key]);\n      }\n    });\n  }\n  function setNodeInstance(node, index, style) {\n    node.props ??= {};\n    node.props.style ??= {};\n    node.props.style = { ...node.props.style, ...style };\n    const elementMotionConfig = applyTransitionHelpers(\n      clone(motionConfig.value),\n      node.props\n    );\n    node.props.onVnodeMounted = ({ el }) => {\n      instances[index] = useMotion(\n        el,\n        elementMotionConfig\n      );\n    };\n    node.props.onVnodeUpdated = ({ el }) => {\n      const styles = variantToStyle(instances[index].state);\n      for (const [key, val] of Object.entries(styles)) {\n        el.style[key] = val;\n      }\n    };\n    return node;\n  }\n  return {\n    motionConfig,\n    setNodeInstance\n  };\n}\n\nconst MotionComponent = defineComponent({\n  name: \"Motion\",\n  props: {\n    ...MotionComponentProps,\n    is: {\n      type: [String, Object],\n      default: \"div\"\n    }\n  },\n  setup(props) {\n    const slots = useSlots();\n    const { motionConfig, setNodeInstance } = setupMotionComponent(props);\n    return () => {\n      const style = variantToStyle(motionConfig.value.initial || {});\n      const node = h(props.is, void 0, slots);\n      setNodeInstance(node, 0, style);\n      return node;\n    };\n  }\n});\n\nconst MotionGroupComponent = defineComponent({\n  name: \"MotionGroup\",\n  props: {\n    ...MotionComponentProps,\n    is: {\n      type: [String, Object],\n      required: false\n    }\n  },\n  setup(props) {\n    const slots = useSlots();\n    const { motionConfig, setNodeInstance } = setupMotionComponent(props);\n    return () => {\n      const style = variantToStyle(motionConfig.value.initial || {});\n      const nodes = slots.default?.() || [];\n      for (let i = 0; i < nodes.length; i++) {\n        const n = nodes[i];\n        if (n.type === Fragment && Array.isArray(n.children)) {\n          n.children.forEach(function setChildInstance(child, index) {\n            if (child == null)\n              return;\n            if (Array.isArray(child)) {\n              setChildInstance(child, index);\n              return;\n            }\n            if (typeof child === \"object\") {\n              setNodeInstance(child, index, style);\n            }\n          });\n        } else {\n          setNodeInstance(n, i, style);\n        }\n      }\n      if (props.is) {\n        return h(props.is, void 0, nodes);\n      }\n      return nodes;\n    };\n  }\n});\n\nconst MotionPlugin = {\n  install(app, options) {\n    app.directive(\"motion\", directive());\n    if (!options || options && !options.excludePresets) {\n      for (const key in presets) {\n        const preset = presets[key];\n        app.directive(`motion-${slugify(key)}`, directive(preset, true));\n      }\n    }\n    if (options && options.directives) {\n      for (const key in options.directives) {\n        const variants = options.directives[key];\n        if (!variants.initial && import.meta.env?.MODE === \"development\") {\n          console.warn(\n            `Your directive v-motion-${key} is missing initial variant!`\n          );\n        }\n        app.directive(`motion-${key}`, directive(variants, true));\n      }\n    }\n    app.provide(CUSTOM_PRESETS, options?.directives);\n    app.component(\"Motion\", MotionComponent);\n    app.component(\"MotionGroup\", MotionGroupComponent);\n  }\n};\n\nfunction isMotionInstance(obj) {\n  const _obj = obj;\n  return _obj.apply !== void 0 && typeof _obj.apply === \"function\" && _obj.set !== void 0 && typeof _obj.set === \"function\" && _obj.target !== void 0 && isRef(_obj.target);\n}\n\nfunction useMotions() {\n  return motionState;\n}\n\nfunction useSpring(values, spring) {\n  const { stop, get } = useMotionValues();\n  return {\n    values,\n    stop,\n    set: (properties) => Promise.all(\n      Object.entries(properties).map(([key, value]) => {\n        const motionValue = get(key, values[key], values);\n        return motionValue.start((onComplete) => {\n          const options = {\n            type: \"spring\",\n            ...spring || getDefaultTransition(key, value)\n          };\n          return animate({\n            from: motionValue.get(),\n            to: value,\n            velocity: motionValue.getVelocity(),\n            onUpdate: (v) => motionValue.set(v),\n            onComplete,\n            ...options\n          });\n        });\n      })\n    )\n  };\n}\n\nfunction useReducedMotion(options = {}) {\n  const reducedMotion = useMediaQuery(\"(prefers-reduced-motion: reduce)\", options);\n  return computed(() => reducedMotion.value);\n}\n\nexport { MotionComponent, directive as MotionDirective, MotionGroupComponent, MotionPlugin, fade, fadeVisible, fadeVisibleOnce, isMotionInstance, pop, popVisible, popVisibleOnce, reactiveStyle, reactiveTransform, rollBottom, rollLeft, rollRight, rollTop, rollVisibleBottom, rollVisibleLeft, rollVisibleOnceBottom, rollVisibleOnceLeft, rollVisibleOnceRight, rollVisibleOnceTop, rollVisibleRight, rollVisibleTop, slideBottom, slideLeft, slideRight, slideTop, slideVisibleBottom, slideVisibleLeft, slideVisibleOnceBottom, slideVisibleOnceLeft, slideVisibleOnceRight, slideVisibleOnceTop, slideVisibleRight, slideVisibleTop, slugify, useElementStyle, useElementTransform, useMotion, useMotionControls, useMotionFeatures, useMotionProperties, useMotionTransitions, useMotionVariants, useMotions, useReducedMotion, useSpring };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiSA,IAAM,WAAW,OAAO,WAAW,eAAe,OAAO,aAAa;AACtE,IAAM,WAAW,OAAO,sBAAsB,eAAe,sBAAsB;AAoBnF,IAAM,QAAwB,SAAS;AACvC,SAAS,WAAW;AAClB,MAAI,IAAI;AACR,SAAO,cAAc,KAAK,UAAU,OAAO,SAAS,OAAO,cAAc,OAAO,SAAS,GAAG,eAAe,mBAAmB,KAAK,OAAO,UAAU,SAAS,OAAO,KAAK,UAAU,OAAO,SAAS,OAAO,cAAc,OAAO,SAAS,GAAG,kBAAkB,KAAK,iBAAiB,KAAK,UAAU,OAAO,SAAS,OAAO,UAAU,SAAS;AAC9U;AA2OA,SAAS,oBAAoB,IAAI;AAC/B,QAAM,QAAwB,uBAAO,OAAO,IAAI;AAChD,SAAO,CAAC,QAAQ;AACd,UAAM,MAAM,MAAM,GAAG;AACrB,WAAO,QAAQ,MAAM,GAAG,IAAI,GAAG,GAAG;AAAA,EACpC;AACF;AACA,IAAM,cAAc;AACpB,IAAM,YAAY,oBAAoB,CAAC,QAAQ,IAAI,QAAQ,aAAa,KAAK,EAAE,YAAY,CAAC;AAC5F,IAAM,aAAa;AACnB,IAAM,WAAW,oBAAoB,CAAC,QAAQ;AAC5C,SAAO,IAAI,QAAQ,YAAY,CAAC,GAAGA,OAAMA,KAAIA,GAAE,YAAY,IAAI,EAAE;AACnE,CAAC;AAED,SAAS,mBAAmB,QAAQ;AAClC,SAAO,UAAU,mBAAmB;AACtC;AAiQA,SAAS,eAAe,IAAI,QAAQ;AAClC,QAAM,WAAW,mBAAmB,MAAM;AAC1C,MAAI;AACF,gBAAY,IAAI,MAAM;AAC1B;;;AC1zBA,IAAM,kBAAmB,IAAI,KAAM;AACnC,IAAM,iBAAiB,OAAO,gBAAgB,cACxC,MAAM,YAAY,IAAI,IACtB,MAAM,KAAK,IAAI;AACrB,IAAM,cAAc,OAAO,WAAW,cAChC,CAAC,aAAa,OAAO,sBAAsB,QAAQ,IACnD,CAAC,aAAa,WAAW,MAAM,SAAS,eAAe,CAAC,GAAG,eAAe;;;ACNhF,SAAS,iBAAiBC,eAAc;AACpC,MAAI,QAAQ,CAAC;AACb,MAAI,iBAAiB,CAAC;AACtB,MAAI,WAAW;AACf,MAAIC,gBAAe;AACnB,MAAI,iBAAiB;AACrB,QAAM,cAAc,oBAAI,QAAQ;AAChC,QAAM,OAAO;AAAA,IACT,UAAU,CAAC,UAAU,YAAY,OAAO,YAAY,UAAU;AAC1D,YAAM,oBAAoB,aAAaA;AACvC,YAAM,SAAS,oBAAoB,QAAQ;AAC3C,UAAI;AACA,oBAAY,IAAI,QAAQ;AAC5B,UAAI,OAAO,QAAQ,QAAQ,MAAM,IAAI;AACjC,eAAO,KAAK,QAAQ;AACpB,YAAI,qBAAqBA;AACrB,qBAAW,MAAM;AAAA,MACzB;AACA,aAAO;AAAA,IACX;AAAA,IACA,QAAQ,CAAC,aAAa;AAClB,YAAM,QAAQ,eAAe,QAAQ,QAAQ;AAC7C,UAAI,UAAU;AACV,uBAAe,OAAO,OAAO,CAAC;AAClC,kBAAY,OAAO,QAAQ;AAAA,IAC/B;AAAA,IACA,SAAS,CAAC,cAAc;AACpB,UAAIA,eAAc;AACd,yBAAiB;AACjB;AAAA,MACJ;AACA,MAAAA,gBAAe;AACf,OAAC,OAAO,cAAc,IAAI,CAAC,gBAAgB,KAAK;AAChD,qBAAe,SAAS;AACxB,iBAAW,MAAM;AACjB,UAAI,UAAU;AACV,iBAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AAC/B,gBAAM,WAAW,MAAM,CAAC;AACxB,mBAAS,SAAS;AAClB,cAAI,YAAY,IAAI,QAAQ,GAAG;AAC3B,iBAAK,SAAS,QAAQ;AACtB,YAAAD,cAAa;AAAA,UACjB;AAAA,QACJ;AAAA,MACJ;AACA,MAAAC,gBAAe;AACf,UAAI,gBAAgB;AAChB,yBAAiB;AACjB,aAAK,QAAQ,SAAS;AAAA,MAC1B;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;;;AClDA,IAAM,aAAa;AACnB,IAAI,oBAAoB;AACxB,IAAI,eAAe;AACnB,IAAI,eAAe;AACnB,IAAM,QAAQ;AAAA,EACV,OAAO;AAAA,EACP,WAAW;AACf;AACA,IAAM,aAAa;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACA,IAAM,QAAQ,WAAW,OAAO,CAAC,KAAK,QAAQ;AAC1C,MAAI,GAAG,IAAI,iBAAiB,MAAO,eAAe,IAAK;AACvD,SAAO;AACX,GAAG,CAAC,CAAC;AACL,IAAM,OAAO,WAAW,OAAO,CAAC,KAAK,QAAQ;AACzC,QAAM,OAAO,MAAM,GAAG;AACtB,MAAI,GAAG,IAAI,CAACC,UAAS,YAAY,OAAO,YAAY,UAAU;AAC1D,QAAI,CAAC;AACD,gBAAU;AACd,WAAO,KAAK,SAASA,UAAS,WAAW,SAAS;AAAA,EACtD;AACA,SAAO;AACX,GAAG,CAAC,CAAC;AACL,IAAM,aAAa,WAAW,OAAO,CAAC,KAAK,QAAQ;AAC/C,MAAI,GAAG,IAAI,MAAM,GAAG,EAAE;AACtB,SAAO;AACX,GAAG,CAAC,CAAC;AACL,IAAM,YAAY,WAAW,OAAO,CAAC,KAAK,QAAQ;AAC9C,MAAI,GAAG,IAAI,MAAM,MAAM,GAAG,EAAE,QAAQ,KAAK;AACzC,SAAO;AACX,GAAG,CAAC,CAAC;AACL,IAAM,cAAc,CAAC,WAAW,MAAM,MAAM,EAAE,QAAQ,KAAK;AAC3D,IAAM,eAAe,CAAC,cAAc;AAChC,iBAAe;AACf,QAAM,QAAQ,oBACR,kBACA,KAAK,IAAI,KAAK,IAAI,YAAY,MAAM,WAAW,UAAU,GAAG,CAAC;AACnE,QAAM,YAAY;AAClB,iBAAe;AACf,aAAW,QAAQ,WAAW;AAC9B,iBAAe;AACf,MAAI,cAAc;AACd,wBAAoB;AACpB,gBAAY,YAAY;AAAA,EAC5B;AACJ;AACA,IAAM,YAAY,MAAM;AACpB,iBAAe;AACf,sBAAoB;AACpB,MAAI,CAAC;AACD,gBAAY,YAAY;AAChC;AACA,IAAM,eAAe,MAAM;AAE3B,IAAO,aAAQ;;;ACpBR,SAAS,OAAO,GAAG,GAAG;AACzB,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI;AAC9E,MAAE,CAAC,IAAI,EAAE,CAAC;AACd,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,aAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AACpE,UAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC;AACzE,UAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,IACxB;AACJ,SAAO;AACX;;;ACpDA,IAAI,UAAU,WAAY;AAAE;AAC5B,IAAI,YAAY,WAAY;AAAE;AAC9B,IAAI,MAAuC;AACvC,YAAU,SAAU,OAAO,SAAS;AAChC,QAAI,CAAC,SAAS,OAAO,YAAY,aAAa;AAC1C,cAAQ,KAAK,OAAO;AAAA,IACxB;AAAA,EACJ;AACA,cAAY,SAAU,OAAO,SAAS;AAClC,QAAI,CAAC,OAAO;AACR,YAAM,IAAI,MAAM,OAAO;AAAA,IAC3B;AAAA,EACJ;AACJ;;;ACbA,IAAM,QAAQ,CAAC,KAAK,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,GAAG,GAAG,GAAG,GAAG;;;ACG7D,IAAM,UAAU;AAChB,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,aAAa;AACnB,SAAS,WAAW,EAAE,WAAW,KAAK,SAAS,MAAM,WAAW,GAAG,OAAO,EAAG,GAAG;AAC5E,MAAI;AACJ,MAAI;AACJ,UAAQ,YAAY,cAAc,KAAM,4CAA4C;AACpF,MAAI,eAAe,IAAI;AACvB,iBAAe,MAAM,YAAY,YAAY,YAAY;AACzD,aAAW,MAAM,aAAa,aAAa,WAAW,GAAI;AAC1D,MAAI,eAAe,GAAG;AAClB,eAAW,CAACC,kBAAiB;AACzB,YAAM,mBAAmBA,gBAAe;AACxC,YAAM,QAAQ,mBAAmB;AACjC,YAAMC,KAAI,mBAAmB;AAC7B,YAAMC,KAAI,gBAAgBF,eAAc,YAAY;AACpD,YAAMG,KAAI,KAAK,IAAI,CAAC,KAAK;AACzB,aAAO,UAAWF,KAAIC,KAAKC;AAAA,IAC/B;AACA,iBAAa,CAACH,kBAAiB;AAC3B,YAAM,mBAAmBA,gBAAe;AACxC,YAAM,QAAQ,mBAAmB;AACjC,YAAM,IAAI,QAAQ,WAAW;AAC7B,YAAM,IAAI,KAAK,IAAI,cAAc,CAAC,IAAI,KAAK,IAAIA,eAAc,CAAC,IAAI;AAClE,YAAM,IAAI,KAAK,IAAI,CAAC,KAAK;AACzB,YAAM,IAAI,gBAAgB,KAAK,IAAIA,eAAc,CAAC,GAAG,YAAY;AACjE,YAAM,SAAS,CAAC,SAASA,aAAY,IAAI,UAAU,IAAI,KAAK;AAC5D,aAAQ,WAAW,IAAI,KAAK,KAAM;AAAA,IACtC;AAAA,EACJ,OACK;AACD,eAAW,CAACA,kBAAiB;AACzB,YAAMC,KAAI,KAAK,IAAI,CAACD,gBAAe,QAAQ;AAC3C,YAAME,MAAKF,gBAAe,YAAY,WAAW;AACjD,aAAO,CAAC,UAAUC,KAAIC;AAAA,IAC1B;AACA,iBAAa,CAACF,kBAAiB;AAC3B,YAAMC,KAAI,KAAK,IAAI,CAACD,gBAAe,QAAQ;AAC3C,YAAME,MAAK,WAAWF,kBAAiB,WAAW;AAClD,aAAOC,KAAIC;AAAA,IACf;AAAA,EACJ;AACA,QAAM,eAAe,IAAI;AACzB,QAAM,eAAe,gBAAgB,UAAU,YAAY,YAAY;AACvE,aAAW,WAAW;AACtB,MAAI,MAAM,YAAY,GAAG;AACrB,WAAO;AAAA,MACH,WAAW;AAAA,MACX,SAAS;AAAA,MACT;AAAA,IACJ;AAAA,EACJ,OACK;AACD,UAAM,YAAY,KAAK,IAAI,cAAc,CAAC,IAAI;AAC9C,WAAO;AAAA,MACH;AAAA,MACA,SAAS,eAAe,IAAI,KAAK,KAAK,OAAO,SAAS;AAAA,MACtD;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,IAAM,iBAAiB;AACvB,SAAS,gBAAgB,UAAU,YAAY,cAAc;AACzD,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACrC,aAAS,SAAS,SAAS,MAAM,IAAI,WAAW,MAAM;AAAA,EAC1D;AACA,SAAO;AACX;AACA,SAAS,gBAAgB,cAAc,cAAc;AACjD,SAAO,eAAe,KAAK,KAAK,IAAI,eAAe,YAAY;AACnE;;;ACzEA,IAAM,eAAe,CAAC,YAAY,QAAQ;AAC1C,IAAM,cAAc,CAAC,aAAa,WAAW,MAAM;AACnD,SAAS,aAAa,SAAS,MAAM;AACjC,SAAO,KAAK,KAAK,CAAC,QAAQ,QAAQ,GAAG,MAAM,MAAS;AACxD;AACA,SAAS,iBAAiB,SAAS;AAC/B,MAAI,gBAAgB,OAAO,OAAO,EAAE,UAAU,GAAK,WAAW,KAAK,SAAS,IAAI,MAAM,GAAK,wBAAwB,MAAM,GAAG,OAAO;AACnI,MAAI,CAAC,aAAa,SAAS,WAAW,KAClC,aAAa,SAAS,YAAY,GAAG;AACrC,UAAM,UAAU,WAAW,OAAO;AAClC,oBAAgB,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,aAAa,GAAG,OAAO,GAAG,EAAE,UAAU,GAAK,MAAM,EAAI,CAAC;AACpH,kBAAc,yBAAyB;AAAA,EAC3C;AACA,SAAO;AACX;AACA,SAAS,OAAO,IAAI;AAChB,MAAI,EAAE,OAAO,GAAK,KAAK,GAAK,YAAY,GAAG,UAAU,IAAI,IAAI,UAAU,OAAO,IAAI,CAAC,QAAQ,MAAM,aAAa,WAAW,CAAC;AAC1H,QAAM,QAAQ,EAAE,MAAM,OAAO,OAAO,KAAK;AACzC,MAAI,EAAE,WAAW,SAAS,MAAM,UAAU,UAAU,uBAAwB,IAAI,iBAAiB,OAAO;AACxG,MAAI,gBAAgB;AACpB,MAAI,kBAAkB;AACtB,WAAS,eAAe;AACpB,UAAM,kBAAkB,WAAW,EAAE,WAAW,OAAQ;AACxD,UAAM,eAAe,KAAK;AAC1B,UAAM,eAAe,WAAW,IAAI,KAAK,KAAK,YAAY,IAAI;AAC9D,UAAM,sBAAsB,KAAK,KAAK,YAAY,IAAI,IAAI;AAC1D,QAAI,cAAc,QAAW;AACzB,kBAAY,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,GAAG;AAAA,IACvD;AACA,QAAI,eAAe,GAAG;AAClB,YAAM,cAAc,gBAAgB,qBAAqB,YAAY;AACrE,sBAAgB,CAAC,MAAM;AACnB,cAAM,WAAW,KAAK,IAAI,CAAC,eAAe,sBAAsB,CAAC;AACjE,eAAQ,KACJ,aACO,kBACC,eAAe,sBAAsB,gBACrC,cACA,KAAK,IAAI,cAAc,CAAC,IACxB,eAAe,KAAK,IAAI,cAAc,CAAC;AAAA,MACvD;AACA,wBAAkB,CAAC,MAAM;AACrB,cAAM,WAAW,KAAK,IAAI,CAAC,eAAe,sBAAsB,CAAC;AACjE,eAAQ,eACJ,sBACA,YACE,KAAK,IAAI,cAAc,CAAC,KACrB,kBACG,eACI,sBACA,gBACR,cACA,eAAe,KAAK,IAAI,cAAc,CAAC,KAC3C,YACK,KAAK,IAAI,cAAc,CAAC,KACpB,kBACG,eACI,sBACA,gBACR,cACI,eACA,KAAK,IAAI,cAAc,CAAC;AAAA,MAC5C;AAAA,IACJ,WACS,iBAAiB,GAAG;AACzB,sBAAgB,CAAC,MAAM,KACnB,KAAK,IAAI,CAAC,sBAAsB,CAAC,KAC5B,gBACI,kBAAkB,sBAAsB,gBACrC;AAAA,IACpB,OACK;AACD,YAAM,oBAAoB,sBAAsB,KAAK,KAAK,eAAe,eAAe,CAAC;AACzF,sBAAgB,CAAC,MAAM;AACnB,cAAM,WAAW,KAAK,IAAI,CAAC,eAAe,sBAAsB,CAAC;AACjE,cAAM,WAAW,KAAK,IAAI,oBAAoB,GAAG,GAAG;AACpD,eAAQ,KACH,aACK,kBACE,eAAe,sBAAsB,gBACrC,KAAK,KAAK,QAAQ,IAClB,oBACI,eACA,KAAK,KAAK,QAAQ,KAC1B;AAAA,MACZ;AAAA,IACJ;AAAA,EACJ;AACA,eAAa;AACb,SAAO;AAAA,IACH,MAAM,CAAC,MAAM;AACT,YAAM,UAAU,cAAc,CAAC;AAC/B,UAAI,CAAC,wBAAwB;AACzB,cAAM,kBAAkB,gBAAgB,CAAC,IAAI;AAC7C,cAAM,2BAA2B,KAAK,IAAI,eAAe,KAAK;AAC9D,cAAM,+BAA+B,KAAK,IAAI,KAAK,OAAO,KAAK;AAC/D,cAAM,OACF,4BAA4B;AAAA,MACpC,OACK;AACD,cAAM,OAAO,KAAK;AAAA,MACtB;AACA,YAAM,QAAQ,MAAM,OAAO,KAAK;AAChC,aAAO;AAAA,IACX;AAAA,IACA,YAAY,MAAM;AACd,iBAAW,CAAC;AACZ,OAAC,MAAM,EAAE,IAAI,CAAC,IAAI,IAAI;AACtB,mBAAa;AAAA,IACjB;AAAA,EACJ;AACJ;AACA,OAAO,qBAAqB,CAACE,IAAGC,OAAM,OAAOD,OAAM,YAAY,OAAOC,OAAM;AAC5E,IAAM,OAAO,CAAC,OAAO;;;ACpHrB,IAAM,WAAW,CAAC,MAAM,IAAI,UAAU;AAClC,QAAM,mBAAmB,KAAK;AAC9B,SAAO,qBAAqB,IAAI,KAAK,QAAQ,QAAQ;AACzD;;;ACHA,IAAM,MAAM,CAAC,MAAM,IAAIC,cAAa,CAACA,YAAW,OAAOA,YAAW,KAAK;;;ACAvE,IAAMC,SAAQ,CAAC,KAAK,QAAQ,CAAC,MAAM,KAAK,IAAI,KAAK,IAAI,GAAG,GAAG,GAAG,GAAG;AACjE,IAAM,WAAW,CAAC,MAAO,IAAI,IAAI,OAAO,EAAE,QAAQ,CAAC,CAAC,IAAI;AACxD,IAAM,aAAa;AACnB,IAAM,aAAa;AACnB,IAAM,mBAAmB;AACzB,SAAS,SAAS,GAAG;AACjB,SAAO,OAAO,MAAM;AACxB;;;ACLA,IAAM,SAAS;AAAA,EACX,MAAM,CAAC,MAAM,OAAO,MAAM;AAAA,EAC1B,OAAO;AAAA,EACP,WAAW,CAAC,MAAM;AACtB;AACA,IAAM,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG,EAAE,WAAWC,OAAM,GAAG,CAAC,EAAE,CAAC;AACjF,IAAM,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG,EAAE,SAAS,EAAE,CAAC;;;ACNrE,IAAM,iBAAiB,CAAC,UAAU;AAAA,EAC9B,MAAM,CAAC,MAAM,SAAS,CAAC,KAAK,EAAE,SAAS,IAAI,KAAK,EAAE,MAAM,GAAG,EAAE,WAAW;AAAA,EACxE,OAAO;AAAA,EACP,WAAW,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI;AACjC;AACA,IAAM,UAAU,eAAe,KAAK;AACpC,IAAM,UAAU,eAAe,GAAG;AAClC,IAAM,KAAK,eAAe,IAAI;AAC9B,IAAM,KAAK,eAAe,IAAI;AAC9B,IAAM,KAAK,eAAe,IAAI;AAC9B,IAAM,qBAAqB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,OAAO,CAAC,MAAM,QAAQ,MAAM,CAAC,IAAI,KAAK,WAAW,CAAC,MAAM,QAAQ,UAAU,IAAI,GAAG,EAAE,CAAC;;;ACV3J,IAAM,gBAAgB,CAAC,MAAM,aAAa,CAAC,MAAM;AAC7C,SAAO,QAAS,SAAS,CAAC,KAAK,iBAAiB,KAAK,CAAC,KAAK,EAAE,WAAW,IAAI,KACvE,YAAY,OAAO,UAAU,eAAe,KAAK,GAAG,QAAQ,CAAE;AACvE;AACA,IAAM,aAAa,CAAC,OAAO,OAAO,UAAU,CAAC,MAAM;AAC/C,MAAI,CAAC,SAAS,CAAC;AACX,WAAO;AACX,QAAM,CAACC,IAAGC,IAAGC,IAAGC,MAAK,IAAI,EAAE,MAAM,UAAU;AAC3C,SAAO;AAAA,IACH,CAAC,KAAK,GAAG,WAAWH,EAAC;AAAA,IACrB,CAAC,KAAK,GAAG,WAAWC,EAAC;AAAA,IACrB,CAAC,KAAK,GAAG,WAAWC,EAAC;AAAA,IACrB,OAAOC,WAAU,SAAY,WAAWA,MAAK,IAAI;AAAA,EACrD;AACJ;;;ACXA,IAAM,OAAO;AAAA,EACT,MAAM,cAAc,OAAO,KAAK;AAAA,EAChC,OAAO,WAAW,OAAO,cAAc,WAAW;AAAA,EAClD,WAAW,CAAC,EAAE,KAAK,YAAY,WAAW,OAAO,UAAU,EAAE,MAAM;AAC/D,WAAQ,UACJ,KAAK,MAAM,GAAG,IACd,OACA,QAAQ,UAAU,SAAS,UAAU,CAAC,IACtC,OACA,QAAQ,UAAU,SAAS,SAAS,CAAC,IACrC,OACA,SAAS,MAAM,UAAU,OAAO,CAAC,IACjC;AAAA,EACR;AACJ;;;ACfA,IAAM,eAAeC,OAAM,GAAG,GAAG;AACjC,IAAM,UAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG,EAAE,WAAW,CAAC,MAAM,KAAK,MAAM,aAAa,CAAC,CAAC,EAAE,CAAC;AAC1G,IAAM,OAAO;AAAA,EACT,MAAM,cAAc,OAAO,KAAK;AAAA,EAChC,OAAO,WAAW,OAAO,SAAS,MAAM;AAAA,EACxC,WAAW,CAAC,EAAE,KAAK,OAAO,MAAM,OAAO,UAAU,EAAE,MAAM,UACrD,QAAQ,UAAU,GAAG,IACrB,OACA,QAAQ,UAAU,KAAK,IACvB,OACA,QAAQ,UAAU,IAAI,IACtB,OACA,SAAS,MAAM,UAAU,OAAO,CAAC,IACjC;AACR;;;ACfA,SAAS,SAAS,GAAG;AACjB,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAIC,KAAI;AACR,MAAIC,KAAI;AACR,MAAI,EAAE,SAAS,GAAG;AACd,QAAI,EAAE,OAAO,GAAG,CAAC;AACjB,QAAI,EAAE,OAAO,GAAG,CAAC;AACjB,IAAAD,KAAI,EAAE,OAAO,GAAG,CAAC;AACjB,IAAAC,KAAI,EAAE,OAAO,GAAG,CAAC;AAAA,EACrB,OACK;AACD,QAAI,EAAE,OAAO,GAAG,CAAC;AACjB,QAAI,EAAE,OAAO,GAAG,CAAC;AACjB,IAAAD,KAAI,EAAE,OAAO,GAAG,CAAC;AACjB,IAAAC,KAAI,EAAE,OAAO,GAAG,CAAC;AACjB,SAAK;AACL,SAAK;AACL,IAAAD,MAAKA;AACL,IAAAC,MAAKA;AAAA,EACT;AACA,SAAO;AAAA,IACH,KAAK,SAAS,GAAG,EAAE;AAAA,IACnB,OAAO,SAAS,GAAG,EAAE;AAAA,IACrB,MAAM,SAASD,IAAG,EAAE;AAAA,IACpB,OAAOC,KAAI,SAASA,IAAG,EAAE,IAAI,MAAM;AAAA,EACvC;AACJ;AACA,IAAM,MAAM;AAAA,EACR,MAAM,cAAc,GAAG;AAAA,EACvB,OAAO;AAAA,EACP,WAAW,KAAK;AACpB;;;AC9BA,IAAM,QAAQ;AAAA,EACV,MAAM,CAAC,MAAM,KAAK,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,EACvD,OAAO,CAAC,MAAM;AACV,QAAI,KAAK,KAAK,CAAC,GAAG;AACd,aAAO,KAAK,MAAM,CAAC;AAAA,IACvB,WACS,KAAK,KAAK,CAAC,GAAG;AACnB,aAAO,KAAK,MAAM,CAAC;AAAA,IACvB,OACK;AACD,aAAO,IAAI,MAAM,CAAC;AAAA,IACtB;AAAA,EACJ;AAAA,EACA,WAAW,CAAC,MAAM;AACd,WAAO,SAAS,CAAC,IACX,IACA,EAAE,eAAe,KAAK,IAClB,KAAK,UAAU,CAAC,IAChB,KAAK,UAAU,CAAC;AAAA,EAC9B;AACJ;;;ACrBA,IAAM,aAAa;AACnB,IAAM,cAAc;AACpB,SAAS,KAAK,GAAG;AACb,MAAI,IAAI,IAAI,IAAI;AAChB,SAAQ,MAAM,CAAC,KACX,SAAS,CAAC,OACR,MAAM,KAAK,EAAE,MAAM,UAAU,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,QAAQ,OAAO,SAAS,KAAK,OAAO,MAAM,KAAK,EAAE,MAAM,UAAU,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,QAAQ,OAAO,SAAS,KAAK,KAAK;AACxP;AACA,SAAS,QAAQ,GAAG;AAChB,MAAI,OAAO,MAAM;AACb,QAAI,GAAG,CAAC;AACZ,QAAM,SAAS,CAAC;AAChB,MAAI,YAAY;AAChB,QAAM,SAAS,EAAE,MAAM,UAAU;AACjC,MAAI,QAAQ;AACR,gBAAY,OAAO;AACnB,QAAI,EAAE,QAAQ,YAAY,UAAU;AACpC,WAAO,KAAK,GAAG,OAAO,IAAI,MAAM,KAAK,CAAC;AAAA,EAC1C;AACA,QAAM,UAAU,EAAE,MAAM,UAAU;AAClC,MAAI,SAAS;AACT,QAAI,EAAE,QAAQ,YAAY,WAAW;AACrC,WAAO,KAAK,GAAG,QAAQ,IAAI,OAAO,KAAK,CAAC;AAAA,EAC5C;AACA,SAAO,EAAE,QAAQ,WAAW,WAAW,EAAE;AAC7C;AACA,SAAS,MAAM,GAAG;AACd,SAAO,QAAQ,CAAC,EAAE;AACtB;AACA,SAAS,kBAAkB,GAAG;AAC1B,QAAM,EAAE,QAAQ,WAAW,UAAU,IAAI,QAAQ,CAAC;AAClD,QAAM,YAAY,OAAO;AACzB,SAAO,CAACC,OAAM;AACV,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAChC,eAAS,OAAO,QAAQ,IAAI,YAAY,aAAa,aAAa,IAAI,YAAY,MAAM,UAAUA,GAAE,CAAC,CAAC,IAAI,SAASA,GAAE,CAAC,CAAC,CAAC;AAAA,IAC5H;AACA,WAAO;AAAA,EACX;AACJ;AACA,IAAM,uBAAuB,CAAC,MAAM,OAAO,MAAM,WAAW,IAAI;AAChE,SAAS,kBAAkB,GAAG;AAC1B,QAAM,SAAS,MAAM,CAAC;AACtB,QAAM,cAAc,kBAAkB,CAAC;AACvC,SAAO,YAAY,OAAO,IAAI,oBAAoB,CAAC;AACvD;AACA,IAAM,UAAU,EAAE,MAAM,OAAO,mBAAmB,kBAAkB;;;AC/CpE,IAAM,cAAc,oBAAI,IAAI,CAAC,cAAc,YAAY,YAAY,SAAS,CAAC;AAC7E,SAAS,mBAAmB,GAAG;AAC3B,MAAI,CAAC,MAAM,KAAK,IAAI,EAAE,MAAM,GAAG,EAAE,EAAE,MAAM,GAAG;AAC5C,MAAI,SAAS;AACT,WAAO;AACX,QAAM,CAACC,OAAM,IAAI,MAAM,MAAM,UAAU,KAAK,CAAC;AAC7C,MAAI,CAACA;AACD,WAAO;AACX,QAAM,OAAO,MAAM,QAAQA,SAAQ,EAAE;AACrC,MAAI,eAAe,YAAY,IAAI,IAAI,IAAI,IAAI;AAC/C,MAAIA,YAAW;AACX,oBAAgB;AACpB,SAAO,OAAO,MAAM,eAAe,OAAO;AAC9C;AACA,IAAM,gBAAgB;AACtB,IAAM,SAAS,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,mBAAmB,CAAC,MAAM;AAC7E,QAAM,YAAY,EAAE,MAAM,aAAa;AACvC,SAAO,YAAY,UAAU,IAAI,kBAAkB,EAAE,KAAK,GAAG,IAAI;AACrE,EAAE,CAAC;;;ACrBP,SAAS,SAAS,GAAG,GAAG,GAAG;AACvB,MAAI,IAAI;AACJ,SAAK;AACT,MAAI,IAAI;AACJ,SAAK;AACT,MAAI,IAAI,IAAI;AACR,WAAO,KAAK,IAAI,KAAK,IAAI;AAC7B,MAAI,IAAI,IAAI;AACR,WAAO;AACX,MAAI,IAAI,IAAI;AACR,WAAO,KAAK,IAAI,MAAM,IAAI,IAAI,KAAK;AACvC,SAAO;AACX;AACA,SAAS,WAAW,EAAE,KAAK,YAAY,WAAW,OAAAC,OAAM,GAAG;AACvD,SAAO;AACP,gBAAc;AACd,eAAa;AACb,MAAI,MAAM;AACV,MAAI,QAAQ;AACZ,MAAI,OAAO;AACX,MAAI,CAAC,YAAY;AACb,UAAM,QAAQ,OAAO;AAAA,EACzB,OACK;AACD,UAAM,IAAI,YAAY,MAChB,aAAa,IAAI,cACjB,YAAY,aAAa,YAAY;AAC3C,UAAM,IAAI,IAAI,YAAY;AAC1B,UAAM,SAAS,GAAG,GAAG,MAAM,IAAI,CAAC;AAChC,YAAQ,SAAS,GAAG,GAAG,GAAG;AAC1B,WAAO,SAAS,GAAG,GAAG,MAAM,IAAI,CAAC;AAAA,EACrC;AACA,SAAO;AAAA,IACH,KAAK,KAAK,MAAM,MAAM,GAAG;AAAA,IACzB,OAAO,KAAK,MAAM,QAAQ,GAAG;AAAA,IAC7B,MAAM,KAAK,MAAM,OAAO,GAAG;AAAA,IAC3B,OAAAA;AAAA,EACJ;AACJ;;;ACjCA,IAAM,iBAAiB,CAAC,MAAM,IAAI,MAAM;AACpC,QAAM,WAAW,OAAO;AACxB,QAAM,SAAS,KAAK;AACpB,SAAO,KAAK,KAAK,KAAK,IAAI,GAAG,KAAK,SAAS,YAAY,QAAQ,CAAC;AACpE;AACA,IAAM,aAAa,CAAC,KAAK,MAAM,IAAI;AACnC,IAAM,eAAe,CAAC,MAAM,WAAW,KAAK,CAAC,SAAS,KAAK,KAAK,CAAC,CAAC;AAClE,IAAM,gBAAgB,CAACC,WAAU,IAAIA,MAAK;AAC1C,IAAM,WAAW,CAAC,MAAM,OAAO;AAC3B,MAAI,gBAAgB,aAAa,IAAI;AACrC,MAAI,cAAc,aAAa,EAAE;AACjC,YAAU,CAAC,CAAC,eAAe,cAAc,IAAI,CAAC;AAC9C,YAAU,CAAC,CAAC,aAAa,cAAc,EAAE,CAAC;AAC1C,MAAI,YAAY,cAAc,MAAM,IAAI;AACxC,MAAI,UAAU,YAAY,MAAM,EAAE;AAClC,MAAI,kBAAkB,MAAM;AACxB,gBAAY,WAAW,SAAS;AAChC,oBAAgB;AAAA,EACpB;AACA,MAAI,gBAAgB,MAAM;AACtB,cAAU,WAAW,OAAO;AAC5B,kBAAc;AAAA,EAClB;AACA,QAAM,UAAU,OAAO,OAAO,CAAC,GAAG,SAAS;AAC3C,SAAO,CAAC,MAAM;AACV,eAAW,OAAO,SAAS;AACvB,UAAI,QAAQ,SAAS;AACjB,gBAAQ,GAAG,IAAI,eAAe,UAAU,GAAG,GAAG,QAAQ,GAAG,GAAG,CAAC;AAAA,MACjE;AAAA,IACJ;AACA,YAAQ,QAAQ,IAAI,UAAU,OAAO,QAAQ,OAAO,CAAC;AACrD,WAAO,cAAc,UAAU,OAAO;AAAA,EAC1C;AACJ;;;ACjCA,IAAM,QAAQ,CAAC,MAAM,OAAO,MAAM;;;ACLlC,IAAM,mBAAmB,CAACC,IAAGC,OAAM,CAAC,MAAMA,GAAED,GAAE,CAAC,CAAC;AAChD,IAAM,OAAO,IAAI,iBAAiB,aAAa,OAAO,gBAAgB;;;ACMtE,SAAS,SAAS,QAAQ,QAAQ;AAC9B,MAAI,MAAM,MAAM,GAAG;AACf,WAAO,CAAC,MAAM,IAAI,QAAQ,QAAQ,CAAC;AAAA,EACvC,WACS,MAAM,KAAK,MAAM,GAAG;AACzB,WAAO,SAAS,QAAQ,MAAM;AAAA,EAClC,OACK;AACD,WAAO,WAAW,QAAQ,MAAM;AAAA,EACpC;AACJ;AACA,IAAM,WAAW,CAAC,MAAM,OAAO;AAC3B,QAAM,SAAS,CAAC,GAAG,IAAI;AACvB,QAAM,YAAY,OAAO;AACzB,QAAM,aAAa,KAAK,IAAI,CAAC,UAAU,MAAM,SAAS,UAAU,GAAG,CAAC,CAAC,CAAC;AACtE,SAAO,CAAC,MAAM;AACV,aAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAChC,aAAO,CAAC,IAAI,WAAW,CAAC,EAAE,CAAC;AAAA,IAC/B;AACA,WAAO;AAAA,EACX;AACJ;AACA,IAAM,YAAY,CAAC,QAAQ,WAAW;AAClC,QAAM,SAAS,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG,MAAM;AAC9D,QAAM,aAAa,CAAC;AACpB,aAAW,OAAO,QAAQ;AACtB,QAAI,OAAO,GAAG,MAAM,UAAa,OAAO,GAAG,MAAM,QAAW;AACxD,iBAAW,GAAG,IAAI,SAAS,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC;AAAA,IACvD;AAAA,EACJ;AACA,SAAO,CAAC,MAAM;AACV,eAAW,OAAO,YAAY;AAC1B,aAAO,GAAG,IAAI,WAAW,GAAG,EAAE,CAAC;AAAA,IACnC;AACA,WAAO;AAAA,EACX;AACJ;AACA,SAASE,SAAQ,OAAO;AACpB,QAAM,SAAS,QAAQ,MAAM,KAAK;AAClC,QAAM,YAAY,OAAO;AACzB,MAAI,aAAa;AACjB,MAAI,SAAS;AACb,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAChC,QAAI,cAAc,OAAO,OAAO,CAAC,MAAM,UAAU;AAC7C;AAAA,IACJ,OACK;AACD,UAAI,OAAO,CAAC,EAAE,QAAQ,QAAW;AAC7B;AAAA,MACJ,OACK;AACD;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,EAAE,QAAQ,YAAY,QAAQ,OAAO;AAChD;AACA,IAAM,aAAa,CAAC,QAAQ,WAAW;AACnC,QAAM,WAAW,QAAQ,kBAAkB,MAAM;AACjD,QAAM,cAAcA,SAAQ,MAAM;AAClC,QAAM,cAAcA,SAAQ,MAAM;AAClC,QAAM,iBAAiB,YAAY,WAAW,YAAY,UACtD,YAAY,WAAW,YAAY,UACnC,YAAY,cAAc,YAAY;AAC1C,MAAI,gBAAgB;AAChB,WAAO,KAAK,SAAS,YAAY,QAAQ,YAAY,MAAM,GAAG,QAAQ;AAAA,EAC1E,OACK;AACD,YAAQ,MAAM,mBAAmB,MAAM,UAAU,MAAM,0KAA0K;AACjO,WAAO,CAAC,MAAM,GAAG,IAAI,IAAI,SAAS,MAAM;AAAA,EAC5C;AACJ;;;ACtEA,IAAM,YAAY,CAAC,MAAM,OAAO,CAAC,MAAM,IAAI,MAAM,IAAI,CAAC;AACtD,SAAS,mBAAmB,GAAG;AAC3B,MAAI,OAAO,MAAM,UAAU;AACvB,WAAO;AAAA,EACX,WACS,OAAO,MAAM,UAAU;AAC5B,QAAI,MAAM,KAAK,CAAC,GAAG;AACf,aAAO;AAAA,IACX,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ,WACS,MAAM,QAAQ,CAAC,GAAG;AACvB,WAAO;AAAA,EACX,WACS,OAAO,MAAM,UAAU;AAC5B,WAAO;AAAA,EACX;AACJ;AACA,SAAS,aAAa,QAAQ,MAAM,aAAa;AAC7C,QAAM,SAAS,CAAC;AAChB,QAAM,eAAe,eAAe,mBAAmB,OAAO,CAAC,CAAC;AAChE,QAAM,YAAY,OAAO,SAAS;AAClC,WAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAChC,QAAI,QAAQ,aAAa,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC;AACjD,QAAI,MAAM;AACN,YAAM,iBAAiB,MAAM,QAAQ,IAAI,IAAI,KAAK,CAAC,IAAI;AACvD,cAAQ,KAAK,gBAAgB,KAAK;AAAA,IACtC;AACA,WAAO,KAAK,KAAK;AAAA,EACrB;AACA,SAAO;AACX;AACA,SAAS,gBAAgB,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,GAAG;AAC1C,SAAO,CAAC,MAAM,MAAM,SAAS,MAAM,IAAI,CAAC,CAAC;AAC7C;AACA,SAAS,gBAAgB,OAAO,QAAQ;AACpC,QAAM,cAAc,MAAM;AAC1B,QAAM,iBAAiB,cAAc;AACrC,SAAO,CAAC,MAAM;AACV,QAAI,aAAa;AACjB,QAAI,kBAAkB;AACtB,QAAI,KAAK,MAAM,CAAC,GAAG;AACf,wBAAkB;AAAA,IACtB,WACS,KAAK,MAAM,cAAc,GAAG;AACjC,mBAAa,iBAAiB;AAC9B,wBAAkB;AAAA,IACtB;AACA,QAAI,CAAC,iBAAiB;AAClB,UAAI,IAAI;AACR,aAAO,IAAI,aAAa,KAAK;AACzB,YAAI,MAAM,CAAC,IAAI,KAAK,MAAM,gBAAgB;AACtC;AAAA,QACJ;AAAA,MACJ;AACA,mBAAa,IAAI;AAAA,IACrB;AACA,UAAM,kBAAkB,SAAS,MAAM,UAAU,GAAG,MAAM,aAAa,CAAC,GAAG,CAAC;AAC5E,WAAO,OAAO,UAAU,EAAE,eAAe;AAAA,EAC7C;AACJ;AACA,SAAS,YAAY,OAAO,QAAQ,EAAE,OAAO,UAAU,MAAM,MAAM,MAAM,IAAI,CAAC,GAAG;AAC7E,QAAM,cAAc,MAAM;AAC1B,YAAU,gBAAgB,OAAO,QAAQ,sDAAsD;AAC/F,YAAU,CAAC,QAAQ,CAAC,MAAM,QAAQ,IAAI,KAAK,KAAK,WAAW,cAAc,GAAG,kIAAkI;AAC9M,MAAI,MAAM,CAAC,IAAI,MAAM,cAAc,CAAC,GAAG;AACnC,YAAQ,CAAC,EAAE,OAAO,KAAK;AACvB,aAAS,CAAC,EAAE,OAAO,MAAM;AACzB,UAAM,QAAQ;AACd,WAAO,QAAQ;AAAA,EACnB;AACA,QAAM,SAAS,aAAa,QAAQ,MAAM,KAAK;AAC/C,QAAM,eAAe,gBAAgB,IAC/B,gBAAgB,OAAO,MAAM,IAC7B,gBAAgB,OAAO,MAAM;AACnC,SAAO,UACD,CAAC,MAAM,aAAa,MAAM,MAAM,CAAC,GAAG,MAAM,cAAc,CAAC,GAAG,CAAC,CAAC,IAC9D;AACV;;;ACzFA,IAAM,gBAAgB,YAAU,OAAK,IAAI,OAAO,IAAI,CAAC;AACrD,IAAM,eAAe,YAAU,OAAK,KAAK,MAAM,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,EAAE,KAAK;AAC/F,IAAM,eAAe,CAAC,UAAU,OAAK,KAAK,IAAI,GAAG,KAAK;AACtD,IAAM,eAAe,CAAC,UAAU,OAAK,IAAI,MAAM,QAAQ,KAAK,IAAI;AAChE,IAAM,mBAAmB,CAAC,UAAU;AAChC,QAAM,aAAa,aAAa,KAAK;AACrC,SAAO,QAAM,KAAK,KAAK,IACjB,MAAM,WAAW,CAAC,IAClB,OAAO,IAAI,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE;AAC9C;;;ACPA,IAAM,6BAA6B;AACnC,IAAM,yBAAyB,IAAM;AACrC,IAAM,0BAA0B,IAAM;AACtC,IAAM,yBAAyB,IAAM;AACrC,IAAM,SAAS,OAAK;AACpB,IAAM,SAAS,aAAa,CAAC;AAC7B,IAAM,UAAU,cAAc,MAAM;AACpC,IAAM,YAAY,aAAa,MAAM;AACrC,IAAM,SAAS,OAAK,IAAI,KAAK,IAAI,KAAK,KAAK,CAAC,CAAC;AAC7C,IAAM,UAAU,cAAc,MAAM;AACpC,IAAM,YAAY,aAAa,OAAO;AACtC,IAAM,SAAS,aAAa,0BAA0B;AACtD,IAAM,UAAU,cAAc,MAAM;AACpC,IAAM,YAAY,aAAa,MAAM;AACrC,IAAM,aAAa,iBAAiB,0BAA0B;AAC9D,IAAM,KAAK,OAAS;AACpB,IAAM,KAAK,QAAU;AACrB,IAAM,KAAK,QAAU;AACrB,IAAM,YAAY,CAAC,MAAM;AACrB,MAAI,MAAM,KAAK,MAAM;AACjB,WAAO;AACX,QAAM,KAAK,IAAI;AACf,SAAO,IAAI,yBACL,SAAS,KACT,IAAI,0BACA,QAAQ,KAAK,MAAM,IAAI,MACvB,IAAI,yBACA,KAAK,KAAK,KAAK,IAAI,KACnB,OAAO,IAAI,IAAI,QAAQ,IAAI;AAC7C;AACA,IAAM,WAAW,cAAc,SAAS;AACxC,IAAM,cAAc,CAAC,MAAM,IAAI,MACzB,OAAO,IAAM,UAAU,IAAM,IAAI,CAAG,KACpC,MAAM,UAAU,IAAI,IAAM,CAAG,IAAI;;;AChCvC,SAAS,cAAc,QAAQ,QAAQ;AACnC,SAAO,OAAO,IAAI,MAAM,UAAU,SAAS,EAAE,OAAO,GAAG,OAAO,SAAS,CAAC;AAC5E;AACA,SAAS,cAAc,QAAQ;AAC3B,QAAM,YAAY,OAAO;AACzB,SAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,MAAM,IAAI,KAAK,YAAY,KAAK,CAAC;AACtE;AACA,SAAS,qBAAqB,QAAQ,UAAU;AAC5C,SAAO,OAAO,IAAI,CAAC,MAAM,IAAI,QAAQ;AACzC;AACA,SAAS,UAAU,EAAE,OAAO,GAAG,KAAK,GAAG,MAAM,QAAQ,WAAW,IAAK,GAAG;AACpE,QAAM,QAAQ,EAAE,MAAM,OAAO,OAAO,KAAK;AACzC,QAAM,SAAS,MAAM,QAAQ,EAAE,IAAI,KAAK,CAAC,MAAM,EAAE;AACjD,QAAM,QAAQ,qBAAqB,UAAU,OAAO,WAAW,OAAO,SAChE,SACA,cAAc,MAAM,GAAG,QAAQ;AACrC,WAAS,qBAAqB;AAC1B,WAAO,YAAY,OAAO,QAAQ;AAAA,MAC9B,MAAM,MAAM,QAAQ,IAAI,IAAI,OAAO,cAAc,QAAQ,IAAI;AAAA,IACjE,CAAC;AAAA,EACL;AACA,MAAI,eAAe,mBAAmB;AACtC,SAAO;AAAA,IACH,MAAM,CAAC,MAAM;AACT,YAAM,QAAQ,aAAa,CAAC;AAC5B,YAAM,OAAO,KAAK;AAClB,aAAO;AAAA,IACX;AAAA,IACA,YAAY,MAAM;AACd,aAAO,QAAQ;AACf,qBAAe,mBAAmB;AAAA,IACtC;AAAA,EACJ;AACJ;;;ACpCA,SAAS,MAAM,EAAE,WAAW,GAAG,OAAO,GAAG,QAAQ,KAAK,eAAe,KAAK,YAAY,KAAK,aAAc,GAAG;AACxG,QAAM,QAAQ,EAAE,MAAM,OAAO,OAAO,KAAK;AACzC,MAAI,YAAY,QAAQ;AACxB,QAAM,QAAQ,OAAO;AACrB,QAAM,SAAS,iBAAiB,SAAY,QAAQ,aAAa,KAAK;AACtE,MAAI,WAAW;AACX,gBAAY,SAAS;AACzB,SAAO;AAAA,IACH,MAAM,CAAC,MAAM;AACT,YAAM,QAAQ,CAAC,YAAY,KAAK,IAAI,CAAC,IAAI,YAAY;AACrD,YAAM,OAAO,EAAE,QAAQ,aAAa,QAAQ,CAAC;AAC7C,YAAM,QAAQ,MAAM,OAAO,SAAS,SAAS;AAC7C,aAAO;AAAA,IACX;AAAA,IACA,YAAY,MAAM;AAAA,IAAE;AAAA,EACxB;AACJ;;;ACZA,IAAM,QAAQ,EAAE,WAAW,QAAQ,MAAM;AACzC,SAAS,2BAA2B,QAAQ;AACxC,MAAI,MAAM,QAAQ,OAAO,EAAE,GAAG;AAC1B,WAAO;AAAA,EACX,WACS,MAAM,OAAO,IAAI,GAAG;AACzB,WAAO,MAAM,OAAO,IAAI;AAAA,EAC5B;AACA,QAAM,OAAO,IAAI,IAAI,OAAO,KAAK,MAAM,CAAC;AACxC,MAAI,KAAK,IAAI,MAAM,KACd,KAAK,IAAI,UAAU,KAAK,CAAC,KAAK,IAAI,cAAc,GAAI;AACrD,WAAO;AAAA,EACX,WACS,KAAK,IAAI,cAAc,KAC5B,KAAK,IAAI,WAAW,KACpB,KAAK,IAAI,MAAM,KACf,KAAK,IAAI,SAAS,KAClB,KAAK,IAAI,WAAW,KACpB,KAAK,IAAI,WAAW,GAAG;AACvB,WAAO;AAAA,EACX;AACA,SAAO;AACX;;;AC1BA,SAAS,YAAY,SAAS,UAAU,QAAQ,GAAG;AAC/C,SAAO,UAAU,WAAW;AAChC;AACA,SAAS,eAAe,SAAS,UAAU,QAAQ,GAAG,oBAAoB,MAAM;AAC5E,SAAO,oBACD,YAAY,WAAW,CAAC,SAAS,UAAU,KAAK,IAChD,YAAY,UAAU,YAAY;AAC5C;AACA,SAAS,sBAAsB,SAAS,UAAU,OAAO,mBAAmB;AACxE,SAAO,oBAAoB,WAAW,WAAW,QAAQ,WAAW,CAAC;AACzE;;;ACJA,IAAM,YAAY,CAAC,WAAW;AAC1B,QAAM,gBAAgB,CAAC,EAAE,MAAM,MAAM,OAAO,KAAK;AACjD,SAAO;AAAA,IACH,OAAO,MAAM,WAAK,OAAO,eAAe,IAAI;AAAA,IAC5C,MAAM,MAAM,WAAW,OAAO,aAAa;AAAA,EAC/C;AACJ;AACA,SAAS,QAAQ,IAAI;AACjB,MAAI,IAAI;AACR,MAAI,EAAE,MAAM,WAAW,MAAM,SAAS,WAAW,UAAU,GAAG,QAAQ,YAAY,GAAG,aAAa,QAAQ,cAAc,GAAG,QAAQ,QAAQ,YAAY,UAAU,SAAS,IAAI,IAAI,UAAU,OAAO,IAAI,CAAC,QAAQ,YAAY,UAAU,WAAW,UAAU,cAAc,eAAe,UAAU,UAAU,cAAc,YAAY,UAAU,CAAC;AACjV,MAAI,EAAE,GAAG,IAAI;AACb,MAAI;AACJ,MAAI,cAAc;AAClB,MAAI,mBAAmB,QAAQ;AAC/B,MAAI;AACJ,MAAI,aAAa;AACjB,MAAI,oBAAoB;AACxB,MAAI;AACJ,QAAM,WAAW,2BAA2B,OAAO;AACnD,OAAK,MAAM,KAAK,UAAU,wBAAwB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,MAAM,EAAE,GAAG;AACtG,4BAAwB,YAAY,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,GAAG;AAAA,MACtD,OAAO;AAAA,IACX,CAAC;AACD,WAAO;AACP,SAAK;AAAA,EACT;AACA,QAAM,YAAY,SAAS,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,MAAM,GAAG,CAAC,CAAC;AAClF,WAAS,SAAS;AACd;AACA,QAAI,eAAe,WAAW;AAC1B,0BAAoB,cAAc,MAAM;AACxC,gBAAU,eAAe,SAAS,kBAAkB,aAAa,iBAAiB;AAAA,IACtF,OACK;AACD,gBAAU,YAAY,SAAS,kBAAkB,WAAW;AAC5D,UAAI,eAAe;AACf,kBAAU,WAAW;AAAA,IAC7B;AACA,iBAAa;AACb,gBAAY,SAAS;AAAA,EACzB;AACA,WAAS,WAAW;AAChB,mBAAe,KAAK;AACpB,kBAAc,WAAW;AAAA,EAC7B;AACA,WAAS,OAAO,OAAO;AACnB,QAAI,CAAC;AACD,cAAQ,CAAC;AACb,eAAW;AACX,QAAI,CAAC,YAAY;AACb,YAAM,QAAQ,UAAU,KAAK,KAAK,IAAI,GAAG,OAAO,CAAC;AACjD,eAAS,MAAM;AACf,UAAI;AACA,iBAAS,sBAAsB,MAAM;AACzC,mBAAa,oBAAoB,MAAM,OAAO,WAAW;AAAA,IAC7D;AACA,iBAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,MAAM;AACnE,QAAI,YAAY;AACZ,UAAI,gBAAgB;AAChB,6BAAqB,QAAQ,qBAAqB,SAAS,mBAAoB,mBAAmB;AACtG,UAAI,cAAc,WAAW;AACzB,8BAAsB,SAAS,kBAAkB,aAAa,iBAAiB,KAAK,OAAO;AAAA,MAC/F,OACK;AACD,iBAAS;AAAA,MACb;AAAA,IACJ;AAAA,EACJ;AACA,WAAS,OAAO;AACZ,eAAW,QAAQ,WAAW,SAAS,SAAS,OAAO;AACvD,qBAAiB,OAAO,MAAM;AAC9B,mBAAe,MAAM;AAAA,EACzB;AACA,cAAY,KAAK;AACjB,SAAO;AAAA,IACH,MAAM,MAAM;AACR,iBAAW,QAAQ,WAAW,SAAS,SAAS,OAAO;AACvD,qBAAe,KAAK;AAAA,IACxB;AAAA,EACJ;AACJ;;;ACtFA,SAAS,kBAAkB,UAAU,eAAe;AAChD,SAAO,gBAAgB,YAAY,MAAO,iBAAiB;AAC/D;;;ACEA,SAAS,QAAQ,EAAE,OAAO,GAAG,WAAW,GAAG,KAAK,KAAK,QAAQ,KAAK,eAAe,KAAK,kBAAkB,KAAK,gBAAgB,IAAI,YAAY,GAAG,cAAc,QAAQ,UAAU,YAAY,OAAQ,GAAG;AACnM,MAAI;AACJ,WAAS,cAAc,GAAG;AACtB,WAAQ,QAAQ,UAAa,IAAI,OAAS,QAAQ,UAAa,IAAI;AAAA,EACvE;AACA,WAAS,gBAAgB,GAAG;AACxB,QAAI,QAAQ;AACR,aAAO;AACX,QAAI,QAAQ;AACR,aAAO;AACX,WAAO,KAAK,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,MAAM,CAAC,IAAI,MAAM;AAAA,EACzD;AACA,WAAS,eAAe,SAAS;AAC7B,yBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,KAAK;AAC1F,uBAAmB,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG;AAAA,MAAE;AAAA,MAAQ,UAAU,CAAC,MAAM;AACxF,YAAI;AACJ,qBAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,CAAC;AAC9D,SAAC,KAAK,QAAQ,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,SAAS,CAAC;AAAA,MACnF;AAAA,MAAG;AAAA,MACH;AAAA,IAAO,CAAC,CAAC;AAAA,EACjB;AACA,WAAS,YAAY,SAAS;AAC1B,mBAAe,OAAO,OAAO,EAAE,MAAM,UAAU,WAAW,iBAAiB,SAAS,eAAe,UAAU,GAAG,OAAO,CAAC;AAAA,EAC5H;AACA,MAAI,cAAc,IAAI,GAAG;AACrB,gBAAY,EAAE,MAAM,UAAU,IAAI,gBAAgB,IAAI,EAAE,CAAC;AAAA,EAC7D,OACK;AACD,QAAI,SAAS,QAAQ,WAAW;AAChC,QAAI,OAAO,iBAAiB;AACxB,eAAS,aAAa,MAAM;AAChC,UAAM,WAAW,gBAAgB,MAAM;AACvC,UAAM,UAAU,aAAa,MAAM,KAAK;AACxC,QAAI;AACJ,QAAI;AACJ,UAAM,gBAAgB,CAAC,MAAM;AACzB,aAAO;AACP,gBAAU;AACV,iBAAW,kBAAkB,IAAI,MAAM,aAAa,EAAE,KAAK;AAC3D,UAAK,YAAY,KAAK,IAAI,YACrB,YAAY,MAAM,IAAI,UAAW;AAClC,oBAAY,EAAE,MAAM,GAAG,IAAI,UAAU,SAAS,CAAC;AAAA,MACnD;AAAA,IACJ;AACA,mBAAe;AAAA,MACX,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU,cAAc,MAAM,IAAI,gBAAgB;AAAA,IACtD,CAAC;AAAA,EACL;AACA,SAAO;AAAA,IACH,MAAM,MAAM,qBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,KAAK;AAAA,EAC1G;AACJ;;;AC9DA,IAAM,WAAW,CAAC,MAAM;AACxB,IAAM,kBAAkB,CAAC,oBAAoB,aAAa,CAAC,UAAU,QAAQ,MAAM;AAC/E,QAAM,eAAe,SAAS;AAC9B,QAAM,6BAA6B,EAAE,IAAI,WAAW,MAAM,IAAI,kBAAkB,KAAK,IAAI,YAAY,CAAC;AACtG,SAAO,gBAAgB,IACjB,SAAS,6BACT,SAAS;AACnB;AACA,IAAM,UAAU,gBAAgB;AAChC,IAAM,cAAc,gBAAgB,KAAK,IAAI;;;ACP7C,IAAM,IAAI,CAAC,IAAI,OAAO,IAAM,IAAM,KAAK,IAAM;AAC7C,IAAM,IAAI,CAAC,IAAI,OAAO,IAAM,KAAK,IAAM;AACvC,IAAM,IAAI,CAAC,OAAO,IAAM;AACxB,IAAM,aAAa,CAAC,GAAG,IAAI,SAAS,EAAE,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,EAAE,KAAK,IAAI,EAAE,EAAE,KAAK;AAC9E,IAAM,WAAW,CAAC,GAAG,IAAI,OAAO,IAAM,EAAE,IAAI,EAAE,IAAI,IAAI,IAAI,IAAM,EAAE,IAAI,EAAE,IAAI,IAAI,EAAE,EAAE;AACpF,IAAM,uBAAuB;AAC7B,IAAM,2BAA2B;AACjC,SAAS,gBAAgB,IAAI,IAAI,IAAI,KAAK,KAAK;AAC3C,MAAI;AACJ,MAAI;AACJ,MAAI,IAAI;AACR,KAAG;AACC,eAAW,MAAM,KAAK,MAAM;AAC5B,eAAW,WAAW,UAAU,KAAK,GAAG,IAAI;AAC5C,QAAI,WAAW,GAAK;AAChB,WAAK;AAAA,IACT,OACK;AACD,WAAK;AAAA,IACT;AAAA,EACJ,SAAS,KAAK,IAAI,QAAQ,IAAI,wBAC1B,EAAE,IAAI;AACV,SAAO;AACX;AACA,IAAM,mBAAmB;AACzB,IAAM,iBAAiB;AACvB,SAAS,qBAAqB,IAAI,SAAS,KAAK,KAAK;AACjD,WAAS,IAAI,GAAG,IAAI,kBAAkB,EAAE,GAAG;AACvC,UAAM,eAAe,SAAS,SAAS,KAAK,GAAG;AAC/C,QAAI,iBAAiB,GAAK;AACtB,aAAO;AAAA,IACX;AACA,UAAM,WAAW,WAAW,SAAS,KAAK,GAAG,IAAI;AACjD,eAAW,WAAW;AAAA,EAC1B;AACA,SAAO;AACX;AACA,IAAM,mBAAmB;AACzB,IAAM,kBAAkB,KAAO,mBAAmB;AAClD,SAAS,YAAY,KAAK,KAAK,KAAK,KAAK;AACrC,MAAI,QAAQ,OAAO,QAAQ;AACvB,WAAO;AACX,QAAM,eAAe,IAAI,aAAa,gBAAgB;AACtD,WAAS,IAAI,GAAG,IAAI,kBAAkB,EAAE,GAAG;AACvC,iBAAa,CAAC,IAAI,WAAW,IAAI,iBAAiB,KAAK,GAAG;AAAA,EAC9D;AACA,WAAS,SAAS,IAAI;AAClB,QAAI,gBAAgB;AACpB,QAAI,gBAAgB;AACpB,UAAM,aAAa,mBAAmB;AACtC,WAAO,kBAAkB,cAAc,aAAa,aAAa,KAAK,IAAI,EAAE,eAAe;AACvF,uBAAiB;AAAA,IACrB;AACA,MAAE;AACF,UAAM,QAAQ,KAAK,aAAa,aAAa,MACxC,aAAa,gBAAgB,CAAC,IAAI,aAAa,aAAa;AACjE,UAAM,YAAY,gBAAgB,OAAO;AACzC,UAAM,eAAe,SAAS,WAAW,KAAK,GAAG;AACjD,QAAI,gBAAgB,gBAAgB;AAChC,aAAO,qBAAqB,IAAI,WAAW,KAAK,GAAG;AAAA,IACvD,WACS,iBAAiB,GAAK;AAC3B,aAAO;AAAA,IACX,OACK;AACD,aAAO,gBAAgB,IAAI,eAAe,gBAAgB,iBAAiB,KAAK,GAAG;AAAA,IACvF;AAAA,EACJ;AACA,SAAO,CAAC,MAAM,MAAM,KAAK,MAAM,IAAI,IAAI,WAAW,SAAS,CAAC,GAAG,KAAK,GAAG;AAC3E;;;AC/DA,IAAM,cAAc,CAAC;AAErB,IAAM,sBAAN,MAA0B;AAAA,EACxB,gBAAgC,oBAAI,IAAI;AAAA,EACxC,IAAI,SAAS;AACX,SAAK,cAAc,IAAI,OAAO;AAC9B,WAAO,MAAM,KAAK,cAAc,OAAO,OAAO;AAAA,EAChD;AAAA,EACA,OAAOC,IAAGC,IAAGC,IAAG;AACd,QAAI,CAAC,KAAK,cAAc;AACtB;AACF,eAAW,WAAW,KAAK,cAAe,SAAQF,IAAGC,IAAGC,EAAC;AAAA,EAC3D;AAAA,EACA,QAAQ;AACN,SAAK,cAAc,MAAM;AAAA,EAC3B;AACF;AAEA,SAAS,QAAQ,OAAO;AACtB,SAAO,CAAC,OAAO,MAAM,OAAO,WAAW,KAAK,CAAC;AAC/C;AACA,IAAM,cAAN,MAAkB;AAAA;AAAA;AAAA;AAAA,EAIhB;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY;AAAA;AAAA;AAAA;AAAA,EAIZ,cAAc;AAAA;AAAA;AAAA;AAAA,EAId,oBAAoB,IAAI,oBAAoB;AAAA;AAAA;AAAA;AAAA,EAI5C;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnB,YAAY,MAAM;AAChB,SAAK,OAAO,KAAK,UAAU;AAC3B,SAAK,mBAAmB,QAAQ,KAAK,OAAO;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,cAAc;AACrB,WAAO,KAAK,kBAAkB,IAAI,YAAY;AAAA,EAChD;AAAA,EACA,iBAAiB;AACf,SAAK,kBAAkB,MAAM;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,GAAG;AACL,SAAK,gBAAgB,CAAC;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,CAAC,MAAM;AACvB,SAAK,OAAO,KAAK;AACjB,SAAK,UAAU;AACf,UAAM,EAAE,OAAO,UAAU,IAAI,aAAa;AAC1C,QAAI,KAAK,gBAAgB,WAAW;AAClC,WAAK,YAAY;AACjB,WAAK,cAAc;AAAA,IACrB;AACA,eAAK,WAAW,KAAK,qBAAqB;AAC1C,SAAK,kBAAkB,OAAO,KAAK,OAAO;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM;AACJ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc;AACZ,WAAO,KAAK,mBAAmB,kBAAkB,OAAO,WAAW,KAAK,OAAO,IAAI,OAAO,WAAW,KAAK,IAAI,GAAG,KAAK,SAAS,IAAI;AAAA,EACrI;AAAA;AAAA;AAAA;AAAA,EAIA,wBAAwB,MAAM,WAAK,WAAW,KAAK,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhE,gBAAgB,CAAC,EAAE,UAAU,MAAM;AACjC,QAAI,CAAC,KAAK;AACR,WAAK,mBAAmB,QAAQ,KAAK,OAAO;AAC9C,QAAI,cAAc,KAAK;AACrB,WAAK,OAAO,KAAK;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,WAAW;AACf,SAAK,KAAK;AACV,WAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,YAAM,EAAE,KAAK,IAAI,UAAU,OAAO;AAClC,WAAK,gBAAgB;AAAA,IACvB,CAAC,EAAE,KAAK,MAAM,KAAK,eAAe,CAAC;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO;AACL,QAAI,KAAK;AACP,WAAK,cAAc;AACrB,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACf,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACR,SAAK,kBAAkB,MAAM;AAC7B,SAAK,KAAK;AAAA,EACZ;AACF;AACA,SAAS,eAAe,MAAM;AAC5B,SAAO,IAAI,YAAY,IAAI;AAC7B;AAEA,IAAM,EAAE,QAAQ,IAAI;AACpB,SAAS,kBAAkB;AACzB,QAAM,eAAe,IAAI,CAAC,CAAC;AAC3B,QAAM,OAAO,CAAC,SAAS;AACrB,UAAM,aAAa,CAAC,QAAQ;AAC1B,UAAI,CAAC,aAAa,MAAM,GAAG;AACzB;AACF,mBAAa,MAAM,GAAG,EAAE,KAAK;AAC7B,mBAAa,MAAM,GAAG,EAAE,QAAQ;AAChC,aAAO,aAAa,MAAM,GAAG;AAAA,IAC/B;AACA,QAAI,MAAM;AACR,UAAI,QAAQ,IAAI,GAAG;AACjB,aAAK,QAAQ,UAAU;AAAA,MACzB,OAAO;AACL,mBAAW,IAAI;AAAA,MACjB;AAAA,IACF,OAAO;AACL,aAAO,KAAK,aAAa,KAAK,EAAE,QAAQ,UAAU;AAAA,IACpD;AAAA,EACF;AACA,QAAM,MAAM,CAAC,KAAK,MAAM,WAAW;AACjC,QAAI,aAAa,MAAM,GAAG;AACxB,aAAO,aAAa,MAAM,GAAG;AAC/B,UAAM,cAAc,eAAe,IAAI;AACvC,gBAAY,SAAS,CAAC,MAAM,OAAO,GAAG,IAAI,CAAC;AAC3C,iBAAa,MAAM,GAAG,IAAI;AAC1B,WAAO;AAAA,EACT;AACA,iBAAe,IAAI;AACnB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,kBAAkB,GAAG;AAC5B,SAAO,MAAM,QAAQ,CAAC;AACxB;AACA,SAAS,oBAAoB;AAC3B,SAAO;AAAA,IACL,MAAM;AAAA,IACN,WAAW;AAAA,IACX,SAAS;AAAA,IACT,WAAW;AAAA,IACX,WAAW;AAAA,EACb;AACF;AACA,SAAS,uBAAuB,IAAI;AAClC,SAAO;AAAA,IACL,MAAM;AAAA,IACN,WAAW;AAAA,IACX,SAAS,OAAO,IAAI,IAAI,KAAK,KAAK,GAAG,IAAI;AAAA,IACzC,WAAW;AAAA,IACX,WAAW;AAAA,EACb;AACF;AACA,SAAS,iBAAiB,IAAI;AAC5B,SAAO;AAAA,IACL,MAAM;AAAA,IACN,WAAW;AAAA,IACX,SAAS,OAAO,IAAI,MAAM;AAAA,IAC1B,WAAW;AAAA,IACX,WAAW;AAAA,EACb;AACF;AACA,SAAS,cAAc;AACrB,SAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AACF;AACA,SAASC,WAAU,QAAQ;AACzB,SAAO;AAAA,IACL,MAAM;AAAA,IACN,UAAU;AAAA,IACV;AAAA,EACF;AACF;AACA,IAAM,qBAAqB;AAAA,EACzB,SAAS;AAAA,EACT,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,iBAAiB;AAAA,EACjB,OAAO;AAAA,EACP,SAAS;AACX;AACA,SAAS,qBAAqB,UAAU,IAAI;AAC1C,MAAI;AACJ,MAAI,kBAAkB,EAAE,GAAG;AACzB,wBAAoBA;AAAA,EACtB,OAAO;AACL,wBAAoB,mBAAmB,QAAQ,KAAK,mBAAmB;AAAA,EACzE;AACA,SAAO,EAAE,IAAI,GAAG,kBAAkB,EAAE,EAAE;AACxC;AAEA,IAAM,MAAM;AAAA,EACV,GAAG;AAAA,EACH,WAAW,KAAK;AAClB;AACA,IAAM,aAAa;AAAA;AAAA,EAEjB;AAAA,EACA,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,MAAM;AAAA,EACN,QAAQ;AAAA;AAAA,EAER,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,QAAQ;AAAA,EACR,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,wBAAwB;AAAA;AAAA,EAExB,OAAO;AAAA,EACP,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,MAAM;AAAA,EACN,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AAAA;AAAA,EAEN,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,eAAe;AAAA,EACf,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,aAAa;AAAA,EACb,cAAc;AAAA,EACd,YAAY;AAAA;AAAA,EAEZ,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,aAAa;AAAA,EACb,sBAAsB;AAAA,EACtB,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA;AAAA,EAET,QAAQ;AAAA,EACR;AAAA,EACA,cAAc;AAAA;AAAA,EAEd,aAAa;AAAA,EACb,eAAe;AAAA,EACf,YAAY;AACd;AACA,IAAM,eAAe,CAAC,QAAQ,WAAW,GAAG;AAC5C,SAAS,eAAe,OAAO,MAAM;AACnC,SAAO,QAAQ,OAAO,UAAU,YAAY,KAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AACvF;AACA,SAASC,mBAAkB,KAAK,OAAO;AACrC,MAAI,mBAAmB,aAAa,GAAG;AACvC,MAAI,qBAAqB;AACvB,uBAAmB;AACrB,SAAO,iBAAiB,oBAAoB,iBAAiB,kBAAkB,KAAK,IAAI;AAC1F;AAEA,IAAM,eAAe;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,SAAS,2BAA2B,YAAY;AAC9C,MAAI,MAAM,QAAQ,UAAU,GAAG;AAC7B,UAAM,CAAC,IAAI,IAAI,IAAI,EAAE,IAAI;AACzB,WAAO,YAAY,IAAI,IAAI,IAAI,EAAE;AAAA,EACnC,WAAW,OAAO,eAAe,UAAU;AACzC,WAAO,aAAa,UAAU;AAAA,EAChC;AACA,SAAO;AACT;AACA,SAAS,cAAc,MAAM;AAC3B,SAAO,MAAM,QAAQ,IAAI,KAAK,OAAO,KAAK,CAAC,MAAM;AACnD;AACA,SAAS,aAAa,KAAK,OAAO;AAChC,MAAI,QAAQ;AACV,WAAO;AACT,MAAI,OAAO,UAAU,YAAY,MAAM,QAAQ,KAAK;AAClD,WAAO;AACT,MAAI,OAAO,UAAU,YAAY,QAAQ,KAAK,KAAK,KAAK,CAAC,MAAM,WAAW,MAAM,GAAG;AACjF,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,iBAAiB,SAAS;AACjC,MAAI,MAAM,QAAQ,QAAQ,EAAE,KAAK,QAAQ,GAAG,CAAC,MAAM,MAAM;AACvD,YAAQ,KAAK,CAAC,GAAG,QAAQ,EAAE;AAC3B,YAAQ,GAAG,CAAC,IAAI,QAAQ;AAAA,EAC1B;AACA,SAAO;AACT;AACA,SAAS,oCAAoC,EAAE,MAAM,OAAO,OAAO,GAAG,WAAW,GAAG;AAClF,QAAM,UAAU,EAAE,GAAG,WAAW;AAChC,MAAI;AACF,YAAQ,SAAS;AACnB,MAAI,MAAM;AACR,YAAQ,OAAO,cAAc,IAAI,IAAI,KAAK,IAAI,0BAA0B,IAAI,2BAA2B,IAAI;AAAA,EAC7G;AACA,MAAI;AACF,YAAQ,UAAU,CAAC;AACrB,SAAO;AACT;AACA,SAAS,6BAA6B,YAAY,SAAS,KAAK;AAC9D,MAAI,MAAM,QAAQ,QAAQ,EAAE,GAAG;AAC7B,QAAI,CAAC,WAAW;AACd,iBAAW,WAAW;AAAA,EAC1B;AACA,mBAAiB,OAAO;AACxB,MAAI,CAAC,oBAAoB,UAAU,GAAG;AACpC,iBAAa;AAAA,MACX,GAAG;AAAA,MACH,GAAG,qBAAqB,KAAK,QAAQ,EAAE;AAAA,IACzC;AAAA,EACF;AACA,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG,oCAAoC,UAAU;AAAA,EACnD;AACF;AACA,SAAS,oBAAoB,EAAE,OAAO,QAAQ,YAAY,aAAa,MAAM,GAAG,WAAW,GAAG;AAC5F,SAAO,CAAC,CAAC,OAAO,KAAK,UAAU,EAAE;AACnC;AACA,SAAS,mBAAmB,YAAY,KAAK;AAC3C,SAAO,WAAW,GAAG,KAAK,WAAW,WAAW;AAClD;AACA,SAAS,aAAa,KAAK,OAAO,QAAQ,YAAY,YAAY;AAChE,QAAM,kBAAkB,mBAAmB,YAAY,GAAG;AAC1D,MAAI,SAAS,gBAAgB,SAAS,QAAQ,gBAAgB,SAAS,SAAS,MAAM,IAAI,IAAI,gBAAgB;AAC9G,QAAM,qBAAqB,aAAa,KAAK,MAAM;AACnD,MAAI,WAAW,UAAU,sBAAsB,OAAO,WAAW;AAC/D,aAASA,mBAAkB,KAAK,MAAM;AACxC,QAAM,qBAAqB,aAAa,KAAK,MAAM;AACnD,WAAS,MAAM,UAAU;AACvB,UAAM,UAAU;AAAA,MACd,MAAM;AAAA,MACN,IAAI;AAAA,MACJ,UAAU,WAAW,WAAW,WAAW,WAAW,MAAM,YAAY;AAAA,MACxE,UAAU,CAAC,MAAM,MAAM,IAAI,CAAC;AAAA,IAC9B;AACA,WAAO,gBAAgB,SAAS,aAAa,gBAAgB,SAAS,UAAU,QAAQ,EAAE,GAAG,SAAS,GAAG,gBAAgB,CAAC,IAAI,QAAQ;AAAA,MACpI,GAAG,6BAA6B,iBAAiB,SAAS,GAAG;AAAA,MAC7D,UAAU,CAAC,MAAM;AACf,gBAAQ,SAAS,CAAC;AAClB,YAAI,gBAAgB;AAClB,0BAAgB,SAAS,CAAC;AAAA,MAC9B;AAAA,MACA,YAAY,MAAM;AAChB,YAAI;AACF,qBAAW;AACb,YAAI;AACF,mBAAS;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AACA,WAAS,IAAI,UAAU;AACrB,UAAM,IAAI,MAAM;AAChB,QAAI;AACF,iBAAW;AACb,QAAI;AACF,eAAS;AACX,WAAO,EAAE,MAAM,MAAM;AAAA,IACrB,EAAE;AAAA,EACJ;AACA,SAAO,CAAC,sBAAsB,CAAC,sBAAsB,gBAAgB,SAAS,QAAQ,MAAM;AAC9F;AAEA,SAAS,uBAAuB;AAC9B,QAAM,EAAE,cAAc,MAAM,IAAI,IAAI,gBAAgB;AACpD,QAAM,OAAO,CAAC,KAAK,OAAO,QAAQ,aAAa,CAAC,GAAG,eAAe;AAChE,UAAM,OAAO,OAAO,GAAG;AACvB,UAAM,cAAc,IAAI,KAAK,MAAM,MAAM;AACzC,QAAI,cAAc,WAAW,WAAW;AACtC,kBAAY,IAAI,KAAK;AACrB;AAAA,IACF;AACA,UAAM,YAAY,aAAa,KAAK,aAAa,OAAO,YAAY,UAAU;AAC9E,gBAAY,MAAM,SAAS;AAAA,EAC7B;AACA,SAAO,EAAE,cAAc,MAAM,KAAK;AACpC;AAEA,SAAS,kBAAkB,kBAAkB,WAAW,CAAC,GAAG,EAAE,cAAc,MAAM,KAAK,IAAI,qBAAqB,GAAG;AACjH,QAAM,YAAY,MAAM,QAAQ;AAChC,QAAM,cAAc,IAAI,KAAK;AAC7B;AAAA,IACE;AAAA,IACA,CAAC,WAAW;AACV,kBAAY,QAAQ,OAAO,OAAO,MAAM,EAAE,OAAO,CAAC,UAAU,MAAM,YAAY,CAAC,EAAE,SAAS;AAAA,IAC5F;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,IACR;AAAA,EACF;AACA,QAAM,oBAAoB,CAAC,YAAY;AACrC,QAAI,CAAC,aAAa,CAAC,UAAU,OAAO;AAClC,YAAM,IAAI,MAAM,eAAe,OAAO,kBAAkB;AAC1D,WAAO,UAAU,OAAO;AAAA,EAC1B;AACA,QAAM,QAAQ,CAAC,YAAY;AACzB,QAAI,OAAO,YAAY;AACrB,gBAAU,kBAAkB,OAAO;AACrC,UAAM,aAAa,OAAO,QAAQ,OAAO,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;AAC/D,UAAI,QAAQ;AACV,eAAO;AACT,aAAO,IAAI;AAAA,QACT,CAAC;AAAA;AAAA,UAEC,KAAK,KAAK,OAAO,kBAAkB,QAAQ,cAAc,qBAAqB,KAAK,QAAQ,GAAG,CAAC,GAAG,OAAO;AAAA;AAAA,MAE7G;AAAA,IACF,CAAC,EAAE,OAAO,OAAO;AACjB,mBAAe,kBAAkB;AAC/B,YAAM,QAAQ,IAAI,UAAU;AAC5B,cAAQ,YAAY,aAAa;AAAA,IACnC;AACA,WAAO,QAAQ,IAAI,CAAC,gBAAgB,CAAC,CAAC;AAAA,EACxC;AACA,QAAM,MAAM,CAAC,YAAY;AACvB,UAAM,cAAc,SAAW,OAAO,IAAI,UAAU,kBAAkB,OAAO;AAC7E,WAAO,QAAQ,WAAW,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACpD,UAAI,QAAQ;AACV;AACF,WAAK,KAAK,OAAO,kBAAkB;AAAA,QACjC,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,QAAM,QAAQ,OAAO,SAAS;AAC5B,QAAI;AACJ,QAAI,WAAW;AACb,UAAI,UAAU;AACZ,uBAAe,UAAU;AAC3B,UAAI,CAAC,UAAU,SAAS,UAAU;AAChC,uBAAe,UAAU;AAAA,IAC7B;AACA,QAAI,CAAC,cAAc;AACjB,WAAK;AACL;AAAA,IACF;AACA,UAAM,MAAM,YAAY;AACxB,SAAK;AAAA,EACP;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,YAAY,OAAO,WAAW;AACpC,IAAM,wBAAwB,MAAM,cAAc,OAAO,kBAAkB,QAAQ,YAAY,KAAK;AACpG,IAAM,sBAAsB,MAAM,cAAc,OAAO,iBAAiB,QAAQ,YAAY,KAAK;AACjG,IAAM,sBAAsB,MAAM,cAAc,OAAO,gBAAgB,QAAQ,YAAY,KAAK;AAEhG,SAAS,uBAAuB,EAAE,QAAQ,OAAO,UAAU,MAAM,GAAG;AAClE,QAAM,YAAY,MAAM,QAAQ;AAChC,QAAM,UAAU,IAAI,KAAK;AACzB,QAAM,SAAS,IAAI,KAAK;AACxB,QAAM,UAAU,IAAI,KAAK;AACzB,QAAM,cAAc,SAAS,MAAM;AACjC,QAAI,SAAS,CAAC,GAAG,OAAO,KAAK,MAAM,SAAS,CAAC,CAAC,CAAC;AAC/C,QAAI,CAAC;AACH,aAAO;AACT,QAAI,UAAU;AACZ,eAAS,CAAC,GAAG,QAAQ,GAAG,OAAO,KAAK,UAAU,OAAO,CAAC;AACxD,QAAI,UAAU;AACZ,eAAS,CAAC,GAAG,QAAQ,GAAG,OAAO,KAAK,UAAU,MAAM,CAAC;AACvD,QAAI,UAAU;AACZ,eAAS,CAAC,GAAG,QAAQ,GAAG,OAAO,KAAK,UAAU,OAAO,CAAC;AACxD,WAAO;AAAA,EACT,CAAC;AACD,QAAM,qBAAqB,SAAS,MAAM;AACxC,UAAM,SAAS,CAAC;AAChB,WAAO,OAAO,QAAQ,MAAM,KAAK;AACjC,QAAI,QAAQ,SAAS,UAAU;AAC7B,aAAO,OAAO,QAAQ,UAAU,OAAO;AACzC,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO,OAAO,QAAQ,UAAU,MAAM;AACxC,QAAI,QAAQ,SAAS,UAAU;AAC7B,aAAO,OAAO,QAAQ,UAAU,OAAO;AACzC,eAAW,OAAO,QAAQ;AACxB,UAAI,CAAC,YAAY,MAAM,SAAS,GAAG;AACjC,eAAO,OAAO,GAAG;AAAA,IACrB;AACA,WAAO;AAAA,EACT,CAAC;AACD,MAAI,UAAU,SAAS;AACrB,qBAAiB,QAAQ,cAAc,MAAM,QAAQ,QAAQ,IAAI;AACjE,qBAAiB,QAAQ,cAAc,MAAM;AAC3C,cAAQ,QAAQ;AAChB,aAAO,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH;AACA,MAAI,UAAU,QAAQ;AACpB,QAAI,oBAAoB,GAAG;AACzB,uBAAiB,QAAQ,aAAa,MAAM,OAAO,QAAQ,IAAI;AAC/D,uBAAiB,QAAQ,WAAW,MAAM,OAAO,QAAQ,KAAK;AAAA,IAChE;AACA,QAAI,sBAAsB,GAAG;AAC3B,uBAAiB,QAAQ,eAAe,MAAM,OAAO,QAAQ,IAAI;AACjE,uBAAiB,QAAQ,aAAa,MAAM,OAAO,QAAQ,KAAK;AAAA,IAClE;AACA,QAAI,oBAAoB,GAAG;AACzB,uBAAiB,QAAQ,cAAc,MAAM,OAAO,QAAQ,IAAI;AAChE,uBAAiB,QAAQ,YAAY,MAAM,OAAO,QAAQ,KAAK;AAAA,IACjE;AAAA,EACF;AACA,MAAI,UAAU,SAAS;AACrB,qBAAiB,QAAQ,SAAS,MAAM,QAAQ,QAAQ,IAAI;AAC5D,qBAAiB,QAAQ,QAAQ,MAAM,QAAQ,QAAQ,KAAK;AAAA,EAC9D;AACA,QAAM,CAAC,SAAS,QAAQ,OAAO,GAAG,MAAM;AACtC,UAAM,mBAAmB,KAAK;AAAA,EAChC,CAAC;AACH;AAEA,SAAS,uBAAuB,EAAE,KAAK,QAAQ,UAAU,QAAQ,GAAG;AAClE,QAAM,YAAY,MAAM,QAAQ;AAChC;AAAA,IACE,MAAM;AAAA,IACN,MAAM;AACJ,UAAI,CAAC;AACH;AACF,UAAI,UAAU,SAAS;AACrB,YAAI,SAAS;AACb,gBAAQ,QAAQ;AAAA,MAClB;AACA,UAAI,UAAU;AACZ,gBAAQ,QAAQ;AAAA,IACpB;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,OAAO;AAAA,IACT;AAAA,EACF;AACF;AAEA,SAAS,qBAAqB,EAAE,OAAO,MAAM,GAAG;AAC9C;AAAA,IACE;AAAA,IACA,CAAC,WAAW;AACV,UAAI;AACF,cAAM,MAAM;AAAA,IAChB;AAAA,IACA;AAAA,MACE,WAAW;AAAA,IACb;AAAA,EACF;AACF;AAEA,SAAS,wBAAwB,EAAE,QAAQ,UAAU,QAAQ,GAAG;AAC9D,QAAM,YAAY,MAAM,QAAQ;AAChC,MAAI,cAAc,UAAU,WAAW,UAAU,cAAc;AAC7D,4BAAwB,QAAQ,CAAC,CAAC,EAAE,eAAe,CAAC,MAAM;AACxD,UAAI,UAAU,SAAS;AACrB,YAAI;AACF,kBAAQ,QAAQ;AAAA,YACb,SAAQ,QAAQ;AAAA,MACvB,WAAW,UAAU,aAAa;AAChC,YAAI,kBAAkB,QAAQ,UAAU;AACtC,kBAAQ,QAAQ;AAAA,iBACT,CAAC,QAAQ;AAChB,kBAAQ,QAAQ;AAAA,MACpB;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEA,SAAS,kBAAkB,UAAU,UAAU;AAAA,EAC7C,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,gBAAgB;AAClB,GAAG;AACD,MAAI,QAAQ;AACV,2BAAuB,QAAQ;AACjC,MAAI,QAAQ;AACV,yBAAqB,QAAQ;AAC/B,MAAI,QAAQ;AACV,4BAAwB,QAAQ;AAClC,MAAI,QAAQ;AACV,2BAAuB,QAAQ;AACnC;AAEA,SAAS,cAAc,QAAQ,CAAC,GAAG;AACjC,QAAM,QAAQ,SAAS;AAAA,IACrB,GAAG;AAAA,EACL,CAAC;AACD,QAAM,QAAQ,IAAI,CAAC,CAAC;AACpB;AAAA,IACE;AAAA,IACA,MAAM;AACJ,YAAM,SAAS,CAAC;AAChB,iBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,KAAK,GAAG;AAChD,cAAM,YAAY,aAAa,GAAG;AAClC,cAAM,cAAc,eAAe,OAAO,SAAS;AACnD,eAAO,GAAG,IAAI;AAAA,MAChB;AACA,YAAM,QAAQ;AAAA,IAChB;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,IACR;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,oBAAoB,QAAQ,UAAU;AAC7C;AAAA,IACE,MAAM,aAAa,MAAM;AAAA,IACzB,CAAC,OAAO;AACN,UAAI,CAAC;AACH;AACF,eAAS,EAAE;AAAA,IACb;AAAA,IACA;AAAA,MACE,WAAW;AAAA,IACb;AAAA,EACF;AACF;AAEA,IAAM,iBAAiB;AAAA,EACrB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AACL;AACA,SAAS,kBAAkB,QAAQ,CAAC,GAAG,6BAA6B,MAAM;AACxE,QAAM,QAAQ,SAAS,EAAE,GAAG,MAAM,CAAC;AACnC,QAAM,YAAY,IAAI,EAAE;AACxB;AAAA,IACE;AAAA,IACA,CAAC,WAAW;AACV,UAAI,SAAS;AACb,UAAI,0BAA0B;AAC9B,UAAI,+BAA+B,OAAO,KAAK,OAAO,KAAK,OAAO,IAAI;AACpE,cAAM,MAAM,CAAC,OAAO,KAAK,GAAG,OAAO,KAAK,GAAG,OAAO,KAAK,CAAC,EAAE,IAAI,CAAC,QAAQ,eAAe,KAAK,EAAE,CAAC,EAAE,KAAK,GAAG;AACxG,kBAAU,eAAe,GAAG;AAC5B,kCAA0B;AAAA,MAC5B;AACA,iBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AACjD,YAAI,+BAA+B,QAAQ,OAAO,QAAQ,OAAO,QAAQ;AACvE;AACF,cAAM,YAAY,aAAa,GAAG;AAClC,cAAM,cAAc,eAAe,OAAO,SAAS;AACnD,kBAAU,GAAG,eAAe,GAAG,KAAK,GAAG,IAAI,WAAW;AAAA,MACxD;AACA,UAAI,8BAA8B,CAAC;AACjC,kBAAU;AACZ,gBAAU,QAAQ,OAAO,KAAK;AAAA,IAChC;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,IACR;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,gBAAgB,CAAC,IAAI,KAAK,KAAK,GAAG;AACxC,IAAM,QAAQ,CAAC,eAAe,aAAa,SAAS,UAAU,MAAM;AACpE,IAAM,iBAAiB,CAAC,wBAAwB,KAAK,KAAK,GAAG;AAC7D,MAAM,QAAQ,CAAC,iBAAiB;AAC9B,gBAAc,QAAQ,CAAC,YAAY;AACjC,UAAM,MAAM,eAAe;AAC3B,mBAAe,KAAK,GAAG;AAAA,EACzB,CAAC;AACH,CAAC;AACD,IAAM,mBAAmB,IAAI,IAAI,cAAc;AAC/C,SAAS,gBAAgB,KAAK;AAC5B,SAAO,iBAAiB,IAAI,GAAG;AACjC;AACA,IAAM,uBAAuC,oBAAI,IAAI,CAAC,WAAW,WAAW,SAAS,CAAC;AACtF,SAAS,sBAAsB,KAAK;AAClC,SAAO,qBAAqB,IAAI,GAAG;AACrC;AACA,SAAS,YAAY,SAAS;AAC5B,QAAM,YAAY,CAAC;AACnB,QAAM,QAAQ,CAAC;AACf,SAAO,QAAQ,OAAO,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAChD,QAAI,gBAAgB,GAAG,KAAK,sBAAsB,GAAG;AACnD,gBAAU,GAAG,IAAI;AAAA,QACd,OAAM,GAAG,IAAI;AAAA,EACpB,CAAC;AACD,SAAO,EAAE,WAAW,MAAM;AAC5B;AACA,SAAS,eAAe,SAAS;AAC/B,QAAM,EAAE,WAAW,YAAY,OAAO,OAAO,IAAI,YAAY,OAAO;AACpE,QAAM,EAAE,UAAU,IAAI,kBAAkB,UAAU;AAClD,QAAM,EAAE,MAAM,IAAI,cAAc,MAAM;AACtC,MAAI,UAAU;AACZ,UAAM,MAAM,YAAY,UAAU;AACpC,SAAO,MAAM;AACf;AAEA,SAAS,gBAAgB,QAAQ,QAAQ;AACvC,MAAI;AACJ,MAAI;AACJ,QAAM,EAAE,OAAO,MAAM,IAAI,cAAc;AACvC,sBAAoB,QAAQ,CAAC,OAAO;AAClC,cAAU;AACV,eAAW,OAAO,OAAO,KAAK,UAAU,GAAG;AACzC,UAAI,GAAG,MAAM,GAAG,MAAM,QAAQ,GAAG,MAAM,GAAG,MAAM,MAAM,gBAAgB,GAAG,KAAK,sBAAsB,GAAG;AACrG;AACF,YAAM,GAAG,IAAI,GAAG,MAAM,GAAG;AAAA,IAC3B;AACA,QAAI,QAAQ;AACV,aAAO,QAAQ,MAAM,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM,GAAG,MAAM,GAAG,IAAI,KAAK;AAAA,IACxE;AACA,QAAI;AACF,aAAO,KAAK;AAAA,EAChB,CAAC;AACD;AAAA,IACE;AAAA,IACA,CAAC,WAAW;AACV,UAAI,CAAC,SAAS;AACZ,iBAAS;AACT;AAAA,MACF;AACA,iBAAW,OAAO,OAAQ,SAAQ,MAAM,GAAG,IAAI,OAAO,GAAG;AAAA,IAC3D;AAAA,IACA;AAAA,MACE,WAAW;AAAA,IACb;AAAA,EACF;AACA,SAAO;AAAA,IACL,OAAO;AAAA,EACT;AACF;AAEA,SAAS,eAAe,WAAW;AACjC,QAAM,aAAa,UAAU,KAAK,EAAE,MAAM,QAAQ;AAClD,MAAI,WAAW,WAAW;AACxB,WAAO,CAAC;AACV,QAAM,cAAc,CAAC,UAAU;AAC7B,QAAI,MAAM,SAAS,IAAI,KAAK,MAAM,SAAS,KAAK;AAC9C,aAAO,OAAO,WAAW,KAAK;AAChC,QAAI,OAAO,MAAM,OAAO,KAAK,CAAC;AAC5B,aAAO,OAAO,KAAK;AACrB,WAAO;AAAA,EACT;AACA,SAAO,WAAW,OAAO,CAAC,KAAK,eAAe;AAC5C,QAAI,CAAC;AACH,aAAO;AACT,UAAM,CAAC,MAAM,cAAc,IAAI,WAAW,MAAM,GAAG;AACnD,UAAM,aAAa,eAAe,MAAM,GAAG;AAC3C,UAAM,SAAS,WAAW,IAAI,CAAC,QAAQ;AACrC,aAAO,YAAY,IAAI,SAAS,GAAG,IAAI,IAAI,QAAQ,KAAK,EAAE,IAAI,IAAI,KAAK,CAAC;AAAA,IAC1E,CAAC;AACD,UAAM,QAAQ,OAAO,WAAW,IAAI,OAAO,CAAC,IAAI;AAChD,WAAO;AAAA,MACL,GAAG;AAAA,MACH,CAAC,IAAI,GAAG;AAAA,IACV;AAAA,EACF,GAAG,CAAC,CAAC;AACP;AACA,SAAS,mBAAmB,OAAO,WAAW;AAC5C,SAAO,QAAQ,eAAe,SAAS,CAAC,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAClE,UAAM,OAAO,CAAC,KAAK,KAAK,GAAG;AAC3B,QAAI,QAAQ,eAAe;AACzB,UAAI,UAAU,GAAG;AACf,aAAK,QAAQ,CAAC,SAAS,MAAM,IAAI,IAAI,CAAC;AACtC;AAAA,MACF;AACA,YAAM,QAAQ,CAAC,WAAW,UAAU,MAAM,KAAK,KAAK,CAAC,IAAI,SAAS;AAClE;AAAA,IACF;AACA,YAAQ,OAAO,WAAW,GAAG,KAAK,EAAE;AACpC,QAAI,QAAQ,cAAc;AACxB,YAAM,IAAI;AACV;AAAA,IACF;AACA,QAAI,QAAQ,cAAc;AACxB,YAAM,IAAI;AACV;AAAA,IACF;AACA,QAAI,QAAQ,cAAc;AACxB,YAAM,IAAI;AACV;AAAA,IACF;AACA,UAAM,GAAG,IAAI;AAAA,EACf,CAAC;AACH;AAEA,SAAS,oBAAoB,QAAQ,QAAQ;AAC3C,MAAI;AACJ,MAAI;AACJ,QAAM,EAAE,OAAO,UAAU,IAAI,kBAAkB;AAC/C,sBAAoB,QAAQ,CAAC,OAAO;AAClC,cAAU;AACV,QAAI,GAAG,MAAM;AACX,yBAAmB,OAAO,GAAG,MAAM,SAAS;AAC9C,QAAI;AACF,SAAG,MAAM,YAAY;AACvB,QAAI;AACF,aAAO,KAAK;AAAA,EAChB,CAAC;AACD;AAAA,IACE;AAAA,IACA,CAAC,aAAa;AACZ,UAAI,CAAC,SAAS;AACZ,iBAAS;AACT;AAAA,MACF;AACA,cAAQ,MAAM,YAAY;AAAA,IAC5B;AAAA,IACA;AAAA,MACE,WAAW;AAAA,IACb;AAAA,EACF;AACA,SAAO;AAAA,IACL,WAAW;AAAA,EACb;AACF;AAEA,SAAS,cAAc,KAAK;AAC1B,SAAO,OAAO,QAAQ,GAAG;AAC3B;AAEA,SAAS,oBAAoB,QAAQ,eAAe;AAClD,QAAM,mBAAmB,SAAS,CAAC,CAAC;AACpC,QAAM,QAAQ,CAAC,WAAW,OAAO,QAAQ,MAAM,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM,iBAAiB,GAAG,IAAI,KAAK;AACxG,QAAM,EAAE,MAAM,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,QAAM,EAAE,UAAU,IAAI,oBAAoB,QAAQ,KAAK;AACvD;AAAA,IACE;AAAA,IACA,CAAC,WAAW;AACV,oBAAc,MAAM,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC9C,cAAM,UAAU,gBAAgB,GAAG,IAAI,YAAY;AACnD,YAAI,QAAQ,GAAG,KAAK,QAAQ,GAAG,MAAM;AACnC;AACF,gBAAQ,GAAG,IAAI;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,IACR;AAAA,EACF;AACA,sBAAoB,QAAQ,MAAM,iBAAiB,MAAM,aAAa,CAAC;AACvE,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,kBAAkB,WAAW,CAAC,GAAG;AACxC,QAAM,YAAY,MAAM,QAAQ;AAChC,QAAM,UAAU,IAAI;AACpB,QAAM,QAAQ,SAAS,MAAM;AAC3B,QAAI,CAAC,QAAQ;AACX;AACF,WAAO,UAAU,QAAQ,KAAK;AAAA,EAChC,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,UAAU,QAAQ,WAAW,CAAC,GAAG,SAAS;AACjD,QAAM,EAAE,iBAAiB,IAAI,oBAAoB,MAAM;AACvD,QAAM,EAAE,SAAS,MAAM,IAAI,kBAAkB,QAAQ;AACrD,QAAM,WAAW,kBAAkB,kBAAkB,QAAQ;AAC7D,QAAM,WAAW;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL;AACA,oBAAkB,UAAU,OAAO;AACnC,SAAO;AACT;AAEA,IAAM,iBAAiB,CAAC,SAAS,UAAU;AAC3C,IAAM,qBAAqB,CAAC,WAAW,SAAS,SAAS,WAAW,gBAAgB,eAAe,WAAW,UAAU,WAAW,GAAG,cAAc;AACpJ,SAAS,gBAAgB,KAAK;AAC5B,SAAO,eAAe,SAAS,GAAG;AACpC;AACA,SAAS,gBAAgB,MAAM,aAAa;AAC1C,QAAM,SAAS,KAAK,QAAQ,KAAK,QAAQ,KAAK,QAAQ,KAAK,KAAK,QAAQ,KAAK,KAAK,QAAQ,CAAC;AAC3F,MAAI,QAAQ;AACV,QAAI,OAAO,YAAY,SAAW,OAAO,QAAQ,GAAG;AAClD,kBAAY,QAAQ;AAAA,QAClB,GAAG,YAAY;AAAA,QACf,GAAG,OAAO;AAAA,MACZ;AAAA,IACF;AACA,aAAS,OAAO,oBAAoB;AAClC,UAAI,CAAC,UAAU,CAAC,OAAO,GAAG;AACxB;AACF,UAAI,gBAAgB,GAAG,KAAK,OAAO,OAAO,GAAG,MAAM,UAAU;AAC3D,mBAAW,cAAc,CAAC,SAAS,WAAW,aAAa,GAAG;AAC5D,gBAAM,gBAAgB,YAAY,MAAM,UAAU;AAClD,cAAI,iBAAiB;AACnB;AACF,wBAAc,eAAe,CAAC;AAC9B,wBAAc,WAAW,GAAG,IAAI,OAAO,GAAG;AAAA,QAC5C;AACA;AAAA,MACF;AACA,UAAI,SAAW,OAAO,GAAG,CAAC,GAAG;AAC3B,cAAM,OAAO,OAAO,GAAG;AACvB,YAAI,QAAQ;AACV,gBAAM;AACR,oBAAY,MAAM,GAAG,IAAI;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,UAAU,UAAU,WAAW,OAAO;AAC7C,QAAM,WAAW,CAAC,IAAI,SAAS,SAAS;AACtC,UAAM,MAAM,QAAQ,SAAS,OAAO,QAAQ,UAAU,WAAW,QAAQ,QAAQ,KAAK;AACtF,QAAI,OAAO,YAAY,GAAG;AACxB,kBAAY,GAAG,EAAE,KAAK;AACxB,UAAM,iBAAiB,WAAW,gBAAgB,MAAM,QAAQ,KAAK,CAAC,CAAC,IAAI,YAAY,CAAC;AACxF,UAAM,cAAc,IAAI,cAAc;AACtC,QAAI,OAAO,QAAQ,UAAU;AAC3B,kBAAY,QAAQ,QAAQ;AAC9B,oBAAgB,MAAM,WAAW;AACjC,UAAM,gBAAgB,EAAE,gBAAgB,MAAM,gBAAgB,MAAM,cAAc,MAAM,iBAAiB,MAAM;AAC/G,UAAM,iBAAiB;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,OAAG,iBAAiB;AACpB,QAAI;AACF,kBAAY,GAAG,IAAI;AAAA,EACvB;AACA,QAAM,UAAU,CAAC,IAAI,UAAU,UAAU;AACvC,OAAG,kBAAkB,wBAAwB,GAAG,cAAc;AAAA,EAChE;AACA,SAAO;AAAA,IACL,SAAS;AAAA,IACT;AAAA,IACA,YAAY,SAAS,MAAM;AACzB,UAAI,EAAE,SAAS,eAAe,IAAI,QAAQ,SAAS,QAAQ,MAAM,SAAS,CAAC;AAC3E,uBAAiB,MAAM,cAAc;AACrC,YAAM,UAAU,KAAK,CAAC,GAAG,UAAU,WAAW,CAAC,GAAG,kBAAkB,CAAC,CAAC;AACtE,UAAI,CAAC,WAAW,OAAO,KAAK,OAAO,EAAE,WAAW;AAC9C;AACF,YAAM,QAAQ,eAAe,OAAO;AACpC,aAAO;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,OAAO;AAAA,EACX,SAAS;AAAA,IACP,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AACF;AACA,IAAM,cAAc;AAAA,EAClB,SAAS;AAAA,IACP,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,EACX;AACF;AACA,IAAM,kBAAkB;AAAA,EACtB,SAAS;AAAA,IACP,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,EACX;AACF;AAEA,IAAM,MAAM;AAAA,EACV,SAAS;AAAA,IACP,OAAO;AAAA,IACP,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,OAAO;AAAA,IACP,SAAS;AAAA,EACX;AACF;AACA,IAAM,aAAa;AAAA,EACjB,SAAS;AAAA,IACP,OAAO;AAAA,IACP,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,IACP,SAAS;AAAA,EACX;AACF;AACA,IAAM,iBAAiB;AAAA,EACrB,SAAS;AAAA,IACP,OAAO;AAAA,IACP,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,IACP,SAAS;AAAA,EACX;AACF;AAEA,IAAM,WAAW;AAAA,EACf,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AACF;AACA,IAAM,kBAAkB;AAAA,EACtB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AACF;AACA,IAAM,sBAAsB;AAAA,EAC1B,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AACF;AACA,IAAM,YAAY;AAAA,EAChB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AACF;AACA,IAAM,mBAAmB;AAAA,EACvB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AACF;AACA,IAAM,uBAAuB;AAAA,EAC3B,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AACF;AACA,IAAM,UAAU;AAAA,EACd,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AACF;AACA,IAAM,iBAAiB;AAAA,EACrB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AACF;AACA,IAAM,qBAAqB;AAAA,EACzB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AACF;AACA,IAAM,aAAa;AAAA,EACjB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AACF;AACA,IAAM,oBAAoB;AAAA,EACxB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AACF;AACA,IAAM,wBAAwB;AAAA,EAC5B,SAAS;AAAA,IACP,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AACF;AAEA,IAAM,YAAY;AAAA,EAChB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AACF;AACA,IAAM,mBAAmB;AAAA,EACvB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AACF;AACA,IAAM,uBAAuB;AAAA,EAC3B,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AACF;AACA,IAAM,aAAa;AAAA,EACjB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AACF;AACA,IAAM,oBAAoB;AAAA,EACxB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AACF;AACA,IAAM,wBAAwB;AAAA,EAC5B,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AACF;AACA,IAAM,WAAW;AAAA,EACf,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AACF;AACA,IAAM,kBAAkB;AAAA,EACtB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AACF;AACA,IAAM,sBAAsB;AAAA,EAC1B,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AACF;AACA,IAAM,cAAc;AAAA,EAClB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AACF;AACA,IAAM,qBAAqB;AAAA,EACzB,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AACF;AACA,IAAM,yBAAyB;AAAA,EAC7B,SAAS;AAAA,IACP,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AACF;AAEA,IAAM,UAAU;AAAA,EACd,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,QAAQ,KAAK;AACpB,QAAMJ,KAAI;AACV,QAAMC,KAAI;AACV,QAAM,IAAI,IAAI,OAAOD,GAAE,MAAM,EAAE,EAAE,KAAK,GAAG,GAAG,GAAG;AAC/C,SAAO,IAAI,SAAS,EAAE,QAAQ,UAAU,CAAC,MAAM,IAAI,CAAC,EAAE,EAAE,YAAY,EAAE,QAAQ,QAAQ,GAAG,EAAE,QAAQ,GAAG,CAACE,OAAMD,GAAE,OAAOD,GAAE,QAAQE,EAAC,CAAC,CAAC,EAAE,QAAQ,MAAM,OAAO,EAAE,QAAQ,aAAa,EAAE,EAAE,QAAQ,UAAU,GAAG,EAAE,QAAQ,OAAO,EAAE,EAAE,QAAQ,OAAO,EAAE;AAClP;AAEA,IAAM,iBAAiB;AAAA,EACrB,YAAY,KAAK,SAAS,gBAAgB,wBAAwB;AACpE;AAEA,IAAM,uBAAuB;AAAA;AAAA,EAE3B,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAAA;AAAA,EAEA,UAAU;AAAA,IACR,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAAA;AAAA,EAEA,UAAU;AAAA,IACR,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAAA;AAAA,EAEA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAAA;AAAA,EAEA,OAAO;AAAA,IACL,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAAA,EACA,OAAO;AAAA,IACL,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAAA;AAAA,EAEA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAAA,EACA,aAAa;AAAA,IACX,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAAA;AAAA,EAEA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAAA,EACA,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAAA,EACA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAAA;AAAA,EAEA,OAAO;AAAA,IACL,MAAM,CAAC,QAAQ,MAAM;AAAA,IACrB,UAAU;AAAA,EACZ;AAAA,EACA,UAAU;AAAA,IACR,MAAM,CAAC,QAAQ,MAAM;AAAA,IACrB,UAAU;AAAA,EACZ;AACF;AACA,SAASG,UAAS,KAAK;AACrB,SAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AACjD;AACA,SAAS,MAAM,GAAG;AAChB,MAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,WAAO,EAAE,IAAI,KAAK;AAAA,EACpB;AACA,MAAIA,UAAS,CAAC,GAAG;AACf,UAAM,MAAM,CAAC;AACb,eAAW,OAAO,GAAG;AACnB,UAAI,GAAG,IAAI,MAAM,EAAE,GAAG,CAAC;AAAA,IACzB;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,OAAO;AACnC,QAAM,YAAY,SAAS,CAAC,CAAC;AAC7B,QAAM,gBAAgB,OAAO,gBAAgB,CAAC,CAAC;AAC/C,QAAM,SAAS,SAAS,MAAM;AAC5B,QAAI,MAAM,UAAU,MAAM;AACxB,aAAO,CAAC;AAAA,IACV;AACA,QAAI,iBAAiB,QAAQ,MAAM,UAAU,eAAe;AAC1D,aAAO,gBAAgB,MAAM,aAAa,EAAE,MAAM,MAAM,CAAC;AAAA,IAC3D;AACA,QAAI,MAAM,UAAU,SAAS;AAC3B,aAAO,gBAAgB,QAAQ,MAAM,MAAM,CAAC;AAAA,IAC9C;AACA,WAAO,CAAC;AAAA,EACV,CAAC;AACD,QAAM,cAAc,SAAS,OAAO;AAAA,IAClC,SAAS,MAAM;AAAA,IACf,OAAO,MAAM;AAAA,IACb,OAAO,MAAM;AAAA,IACb,SAAS,MAAM;AAAA,IACf,aAAa,MAAM;AAAA,IACnB,SAAS,MAAM;AAAA,IACf,QAAQ,MAAM;AAAA,IACd,SAAS,MAAM;AAAA,EACjB,EAAE;AACF,WAAS,uBAAuB,QAAQ,QAAQ;AAC9C,eAAW,iBAAiB,CAAC,SAAS,UAAU,GAAG;AACjD,UAAI,OAAO,aAAa,KAAK;AAC3B;AACF,YAAM,wBAAwB,OAAO;AAAA,QACnC,OAAO,aAAa;AAAA,MACtB;AACA,iBAAW,cAAc,CAAC,SAAS,WAAW,aAAa,GAAG;AAC5D,cAAM,gBAAgB,OAAO,UAAU;AACvC,YAAI,iBAAiB;AACnB;AACF,sBAAc,eAAe,CAAC;AAC9B,sBAAc,WAAW,aAAa,IAAI;AAAA,MAC5C;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,eAAe,SAAS,MAAM;AAClC,UAAM,SAAS;AAAA,MACb,CAAC;AAAA,MACD,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,MAAM,YAAY,CAAC;AAAA,IACrB;AACA,WAAO,uBAAuB,EAAE,GAAG,OAAO,GAAG,KAAK;AAAA,EACpD,CAAC;AACD,MAAI,YAAY,KAAK,SAAS,eAAe;AAC3C,QAAI,MAAM,UAAU,QAAQ,UAAU,MAAM,MAAM,KAAK,QAAQ,gBAAgB,MAAM,MAAM,KAAK,MAAM;AACpG,cAAQ,KAAK,8BAA8B,MAAM,MAAM,eAAe;AAAA,IACxE;AACA,UAAM,kBAAkB,CAAC,aAAa;AACpC,UAAI,SAAS,UAAU,SAAS;AAC9B,iBAAS,IAAI,SAAS;AAAA,MACxB;AACA,eAAS,MAAM;AACb,YAAI,SAAS,UAAU;AACrB,mBAAS,MAAM,OAAO;AACxB,YAAI,SAAS,UAAU;AACrB,mBAAS,MAAM,SAAS;AAC1B,YAAI,SAAS,UAAU;AACrB,mBAAS,MAAM,aAAa;AAAA,MAChC,CAAC;AAAA,IACH;AACA,cAAU,MAAM;AACd,iBAAW,OAAO,WAAW;AAC3B,wBAAgB,UAAU,GAAG,CAAC;AAAA,MAChC;AAAA,IACF,CAAC;AAAA,EACH;AACA,WAAS,gBAAgB,MAAM,OAAO,OAAO;AAC3C,SAAK,UAAU,CAAC;AAChB,SAAK,MAAM,UAAU,CAAC;AACtB,SAAK,MAAM,QAAQ,EAAE,GAAG,KAAK,MAAM,OAAO,GAAG,MAAM;AACnD,UAAM,sBAAsB;AAAA,MAC1B,MAAM,aAAa,KAAK;AAAA,MACxB,KAAK;AAAA,IACP;AACA,SAAK,MAAM,iBAAiB,CAAC,EAAE,GAAG,MAAM;AACtC,gBAAU,KAAK,IAAI;AAAA,QACjB;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,SAAK,MAAM,iBAAiB,CAAC,EAAE,GAAG,MAAM;AACtC,YAAM,SAAS,eAAe,UAAU,KAAK,EAAE,KAAK;AACpD,iBAAW,CAAC,KAAK,GAAG,KAAK,OAAO,QAAQ,MAAM,GAAG;AAC/C,WAAG,MAAM,GAAG,IAAI;AAAA,MAClB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,kBAAkB,gBAAgB;AAAA,EACtC,MAAM;AAAA,EACN,OAAO;AAAA,IACL,GAAG;AAAA,IACH,IAAI;AAAA,MACF,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,MAAM,OAAO;AACX,UAAM,QAAQ,SAAS;AACvB,UAAM,EAAE,cAAc,gBAAgB,IAAI,qBAAqB,KAAK;AACpE,WAAO,MAAM;AACX,YAAM,QAAQ,eAAe,aAAa,MAAM,WAAW,CAAC,CAAC;AAC7D,YAAM,OAAO,EAAE,MAAM,IAAI,QAAQ,KAAK;AACtC,sBAAgB,MAAM,GAAG,KAAK;AAC9B,aAAO;AAAA,IACT;AAAA,EACF;AACF,CAAC;AAED,IAAM,uBAAuB,gBAAgB;AAAA,EAC3C,MAAM;AAAA,EACN,OAAO;AAAA,IACL,GAAG;AAAA,IACH,IAAI;AAAA,MACF,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,MAAM,OAAO;AACX,UAAM,QAAQ,SAAS;AACvB,UAAM,EAAE,cAAc,gBAAgB,IAAI,qBAAqB,KAAK;AACpE,WAAO,MAAM;AACX,YAAM,QAAQ,eAAe,aAAa,MAAM,WAAW,CAAC,CAAC;AAC7D,YAAM,QAAQ,MAAM,UAAU,KAAK,CAAC;AACpC,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAM,IAAI,MAAM,CAAC;AACjB,YAAI,EAAE,SAAS,YAAY,MAAM,QAAQ,EAAE,QAAQ,GAAG;AACpD,YAAE,SAAS,QAAQ,SAAS,iBAAiB,OAAO,OAAO;AACzD,gBAAI,SAAS;AACX;AACF,gBAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,+BAAiB,OAAO,KAAK;AAC7B;AAAA,YACF;AACA,gBAAI,OAAO,UAAU,UAAU;AAC7B,8BAAgB,OAAO,OAAO,KAAK;AAAA,YACrC;AAAA,UACF,CAAC;AAAA,QACH,OAAO;AACL,0BAAgB,GAAG,GAAG,KAAK;AAAA,QAC7B;AAAA,MACF;AACA,UAAI,MAAM,IAAI;AACZ,eAAO,EAAE,MAAM,IAAI,QAAQ,KAAK;AAAA,MAClC;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF,CAAC;AAED,IAAM,eAAe;AAAA,EACnB,QAAQ,KAAK,SAAS;AACpB,QAAI,UAAU,UAAU,UAAU,CAAC;AACnC,QAAI,CAAC,WAAW,WAAW,CAAC,QAAQ,gBAAgB;AAClD,iBAAW,OAAO,SAAS;AACzB,cAAM,SAAS,QAAQ,GAAG;AAC1B,YAAI,UAAU,UAAU,QAAQ,GAAG,CAAC,IAAI,UAAU,QAAQ,IAAI,CAAC;AAAA,MACjE;AAAA,IACF;AACA,QAAI,WAAW,QAAQ,YAAY;AACjC,iBAAW,OAAO,QAAQ,YAAY;AACpC,cAAM,WAAW,QAAQ,WAAW,GAAG;AACvC,YAAI,CAAC,SAAS,WAAW,YAAY,KAAK,SAAS,eAAe;AAChE,kBAAQ;AAAA,YACN,2BAA2B,GAAG;AAAA,UAChC;AAAA,QACF;AACA,YAAI,UAAU,UAAU,GAAG,IAAI,UAAU,UAAU,IAAI,CAAC;AAAA,MAC1D;AAAA,IACF;AACA,QAAI,QAAQ,gBAAgB,SAAS,UAAU;AAC/C,QAAI,UAAU,UAAU,eAAe;AACvC,QAAI,UAAU,eAAe,oBAAoB;AAAA,EACnD;AACF;AAEA,SAAS,iBAAiB,KAAK;AAC7B,QAAM,OAAO;AACb,SAAO,KAAK,UAAU,UAAU,OAAO,KAAK,UAAU,cAAc,KAAK,QAAQ,UAAU,OAAO,KAAK,QAAQ,cAAc,KAAK,WAAW,UAAU,MAAM,KAAK,MAAM;AAC1K;AAEA,SAAS,aAAa;AACpB,SAAO;AACT;AAEA,SAAS,UAAU,QAAQC,SAAQ;AACjC,QAAM,EAAE,MAAM,IAAI,IAAI,gBAAgB;AACtC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,KAAK,CAAC,eAAe,QAAQ;AAAA,MAC3B,OAAO,QAAQ,UAAU,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;AAC/C,cAAM,cAAc,IAAI,KAAK,OAAO,GAAG,GAAG,MAAM;AAChD,eAAO,YAAY,MAAM,CAAC,eAAe;AACvC,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,YACN,GAAGA,WAAU,qBAAqB,KAAK,KAAK;AAAA,UAC9C;AACA,iBAAO,QAAQ;AAAA,YACb,MAAM,YAAY,IAAI;AAAA,YACtB,IAAI;AAAA,YACJ,UAAU,YAAY,YAAY;AAAA,YAClC,UAAU,CAAC,MAAM,YAAY,IAAI,CAAC;AAAA,YAClC;AAAA,YACA,GAAG;AAAA,UACL,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEA,SAAS,iBAAiB,UAAU,CAAC,GAAG;AACtC,QAAM,gBAAgB,cAAc,oCAAoC,OAAO;AAC/E,SAAO,SAAS,MAAM,cAAc,KAAK;AAC3C;", "names": ["c", "runNextFrame", "isProcessing", "process", "undampedFreq", "a", "b", "c", "a", "b", "progress", "clamp", "clamp", "a", "b", "c", "alpha", "clamp", "b", "a", "v", "number", "alpha", "color", "a", "b", "analyse", "a", "b", "c", "keyframes", "getAnimatableNone", "isObject", "spring"]}