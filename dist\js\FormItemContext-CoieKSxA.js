import{ab as I}from"./bootstrap-CFDAkNgp.js";import{a4 as i,R as u,q as p,az as C,J as c,P as f,Y as x,aF as a}from"../jse/index-index-B2UBupFX.js";const r=Symbol("ContextProps"),s=Symbol("InternalContextProps"),P=function(n){let o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:c(()=>!0);const e=f(new Map),t=(m,v)=>{e.value.set(m,v),e.value=new Map(e.value)},F=m=>{e.value.delete(m),e.value=new Map(e.value)};x([o,e],()=>{}),a(r,n),a(s,{addFormItemField:t,removeFormItemField:F})},l={id:c(()=>{}),onFieldBlur:()=>{},onFieldChange:()=>{},clearValidate:()=>{}},d={addFormItemField:()=>{},removeFormItemField:()=>{}},S=()=>{const n=u(s,d),o=Symbol("FormItemFieldKey"),e=p();return n.addFormItemField(o,e.type),C(()=>{n.removeFormItemField(o)}),a(s,d),a(r,l),u(r,l)},w=i({compatConfig:{MODE:3},name:"AFormItemRest",setup(n,o){let{slots:e}=o;return a(s,d),a(r,l),()=>{var t;return(t=e.default)===null||t===void 0?void 0:t.call(e)}}}),y=I({}),K=i({name:"NoFormStatus",setup(n,o){let{slots:e}=o;return y.useProvide({}),()=>{var t;return(t=e.default)===null||t===void 0?void 0:t.call(e)}}});export{y as F,K as N,P as a,w as b,S as u};
