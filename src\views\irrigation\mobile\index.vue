<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue';
import {
  Card,
  Button,
  Switch,
  Slider,
  Progress,
  Row,
  Col,
  Statistic,
  Badge,
  Tag,
  Modal,
  message,
  Space,
  Select,
  Input,
  Alert,
} from 'ant-design-vue';

defineOptions({ name: 'IrrigationMobile' });

// 当前选中的灌站
const selectedStation = ref(1);

// 模拟灌站数据
const stationData = ref([
  {
    id: 1,
    name: '东风灌站',
    status: 'online',
    isRunning: true,
    flow: 85.6,
    targetFlow: 90,
    power: 450,
    voltage: 380,
    current: 125.8,
    waterLevel: 78.5,
    temperature: 45.2,
    pressure: 2.5,
    runningTime: 8.5, // 小时
    totalRunTime: 2580, // 小时
    lastMaintenance: '2024-01-10',
    nextMaintenance: '2024-04-10',
  },
  {
    id: 2,
    name: '红旗灌站',
    status: 'maintenance',
    isRunning: false,
    flow: 0,
    targetFlow: 75,
    power: 280,
    voltage: 0,
    current: 0,
    waterLevel: 65.2,
    temperature: 25.0,
    pressure: 0,
    runningTime: 0,
    totalRunTime: 1890,
    lastMaintenance: '2023-12-15',
    nextMaintenance: '2024-03-15',
  },
  {
    id: 3,
    name: '胜利灌站',
    status: 'online',
    isRunning: true,
    flow: 42.3,
    targetFlow: 50,
    power: 150,
    voltage: 380,
    current: 68.5,
    waterLevel: 82.1,
    temperature: 38.8,
    pressure: 1.8,
    runningTime: 6.2,
    totalRunTime: 1245,
    lastMaintenance: '2024-01-05',
    nextMaintenance: '2024-07-05',
  },
]);

// 当前灌站数据
const currentStation = computed(() => {
  return (
    stationData.value.find((s) => s.id === selectedStation.value) ||
    stationData.value[0]
  );
});

// 控制面板状态
const controlPanel = reactive({
  autoMode: true,
  targetFlow: 90,
  maxPower: 500,
  emergencyStop: false,
});

// 操作确认模态框
const confirmModal = reactive({
  visible: false,
  title: '',
  content: '',
  action: null as (() => void) | null,
});

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case 'online':
      return 'success';
    case 'offline':
      return 'error';
    case 'maintenance':
      return 'warning';
    default:
      return 'default';
  }
};

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'online':
      return '在线';
    case 'offline':
      return '离线';
    case 'maintenance':
      return '维修中';
    default:
      return '未知';
  }
};

// 启动灌站
const startStation = () => {
  if (currentStation.value.status !== 'online') {
    message.error('灌站不在线，无法启动');
    return;
  }

  confirmModal.title = '确认启动';
  confirmModal.content = `确定要启动 ${currentStation.value.name} 吗？`;
  confirmModal.action = () => {
    currentStation.value.isRunning = true;
    currentStation.value.flow = Math.random() * 50 + 50;
    currentStation.value.current = Math.random() * 50 + 80;
    currentStation.value.pressure = Math.random() * 1 + 1.5;
    message.success('灌站启动成功');
  };
  confirmModal.visible = true;
};

// 停止灌站
const stopStation = () => {
  confirmModal.title = '确认停止';
  confirmModal.content = `确定要停止 ${currentStation.value.name} 吗？`;
  confirmModal.action = () => {
    currentStation.value.isRunning = false;
    currentStation.value.flow = 0;
    currentStation.value.current = 0;
    currentStation.value.pressure = 0;
    message.success('灌站停止成功');
  };
  confirmModal.visible = true;
};

// 紧急停止
const emergencyStop = () => {
  confirmModal.title = '紧急停止';
  confirmModal.content = '紧急停止将立即关闭所有设备，确定执行吗？';
  confirmModal.action = () => {
    controlPanel.emergencyStop = true;
    currentStation.value.isRunning = false;
    currentStation.value.flow = 0;
    currentStation.value.current = 0;
    currentStation.value.pressure = 0;
    message.warning('已执行紧急停止');

    // 3秒后重置紧急停止状态
    setTimeout(() => {
      controlPanel.emergencyStop = false;
    }, 3000);
  };
  confirmModal.visible = true;
};

// 调节流量
const adjustFlow = (value: number) => {
  if (!currentStation.value.isRunning) {
    message.error('灌站未运行，无法调节流量');
    return;
  }

  controlPanel.targetFlow = value;
  // 模拟流量调节
  setTimeout(() => {
    currentStation.value.targetFlow = value;
    currentStation.value.flow = value * (0.9 + Math.random() * 0.2);
    message.success(`目标流量已调节至 ${value} m³/h`);
  }, 1000);
};

// 切换自动模式
const toggleAutoMode = (checked: boolean) => {
  controlPanel.autoMode = checked;
  message.info(checked ? '已切换到自动模式' : '已切换到手动模式');
};

// 执行确认操作
const executeConfirmAction = () => {
  if (confirmModal.action) {
    confirmModal.action();
  }
  confirmModal.visible = false;
};

// 模拟实时数据更新
onMounted(() => {
  setInterval(() => {
    stationData.value.forEach((station) => {
      if (station.isRunning && station.status === 'online') {
        // 模拟数据波动
        station.flow += (Math.random() - 0.5) * 2;
        station.flow = Math.max(
          0,
          Math.min(station.flow, station.targetFlow * 1.1),
        );

        station.current += (Math.random() - 0.5) * 5;
        station.current = Math.max(0, station.current);

        station.temperature += (Math.random() - 0.5) * 1;
        station.temperature = Math.max(20, Math.min(station.temperature, 60));

        station.waterLevel += (Math.random() - 0.5) * 0.5;
        station.waterLevel = Math.max(50, Math.min(station.waterLevel, 100));

        station.runningTime += 1 / 3600; // 增加运行时间
      }
    });
  }, 3000);
});
</script>

<template>
  <div class="p-5">
    <!-- 专用APP介绍 -->
    <Alert
      message="提灌站专用APP操作界面"
      description="专为该批次提灌站开发的移动端APP，满足启停操作、故障报警、流量显示等功能需求，支持现场作业人员便捷操作"
      type="success"
      show-icon
      closable
      class="mb-5"
    />

    <!-- APP功能特色 -->
    <Card title="专用APP功能特色" class="mb-5">
      <Row :gutter="16">
        <Col :span="6">
          <div class="rounded bg-blue-50 p-4 text-center">
            <div class="mb-2 text-2xl">🚀</div>
            <div class="font-bold text-blue-600">启停操作</div>
            <div class="text-sm text-gray-600">远程控制灌站启停</div>
          </div>
        </Col>
        <Col :span="6">
          <div class="rounded bg-red-50 p-4 text-center">
            <div class="mb-2 text-2xl">⚠️</div>
            <div class="font-bold text-red-600">故障报警</div>
            <div class="text-sm text-gray-600">实时故障推送提醒</div>
          </div>
        </Col>
        <Col :span="6">
          <div class="rounded bg-green-50 p-4 text-center">
            <div class="mb-2 text-2xl">💧</div>
            <div class="font-bold text-green-600">流量显示</div>
            <div class="text-sm text-gray-600">实时流量数据监控</div>
          </div>
        </Col>
        <Col :span="6">
          <div class="rounded bg-purple-50 p-4 text-center">
            <div class="mb-2 text-2xl">📱</div>
            <div class="font-bold text-purple-600">移动便捷</div>
            <div class="text-sm text-gray-600">随时随地操作管理</div>
          </div>
        </Col>
      </Row>
    </Card>

    <!-- 灌站选择 -->
    <Card title="选择灌站" class="mb-5">
      <Select v-model:value="selectedStation" style="width: 100%" size="large">
        <Select.Option
          v-for="station in stationData"
          :key="station.id"
          :value="station.id"
        >
          <div class="flex items-center justify-between">
            <span>{{ station.name }}</span>
            <Badge
              :status="getStatusColor(station.status)"
              :text="getStatusText(station.status)"
            />
          </div>
        </Select.Option>
      </Select>
    </Card>

    <!-- 灌站状态概览 -->
    <Card :title="currentStation.name + ' - 状态概览'" class="mb-5">
      <Row :gutter="[16, 16]">
        <Col :span="12">
          <div class="text-center">
            <div
              class="mb-2 text-2xl font-bold"
              :class="{
                'text-green-500': currentStation.isRunning,
                'text-red-500': !currentStation.isRunning,
              }"
            >
              {{ currentStation.isRunning ? '运行中' : '已停止' }}
            </div>
            <Badge
              :status="getStatusColor(currentStation.status)"
              :text="getStatusText(currentStation.status)"
            />
          </div>
        </Col>
        <Col :span="12">
          <div class="text-center">
            <div class="text-lg text-gray-600">运行时间</div>
            <div class="text-xl font-bold">
              {{ currentStation.runningTime.toFixed(1) }}h
            </div>
          </div>
        </Col>
      </Row>
    </Card>

    <!-- 实时数据监控 -->
    <Card title="实时监控" class="mb-5">
      <Row :gutter="[16, 16]">
        <Col :span="12">
          <Statistic
            title="当前流量"
            :value="currentStation.flow"
            suffix="m³/h"
            :precision="1"
            :value-style="{
              color: currentStation.isRunning ? '#52c41a' : '#999',
              fontSize: '20px',
            }"
          />
          <Progress
            :percent="(currentStation.flow / currentStation.targetFlow) * 100"
            :stroke-color="currentStation.isRunning ? '#52c41a' : '#d9d9d9'"
            class="mt-2"
          />
        </Col>
        <Col :span="12">
          <Statistic
            title="功率"
            :value="currentStation.isRunning ? currentStation.power : 0"
            suffix="kW"
            :value-style="{
              color: currentStation.isRunning ? '#1890ff' : '#999',
              fontSize: '20px',
            }"
          />
        </Col>
        <Col :span="12">
          <Statistic
            title="电流"
            :value="currentStation.current"
            suffix="A"
            :precision="1"
            :value-style="{
              color: currentStation.isRunning ? '#faad14' : '#999',
              fontSize: '18px',
            }"
          />
        </Col>
        <Col :span="12">
          <Statistic
            title="水位"
            :value="currentStation.waterLevel"
            suffix="%"
            :precision="1"
            :value-style="{
              color: '#722ed1',
              fontSize: '18px',
            }"
          />
          <Progress
            :percent="currentStation.waterLevel"
            stroke-color="#722ed1"
            class="mt-2"
          />
        </Col>
      </Row>
    </Card>

    <!-- 控制面板 -->
    <Card title="控制面板" class="mb-5">
      <div class="space-y-4">
        <!-- 运行控制 -->
        <div class="flex items-center justify-between rounded bg-gray-50 p-4">
          <span class="font-medium">运行控制</span>
          <Space size="large">
            <Button
              type="primary"
              size="large"
              :disabled="
                currentStation.isRunning || currentStation.status !== 'online'
              "
              @click="startStation"
            >
              启动
            </Button>
            <Button
              danger
              size="large"
              :disabled="!currentStation.isRunning"
              @click="stopStation"
            >
              停止
            </Button>
          </Space>
        </div>

        <!-- 自动模式 -->
        <div class="flex items-center justify-between rounded bg-gray-50 p-4">
          <span class="font-medium">自动模式</span>
          <Switch
            v-model:checked="controlPanel.autoMode"
            @change="toggleAutoMode"
          />
        </div>

        <!-- 流量调节 -->
        <div class="rounded bg-gray-50 p-4">
          <div class="mb-3 flex items-center justify-between">
            <span class="font-medium">目标流量</span>
            <span class="text-lg font-bold"
              >{{ controlPanel.targetFlow }} m³/h</span
            >
          </div>
          <Slider
            v-model:value="controlPanel.targetFlow"
            :min="0"
            :max="150"
            :step="5"
            :disabled="!controlPanel.autoMode || !currentStation.isRunning"
            @afterChange="adjustFlow"
          />
        </div>

        <!-- 紧急停止 -->
        <div class="flex justify-center">
          <Button
            danger
            size="large"
            type="primary"
            :disabled="controlPanel.emergencyStop"
            @click="emergencyStop"
            class="h-16 w-full text-xl font-bold"
          >
            {{ controlPanel.emergencyStop ? '紧急停止中...' : '紧急停止' }}
          </Button>
        </div>
      </div>
    </Card>

    <!-- 设备信息 -->
    <Card title="设备信息" class="mb-5">
      <div class="space-y-3">
        <div class="flex justify-between">
          <span>电压：</span>
          <span class="font-medium">{{ currentStation.voltage }}V</span>
        </div>
        <div class="flex justify-between">
          <span>温度：</span>
          <span
            class="font-medium"
            :class="{
              'text-red-500': currentStation.temperature > 50,
              'text-orange-500': currentStation.temperature > 40,
              'text-green-500': currentStation.temperature <= 40,
            }"
          >
            {{ currentStation.temperature.toFixed(1) }}°C
          </span>
        </div>
        <div class="flex justify-between">
          <span>压力：</span>
          <span class="font-medium"
            >{{ currentStation.pressure.toFixed(1) }}MPa</span
          >
        </div>
        <div class="flex justify-between">
          <span>累计运行：</span>
          <span class="font-medium">{{ currentStation.totalRunTime }}h</span>
        </div>
        <div class="flex justify-between">
          <span>上次维护：</span>
          <span class="font-medium">{{ currentStation.lastMaintenance }}</span>
        </div>
        <div class="flex justify-between">
          <span>下次维护：</span>
          <span class="font-medium text-orange-500">{{
            currentStation.nextMaintenance
          }}</span>
        </div>
      </div>
    </Card>

    <!-- 快捷操作 -->
    <Card title="快捷操作">
      <Row :gutter="[16, 16]">
        <Col :span="12">
          <Button block size="large" type="default"> 查看历史数据 </Button>
        </Col>
        <Col :span="12">
          <Button block size="large" type="default"> 故障报警 </Button>
        </Col>
        <Col :span="12">
          <Button block size="large" type="default"> 维护记录 </Button>
        </Col>
        <Col :span="12">
          <Button block size="large" type="default"> 联系管理员 </Button>
        </Col>
      </Row>
    </Card>

    <!-- 确认操作模态框 -->
    <Modal
      v-model:open="confirmModal.visible"
      :title="confirmModal.title"
      @ok="executeConfirmAction"
      @cancel="confirmModal.visible = false"
    >
      <p>{{ confirmModal.content }}</p>
    </Modal>
  </div>
</template>

<style scoped>
.space-y-3 > * + * {
  margin-top: 0.75rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

/* 移动端样式优化 */
@media (max-width: 768px) {
  .ant-statistic-content {
    font-size: 18px !important;
  }

  .ant-btn-lg {
    height: 48px;
    font-size: 16px;
  }
}
</style>
