// 高德地图工具类
declare global {
  interface Window {
    AMap: any;
    AMapLoader: any;
  }
}

export interface StationMarker {
  id: number;
  name: string;
  longitude: number;
  latitude: number;
  status: 'online' | 'offline' | 'maintenance';
  type: 'large' | 'medium' | 'small' | 'micro';
  power: number;
  buildYear: number;
  irrigationArea: number;
  manager: string;
  phone: string;
  flow: number;
  waterLevel: number;
  voltage: number;
  current: number;
  // 新增字段以体现业务需求
  lineStatus: 'normal' | 'aging' | 'replaced'; // 线路状态
  safetyLevel: 'high' | 'medium' | 'low'; // 安全等级
  maintenanceRecord: string; // 维修记录
  equipmentStatus: 'normal' | 'need_repair' | 'replaced'; // 设备状态
  pipelineStatus: 'normal' | 'blocked' | 'repaired'; // 管道状态
  lastInspection: string; // 最后检查时间
  nextMaintenance: string; // 下次维护时间
}

// 高德地图配置
export const AMAP_CONFIG = {
  key: 'YOUR_AMAP_KEY', // 请替换为您的高德地图API Key
  version: '2.0',
  plugins: ['AMap.Scale', 'AMap.ToolBar', 'AMap.ControlBar'],
};

// 地图样式配置
export const MAP_STYLES = {
  normal: 'amap://styles/normal',
  satellite: 'amap://styles/satellite',
  dark: 'amap://styles/dark',
};

// 标记点样式配置
export const MARKER_STYLES = {
  online: {
    fillColor: '#52c41a',
    strokeColor: '#389e0d',
    fillOpacity: 0.8,
    strokeWidth: 2,
    radius: 8,
  },
  offline: {
    fillColor: '#ff4d4f',
    strokeColor: '#cf1322',
    fillOpacity: 0.8,
    strokeWidth: 2,
    radius: 8,
  },
  maintenance: {
    fillColor: '#faad14',
    strokeColor: '#d48806',
    fillOpacity: 0.8,
    strokeWidth: 2,
    radius: 8,
  },
};

// 加载高德地图
export const loadAMap = (): Promise<any> => {
  return new Promise((resolve, reject) => {
    if (window.AMap) {
      resolve(window.AMap);
      return;
    }

    // 动态加载高德地图脚本
    const script = document.createElement('script');
    script.src = `https://webapi.amap.com/maps?v=${AMAP_CONFIG.version}&key=${AMAP_CONFIG.key}&plugin=${AMAP_CONFIG.plugins.join(',')}`;
    script.onload = () => {
      if (window.AMap) {
        resolve(window.AMap);
      } else {
        reject(new Error('高德地图加载失败'));
      }
    };
    script.onerror = () => {
      reject(new Error('高德地图脚本加载失败'));
    };
    document.head.appendChild(script);
  });
};

// 创建地图实例
export const createMap = (container: HTMLElement, options?: any) => {
  const defaultOptions = {
    zoom: 11,
    center: [104.0668, 30.5728], // 成都坐标
    mapStyle: MAP_STYLES.normal,
    showLabel: true,
    features: ['bg', 'road', 'building', 'point'],
  };

  return new window.AMap.Map(container, { ...defaultOptions, ...options });
};

// 创建标记点
export const createMarker = (station: StationMarker, map: any, onClick?: (station: StationMarker) => void) => {
  const style = MARKER_STYLES[station.status];
  
  // 创建自定义标记
  const marker = new window.AMap.CircleMarker({
    center: [station.longitude, station.latitude],
    radius: style.radius,
    fillColor: style.fillColor,
    strokeColor: style.strokeColor,
    fillOpacity: style.fillOpacity,
    strokeWidth: style.strokeWidth,
    cursor: 'pointer',
  });

  // 创建信息窗体
  const infoWindow = new window.AMap.InfoWindow({
    content: createInfoWindowContent(station),
    offset: new window.AMap.Pixel(0, -30),
  });

  // 添加点击事件
  marker.on('click', () => {
    infoWindow.open(map, marker.getCenter());
    if (onClick) {
      onClick(station);
    }
  });

  // 添加标签
  const label = new window.AMap.Text({
    text: station.name,
    position: [station.longitude, station.latitude],
    offset: new window.AMap.Pixel(0, 15),
    style: {
      'font-size': '12px',
      'font-weight': 'bold',
      'color': '#333',
      'background-color': 'rgba(255, 255, 255, 0.8)',
      'border': '1px solid #ccc',
      'border-radius': '3px',
      'padding': '2px 5px',
    },
  });

  map.add([marker, label]);
  
  return { marker, label, infoWindow };
};

// 创建信息窗体内容
const createInfoWindowContent = (station: StationMarker) => {
  const statusText = station.status === 'online' ? '在线' : 
                    station.status === 'offline' ? '离线' : '维修中';
  const statusColor = station.status === 'online' ? '#52c41a' : 
                     station.status === 'offline' ? '#ff4d4f' : '#faad14';

  return `
    <div style="padding: 10px; min-width: 250px;">
      <h4 style="margin: 0 0 10px 0; color: #333;">${station.name}</h4>
      <div style="margin-bottom: 8px;">
        <span style="color: #666;">状态：</span>
        <span style="color: ${statusColor}; font-weight: bold;">${statusText}</span>
      </div>
      <div style="margin-bottom: 8px;">
        <span style="color: #666;">功率：</span>
        <span style="font-weight: bold;">${station.power}kW</span>
      </div>
      <div style="margin-bottom: 8px;">
        <span style="color: #666;">流量：</span>
        <span style="font-weight: bold;">${station.flow.toFixed(1)}m³/h</span>
      </div>
      <div style="margin-bottom: 8px;">
        <span style="color: #666;">灌溉面积：</span>
        <span style="font-weight: bold;">${station.irrigationArea}亩</span>
      </div>
      <div style="margin-bottom: 8px;">
        <span style="color: #666;">管护人员：</span>
        <span style="font-weight: bold;">${station.manager}</span>
      </div>
      <div style="margin-bottom: 8px;">
        <span style="color: #666;">联系电话：</span>
        <span style="font-weight: bold;">${station.phone}</span>
      </div>
      <div style="margin-bottom: 8px;">
        <span style="color: #666;">建设年限：</span>
        <span style="font-weight: bold;">${station.buildYear}年</span>
      </div>
      <div style="margin-bottom: 8px;">
        <span style="color: #666;">线路状态：</span>
        <span style="font-weight: bold; color: ${station.lineStatus === 'normal' ? '#52c41a' : station.lineStatus === 'aging' ? '#faad14' : '#52c41a'};">
          ${station.lineStatus === 'normal' ? '正常' : station.lineStatus === 'aging' ? '老旧待改造' : '已更换'}
        </span>
      </div>
      <div style="margin-bottom: 8px;">
        <span style="color: #666;">设备状态：</span>
        <span style="font-weight: bold; color: ${station.equipmentStatus === 'normal' ? '#52c41a' : station.equipmentStatus === 'need_repair' ? '#ff4d4f' : '#1890ff'};">
          ${station.equipmentStatus === 'normal' ? '正常' : station.equipmentStatus === 'need_repair' ? '需要维修' : '已更换'}
        </span>
      </div>
      <div style="text-align: center; margin-top: 10px;">
        <button onclick="window.showStationDetail && window.showStationDetail(${station.id})" 
                style="background: #1890ff; color: white; border: none; padding: 5px 15px; border-radius: 4px; cursor: pointer;">
          查看详情
        </button>
      </div>
    </div>
  `;
};

// 批量添加标记点
export const addMarkersToMap = (stations: StationMarker[], map: any, onClick?: (station: StationMarker) => void) => {
  const markers: any[] = [];
  
  stations.forEach(station => {
    const markerGroup = createMarker(station, map, onClick);
    markers.push(markerGroup);
  });
  
  return markers;
};

// 清除所有标记点
export const clearMarkers = (markers: any[], map: any) => {
  markers.forEach(markerGroup => {
    map.remove([markerGroup.marker, markerGroup.label]);
  });
};
