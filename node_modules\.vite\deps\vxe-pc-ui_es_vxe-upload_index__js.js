import {
  button_default
} from "./chunk-TALEYQFL.js";
import "./chunk-6V4MV7LO.js";
import {
  getEventTargetNode,
  getTpImg,
  initTpImg,
  toCssUnit
} from "./chunk-TOENFBLC.js";
import {
  getSlotVNs
} from "./chunk-6CXTBDBP.js";
import "./chunk-JWKCLBKN.js";
import {
  defineVxeComponent,
  dynamicApp,
  errLog,
  warnLog
} from "./chunk-KAYKK4JO.js";
import {
  require_xe_utils
} from "./chunk-RWHEUJNV.js";
import {
  VxeUI,
  createEvent,
  getConfig,
  getI18n,
  getIcon,
  globalEvents,
  renderEmptyElement,
  useSize
} from "./chunk-KJAC55GV.js";
import "./chunk-G6CDOZZI.js";
import {
  TransitionGroup,
  computed,
  h,
  inject,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  watch
} from "./chunk-ZCM5A7SR.js";
import "./chunk-WDDBQLJB.js";
import {
  __toESM
} from "./chunk-V4OQ3NZ2.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.9.29_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/upload/src/upload.js
var import_xe_utils2 = __toESM(require_xe_utils());

// ../../node_modules/.pnpm/vxe-pc-ui@4.9.29_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/upload/src/util.js
var import_xe_utils = __toESM(require_xe_utils());
var fileForm = null;
var fileInput = null;
function parseFile(file) {
  const name = file.name;
  const tIndex = import_xe_utils.default.lastIndexOf(name, ".");
  const type = name.substring(tIndex + 1, name.length).toLowerCase();
  const filename = name.substring(0, tIndex);
  return { filename, type };
}
var readLocalFile = (options) => {
  const opts = Object.assign({}, options);
  return new Promise((resolve, reject) => {
    if (!fileInput) {
      fileInput = document.createElement("input");
      fileInput.name = "file";
      fileInput.type = "file";
    }
    if (!fileForm) {
      fileForm = document.createElement("form");
      fileForm.style.display = "none";
      fileForm.appendChild(fileInput);
      document.body.appendChild(fileForm);
    }
    const types = opts.types || [];
    const isAllType = !types.length || types.some((type) => type === "*");
    fileInput.multiple = !!opts.multiple;
    fileInput.accept = isAllType ? "" : `.${types.join(", .")}`;
    fileInput.onchange = (evnt) => {
      const eventTarget = evnt.target;
      const files = Array.from(eventTarget.files || []);
      const file = files[0];
      let errType = "";
      if (!isAllType) {
        for (let fIndex = 0; fIndex < files.length; fIndex++) {
          const { type } = parseFile(files[fIndex]);
          if (!import_xe_utils.default.includes(types, type)) {
            errType = type;
            break;
          }
        }
      }
      if (!errType) {
        resolve({ status: true, files, file });
      } else {
        if (opts.message !== false) {
          if (VxeUI.modal) {
            VxeUI.modal.message({
              content: getI18n("vxe.error.notType", [errType]),
              status: "error"
            });
          }
        }
        const params = { status: false, files, file };
        reject(params);
      }
    };
    fileForm.reset();
    fileInput.click();
  });
};
function getExportBlobByContent(content, options) {
  return new Blob([content], { type: `text/${options.type};charset=utf-8;` });
}
var saveLocalFile = (options) => {
  const opts = Object.assign({ type: "" }, options);
  const { filename, type, content } = opts;
  const name = type ? `${filename}.${type}` : `${filename}`;
  if (window.Blob) {
    const blob = content instanceof Blob ? content : getExportBlobByContent(import_xe_utils.default.toValueString(content), opts);
    const winNavigator = window.navigator;
    if (winNavigator.msSaveBlob) {
      winNavigator.msSaveBlob(blob, name);
    } else {
      const url = URL.createObjectURL(blob);
      const linkElem = document.createElement("a");
      linkElem.target = "_blank";
      linkElem.download = name;
      linkElem.href = url;
      document.body.appendChild(linkElem);
      linkElem.click();
      requestAnimationFrame(() => {
        if (linkElem.parentNode) {
          linkElem.parentNode.removeChild(linkElem);
        }
        URL.revokeObjectURL(url);
      });
    }
    return Promise.resolve();
  }
  return Promise.reject(new Error(getI18n("vxe.error.notExp")));
};

// ../../node_modules/.pnpm/vxe-pc-ui@4.9.29_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/upload/src/upload.js
var upload_default = defineVxeComponent({
  name: "VxeUpload",
  props: {
    modelValue: [Array, String, Object],
    showList: {
      type: Boolean,
      default: () => getConfig().upload.showList
    },
    moreConfig: Object,
    readonly: {
      type: Boolean,
      default: null
    },
    disabled: {
      type: Boolean,
      default: null
    },
    autoSubmit: {
      type: Boolean,
      default: () => getConfig().upload.autoSubmit
    },
    mode: {
      type: String,
      default: () => getConfig().upload.mode
    },
    imageTypes: {
      type: Array,
      default: () => import_xe_utils2.default.clone(getConfig().upload.imageTypes, true)
    },
    imageConfig: {
      type: Object,
      default: () => import_xe_utils2.default.clone(getConfig().upload.imageConfig, true)
    },
    /**
     * 已废弃，被 image-config 替换
     * @deprecated
     */
    imageStyle: {
      type: Object,
      default: () => import_xe_utils2.default.clone(getConfig().upload.imageStyle, true)
    },
    fileTypes: {
      type: Array,
      default: () => import_xe_utils2.default.clone(getConfig().upload.fileTypes, true)
    },
    dragSort: Boolean,
    dragToUpload: {
      type: Boolean,
      default: () => import_xe_utils2.default.clone(getConfig().upload.dragToUpload, true)
    },
    pasteToUpload: {
      type: Boolean,
      default: () => import_xe_utils2.default.clone(getConfig().upload.pasteToUpload, true)
    },
    keyField: String,
    singleMode: Boolean,
    urlMode: Boolean,
    multiple: Boolean,
    limitSize: {
      type: [String, Number],
      default: () => getConfig().upload.limitSize
    },
    showLimitSize: {
      type: Boolean,
      default: () => getConfig().upload.showLimitSize
    },
    limitSizeText: {
      type: [String, Number, Function],
      default: () => getConfig().upload.limitSizeText
    },
    limitCount: {
      type: [String, Number],
      default: () => getConfig().upload.limitCount
    },
    showLimitCount: {
      type: Boolean,
      default: () => getConfig().upload.showLimitCount
    },
    limitCountText: {
      type: [String, Number, Function],
      default: () => getConfig().upload.limitCountText
    },
    nameField: {
      type: String,
      default: () => getConfig().upload.nameField
    },
    typeField: {
      type: String,
      default: () => getConfig().upload.typeField
    },
    urlField: {
      type: String,
      default: () => getConfig().upload.urlField
    },
    sizeField: {
      type: String,
      default: () => getConfig().upload.sizeField
    },
    showErrorStatus: {
      type: Boolean,
      default: () => getConfig().upload.showErrorStatus
    },
    showProgress: {
      type: Boolean,
      default: () => getConfig().upload.showProgress
    },
    progressText: {
      type: [String, Number, Function],
      default: () => getConfig().upload.progressText
    },
    showSubmitButton: Boolean,
    autoHiddenButton: {
      type: Boolean,
      default: () => getConfig().upload.autoHiddenButton
    },
    showUploadButton: {
      type: Boolean,
      default: () => getConfig().upload.showUploadButton
    },
    buttonText: {
      type: [String, Number, Function],
      default: () => getConfig().upload.buttonText
    },
    buttonIcon: {
      type: String,
      default: () => getConfig().upload.buttonIcon
    },
    showButtonText: {
      type: Boolean,
      default: () => getConfig().upload.showButtonText
    },
    showButtonIcon: {
      type: Boolean,
      default: () => getConfig().upload.showButtonIcon
    },
    showRemoveButton: {
      type: Boolean,
      default: () => getConfig().upload.showRemoveButton
    },
    showDownloadButton: {
      type: Boolean,
      default: () => getConfig().upload.showDownloadButton
    },
    showPreview: {
      type: Boolean,
      default: () => getConfig().upload.showPreview
    },
    showTip: {
      type: Boolean,
      default: () => null
    },
    maxSimultaneousUploads: {
      type: Number,
      default: () => getConfig().upload.maxSimultaneousUploads
    },
    tipText: [String, Number, Function],
    hintText: String,
    previewMethod: Function,
    uploadMethod: Function,
    beforeRemoveMethod: Function,
    removeMethod: Function,
    beforeDownloadMethod: Function,
    downloadMethod: Function,
    getUrlMethod: Function,
    getThumbnailUrlMethod: Function,
    size: {
      type: String,
      default: () => getConfig().upload.size || getConfig().size
    }
  },
  emits: [
    "update:modelValue",
    "add",
    "remove",
    "remove-fail",
    "download",
    "download-fail",
    "upload-success",
    "upload-error",
    "sort-dragend"
  ],
  setup(props, context) {
    const { emit, slots } = context;
    const $xeForm = inject("$xeForm", null);
    const formItemInfo = inject("xeFormItemInfo", null);
    const $xeTable = inject("$xeTable", null);
    const xID = import_xe_utils2.default.uniqueId();
    const { computeSize } = useSize(props);
    const refElem = ref();
    const refPopupElem = ref();
    const refDragLineElem = ref();
    const refModalDragLineElem = ref();
    const reactData = reactive({
      isDragUploadStatus: false,
      showMorePopup: false,
      isActivated: false,
      fileList: [],
      fileCacheMaps: {},
      isDragMove: false,
      dragIndex: -1,
      dragTipText: ""
    });
    const internalData = {
      imagePreviewTypes: ["jpg", "jpeg", "png", "gif"],
      prevDragIndex: -1
      // prevDragPos: ''
    };
    const refMaps = {
      refElem
    };
    const computeFormReadonly = computed(() => {
      const { readonly } = props;
      if (readonly === null) {
        if ($xeForm) {
          return $xeForm.props.readonly;
        }
        return false;
      }
      return readonly;
    });
    const computeIsDisabled = computed(() => {
      const { disabled } = props;
      if (disabled === null) {
        if ($xeForm) {
          return $xeForm.props.disabled;
        }
        return false;
      }
      return disabled;
    });
    const computeKeyField = computed(() => {
      return props.keyField || "_X_KEY";
    });
    const computeIsImage = computed(() => {
      return props.mode === "image";
    });
    const computeNameProp = computed(() => {
      return props.nameField || "name";
    });
    const computeTypeProp = computed(() => {
      return props.typeField || "type";
    });
    const computeUrlProp = computed(() => {
      return props.urlField || "url";
    });
    const computeSizeProp = computed(() => {
      return props.sizeField || "size";
    });
    const computeLimitMaxSize = computed(() => {
      return import_xe_utils2.default.toNumber(props.limitSize) * 1024 * 1024;
    });
    const computeLimitMaxCount = computed(() => {
      return props.multiple ? import_xe_utils2.default.toNumber(props.limitCount) : 1;
    });
    const computeOverCount = computed(() => {
      const { multiple } = props;
      const { fileList } = reactData;
      const limitMaxCount = computeLimitMaxCount.value;
      if (multiple) {
        if (limitMaxCount) {
          return fileList.length >= limitMaxCount;
        }
        return true;
      }
      return fileList.length >= 1;
    });
    const computeLimitSizeUnit = computed(() => {
      const limitSize = import_xe_utils2.default.toNumber(props.limitSize);
      if (limitSize) {
        if (limitSize > 1048576) {
          return `${limitSize / 1048576}T`;
        }
        if (limitSize > 1024) {
          return `${limitSize / 1024}G`;
        }
        return `${limitSize}M`;
      }
      return "";
    });
    const computedShowTipText = computed(() => {
      const { showTip, tipText } = props;
      if (import_xe_utils2.default.isBoolean(showTip)) {
        return showTip;
      }
      const defShowTip = getConfig().upload.showTip;
      if (import_xe_utils2.default.isBoolean(defShowTip)) {
        return defShowTip;
      }
      if (tipText) {
        return true;
      }
      return false;
    });
    const computedDefTipText = computed(() => {
      const { limitSize, fileTypes, multiple, limitCount } = props;
      const tipText = props.tipText || props.hintText;
      const isImage = computeIsImage.value;
      const limitSizeUnit = computeLimitSizeUnit.value;
      if (import_xe_utils2.default.isString(tipText)) {
        return tipText;
      }
      if (import_xe_utils2.default.isFunction(tipText)) {
        return `${tipText({})}`;
      }
      const defTips = [];
      if (isImage) {
        if (multiple && limitCount) {
          defTips.push(getI18n("vxe.upload.imgCountHint", [limitCount]));
        }
        if (limitSize && limitSizeUnit) {
          defTips.push(getI18n("vxe.upload.imgSizeHint", [limitSizeUnit]));
        }
      } else {
        if (fileTypes && fileTypes.length) {
          defTips.push(getI18n("vxe.upload.fileTypeHint", [fileTypes.join("/")]));
        }
        if (limitSize && limitSizeUnit) {
          defTips.push(getI18n("vxe.upload.fileSizeHint", [limitSizeUnit]));
        }
        if (multiple && limitCount) {
          defTips.push(getI18n("vxe.upload.fileCountHint", [limitCount]));
        }
      }
      return defTips.join(getI18n("vxe.base.comma"));
    });
    const computeImageOpts = computed(() => {
      return Object.assign({}, props.imageConfig || props.imageStyle);
    });
    const computeImgStyle = computed(() => {
      const imageOpts = computeImageOpts.value;
      const { width, height } = imageOpts;
      const stys = {};
      if (width) {
        stys.width = toCssUnit(width);
      }
      if (height) {
        stys.height = toCssUnit(height);
      }
      return stys;
    });
    const computeMoreOpts = computed(() => {
      return Object.assign({ showMoreButton: true }, props.moreConfig);
    });
    const computeMaps = {};
    const $xeUpload = {
      xID,
      props,
      context,
      reactData,
      internalData,
      getRefMaps: () => refMaps,
      getComputeMaps: () => computeMaps
    };
    const getUniqueKey = () => {
      return import_xe_utils2.default.uniqueId();
    };
    const getFieldKey = (item) => {
      const keyField = computeKeyField.value;
      return item[keyField];
    };
    const updateFileList = () => {
      const { modelValue, multiple } = props;
      const formReadonly = computeFormReadonly.value;
      const keyField = computeKeyField.value;
      const nameProp = computeNameProp.value;
      const typeProp = computeTypeProp.value;
      const urlProp = computeUrlProp.value;
      const sizeProp = computeSizeProp.value;
      const fileList = modelValue ? (modelValue ? import_xe_utils2.default.isArray(modelValue) ? modelValue : [modelValue] : []).map((item) => {
        if (!item || import_xe_utils2.default.isString(item)) {
          const url = `${item || ""}`;
          const urlObj = import_xe_utils2.default.parseUrl(item);
          const name2 = (urlObj ? urlObj.searchQuery[nameProp] : "") || parseFileName(url);
          return {
            [nameProp]: name2,
            [typeProp]: (urlObj ? urlObj.searchQuery[typeProp] : "") || parseFileType(name2),
            [urlProp]: url,
            [sizeProp]: import_xe_utils2.default.toNumber(urlObj ? urlObj.searchQuery[sizeProp] : 0) || 0,
            [keyField]: getUniqueKey()
          };
        }
        const name = item[nameProp] || "";
        item[nameProp] = name;
        item[typeProp] = item[typeProp] || parseFileType(name);
        item[urlProp] = item[urlProp] || "";
        item[sizeProp] = item[sizeProp] || 0;
        item[keyField] = item[keyField] || getUniqueKey();
        return item;
      }) : [];
      reactData.fileList = formReadonly || multiple ? fileList : fileList.slice(0, 1);
    };
    const parseFileName = (url) => {
      return decodeURIComponent(`${url || ""}`).split("/").pop() || "";
    };
    const parseFileType = (name) => {
      const index = name.lastIndexOf(".");
      if (index > 0) {
        return name.substring(index + 1).toLowerCase();
      }
      return "";
    };
    const dispatchEvent = (type, params, evnt) => {
      emit(type, createEvent(evnt, { $upload: $xeUpload }, params));
    };
    const emitModel = (value) => {
      emit("update:modelValue", value);
    };
    const handleChange = (value) => {
      const { singleMode, urlMode } = props;
      const urlProp = computeUrlProp.value;
      const nameProp = computeNameProp.value;
      let restList = value ? value.slice(0) : [];
      if (urlMode) {
        restList = restList.map((item) => {
          const url = item[urlProp];
          if (url) {
            const urlObj = import_xe_utils2.default.parseUrl(url);
            if (!urlObj.searchQuery[nameProp]) {
              if (url.indexOf("blob:") === -1) {
                return `${url}${url.indexOf("?") === -1 ? "?" : "&"}${nameProp}=${encodeURIComponent(item[nameProp] || "")}`;
              }
            }
          }
          return url;
        });
      }
      emitModel(singleMode ? restList[0] || null : restList);
    };
    const getThumbnailFileUrl = (item) => {
      const getThumbnailUrlFn = props.getThumbnailUrlMethod || getConfig().upload.getThumbnailUrlMethod;
      if (getThumbnailUrlFn) {
        return getThumbnailUrlFn({
          $upload: $xeUpload,
          option: item
        });
      }
      return getFileUrl(item);
    };
    const getFileUrl = (item) => {
      const getUrlFn = props.getUrlMethod || getConfig().upload.getUrlMethod;
      const urlProp = computeUrlProp.value;
      return getUrlFn ? getUrlFn({
        $upload: $xeUpload,
        option: item
      }) : item[urlProp];
    };
    const handleDefaultFilePreview = (item) => {
      const { imageTypes, showDownloadButton } = props;
      const typeProp = computeTypeProp.value;
      const beforeDownloadFn = props.beforeDownloadMethod || getConfig().upload.beforeDownloadMethod;
      const { imagePreviewTypes } = internalData;
      if (imagePreviewTypes.concat(imageTypes || []).some((type) => `${type}`.toLowerCase() === `${item[typeProp]}`.toLowerCase())) {
        if (VxeUI.previewImage) {
          VxeUI.previewImage({
            urlList: [getFileUrl(item)],
            showDownloadButton,
            beforeDownloadMethod: beforeDownloadFn ? () => {
              return beforeDownloadFn({
                $upload: $xeUpload,
                option: item
              });
            } : void 0
          });
        }
      }
    };
    const handlePreviewFileEvent = (evnt, item) => {
      const previewFn = props.previewMethod || getConfig().upload.previewMethod;
      if (props.showPreview) {
        if (previewFn) {
          previewFn({
            $upload: $xeUpload,
            option: item
          });
        } else {
          handleDefaultFilePreview(item);
        }
      }
    };
    const handlePreviewImageEvent = (evnt, item, index) => {
      const { showDownloadButton } = props;
      const { fileList } = reactData;
      const beforeDownloadFn = props.beforeDownloadMethod || getConfig().upload.beforeDownloadMethod;
      if (props.showPreview) {
        if (VxeUI.previewImage) {
          VxeUI.previewImage({
            urlList: fileList.map((item2) => getFileUrl(item2)),
            activeIndex: index,
            showDownloadButton,
            beforeDownloadMethod: beforeDownloadFn ? ({ index: index2 }) => {
              return beforeDownloadFn({
                $upload: $xeUpload,
                option: fileList[index2]
              });
            } : void 0
          });
        }
      }
    };
    const handleUploadResult = (item, file) => {
      const { showErrorStatus } = props;
      const fileKey = getFieldKey(item);
      const uploadFn = props.uploadMethod || getConfig().upload.uploadMethod;
      if (uploadFn) {
        return Promise.resolve(uploadFn({
          $upload: $xeUpload,
          file,
          option: item,
          updateProgress(percentNum) {
            const { fileCacheMaps } = reactData;
            const cacheItem = fileCacheMaps[getFieldKey(item)];
            if (cacheItem) {
              cacheItem.percent = Math.max(0, Math.min(99, import_xe_utils2.default.toNumber(percentNum)));
            }
          }
        })).then((res) => {
          const { fileCacheMaps } = reactData;
          const cacheItem = fileCacheMaps[fileKey];
          if (cacheItem) {
            cacheItem.percent = 100;
            cacheItem.status = "success";
          }
          Object.assign(item, res);
          dispatchEvent("upload-success", { option: item, data: res }, null);
        }).catch((res) => {
          const { fileCacheMaps } = reactData;
          const cacheItem = fileCacheMaps[fileKey];
          if (cacheItem) {
            cacheItem.status = "error";
          }
          if (showErrorStatus) {
            Object.assign(item, res);
          } else {
            reactData.fileList = reactData.fileList.filter((obj) => getFieldKey(obj) !== fileKey);
          }
          dispatchEvent("upload-error", { option: item, data: res }, null);
        }).finally(() => {
          const { fileCacheMaps } = reactData;
          const cacheItem = fileCacheMaps[fileKey];
          if (cacheItem) {
            cacheItem.loading = false;
          }
        });
      } else {
        const { fileCacheMaps } = reactData;
        const cacheItem = fileCacheMaps[fileKey];
        if (cacheItem) {
          cacheItem.loading = false;
        }
      }
      return Promise.resolve();
    };
    const handleReUpload = (item) => {
      const { uploadMethod, urlMode } = props;
      const { fileCacheMaps } = reactData;
      const fileKey = getFieldKey(item);
      const cacheItem = fileCacheMaps[fileKey];
      const uploadFn = uploadMethod || getConfig().upload.uploadMethod;
      if (uploadFn && cacheItem) {
        const file = cacheItem.file;
        cacheItem.loading = true;
        cacheItem.status = "pending";
        cacheItem.percent = 0;
        handleUploadResult(item, file).then(() => {
          if (urlMode) {
            handleChange(reactData.fileList);
          }
        });
      }
    };
    const handleUploadFile = (files, evnt) => {
      const { multiple, urlMode, showLimitSize, limitSizeText, showLimitCount, limitCountText, autoSubmit } = props;
      const { fileList } = reactData;
      const uploadFn = props.uploadMethod || getConfig().upload.uploadMethod;
      const keyField = computeKeyField.value;
      const nameProp = computeNameProp.value;
      const typeProp = computeTypeProp.value;
      const urlProp = computeUrlProp.value;
      const sizeProp = computeSizeProp.value;
      const limitMaxSize = computeLimitMaxSize.value;
      const limitMaxCount = computeLimitMaxCount.value;
      const limitSizeUnit = computeLimitSizeUnit.value;
      let selectFiles = files;
      if (multiple && limitMaxCount) {
        if (showLimitCount && fileList.length >= limitMaxCount) {
          if (VxeUI.modal) {
            VxeUI.modal.notification({
              title: getI18n("vxe.modal.errTitle"),
              status: "error",
              content: limitCountText ? `${import_xe_utils2.default.isFunction(limitCountText) ? limitCountText({ maxCount: limitMaxCount }) : limitCountText}` : getI18n("vxe.upload.overCountErr", [limitMaxCount])
            });
          }
          return;
        }
        const overNum = selectFiles.length - (limitMaxCount - fileList.length);
        if (showLimitCount && overNum > 0) {
          const overExtraList = selectFiles.slice(limitMaxCount - fileList.length);
          if (limitCountText) {
            VxeUI.modal.notification({
              title: getI18n("vxe.modal.errTitle"),
              status: "error",
              content: `${import_xe_utils2.default.isFunction(limitCountText) ? limitCountText({ maxCount: limitMaxCount }) : limitCountText}`
            });
          } else if (VxeUI.modal) {
            VxeUI.modal.notification({
              title: getI18n("vxe.modal.errTitle"),
              status: "error",
              width: null,
              slots: {
                default() {
                  return h("div", {
                    class: "vxe-upload--file-message-over-error"
                  }, [
                    h("div", {}, getI18n("vxe.upload.overCountExtraErr", [limitMaxCount, overNum])),
                    h("div", {
                      class: "vxe-upload--file-message-over-extra"
                    }, overExtraList.map((file, index) => {
                      return h("div", {
                        key: index,
                        class: "vxe-upload--file-message-over-extra-item"
                      }, file.name);
                    }))
                  ]);
                }
              }
            });
          }
        }
        selectFiles = selectFiles.slice(0, limitMaxCount - fileList.length);
      }
      if (showLimitSize && limitMaxSize) {
        for (let i = 0; i < files.length; i++) {
          const file = files[0];
          if (file.size > limitMaxSize) {
            if (VxeUI.modal) {
              VxeUI.modal.notification({
                title: getI18n("vxe.modal.errTitle"),
                status: "error",
                content: limitSizeText ? `${import_xe_utils2.default.isFunction(limitSizeText) ? limitSizeText({ maxSize: limitMaxSize }) : limitSizeText}` : getI18n("vxe.upload.overSizeErr", [limitSizeUnit])
              });
            }
            return;
          }
        }
      }
      const cacheMaps = Object.assign({}, reactData.fileCacheMaps);
      const newFileList = multiple ? fileList : [];
      const uploadPromiseRests = [];
      selectFiles.forEach((file) => {
        const { name } = file;
        const fileKey = getUniqueKey();
        const fileObj = {
          [nameProp]: name,
          [typeProp]: parseFileType(name),
          [sizeProp]: file.size,
          [urlProp]: URL.createObjectURL(file),
          [keyField]: fileKey
        };
        if (uploadFn) {
          cacheMaps[fileKey] = {
            file,
            loading: !!autoSubmit,
            status: "pending",
            percent: 0
          };
        }
        const item = reactive(fileObj);
        if (uploadFn && autoSubmit) {
          uploadPromiseRests.push(handleUploadResult(item, file));
        }
        newFileList.push(item);
        dispatchEvent("add", { option: item }, evnt);
      });
      reactData.fileList = newFileList;
      reactData.fileCacheMaps = cacheMaps;
      Promise.all(urlMode ? uploadPromiseRests : []).then(() => {
        handleChange(newFileList);
        if ($xeForm && formItemInfo) {
          $xeForm.triggerItemEvent(evnt, formItemInfo.itemConfig.field, newFileList);
        }
      });
    };
    const handleChoose = (evnt) => {
      const { multiple, imageTypes, fileTypes } = props;
      const isDisabled = computeIsDisabled.value;
      const isImage = computeIsImage.value;
      if (isDisabled) {
        return Promise.resolve({
          status: false,
          files: [],
          file: null
        });
      }
      return readLocalFile({
        multiple,
        types: isImage ? imageTypes : fileTypes
      }).then((params) => {
        handleUploadFile(params.files, evnt);
        return params;
      });
    };
    const clickEvent = (evnt) => {
      handleChoose(evnt).catch(() => {
      });
    };
    const handleRemoveEvent = (evnt, item, index) => {
      const { fileList } = reactData;
      fileList.splice(index, 1);
      handleChange(fileList);
      if ($xeForm && formItemInfo) {
        $xeForm.triggerItemEvent(evnt, formItemInfo.itemConfig.field, fileList);
      }
      dispatchEvent("remove", { option: item }, evnt);
    };
    const removeFileEvent = (evnt, item, index) => {
      const beforeRemoveFn = props.beforeRemoveMethod || getConfig().upload.beforeRemoveMethod;
      const removeFn = props.removeMethod || getConfig().upload.removeMethod;
      Promise.resolve(beforeRemoveFn ? beforeRemoveFn({
        $upload: $xeUpload,
        option: item
      }) : true).then((status) => {
        if (status) {
          if (removeFn) {
            Promise.resolve(removeFn({
              $upload: $xeUpload,
              option: item
            })).then(() => {
              handleRemoveEvent(evnt, item, index);
            }).catch((e) => e);
          } else {
            handleRemoveEvent(evnt, item, index);
          }
        } else {
          dispatchEvent("remove-fail", { option: item }, evnt);
        }
      });
    };
    const handleDownloadEvent = (evnt, item) => {
      dispatchEvent("download", { option: item }, evnt);
    };
    const downloadFileEvent = (evnt, item) => {
      const beforeDownloadFn = props.beforeDownloadMethod || getConfig().upload.beforeDownloadMethod;
      const downloadFn = props.downloadMethod || getConfig().upload.downloadMethod;
      Promise.resolve(beforeDownloadFn ? beforeDownloadFn({
        $upload: $xeUpload,
        option: item
      }) : true).then((status) => {
        if (status) {
          if (downloadFn) {
            Promise.resolve(downloadFn({
              $upload: $xeUpload,
              option: item
            })).then(() => {
              handleDownloadEvent(evnt, item);
            }).catch((e) => e);
          } else {
            handleDownloadEvent(evnt, item);
          }
        } else {
          dispatchEvent("download-fail", { option: item }, evnt);
        }
      });
    };
    const handleUploadDragleaveEvent = (evnt) => {
      const targetElem = evnt.currentTarget;
      const { clientX, clientY } = evnt;
      if (targetElem) {
        const { x: targetX, y: targetY, height: targetHeight, width: targetWidth } = targetElem.getBoundingClientRect();
        if (clientX < targetX || clientX > targetX + targetWidth || clientY < targetY || clientY > targetY + targetHeight) {
          reactData.isDragUploadStatus = false;
        }
      }
    };
    const handleUploadDragoverEvent = (evnt) => {
      const dataTransfer = evnt.dataTransfer;
      if (dataTransfer) {
        const { items } = dataTransfer;
        if (items && items.length) {
          evnt.preventDefault();
          reactData.isDragUploadStatus = true;
        }
      }
    };
    const uploadTransferFileEvent = (evnt, files) => {
      const { imageTypes, fileTypes } = props;
      const { imagePreviewTypes } = internalData;
      const isImage = computeIsImage.value;
      if (isImage) {
        const pasteImgTypes = imagePreviewTypes.concat(imageTypes && imageTypes.length ? imageTypes : []);
        files = files.filter((file) => {
          const fileType = `${file.type.split("/")[1] || ""}`.toLowerCase();
          if (pasteImgTypes.some((type) => `${type}`.toLowerCase() === fileType)) {
            return true;
          }
          return false;
        });
      } else {
        if (fileTypes && fileTypes.length) {
          const errTypes = [];
          files.forEach((file) => {
            const fileType = parseFileType(file.name);
            if (!fileTypes.some((type) => `${type}`.toLowerCase() === fileType)) {
              errTypes.push(fileType);
            }
          });
          if (errTypes.length) {
            if (VxeUI.modal) {
              VxeUI.modal.message({
                content: getI18n("vxe.error.notType", [errTypes.join(", ")]),
                status: "error"
              });
            }
            return;
          }
        }
      }
      if (!files.length) {
        if (VxeUI.modal) {
          VxeUI.modal.notification({
            title: getI18n("vxe.modal.errTitle"),
            status: "error",
            content: getI18n("vxe.upload.uploadTypeErr")
          });
        }
        return;
      }
      handleUploadFile(files, evnt);
    };
    const handleUploadDropEvent = (evnt) => {
      const dataTransfer = evnt.dataTransfer;
      if (dataTransfer) {
        const { items } = dataTransfer;
        if (items && items.length) {
          evnt.preventDefault();
          const files = handleTransferFiles(items);
          if (files.length) {
            uploadTransferFileEvent(evnt, files);
          }
        }
      }
      reactData.isDragUploadStatus = false;
    };
    const handleTransferFiles = (items) => {
      const files = [];
      import_xe_utils2.default.arrayEach(items, (item) => {
        const file = item.getAsFile();
        if (file) {
          files.push(file);
        }
      });
      return files;
    };
    const handleMoreEvent = () => {
      const formReadonly = computeFormReadonly.value;
      const isImage = computeIsImage.value;
      if (VxeUI.modal) {
        VxeUI.modal.open({
          title: formReadonly ? getI18n("vxe.upload.morePopup.readTitle") : getI18n(`vxe.upload.morePopup.${isImage ? "imageTitle" : "fileTitle"}`),
          width: 660,
          height: 500,
          escClosable: true,
          showMaximize: true,
          resize: true,
          maskClosable: true,
          slots: {
            default() {
              const { showErrorStatus, dragToUpload, dragSort } = props;
              const { isActivated, isDragMove, isDragUploadStatus, dragIndex } = reactData;
              const { fileList } = reactData;
              const isDisabled = computeIsDisabled.value;
              const ons = {};
              if (dragToUpload && dragIndex === -1) {
                ons.onDragover = handleUploadDragoverEvent;
                ons.onDragleave = handleUploadDragleaveEvent;
                ons.onDrop = handleUploadDropEvent;
              }
              return h("div", Object.assign({ ref: refPopupElem, class: ["vxe-upload--more-popup", {
                "is--readonly": formReadonly,
                "is--disabled": isDisabled,
                "is--active": isActivated,
                "show--error": showErrorStatus,
                "is--drag": isDragUploadStatus
              }] }, ons), [
                isImage ? dragSort ? h(TransitionGroup, {
                  name: `vxe-upload--drag-list${isDragMove ? "" : "-disabled"}`,
                  tag: "div",
                  class: "vxe-upload--image-more-list"
                }, {
                  default: () => renderImageItemList(fileList, true).concat(renderImageAction(true))
                }) : h("div", {
                  class: "vxe-upload--image-more-list"
                }, renderImageItemList(fileList, true).concat(renderImageAction(true))) : h("div", {
                  class: "vxe-upload--file-more-list"
                }, [
                  renderFileAction(true),
                  dragSort ? h(TransitionGroup, {
                    name: `vxe-upload--drag-list${isDragMove ? "" : "-disabled"}`,
                    tag: "div",
                    class: "vxe-upload--file-list"
                  }, {
                    default: () => renderFileItemList(fileList, false)
                  }) : h("div", {
                    class: "vxe-upload--file-list"
                  }, renderFileItemList(fileList, true))
                ]),
                dragSort ? h("div", {
                  ref: refModalDragLineElem,
                  class: "vxe-upload--drag-line"
                }) : renderEmptyElement($xeUpload),
                isDragUploadStatus ? h("div", {
                  class: "vxe-upload--drag-placeholder"
                }, getI18n("vxe.upload.dragPlaceholder")) : renderEmptyElement($xeUpload)
              ]);
            }
          },
          onShow() {
            reactData.showMorePopup = true;
          },
          onHide() {
            reactData.showMorePopup = false;
          }
        });
      }
    };
    const showDropTip = (evnt, dragEl, dragPos) => {
      const { showMorePopup } = reactData;
      const el = refElem.value;
      const popupEl = refPopupElem.value;
      const wrapperEl = showMorePopup ? popupEl : el;
      if (!wrapperEl) {
        return;
      }
      const wrapperRect = wrapperEl.getBoundingClientRect();
      const ddLineEl = refDragLineElem.value;
      const mdLineEl = refModalDragLineElem.value;
      const currDLineEl = showMorePopup ? mdLineEl : ddLineEl;
      if (currDLineEl) {
        const dragRect = dragEl.getBoundingClientRect();
        currDLineEl.style.display = "block";
        currDLineEl.style.top = `${Math.max(1, dragRect.y - wrapperRect.y)}px`;
        currDLineEl.style.left = `${Math.max(1, dragRect.x - wrapperRect.x)}px`;
        currDLineEl.style.height = `${dragRect.height}px`;
        currDLineEl.style.width = `${dragRect.width - 1}px`;
        currDLineEl.setAttribute("drag-pos", dragPos);
      }
    };
    const hideDropTip = () => {
      const ddLineEl = refDragLineElem.value;
      const mdLineEl = refModalDragLineElem.value;
      if (ddLineEl) {
        ddLineEl.style.display = "";
      }
      if (mdLineEl) {
        mdLineEl.style.display = "";
      }
    };
    const handleDragSortDragstartEvent = (evnt) => {
      evnt.stopPropagation();
      if (evnt.dataTransfer) {
        evnt.dataTransfer.setDragImage(getTpImg(), 0, 0);
      }
      const dragEl = evnt.currentTarget;
      const parentEl = dragEl.parentElement;
      const dragIndex = import_xe_utils2.default.findIndexOf(Array.from(parentEl.children), (item) => dragEl === item);
      reactData.isDragMove = true;
      reactData.dragIndex = dragIndex;
      setTimeout(() => {
        reactData.isDragMove = false;
      }, 500);
    };
    const handleDragSortDragoverEvent = (evnt) => {
      evnt.stopPropagation();
      evnt.preventDefault();
      const { dragIndex } = reactData;
      if (dragIndex === -1) {
        return;
      }
      const isImage = computeIsImage.value;
      const dragEl = evnt.currentTarget;
      const parentEl = dragEl.parentElement;
      const currIndex = import_xe_utils2.default.findIndexOf(Array.from(parentEl.children), (item) => dragEl === item);
      let dragPos = "";
      if (isImage) {
        const offsetX = evnt.clientX - dragEl.getBoundingClientRect().x;
        dragPos = offsetX < dragEl.clientWidth / 2 ? "left" : "right";
      } else {
        const offsetY = evnt.clientY - dragEl.getBoundingClientRect().y;
        dragPos = offsetY < dragEl.clientHeight / 2 ? "top" : "bottom";
      }
      if (dragIndex === currIndex) {
        showDropTip(evnt, dragEl, dragPos);
        return;
      }
      showDropTip(evnt, dragEl, dragPos);
      internalData.prevDragIndex = currIndex;
      internalData.prevDragPos = dragPos;
    };
    const handleDragSortDragendEvent = (evnt) => {
      const { fileList, dragIndex } = reactData;
      const { prevDragIndex, prevDragPos } = internalData;
      const oldIndex = dragIndex;
      const targetIndex = prevDragIndex;
      const dragOffsetIndex = prevDragPos === "bottom" || prevDragPos === "right" ? 1 : 0;
      const oldItem = fileList[oldIndex];
      const newItem = fileList[targetIndex];
      if (oldItem && newItem) {
        fileList.splice(oldIndex, 1);
        const ptfIndex = import_xe_utils2.default.findIndexOf(fileList, (item) => newItem === item);
        const nIndex = ptfIndex + dragOffsetIndex;
        fileList.splice(nIndex, 0, oldItem);
        dispatchEvent("sort-dragend", {
          oldItem,
          newItem,
          dragPos: prevDragPos,
          offsetIndex: dragOffsetIndex,
          _index: {
            newIndex: nIndex,
            oldIndex
          }
        }, evnt);
      }
      hideDropTip();
      reactData.dragIndex = -1;
    };
    const handleItemMousedownEvent = (evnt) => {
      if ($xeTable) {
        evnt.stopPropagation();
      }
      reactData.isActivated = true;
    };
    const handleGlobalPasteEvent = (evnt) => {
      const { pasteToUpload } = props;
      const { isActivated } = reactData;
      if (!isActivated || !pasteToUpload) {
        return;
      }
      const clipboardData = evnt.clipboardData || evnt.originalEvent.clipboardData;
      if (!clipboardData) {
        return;
      }
      const { items } = clipboardData;
      if (!items) {
        return;
      }
      const files = handleTransferFiles(items);
      if (files.length) {
        evnt.preventDefault();
        uploadTransferFileEvent(evnt, files);
      }
    };
    const handleGlobalMousedownEvent = (evnt) => {
      const el = refElem.value;
      const popupEl = refPopupElem.value;
      let isActivated = getEventTargetNode(evnt, el).flag;
      if (!isActivated && popupEl) {
        const parentEl = popupEl.parentElement || popupEl;
        const modalEl = parentEl ? parentEl.parentElement : parentEl;
        isActivated = getEventTargetNode(evnt, modalEl).flag;
      }
      reactData.isActivated = isActivated;
    };
    const handleGlobalBlurEvent = () => {
      reactData.isActivated = false;
    };
    const uploadMethods = {
      dispatchEvent,
      choose() {
        return handleChoose(null);
      },
      submit(isFull) {
        const { maxSimultaneousUploads } = props;
        const msNum = import_xe_utils2.default.toNumber(maxSimultaneousUploads || 1) || 1;
        const { fileList, fileCacheMaps } = reactData;
        const allPendingList = fileList.filter((item) => {
          const fileKey = getFieldKey(item);
          const cacheItem = fileCacheMaps[fileKey];
          return cacheItem && (cacheItem.status === "pending" || isFull && cacheItem.status === "error");
        });
        const handleSubmit = (item) => {
          const fileKey = getFieldKey(item);
          const cacheItem = fileCacheMaps[fileKey];
          if (cacheItem) {
            const file = cacheItem.file;
            if (file && (cacheItem.status === "pending" || isFull && cacheItem.status === "error")) {
              cacheItem.loading = true;
              cacheItem.percent = 0;
              return handleUploadResult(item, file).then(handleNextSubmit);
            }
          }
          return handleNextSubmit();
        };
        const handleNextSubmit = () => {
          if (allPendingList.length) {
            const item = allPendingList[0];
            allPendingList.splice(0, 1);
            return handleSubmit(item).then(handleNextSubmit);
          }
          return Promise.resolve();
        };
        return Promise.all(allPendingList.splice(0, msNum).map(handleSubmit)).then(() => {
        });
      }
    };
    const uploadPrivateMethods = {};
    Object.assign($xeUpload, uploadMethods, uploadPrivateMethods);
    const renderFileItemList = (currList, isMoreView) => {
      const { showRemoveButton, showDownloadButton, showProgress, progressText, showPreview, showErrorStatus, dragSort, autoSubmit, showSubmitButton } = props;
      const { fileCacheMaps } = reactData;
      const isDisabled = computeIsDisabled.value;
      const formReadonly = computeFormReadonly.value;
      const nameProp = computeNameProp.value;
      const typeProp = computeTypeProp.value;
      const cornerSlot = slots.corner;
      const ons = {};
      if (dragSort && currList.length > 1) {
        ons.onDragstart = handleDragSortDragstartEvent;
        ons.onDragover = handleDragSortDragoverEvent;
        ons.onDragend = handleDragSortDragendEvent;
      }
      return currList.map((item, index) => {
        const fileKey = getFieldKey(item);
        const cacheItem = fileCacheMaps[fileKey];
        let isLoading = false;
        let isError = false;
        let isPending = false;
        const fileName = `${item[nameProp] || ""}`;
        if (cacheItem) {
          isLoading = cacheItem.loading;
          isError = cacheItem.status === "error";
          isPending = cacheItem.status === "pending";
        }
        return h("div", Object.assign({ key: dragSort ? fileKey : index, class: ["vxe-upload--file-item", {
          "is--preview": showPreview,
          "is--loading": isLoading,
          "is--pending": isPending,
          "is--error": isError
        }], fileid: fileKey, draggable: dragSort ? true : null }, ons), [
          h("div", {
            class: "vxe-upload--file-item-icon"
          }, [
            h("i", {
              class: getIcon()[`UPLOAD_FILE_TYPE_${`${item[typeProp]}`.toLocaleUpperCase()}`] || getIcon().UPLOAD_FILE_TYPE_DEFAULT
            })
          ]),
          h("div", {
            class: "vxe-upload--file-item-name",
            title: fileName,
            onClick(evnt) {
              if (!isLoading && !isError) {
                handlePreviewFileEvent(evnt, item);
              }
            }
          }, fileName),
          isLoading ? h("div", {
            class: "vxe-upload--file-item-loading-icon"
          }, [
            h("i", {
              class: getIcon().UPLOAD_LOADING
            })
          ]) : renderEmptyElement($xeUpload),
          showProgress && isLoading && cacheItem ? h("div", {
            class: "vxe-upload--file-item-loading-text"
          }, progressText ? import_xe_utils2.default.toFormatString(`${import_xe_utils2.default.isFunction(progressText) ? progressText({}) : progressText}`, { percent: cacheItem.percent }) : getI18n("vxe.upload.uploadProgress", [cacheItem.percent])) : renderEmptyElement($xeUpload),
          !isLoading && (isError && showErrorStatus || isPending && showSubmitButton && !autoSubmit) ? h("div", {
            class: "vxe-upload--file-item-rebtn"
          }, [
            h(button_default, {
              icon: isError ? getIcon().UPLOAD_IMAGE_RE_UPLOAD : getIcon().UPLOAD_IMAGE_UPLOAD,
              mode: "text",
              status: "primary",
              content: isError ? getI18n("vxe.upload.reUpload") : getI18n("vxe.upload.manualUpload"),
              onClick() {
                handleReUpload(item);
              }
            })
          ]) : renderEmptyElement($xeUpload),
          h("div", {
            class: "vxe-upload--file-item-btn-wrapper"
          }, [
            cornerSlot ? h("div", {
              class: "vxe-upload--file-item-corner"
            }, getSlotVNs(cornerSlot({ option: item, isMoreView, readonly: formReadonly }))) : renderEmptyElement($xeUpload),
            showDownloadButton && !(isLoading || isPending) ? h("div", {
              class: "vxe-upload--file-item-download-btn",
              onClick(evnt) {
                downloadFileEvent(evnt, item);
              }
            }, [
              h("i", {
                class: getIcon().UPLOAD_FILE_DOWNLOAD
              })
            ]) : renderEmptyElement($xeUpload),
            showRemoveButton && !formReadonly && !isDisabled && !isLoading ? h("div", {
              class: "vxe-upload--file-item-remove-btn",
              onClick(evnt) {
                removeFileEvent(evnt, item, index);
              }
            }, [
              h("i", {
                class: getIcon().UPLOAD_FILE_REMOVE
              })
            ]) : renderEmptyElement($xeUpload)
          ])
        ]);
      });
    };
    const renderFileAction = (isMoreView) => {
      const { showUploadButton, buttonText, buttonIcon, showButtonText, showButtonIcon, autoHiddenButton } = props;
      const isDisabled = computeIsDisabled.value;
      const formReadonly = computeFormReadonly.value;
      const showTipText = computedShowTipText.value;
      const defTipText = computedDefTipText.value;
      const overCount = computeOverCount.value;
      const defaultSlot = slots.default;
      const tipSlot = slots.tip || slots.hint;
      if (formReadonly || !showUploadButton) {
        return renderEmptyElement($xeUpload);
      }
      return h("div", {
        class: "vxe-upload--file-action"
      }, [
        autoHiddenButton && overCount ? renderEmptyElement($xeUpload) : h("div", {
          class: "vxe-upload--file-action-btn",
          onClick: clickEvent
        }, defaultSlot ? getSlotVNs(defaultSlot({ $upload: $xeUpload })) : [
          h(button_default, {
            class: "vxe-upload--file-action-button",
            content: isMoreView || showButtonText ? buttonText ? `${import_xe_utils2.default.isFunction(buttonText) ? buttonText({}) : buttonText}` : getI18n("vxe.upload.fileBtnText") : "",
            icon: showButtonIcon ? buttonIcon || getIcon().UPLOAD_FILE_ADD : "",
            disabled: isDisabled
          })
        ]),
        showTipText && (defTipText || tipSlot) ? h("div", {
          class: "vxe-upload--file-action-tip"
        }, tipSlot ? getSlotVNs(tipSlot({ $upload: $xeUpload })) : `${defTipText}`) : renderEmptyElement($xeUpload)
      ]);
    };
    const renderAllMode = () => {
      const { showList, moreConfig, dragSort } = props;
      const { fileList, isDragMove } = reactData;
      const moreOpts = computeMoreOpts.value;
      const { maxCount, showMoreButton, layout } = moreOpts;
      const isHorizontal = layout === "horizontal";
      let currList = fileList;
      let overMaxNum = 0;
      if (maxCount && fileList.length > maxCount) {
        overMaxNum = fileList.length - maxCount;
        currList = fileList.slice(0, maxCount);
      }
      return h("div", {
        key: "all",
        class: "vxe-upload--file-wrapper"
      }, showList ? [
        showMoreButton && moreConfig && isHorizontal ? renderEmptyElement($xeUpload) : renderFileAction(true),
        currList.length || showMoreButton && isHorizontal ? h("div", {
          class: ["vxe-upload--file-list-wrapper", {
            "is--horizontal": isHorizontal
          }]
        }, [
          currList.length ? dragSort ? h(TransitionGroup, {
            name: `vxe-upload--drag-list${isDragMove ? "" : "-disabled"}`,
            tag: "div",
            class: "vxe-upload--file-list"
          }, {
            default: () => renderFileItemList(currList, false)
          }) : h("div", {
            class: "vxe-upload--file-list"
          }, renderFileItemList(currList, false)) : renderEmptyElement($xeUpload),
          showMoreButton && overMaxNum ? h("div", {
            class: "vxe-upload--file-over-more"
          }, [
            h(button_default, {
              mode: "text",
              content: getI18n("vxe.upload.moreBtnText", [fileList.length]),
              status: "primary",
              onClick: handleMoreEvent
            })
          ]) : renderEmptyElement($xeUpload),
          showMoreButton && moreConfig && isHorizontal ? renderFileAction(false) : renderEmptyElement($xeUpload)
        ]) : renderEmptyElement($xeUpload)
      ] : [
        renderFileAction(false)
      ]);
    };
    const renderImageItemList = (currList, isMoreView) => {
      const { showRemoveButton, showProgress, progressText, showPreview, showErrorStatus, dragSort, autoSubmit, showSubmitButton } = props;
      const { fileCacheMaps } = reactData;
      const isDisabled = computeIsDisabled.value;
      const formReadonly = computeFormReadonly.value;
      const imageOpts = computeImageOpts.value;
      const imgStyle = computeImgStyle.value;
      const cornerSlot = slots.corner;
      const ons = {
        onMousedown: handleItemMousedownEvent
      };
      if (dragSort && currList.length > 1) {
        ons.onDragstart = handleDragSortDragstartEvent;
        ons.onDragover = handleDragSortDragoverEvent;
        ons.onDragend = handleDragSortDragendEvent;
      }
      return currList.map((item, index) => {
        const fileKey = getFieldKey(item);
        const cacheItem = fileCacheMaps[fileKey];
        let isLoading = false;
        let isError = false;
        let isPending = false;
        if (cacheItem) {
          isLoading = cacheItem.loading;
          isError = cacheItem.status === "error";
          isPending = cacheItem.status === "pending";
        }
        return h("div", Object.assign({ key: dragSort ? fileKey : index, class: ["vxe-upload--image-item", {
          "is--preview": showPreview,
          "is--circle": imageOpts.circle,
          "is--loading": isLoading,
          "is--pending": isPending,
          "is--error": isError
        }], fileid: fileKey, draggable: dragSort ? true : null }, ons), [
          h("div", {
            class: "vxe-upload--image-item-box",
            style: isMoreView ? null : imgStyle,
            onClick(evnt) {
              if (!isLoading && !isError) {
                handlePreviewImageEvent(evnt, item, index);
              }
            }
          }, [
            isLoading && cacheItem ? h("div", {
              class: "vxe-upload--image-item-loading"
            }, [
              h("div", {
                class: "vxe-upload--image-item-loading-icon"
              }, [
                h("i", {
                  class: getIcon().UPLOAD_LOADING
                })
              ]),
              showProgress ? h("div", {
                class: "vxe-upload--image-item-loading-text"
              }, progressText ? import_xe_utils2.default.toFormatString(`${import_xe_utils2.default.isFunction(progressText) ? progressText({}) : progressText}`, { percent: cacheItem.percent }) : getI18n("vxe.upload.uploadProgress", [cacheItem.percent])) : renderEmptyElement($xeUpload)
            ]) : renderEmptyElement($xeUpload),
            h("div", {
              class: "vxe-upload--image-item-img-wrapper",
              title: getI18n("vxe.upload.viewItemTitle")
            }, [
              h("img", {
                class: "vxe-upload--image-item-img",
                src: getThumbnailFileUrl(item)
              })
            ]),
            !isLoading && (isError && showErrorStatus || isPending && showSubmitButton && !autoSubmit) ? h("div", {
              class: "vxe-upload--image-item-rebtn"
            }, [
              h(button_default, {
                icon: isError ? getIcon().UPLOAD_IMAGE_RE_UPLOAD : getIcon().UPLOAD_IMAGE_UPLOAD,
                mode: "text",
                status: "primary",
                content: isError ? getI18n("vxe.upload.reUpload") : getI18n("vxe.upload.manualUpload"),
                onClick() {
                  handleReUpload(item);
                }
              })
            ]) : renderEmptyElement($xeUpload),
            h("div", {
              class: "vxe-upload--image-item-btn-wrapper",
              onClick(evnt) {
                evnt.stopPropagation();
              }
            }, [
              cornerSlot ? h("div", {
                class: "vxe-upload--file-item-corner"
              }, getSlotVNs(cornerSlot({ option: item, isMoreView, readonly: formReadonly }))) : renderEmptyElement($xeUpload),
              showRemoveButton && !formReadonly && !isDisabled && !isLoading ? h("div", {
                class: "vxe-upload--image-item-remove-btn",
                onClick(evnt) {
                  evnt.stopPropagation();
                  removeFileEvent(evnt, item, index);
                }
              }, [
                h("i", {
                  class: getIcon().UPLOAD_IMAGE_REMOVE
                })
              ]) : renderEmptyElement($xeUpload)
            ])
          ])
        ]);
      });
    };
    const renderImageAction = (isMoreView) => {
      const { showUploadButton, buttonText, buttonIcon, showButtonText, showButtonIcon, autoHiddenButton } = props;
      const formReadonly = computeFormReadonly.value;
      const showTipText = computedShowTipText.value;
      const defTipText = computedDefTipText.value;
      const overCount = computeOverCount.value;
      const imgStyle = computeImgStyle.value;
      const defaultSlot = slots.default;
      const tipSlot = slots.tip || slots.hint;
      if (formReadonly || !showUploadButton || autoHiddenButton && overCount) {
        return renderEmptyElement($xeUpload);
      }
      return h("div", {
        key: "action",
        class: "vxe-upload--image-action"
      }, [
        h("div", {
          class: "vxe-upload--image-action-btn",
          onClick: clickEvent
        }, defaultSlot ? defaultSlot({ $upload: $xeUpload }) : [
          h("div", {
            class: "vxe-upload--image-action-box",
            style: isMoreView ? null : imgStyle
          }, [
            showButtonIcon ? h("div", {
              class: "vxe-upload--image-action-icon"
            }, [
              h("i", {
                class: buttonIcon || getIcon().UPLOAD_IMAGE_ADD
              })
            ]) : renderEmptyElement($xeUpload),
            isMoreView || showButtonText ? h("div", {
              class: "vxe-upload--image-action-content"
            }, buttonText ? `${import_xe_utils2.default.isFunction(buttonText) ? buttonText({}) : buttonText}` : getI18n("vxe.upload.imgBtnText")) : renderEmptyElement($xeUpload),
            showTipText && (defTipText || tipSlot) ? h("div", {
              class: "vxe-upload--image-action-hint"
            }, tipSlot ? getSlotVNs(tipSlot({ $upload: $xeUpload })) : `${defTipText}`) : renderEmptyElement($xeUpload)
          ])
        ])
      ]);
    };
    const renderImageMode = () => {
      const { showList, dragSort } = props;
      const { fileList, isDragMove } = reactData;
      const moreOpts = computeMoreOpts.value;
      const { maxCount, showMoreButton } = moreOpts;
      let currList = fileList;
      let overMaxNum = 0;
      if (maxCount && fileList.length > maxCount) {
        overMaxNum = fileList.length - maxCount;
        currList = fileList.slice(0, maxCount);
      }
      return h("div", {
        key: "image",
        class: "vxe-upload--image-wrapper"
      }, showList ? [
        dragSort ? h(TransitionGroup, {
          name: `vxe-upload--drag-list${isDragMove ? "" : "-disabled"}`,
          tag: "div",
          class: "vxe-upload--image-list"
        }, {
          default: () => renderImageItemList(currList, false).concat([
            showMoreButton && overMaxNum ? h("div", {
              key: "om",
              class: "vxe-upload--image-over-more"
            }, [
              h(button_default, {
                mode: "text",
                content: getI18n("vxe.upload.moreBtnText", [fileList.length]),
                status: "primary",
                onClick: handleMoreEvent
              })
            ]) : renderEmptyElement($xeUpload),
            renderImageAction(false)
          ])
        }) : h("div", {
          class: "vxe-upload--image-list"
        }, renderImageItemList(currList, false).concat([
          showMoreButton && overMaxNum ? h("div", {
            class: "vxe-upload--image-over-more"
          }, [
            h(button_default, {
              mode: "text",
              content: getI18n("vxe.upload.moreBtnText", [fileList.length]),
              status: "primary",
              onClick: handleMoreEvent
            })
          ]) : renderEmptyElement($xeUpload),
          renderImageAction(false)
        ]))
      ] : [
        h("div", {
          class: "vxe-upload--image-list"
        }, [
          renderImageAction(false)
        ])
      ]);
    };
    const renderVN = () => {
      const { showErrorStatus, dragToUpload, pasteToUpload, dragSort } = props;
      const { isDragUploadStatus, showMorePopup, isActivated, dragIndex } = reactData;
      const vSize = computeSize.value;
      const isDisabled = computeIsDisabled.value;
      const formReadonly = computeFormReadonly.value;
      const isImage = computeIsImage.value;
      const ons = {
        onMousedown: handleItemMousedownEvent
      };
      if (dragToUpload && dragIndex === -1) {
        ons.onDragover = handleUploadDragoverEvent;
        ons.onDragleave = handleUploadDragleaveEvent;
        ons.onDrop = handleUploadDropEvent;
      }
      return h("div", Object.assign({ ref: refElem, class: ["vxe-upload", {
        [`size--${vSize}`]: vSize,
        "is--active": isActivated,
        "is--readonly": formReadonly,
        "is--disabled": isDisabled,
        "is--paste": pasteToUpload,
        "show--error": showErrorStatus,
        "is--drag": isDragUploadStatus
      }] }, ons), [
        isImage ? renderImageMode() : renderAllMode(),
        dragSort ? h("div", {
          ref: refDragLineElem,
          class: "vxe-upload--drag-line"
        }) : renderEmptyElement($xeUpload),
        isDragUploadStatus && !showMorePopup ? h("div", {
          class: "vxe-upload--drag-placeholder"
        }, getI18n("vxe.upload.dragPlaceholder")) : renderEmptyElement($xeUpload)
      ]);
    };
    const listFlag = ref(0);
    watch(() => props.modelValue ? props.modelValue.length : 0, () => {
      listFlag.value++;
    });
    watch(() => props.modelValue, () => {
      listFlag.value++;
    });
    watch(listFlag, () => {
      updateFileList();
    });
    onMounted(() => {
      if (props.multiple && props.singleMode) {
        errLog("vxe.error.errConflicts", ["multiple", "single-mode"]);
      }
      if (props.imageStyle) {
        warnLog("vxe.error.delProp", ["image-style", "image-config"]);
      }
      if (props.dragSort) {
        initTpImg();
      }
      globalEvents.on($xeUpload, "paste", handleGlobalPasteEvent);
      globalEvents.on($xeUpload, "mousedown", handleGlobalMousedownEvent);
      globalEvents.on($xeUpload, "blur", handleGlobalBlurEvent);
    });
    onUnmounted(() => {
      reactData.isDragUploadStatus = false;
      globalEvents.off($xeUpload, "paste");
      globalEvents.off($xeUpload, "mousedown");
      globalEvents.off($xeUpload, "blur");
    });
    updateFileList();
    $xeUpload.renderVN = renderVN;
    return $xeUpload;
  },
  render() {
    return this.renderVN();
  }
});

// ../../node_modules/.pnpm/vxe-pc-ui@4.9.29_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/upload/index.js
var VxeUpload = Object.assign({}, upload_default, {
  install(app) {
    app.component(upload_default.name, upload_default);
  }
});
dynamicApp.use(VxeUpload);
VxeUI.component(upload_default);
VxeUI.saveFile = saveLocalFile;
VxeUI.readFile = readLocalFile;
var Upload = VxeUpload;
var upload_default2 = VxeUpload;

// ../../node_modules/.pnpm/vxe-pc-ui@4.9.29_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-upload/index.js
var vxe_upload_default = upload_default2;
export {
  Upload,
  VxeUpload,
  vxe_upload_default as default
};
//# sourceMappingURL=vxe-pc-ui_es_vxe-upload_index__js.js.map
