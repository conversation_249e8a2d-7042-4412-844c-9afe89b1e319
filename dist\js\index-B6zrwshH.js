import{_ as r,o as O,w as x,a as B,i as E,b as j,f as G}from"./bootstrap-DlHXJWd_.js";import _,{selectProps as M}from"./index-B18uLgRK.js";import{a4 as P,P as F,x as f}from"../jse/index-index-DYNcUVMZ.js";import"./index-C_slJHcY.js";import"./Trigger-DqFxRNhn.js";import"./vnode-wTLMd7r4.js";import"./ResizeObserver.es-CDE7jhPe.js";import"./BaseInput-IJRN1dIn.js";import"./Overflow-DVJLTYCG.js";import"./index-DHmCRrBp.js";import"./isMobile-8sZ0LT6r.js";import"./DownOutlined-DlRNa_nA.js";import"./CheckOutlined-BwvlgC3h.js";import"./SearchOutlined-B4DC4PFQ.js";import"./move-CmFQWh0R.js";import"./slide-DSq39mcs.js";import"./useMergedState-C62ndRnY.js";import"./FormItemContext-DlTJXH8o.js";import"./statusUtils-1R4B2N1T.js";const n=()=>null;n.isSelectOption=!0;n.displayName="AAutoCompleteOption";const u=()=>null;u.isSelectOptGroup=!0;u.displayName="AAutoCompleteOptGroup";function $(e){var s,o;return((s=e==null?void 0:e.type)===null||s===void 0?void 0:s.isSelectOption)||((o=e==null?void 0:e.type)===null||o===void 0?void 0:o.isSelectOptGroup)}const k=()=>r(r({},O(M(),["loading","mode","optionLabelProp","labelInValue"])),{dataSource:Array,dropdownMenuStyle:{type:Object,default:void 0},dropdownMatchSelectWidth:{type:[Number,Boolean],default:!0},prefixCls:String,showSearch:{type:Boolean,default:void 0},transitionName:String,choiceTransitionName:{type:String,default:"zoom"},autofocus:{type:Boolean,default:void 0},backfill:{type:Boolean,default:void 0},filterOption:{type:[Boolean,Function],default:!1},defaultActiveFirstOption:{type:Boolean,default:!0},status:String}),ne=n,ae=u,v=P({compatConfig:{MODE:3},name:"AAutoComplete",inheritAttrs:!1,props:k(),slots:Object,setup(e,s){let{slots:o,attrs:C,expose:b}=s;x(!e.dropdownClassName);const d=F(),A=()=>{var t;const a=G((t=o.default)===null||t===void 0?void 0:t.call(o));return a.length?a[0]:void 0};b({focus:()=>{var t;(t=d.value)===null||t===void 0||t.focus()},blur:()=>{var t;(t=d.value)===null||t===void 0||t.blur()}});const{prefixCls:i}=B("select",e);return()=>{var t,a,p;const{size:S,dataSource:y,notFoundContent:N=(t=o.notFoundContent)===null||t===void 0?void 0:t.call(o)}=e;let c;const{class:g}=C,h={[g]:!!g,[`${i.value}-lg`]:S==="large",[`${i.value}-sm`]:S==="small",[`${i.value}-show-search`]:!0,[`${i.value}-auto-complete`]:!0};if(e.options===void 0){const m=((a=o.dataSource)===null||a===void 0?void 0:a.call(o))||((p=o.options)===null||p===void 0?void 0:p.call(o))||[];m.length&&$(m[0])?c=m:c=y?y.map(l=>{if(E(l))return l;switch(typeof l){case"string":return f(n,{key:l,value:l},{default:()=>[l]});case"object":return f(n,{key:l.value,value:l.value},{default:()=>[l.text]});default:throw new Error("AutoComplete[dataSource] only supports type `string[] | Object[]`.")}}):[]}const w=O(r(r(r({},e),C),{mode:_.SECRET_COMBOBOX_MODE_DO_NOT_USE,getInputElement:A,notFoundContent:N,class:h,popupClassName:e.popupClassName||e.dropdownClassName,ref:d}),["dataSource","loading"]);return f(_,w,j({default:()=>[c]},O(o,["default","dataSource","options"])))}}}),re=r(v,{Option:n,OptGroup:u,install(e){return e.component(v.name,v),e.component(n.displayName,n),e.component(u.displayName,u),e}});export{ae as AutoCompleteOptGroup,ne as AutoCompleteOption,k as autoCompleteProps,re as default};
