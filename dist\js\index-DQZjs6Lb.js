import{q as R,_ as l}from"./bootstrap-CFDAkNgp.js";import{i as w}from"./ResizeObserver.es-CDE7jhPe.js";import{a4 as x,T as C,a9 as M,p as H,ax as W,Y as _,q}from"../jse/index-index-B2UBupFX.js";const U=x({compatConfig:{MODE:3},name:"ResizeObserver",props:{disabled:Boolean,onResize:Function},emits:["resize"],setup(i,v){let{slots:c}=v;const n=C({width:0,height:0,offsetHeight:0,offsetWidth:0});let h=null,s=null;const r=()=>{s&&(s.disconnect(),s=null)},b=e=>{const{onResize:t}=i,o=e[0].target,{width:z,height:O}=o.getBoundingClientRect(),{offsetWidth:d,offsetHeight:f}=o,g=Math.floor(z),m=Math.floor(O);if(n.width!==g||n.height!==m||n.offsetWidth!==d||n.offsetHeight!==f){const u={width:g,height:m,offsetWidth:d,offsetHeight:f};l(n,u),t&&Promise.resolve().then(()=>{t(l(l({},u),{offsetWidth:d,offsetHeight:f}),o)})}},p=q(),a=()=>{const{disabled:e}=i;if(e){r();return}const t=R(p);t!==h&&(r(),h=t),!s&&t&&(s=new w(b),s.observe(t))};return M(()=>{a()}),H(()=>{a()}),W(()=>{r()}),_(()=>i.disabled,()=>{a()},{flush:"post"}),()=>{var e;return(e=c.default)===null||e===void 0?void 0:e.call(c)[0]}}});export{U as R};
