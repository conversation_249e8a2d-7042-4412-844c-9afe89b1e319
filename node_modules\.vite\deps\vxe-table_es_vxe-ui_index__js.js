import {
  VXETable,
  _t,
  clipboard,
  commands,
  config,
  formats,
  getConfig,
  getI18n,
  getIcon,
  getTheme,
  globalEvents,
  globalResize,
  hooks,
  interceptor,
  log,
  menus,
  modal,
  print,
  readFile,
  renderer,
  saveFile,
  setConfig,
  setI18n,
  setIcon,
  setLanguage,
  setTheme,
  setup,
  t,
  ui_default,
  use,
  validators,
  version
} from "./chunk-4QY7LSTU.js";
import "./chunk-RWHEUJNV.js";
import {
  VxeUI
} from "./chunk-KJAC55GV.js";
import "./chunk-G6CDOZZI.js";
import "./chunk-ZCM5A7SR.js";
import "./chunk-WDDBQLJB.js";
import "./chunk-V4OQ3NZ2.js";

// ../../node_modules/.pnpm/vxe-table@4.16.11_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/vxe-ui/index.js
var vxe_ui_default = ui_default;
export {
  VXETable,
  VxeUI,
  _t,
  clipboard,
  commands,
  config,
  vxe_ui_default as default,
  formats,
  getConfig,
  getI18n,
  getIcon,
  getTheme,
  globalEvents,
  globalResize,
  hooks,
  interceptor,
  log,
  menus,
  modal,
  print,
  readFile,
  renderer,
  saveFile,
  setConfig,
  setI18n,
  setIcon,
  setLanguage,
  setTheme,
  setup,
  t,
  use,
  validators,
  version
};
//# sourceMappingURL=vxe-table_es_vxe-ui_index__js.js.map
