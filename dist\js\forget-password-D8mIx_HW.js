var w=(c,l,t)=>new Promise((n,s)=>{var f=e=>{try{u(t.next(e))}catch(m){s(m)}},r=e=>{try{u(t.throw(e))}catch(m){s(m)}},u=e=>e.done?n(e.value):Promise.resolve(e.value).then(f,r);u((t=t.apply(c,l)).next())});import{b1 as k,b0 as i,b2 as v}from"./bootstrap-CFDAkNgp.js";import{T as P}from"./auth-title-lVew_PFZ.js";import{a4 as _,T as V,J as B,av as $,ab as S,x as p,aB as C,ac as b,a8 as T,ai as g,aj as h,a7 as o,aV as x,P as F,aa as y}from"../jse/index-index-B2UBupFX.js";import{u as N,s as A}from"./use-vben-form-DwBeC3z-.js";import"./render-content.vue_vue_type_script_lang-DAf0xCTA.js";const L=_({name:"ForgetPassword",__name:"forget-password",props:{formSchema:{},loading:{type:Boolean,default:!1},loginPath:{default:"/auth/login"},title:{default:""},subTitle:{default:""},submitButtonText:{default:""}},emits:["submit"],setup(c,{expose:l,emit:t}){const n=c,s=t,[f,r]=N(V({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:B(()=>n.formSchema),showDefaultActions:!1})),u=k();function e(){return w(this,null,function*(){const{valid:a}=yield r.validate(),d=yield r.getValues();a&&s("submit",d)})}function m(){u.push(n.loginPath)}return l({getFormApi:()=>r}),(a,d)=>(S(),$("div",null,[p(P,null,{desc:b(()=>[T(a.$slots,"subTitle",{},()=>[g(h(a.subTitle||o(i)("authentication.forgetPasswordSubtitle")),1)])]),default:b(()=>[T(a.$slots,"title",{},()=>[g(h(a.title||o(i)("authentication.forgetPassword"))+" 🤦🏻‍♂️ ",1)])]),_:3}),p(o(f)),C("div",null,[p(o(v),{class:x([{"cursor-wait":a.loading},"mt-2 w-full"]),"aria-label":"submit",onClick:e},{default:b(()=>[T(a.$slots,"submitButtonText",{},()=>[g(h(a.submitButtonText||o(i)("authentication.sendResetLink")),1)])]),_:3},8,["class"]),p(o(v),{class:"mt-4 w-full",variant:"outline",onClick:d[0]||(d[0]=R=>m())},{default:b(()=>[g(h(o(i)("common.back")),1)]),_:1})])]))}}),J=_({name:"ForgetPassword",__name:"forget-password",setup(c){const l=F(!1),t=B(()=>[{component:"VbenInput",componentProps:{placeholder:"<EMAIL>"},fieldName:"email",label:i("authentication.email"),rules:A().min(1,{message:i("authentication.emailTip")}).email(i("authentication.emailValidErrorTip"))}]);function n(s){console.log("reset email:",s)}return(s,f)=>(S(),y(o(L),{"form-schema":t.value,loading:l.value,onSubmit:n},null,8,["form-schema","loading"]))}});export{J as default};
