import{C as A,A as M}from"./Col-Bjak4A2I.js";import{d as R,g as O,m as P,_ as p,r as U,h as N,a as B,b as C,v as L,cg as $,l as V,G as X,H as _,o as G}from"./bootstrap-CFDAkNgp.js";import{x as m,a4 as z,P as D,a9 as Y,p as q,az as J}from"../jse/index-index-B2UBupFX.js";import{S as K}from"./index-DOXLVRHg.js";const ct=R(A),H=t=>{const{value:n,formatter:e,precision:a,decimalSeparator:s,groupSeparator:u="",prefixCls:r}=t;let c;if(typeof e=="function")c=e({value:n});else{const i=String(n),f=i.match(/^(-?)(\d*)(\.(\d+))?$/);if(!f)c=i;else{const d=f[1];let o=f[2]||"0",l=f[4]||"";o=o.replace(/\B(?=(\d{3})+(?!\d))/g,u),typeof a=="number"&&(l=l.padEnd(a,"0").slice(0,a>0?a:0)),l&&(l=`${s}${l}`),c=[m("span",{key:"int",class:`${r}-content-value-int`},[d,o]),l&&m("span",{key:"decimal",class:`${r}-content-value-decimal`},[l])]}}return m("span",{class:`${r}-content-value`},[c])};H.displayName="StatisticNumber";const Q=t=>{const{componentCls:n,marginXXS:e,padding:a,colorTextDescription:s,statisticTitleFontSize:u,colorTextHeading:r,statisticContentFontSize:c,statisticFontFamily:i}=t;return{[`${n}`]:p(p({},U(t)),{[`${n}-title`]:{marginBottom:e,color:s,fontSize:u},[`${n}-skeleton`]:{paddingTop:a},[`${n}-content`]:{color:r,fontSize:c,fontFamily:i,[`${n}-content-value`]:{display:"inline-block",direction:"ltr"},[`${n}-content-prefix, ${n}-content-suffix`]:{display:"inline-block"},[`${n}-content-prefix`]:{marginInlineEnd:e},[`${n}-content-suffix`]:{marginInlineStart:e}}})}},W=O("Statistic",t=>{const{fontSizeHeading3:n,fontSize:e,fontFamily:a}=t,s=P(t,{statisticTitleFontSize:e,statisticContentFontSize:n,statisticFontFamily:a});return[Q(s)]}),I=()=>({prefixCls:String,decimalSeparator:String,groupSeparator:String,format:String,value:_([Number,String,Object]),valueStyle:{type:Object,default:void 0},valueRender:X(),formatter:V(),precision:Number,prefix:$(),suffix:$(),title:$(),loading:L()}),v=z({compatConfig:{MODE:3},name:"AStatistic",inheritAttrs:!1,props:N(I(),{decimalSeparator:".",groupSeparator:",",loading:!1}),slots:Object,setup(t,n){let{slots:e,attrs:a}=n;const{prefixCls:s,direction:u}=B("statistic",t),[r,c]=W(s);return()=>{var i,f,d,o,l,g,S;const{value:E=0,valueStyle:k,valueRender:T}=t,x=s.value,h=(i=t.title)!==null&&i!==void 0?i:(f=e.title)===null||f===void 0?void 0:f.call(e),b=(d=t.prefix)!==null&&d!==void 0?d:(o=e.prefix)===null||o===void 0?void 0:o.call(e),F=(l=t.suffix)!==null&&l!==void 0?l:(g=e.suffix)===null||g===void 0?void 0:g.call(e),j=(S=t.formatter)!==null&&S!==void 0?S:e.formatter;let y=m(H,C({"data-for-update":Date.now()},p(p({},t),{prefixCls:x,value:E,formatter:j})),null);return T&&(y=T(y)),r(m("div",C(C({},a),{},{class:[x,{[`${x}-rtl`]:u.value==="rtl"},a.class,c.value]}),[h&&m("div",{class:`${x}-title`},[h]),m(K,{paragraph:!1,loading:t.loading},{default:()=>[m("div",{style:k,class:`${x}-content`},[b&&m("span",{class:`${x}-content-prefix`},[b]),y,F&&m("span",{class:`${x}-content-suffix`},[F])])]})]))}}}),Z=[["Y",1e3*60*60*24*365],["M",1e3*60*60*24*30],["D",1e3*60*60*24],["H",1e3*60*60],["m",1e3*60],["s",1e3],["S",1]];function tt(t,n){let e=t;const a=/\[[^\]]*]/g,s=(n.match(a)||[]).map(i=>i.slice(1,-1)),u=n.replace(a,"[]"),r=Z.reduce((i,f)=>{let[d,o]=f;if(i.includes(d)){const l=Math.floor(e/o);return e-=l*o,i.replace(new RegExp(`${d}+`,"g"),g=>{const S=g.length;return l.toString().padStart(S,"0")})}return i},u);let c=0;return r.replace(a,()=>{const i=s[c];return c+=1,i})}function et(t,n){const{format:e=""}=n,a=new Date(t).getTime(),s=Date.now(),u=Math.max(a-s,0);return tt(u,e)}const nt=1e3/30;function w(t){return new Date(t).getTime()}const ot=()=>p(p({},I()),{value:_([Number,String,Object]),format:String,onFinish:Function,onChange:Function}),at=z({compatConfig:{MODE:3},name:"AStatisticCountdown",props:N(ot(),{format:"HH:mm:ss"}),setup(t,n){let{emit:e,slots:a}=n;const s=D(),u=D(),r=()=>{const{value:o}=t;w(o)>=Date.now()?c():i()},c=()=>{if(s.value)return;const o=w(t.value);s.value=setInterval(()=>{u.value.$forceUpdate(),o>Date.now()&&e("change",o-Date.now()),r()},nt)},i=()=>{const{value:o}=t;s.value&&(clearInterval(s.value),s.value=void 0,w(o)<Date.now()&&e("finish"))},f=o=>{let{value:l,config:g}=o;const{format:S}=t;return et(l,p(p({},g),{format:S}))},d=o=>o;return Y(()=>{r()}),q(()=>{r()}),J(()=>{i()}),()=>{const o=t.value;return m(v,C({ref:u},p(p({},G(t,["onFinish","onChange"])),{value:o,valueRender:d,formatter:f})),a)}}});v.Countdown=at;v.install=function(t){return t.component(v.name,v),t.component(v.Countdown.name,v.Countdown),t};v.Countdown;const ut=R(M);export{ct as C,ut as R,v as S};
