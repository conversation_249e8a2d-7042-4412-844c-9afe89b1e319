import{_ as i,j as F,P as z,a as L,O as lt,B as q,g as it,m as st,r as G,a3 as O,b as T,aG as ut,f as ct,X as dt,Y as mt,Z as gt}from"./bootstrap-CFDAkNgp.js";import{a4 as W,J as f,T as bt,P as R,Y as Q,ax as ft,x as v,aA as vt}from"../jse/index-index-B2UBupFX.js";import{g as J,i as k}from"./colors-KzMfSzFw.js";function K(t){let{prefixCls:o,value:a,current:e,offset:n=0}=t,c;return n&&(c={position:"absolute",top:`${n}00%`,left:0}),v("p",{style:c,class:F(`${o}-only-unit`,{current:e})},[a])}function pt(t,o,a){let e=t,n=0;for(;(e+10)%10!==o;)e+=a,n+=a;return n}const ht=W({compatConfig:{MODE:3},name:"SingleNumber",props:{prefixCls:String,value:String,count:Number},setup(t){const o=f(()=>Number(t.value)),a=f(()=>Math.abs(t.count)),e=bt({prevValue:o.value,prevCount:a.value}),n=()=>{e.prevValue=o.value,e.prevCount=a.value},c=R();return Q(o,()=>{clearTimeout(c.value),c.value=setTimeout(()=>{n()},1e3)},{flush:"post"}),ft(()=>{clearTimeout(c.value)}),()=>{let d,p={};const s=o.value;if(e.prevValue===s||Number.isNaN(s)||Number.isNaN(e.prevValue))d=[K(i(i({},t),{current:!0}))],p={transition:"none"};else{d=[];const h=s+10,m=[];for(let r=s;r<=h;r+=1)m.push(r);const l=m.findIndex(r=>r%10===e.prevValue);d=m.map((r,y)=>{const $=r%10;return K(i(i({},t),{value:$,offset:y-l,current:y===l}))});const u=e.prevCount<a.value?1:-1;p={transform:`translateY(${-pt(e.prevValue,s,u)}00%)`}}return v("span",{class:`${t.prefixCls}-only`,style:p,onTransitionend:()=>n()},[d])}}});var $t=function(t,o){var a={};for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&o.indexOf(e)<0&&(a[e]=t[e]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,e=Object.getOwnPropertySymbols(t);n<e.length;n++)o.indexOf(e[n])<0&&Object.prototype.propertyIsEnumerable.call(t,e[n])&&(a[e[n]]=t[e[n]]);return a};const St={prefixCls:String,count:z.any,component:String,title:z.any,show:Boolean},yt=W({compatConfig:{MODE:3},name:"ScrollNumber",inheritAttrs:!1,props:St,setup(t,o){let{attrs:a,slots:e}=o;const{prefixCls:n}=L("scroll-number",t);return()=>{var c;const d=i(i({},t),a),{prefixCls:p,count:s,title:h,show:m,component:l="sup",class:u,style:r}=d,y=$t(d,["prefixCls","count","title","show","component","class","style"]),$=i(i({},y),{style:r,"data-show":t.show,class:F(n.value,u),title:h});let g=s;if(s&&Number(s)%1===0){const b=String(s).split("");g=b.map((P,B)=>v(ht,{prefixCls:n.value,count:Number(s),value:P,key:b.length-B},null))}r&&r.borderColor&&($.style=i(i({},r),{boxShadow:`0 0 0 1px ${r.borderColor} inset`}));const S=lt((c=e.default)===null||c===void 0?void 0:c.call(e));return S&&S.length?q(S,{class:F(`${n.value}-custom-component`)},!1):v(l,$,{default:()=>[g]})}}}),Ct=new O("antStatusProcessing",{"0%":{transform:"scale(0.8)",opacity:.5},"100%":{transform:"scale(2.4)",opacity:0}}),xt=new O("antZoomBadgeIn",{"0%":{transform:"scale(0) translate(50%, -50%)",opacity:0},"100%":{transform:"scale(1) translate(50%, -50%)"}}),wt=new O("antZoomBadgeOut",{"0%":{transform:"scale(1) translate(50%, -50%)"},"100%":{transform:"scale(0) translate(50%, -50%)",opacity:0}}),Nt=new O("antNoWrapperZoomBadgeIn",{"0%":{transform:"scale(0)",opacity:0},"100%":{transform:"scale(1)"}}),Ot=new O("antNoWrapperZoomBadgeOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0)",opacity:0}}),Pt=new O("antBadgeLoadingCircle",{"0%":{transformOrigin:"50%"},"100%":{transform:"translate(50%, -50%) rotate(360deg)",transformOrigin:"50%"}}),Bt=t=>{const{componentCls:o,iconCls:a,antCls:e,badgeFontHeight:n,badgeShadowSize:c,badgeHeightSm:d,motionDurationSlow:p,badgeStatusSize:s,marginXS:h,badgeRibbonOffset:m}=t,l=`${e}-scroll-number`,u=`${e}-ribbon`,r=`${e}-ribbon-wrapper`,y=J(t,(g,S)=>{let{darkColor:b}=S;return{[`&${o} ${o}-color-${g}`]:{background:b,[`&:not(${o}-count)`]:{color:b}}}}),$=J(t,(g,S)=>{let{darkColor:b}=S;return{[`&${u}-color-${g}`]:{background:b,color:b}}});return{[o]:i(i(i(i({},G(t)),{position:"relative",display:"inline-block",width:"fit-content",lineHeight:1,[`${o}-count`]:{zIndex:t.badgeZIndex,minWidth:t.badgeHeight,height:t.badgeHeight,color:t.badgeTextColor,fontWeight:t.badgeFontWeight,fontSize:t.badgeFontSize,lineHeight:`${t.badgeHeight}px`,whiteSpace:"nowrap",textAlign:"center",background:t.badgeColor,borderRadius:t.badgeHeight/2,boxShadow:`0 0 0 ${c}px ${t.badgeShadowColor}`,transition:`background ${t.motionDurationMid}`,a:{color:t.badgeTextColor},"a:hover":{color:t.badgeTextColor},"a:hover &":{background:t.badgeColorHover}},[`${o}-count-sm`]:{minWidth:d,height:d,fontSize:t.badgeFontSizeSm,lineHeight:`${d}px`,borderRadius:d/2},[`${o}-multiple-words`]:{padding:`0 ${t.paddingXS}px`},[`${o}-dot`]:{zIndex:t.badgeZIndex,width:t.badgeDotSize,minWidth:t.badgeDotSize,height:t.badgeDotSize,background:t.badgeColor,borderRadius:"100%",boxShadow:`0 0 0 ${c}px ${t.badgeShadowColor}`},[`${o}-dot${l}`]:{transition:`background ${p}`},[`${o}-count, ${o}-dot, ${l}-custom-component`]:{position:"absolute",top:0,insetInlineEnd:0,transform:"translate(50%, -50%)",transformOrigin:"100% 0%",[`&${a}-spin`]:{animationName:Pt,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear"}},[`&${o}-status`]:{lineHeight:"inherit",verticalAlign:"baseline",[`${o}-status-dot`]:{position:"relative",top:-1,display:"inline-block",width:s,height:s,verticalAlign:"middle",borderRadius:"50%"},[`${o}-status-success`]:{backgroundColor:t.colorSuccess},[`${o}-status-processing`]:{overflow:"visible",color:t.colorPrimary,backgroundColor:t.colorPrimary,"&::after":{position:"absolute",top:0,insetInlineStart:0,width:"100%",height:"100%",borderWidth:c,borderStyle:"solid",borderColor:"inherit",borderRadius:"50%",animationName:Ct,animationDuration:t.badgeProcessingDuration,animationIterationCount:"infinite",animationTimingFunction:"ease-in-out",content:'""'}},[`${o}-status-default`]:{backgroundColor:t.colorTextPlaceholder},[`${o}-status-error`]:{backgroundColor:t.colorError},[`${o}-status-warning`]:{backgroundColor:t.colorWarning},[`${o}-status-text`]:{marginInlineStart:h,color:t.colorText,fontSize:t.fontSize}}}),y),{[`${o}-zoom-appear, ${o}-zoom-enter`]:{animationName:xt,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack,animationFillMode:"both"},[`${o}-zoom-leave`]:{animationName:wt,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack,animationFillMode:"both"},[`&${o}-not-a-wrapper`]:{[`${o}-zoom-appear, ${o}-zoom-enter`]:{animationName:Nt,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack},[`${o}-zoom-leave`]:{animationName:Ot,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack},[`&:not(${o}-status)`]:{verticalAlign:"middle"},[`${l}-custom-component, ${o}-count`]:{transform:"none"},[`${l}-custom-component, ${l}`]:{position:"relative",top:"auto",display:"block",transformOrigin:"50% 50%"}},[`${l}`]:{overflow:"hidden",[`${l}-only`]:{position:"relative",display:"inline-block",height:t.badgeHeight,transition:`all ${t.motionDurationSlow} ${t.motionEaseOutBack}`,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden",[`> p${l}-only-unit`]:{height:t.badgeHeight,margin:0,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden"}},[`${l}-symbol`]:{verticalAlign:"top"}},"&-rtl":{direction:"rtl",[`${o}-count, ${o}-dot, ${l}-custom-component`]:{transform:"translate(-50%, -50%)"}}}),[`${r}`]:{position:"relative"},[`${u}`]:i(i(i(i({},G(t)),{position:"absolute",top:h,padding:`0 ${t.paddingXS}px`,color:t.colorPrimary,lineHeight:`${n}px`,whiteSpace:"nowrap",backgroundColor:t.colorPrimary,borderRadius:t.borderRadiusSM,[`${u}-text`]:{color:t.colorTextLightSolid},[`${u}-corner`]:{position:"absolute",top:"100%",width:m,height:m,color:"currentcolor",border:`${m/2}px solid`,transform:t.badgeRibbonCornerTransform,transformOrigin:"top",filter:t.badgeRibbonCornerFilter}}),$),{[`&${u}-placement-end`]:{insetInlineEnd:-m,borderEndEndRadius:0,[`${u}-corner`]:{insetInlineEnd:0,borderInlineEndColor:"transparent",borderBlockEndColor:"transparent"}},[`&${u}-placement-start`]:{insetInlineStart:-m,borderEndStartRadius:0,[`${u}-corner`]:{insetInlineStart:0,borderBlockEndColor:"transparent",borderInlineStartColor:"transparent"}},"&-rtl":{direction:"rtl"}})}},tt=it("Badge",t=>{const{fontSize:o,lineHeight:a,fontSizeSM:e,lineWidth:n,marginXS:c,colorBorderBg:d}=t,p=Math.round(o*a),s=n,h="auto",m=p-2*s,l=t.colorBgContainer,u="normal",r=e,y=t.colorError,$=t.colorErrorHover,g=o,S=e/2,b=e,P=e/2,B=st(t,{badgeFontHeight:p,badgeShadowSize:s,badgeZIndex:h,badgeHeight:m,badgeTextColor:l,badgeFontWeight:u,badgeFontSize:r,badgeColor:y,badgeColorHover:$,badgeShadowColor:d,badgeHeightSm:g,badgeDotSize:S,badgeFontSizeSm:b,badgeStatusSize:P,badgeProcessingDuration:"1.2s",badgeRibbonOffset:c,badgeRibbonCornerTransform:"scaleY(0.75)",badgeRibbonCornerFilter:"brightness(75%)"});return[Bt(B)]});var It=function(t,o){var a={};for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&o.indexOf(e)<0&&(a[e]=t[e]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,e=Object.getOwnPropertySymbols(t);n<e.length;n++)o.indexOf(e[n])<0&&Object.prototype.propertyIsEnumerable.call(t,e[n])&&(a[e[n]]=t[e[n]]);return a};const Tt=()=>({prefix:String,color:{type:String},text:z.any,placement:{type:String,default:"end"}}),V=W({compatConfig:{MODE:3},name:"ABadgeRibbon",inheritAttrs:!1,props:Tt(),slots:Object,setup(t,o){let{attrs:a,slots:e}=o;const{prefixCls:n,direction:c}=L("ribbon",t),[d,p]=tt(n),s=f(()=>k(t.color,!1)),h=f(()=>[n.value,`${n.value}-placement-${t.placement}`,{[`${n.value}-rtl`]:c.value==="rtl",[`${n.value}-color-${t.color}`]:s.value}]);return()=>{var m,l;const{class:u,style:r}=a,y=It(a,["class","style"]),$={},g={};return t.color&&!s.value&&($.background=t.color,g.color=t.color),d(v("div",T({class:`${n.value}-wrapper ${p.value}`},y),[(m=e.default)===null||m===void 0?void 0:m.call(e),v("div",{class:[h.value,u,p.value],style:i(i({},$),r)},[v("span",{class:`${n.value}-text`},[t.text||((l=e.text)===null||l===void 0?void 0:l.call(e))]),v("div",{class:`${n.value}-corner`,style:g},null)])]))}}}),zt=t=>!isNaN(parseFloat(t))&&isFinite(t),Et=()=>({count:z.any.def(null),showZero:{type:Boolean,default:void 0},overflowCount:{type:Number,default:99},dot:{type:Boolean,default:void 0},prefixCls:String,scrollNumberPrefixCls:String,status:{type:String},size:{type:String,default:"default"},color:String,text:z.any,offset:Array,numberStyle:{type:Object,default:void 0},title:String}),M=W({compatConfig:{MODE:3},name:"ABadge",Ribbon:V,inheritAttrs:!1,props:Et(),slots:Object,setup(t,o){let{slots:a,attrs:e}=o;const{prefixCls:n,direction:c}=L("badge",t),[d,p]=tt(n),s=f(()=>t.count>t.overflowCount?`${t.overflowCount}+`:t.count),h=f(()=>s.value==="0"||s.value===0),m=f(()=>t.count===null||h.value&&!t.showZero),l=f(()=>(t.status!==null&&t.status!==void 0||t.color!==null&&t.color!==void 0)&&m.value),u=f(()=>t.dot&&!h.value),r=f(()=>u.value?"":s.value),y=f(()=>(r.value===null||r.value===void 0||r.value===""||h.value&&!t.showZero)&&!u.value),$=R(t.count),g=R(r.value),S=R(u.value);Q([()=>t.count,r,u],()=>{y.value||($.value=t.count,g.value=r.value,S.value=u.value)},{immediate:!0});const b=f(()=>k(t.color,!1)),P=f(()=>({[`${n.value}-status-dot`]:l.value,[`${n.value}-status-${t.status}`]:!!t.status,[`${n.value}-color-${t.color}`]:b.value})),B=f(()=>t.color&&!b.value?{background:t.color,color:t.color}:{}),et=f(()=>({[`${n.value}-dot`]:S.value,[`${n.value}-count`]:!S.value,[`${n.value}-count-sm`]:t.size==="small",[`${n.value}-multiple-words`]:!S.value&&g.value&&g.value.toString().length>1,[`${n.value}-status-${t.status}`]:!!t.status,[`${n.value}-color-${t.color}`]:b.value}));return()=>{var E,_;const{offset:N,title:Z,color:X}=t,Y=e.style,j=ut(a,t,"text"),x=n.value,C=$.value;let w=ct((E=a.default)===null||E===void 0?void 0:E.call(a));w=w.length?w:null;const A=!!(!y.value||a.count),D=(()=>{if(!N)return i({},Y);const I={marginTop:zt(N[1])?`${N[1]}px`:N[1]};return c.value==="rtl"?I.left=`${parseInt(N[0],10)}px`:I.right=`${-parseInt(N[0],10)}px`,i(i({},I),Y)})(),ot=Z!=null?Z:typeof C=="string"||typeof C=="number"?C:void 0,nt=A||!j?null:v("span",{class:`${x}-status-text`},[j]),at=typeof C=="object"||C===void 0&&a.count?q(C!=null?C:(_=a.count)===null||_===void 0?void 0:_.call(a),{style:D},!1):null,U=F(x,{[`${x}-status`]:l.value,[`${x}-not-a-wrapper`]:!w,[`${x}-rtl`]:c.value==="rtl"},e.class,p.value);if(!w&&l.value){const I=D.color;return d(v("span",T(T({},e),{},{class:U,style:D}),[v("span",{class:P.value,style:B.value},null),v("span",{style:{color:I},class:`${x}-status-text`},[j])]))}const rt=dt(w?`${x}-zoom`:"",{appear:!1});let H=i(i({},D),t.numberStyle);return X&&!b.value&&(H=H||{},H.background=X),d(v("span",T(T({},e),{},{class:U}),[w,v(mt,rt,{default:()=>[vt(v(yt,{prefixCls:t.scrollNumberPrefixCls,show:A,class:et.value,count:g.value,title:ot,style:H,key:"scrollNumber"},{default:()=>[at]}),[[gt,A]])]}),nt]))}}});M.install=function(t){return t.component(M.name,M),t.component(V.name,V),t};export{M as B,zt as i};
