<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue';
import {
  <PERSON><PERSON>,
  Bad<PERSON>,
  Button,
  Card,
  Col,
  Descriptions,
  Progress,
  Row,
  Select,
  Space,
  Statistic,
  Table,
  Tag,
  Tabs,
  TabPane,
} from 'ant-design-vue';
import type { TableColumnsType } from 'ant-design-vue';

defineOptions({ name: 'IrrigationMaintenance' });

// 维修统计数据
const maintenanceStats = reactive({
  totalStations: 154,
  completedMaintenance: 131,
  inProgress: 15,
  pending: 8,
  completionRate: 85.1,
  safetyImprovementRate: 92.3,
  equipmentReplacementRate: 78.6,
  pipelineRepairRate: 88.9,
});

// 维修记录数据
const maintenanceRecords = ref([
  {
    id: 1,
    stationName: '东风灌站',
    workType: '进排水设施检查',
    description: '检查主进水管道、出水阀门、水泵设备运行状态',
    startDate: '2024-01-10',
    endDate: '2024-01-12',
    status: 'completed',
    cost: 8500,
    worker: '张三',
    result: '更换老旧阀门2个，清理管道堵塞物，设备运行正常',
    safetyLevel: 'improved',
  },
  {
    id: 2,
    stationName: '红旗灌站',
    workType: '老旧线路更换改造',
    description: '更换老旧电力线路，消除安全隐患，提升供电稳定性',
    startDate: '2024-01-08',
    endDate: '2024-01-15',
    status: 'in_progress',
    cost: 25000,
    worker: '李四',
    result: '正在进行线路改造，预计3天内完成',
    safetyLevel: 'improving',
  },
  {
    id: 3,
    stationName: '胜利灌站',
    workType: '零配件管道更换',
    description: '更换磨损严重的零配件，维修管道接头，保证正常进排水',
    startDate: '2024-01-05',
    endDate: '2024-01-07',
    status: 'completed',
    cost: 12000,
    worker: '王五',
    result: '更换水泵轴承、密封圈，维修管道接头5处，进排水正常',
    safetyLevel: 'improved',
  },
  {
    id: 4,
    stationName: '新华灌站',
    workType: '设施设备全面检修',
    description: '对提灌站所有设施设备进行全面检查维修，确保安全高效运行',
    startDate: '2024-01-12',
    endDate: '2024-01-18',
    status: 'completed',
    cost: 18500,
    worker: '赵六',
    result: '更换控制柜元件，维修水泵，清洗过滤器，设备运行稳定',
    safetyLevel: 'improved',
  },
]);

// 设备更换记录
const equipmentReplacements = ref([
  {
    id: 1,
    stationName: '东风灌站',
    equipmentName: '主水泵',
    oldModel: 'IS200-150-400A',
    newModel: 'IS200-150-400B',
    replacementDate: '2024-01-10',
    reason: '设备老化，效率下降',
    cost: 45000,
    warranty: '3年',
    status: 'completed',
  },
  {
    id: 2,
    stationName: '红旗灌站',
    equipmentName: '变频器',
    oldModel: 'VFD-M-55kW-A',
    newModel: 'VFD-M-55kW-B',
    replacementDate: '2024-01-08',
    reason: '故障频发，需要更换',
    cost: 28000,
    warranty: '2年',
    status: 'completed',
  },
]);

// 表格列定义
const maintenanceColumns: TableColumnsType = [
  {
    title: '灌站名称',
    dataIndex: 'stationName',
    key: 'stationName',
    width: 120,
  },
  { title: '维修类型', dataIndex: 'workType', key: 'workType', width: 150 },
  { title: '开始日期', dataIndex: 'startDate', key: 'startDate', width: 100 },
  { title: '结束日期', dataIndex: 'endDate', key: 'endDate', width: 100 },
  { title: '状态', dataIndex: 'status', key: 'status', width: 100 },
  { title: '费用(元)', dataIndex: 'cost', key: 'cost', width: 100 },
  { title: '负责人', dataIndex: 'worker', key: 'worker', width: 80 },
  {
    title: '安全提升',
    dataIndex: 'safetyLevel',
    key: 'safetyLevel',
    width: 100,
  },
];

const equipmentColumns: TableColumnsType = [
  {
    title: '灌站名称',
    dataIndex: 'stationName',
    key: 'stationName',
    width: 120,
  },
  {
    title: '设备名称',
    dataIndex: 'equipmentName',
    key: 'equipmentName',
    width: 120,
  },
  { title: '原型号', dataIndex: 'oldModel', key: 'oldModel', width: 150 },
  { title: '新型号', dataIndex: 'newModel', key: 'newModel', width: 150 },
  {
    title: '更换日期',
    dataIndex: 'replacementDate',
    key: 'replacementDate',
    width: 100,
  },
  { title: '费用(元)', dataIndex: 'cost', key: 'cost', width: 100 },
  { title: '保修期', dataIndex: 'warranty', key: 'warranty', width: 80 },
  { title: '状态', dataIndex: 'status', key: 'status', width: 80 },
];

// 获取状态标签
const getStatusTag = (status: string) => {
  switch (status) {
    case 'completed':
      return { color: 'green', text: '已完成' };
    case 'in_progress':
      return { color: 'blue', text: '进行中' };
    case 'pending':
      return { color: 'orange', text: '待开始' };
    default:
      return { color: 'default', text: '未知' };
  }
};

// 获取安全等级标签
const getSafetyTag = (level: string) => {
  switch (level) {
    case 'improved':
      return { color: 'green', text: '已提升' };
    case 'improving':
      return { color: 'blue', text: '提升中' };
    case 'pending':
      return { color: 'orange', text: '待提升' };
    default:
      return { color: 'default', text: '未知' };
  }
};

// 计算统计数据
const computedStats = computed(() => {
  const completionRate = (
    (maintenanceStats.completedMaintenance / maintenanceStats.totalStations) *
    100
  ).toFixed(1);
  const progressRate = (
    (maintenanceStats.inProgress / maintenanceStats.totalStations) *
    100
  ).toFixed(1);

  return {
    completionRate,
    progressRate,
  };
});
</script>

<template>
  <div class="p-5">
    <!-- 维修统计概览 -->
    <Card title="维修工作统计概览" class="mb-5">
      <Row :gutter="16">
        <Col :span="4">
          <Statistic
            title="总灌站数"
            :value="maintenanceStats.totalStations"
            suffix="座"
            :value-style="{ color: '#1890ff' }"
          />
        </Col>
        <Col :span="4">
          <Statistic
            title="已完成维修"
            :value="maintenanceStats.completedMaintenance"
            suffix="座"
            :value-style="{ color: '#52c41a' }"
          />
        </Col>
        <Col :span="4">
          <Statistic
            title="维修中"
            :value="maintenanceStats.inProgress"
            suffix="座"
            :value-style="{ color: '#1890ff' }"
          />
        </Col>
        <Col :span="4">
          <Statistic
            title="待维修"
            :value="maintenanceStats.pending"
            suffix="座"
            :value-style="{ color: '#faad14' }"
          />
        </Col>
        <Col :span="4">
          <Statistic
            title="完成率"
            :value="computedStats.completionRate"
            suffix="%"
            :value-style="{ color: '#722ed1' }"
          />
        </Col>
        <Col :span="4">
          <Statistic
            title="安全提升率"
            :value="maintenanceStats.safetyImprovementRate"
            suffix="%"
            :value-style="{ color: '#13c2c2' }"
          />
        </Col>
      </Row>
    </Card>

    <!-- 维修进度展示 -->
    <Card title="维修改造进度" class="mb-5">
      <Row :gutter="16">
        <Col :span="6">
          <div class="text-center">
            <div class="mb-2 text-lg font-bold text-green-600">设备更换率</div>
            <Progress
              :percent="maintenanceStats.equipmentReplacementRate"
              stroke-color="#52c41a"
              :stroke-width="8"
            />
            <div class="mt-2 text-sm text-gray-500">
              保证设施设备配套使用，满足灌溉需求
            </div>
          </div>
        </Col>
        <Col :span="6">
          <div class="text-center">
            <div class="mb-2 text-lg font-bold text-blue-600">管道维修率</div>
            <Progress
              :percent="maintenanceStats.pipelineRepairRate"
              stroke-color="#1890ff"
              :stroke-width="8"
            />
            <div class="mt-2 text-sm text-gray-500">确保进排水设施正常运行</div>
          </div>
        </Col>
        <Col :span="6">
          <div class="text-center">
            <div class="mb-2 text-lg font-bold text-purple-600">
              安全隐患消除
            </div>
            <Progress
              :percent="maintenanceStats.safetyImprovementRate"
              stroke-color="#722ed1"
              :stroke-width="8"
            />
            <div class="mt-2 text-sm text-gray-500">
              老旧线路更换改造，消除安全隐患
            </div>
          </div>
        </Col>
        <Col :span="6">
          <div class="text-center">
            <div class="mb-2 text-lg font-bold text-orange-600">运行稳定性</div>
            <Progress
              :percent="maintenanceStats.completionRate"
              stroke-color="#faad14"
              :stroke-width="8"
            />
            <div class="mt-2 text-sm text-gray-500">
              维修后安全、高效、稳定运行
            </div>
          </div>
        </Col>
      </Row>
    </Card>

    <!-- 详细记录 -->
    <Card title="维修工作详细记录">
      <Tabs default-active-key="1">
        <TabPane key="1" tab="维修记录">
          <Table
            :columns="maintenanceColumns"
            :data-source="maintenanceRecords"
            :scroll="{ x: 1200 }"
            row-key="id"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <Tag :color="getStatusTag(record.status).color">
                  {{ getStatusTag(record.status).text }}
                </Tag>
              </template>
              <template v-else-if="column.key === 'cost'">
                <span class="font-medium"
                  >¥{{ record.cost.toLocaleString() }}</span
                >
              </template>
              <template v-else-if="column.key === 'safetyLevel'">
                <Tag :color="getSafetyTag(record.safetyLevel).color">
                  {{ getSafetyTag(record.safetyLevel).text }}
                </Tag>
              </template>
            </template>
          </Table>
        </TabPane>

        <TabPane key="2" tab="设备更换">
          <Table
            :columns="equipmentColumns"
            :data-source="equipmentReplacements"
            :scroll="{ x: 1000 }"
            row-key="id"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <Tag :color="getStatusTag(record.status).color">
                  {{ getStatusTag(record.status).text }}
                </Tag>
              </template>
              <template v-else-if="column.key === 'cost'">
                <span class="font-medium"
                  >¥{{ record.cost.toLocaleString() }}</span
                >
              </template>
            </template>
          </Table>
        </TabPane>
      </Tabs>
    </Card>
  </div>
</template>

<style scoped>
.ant-statistic-content {
  font-size: 18px;
  font-weight: bold;
}

.ant-progress-line {
  margin-bottom: 0;
}
</style>
