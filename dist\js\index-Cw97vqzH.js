import{_ as n}from"./bootstrap-DlHXJWd_.js";import{g as c,a}from"./index-N5LJhOjA.js";import"../jse/index-index-DYNcUVMZ.js";import"./colors-DiwvFbr5.js";import"./vnode-wTLMd7r4.js";import"./useMergedState-C62ndRnY.js";import"./Trigger-DqFxRNhn.js";import"./ResizeObserver.es-CDE7jhPe.js";import"./move-CmFQWh0R.js";import"./shallowequal-DYGI_FZo.js";import"./FormItemContext-DlTJXH8o.js";import"./statusUtils-1R4B2N1T.js";import"./index-CMWcCtKN.js";import"./slide-DSq39mcs.js";const{DatePicker:r,WeekPicker:t,MonthPicker:o,YearPicker:k,TimePicker:p,QuarterPicker:m,RangePicker:i}=c(a),R=n(r,{WeekPicker:t,MonthPicker:o,YearPicker:k,RangePicker:i,TimePicker:p,QuarterPicker:m,install:e=>(e.component(r.name,r),e.component(i.name,i),e.component(o.name,o),e.component(t.name,t),e.component(m.name,m),e)});export{o as MonthPicker,m as QuarterPicker,i as RangePicker,t as WeekPicker,R as default};
