{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-pc-ui@4.9.29_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/number-input/index.js", "../../../../../node_modules/.pnpm/vxe-pc-ui@4.9.29_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-number-input/index.js"], "sourcesContent": ["import { VxeUI } from '@vxe-ui/core';\nimport VxeNumberInputComponent from './src/number-input';\nimport { dynamicApp } from '../dynamics';\nexport const VxeNumberInput = Object.assign({}, VxeNumberInputComponent, {\n    install(app) {\n        app.component(VxeNumberInputComponent.name, VxeNumberInputComponent);\n    }\n});\ndynamicApp.use(VxeNumberInput);\nVxeUI.component(VxeNumberInputComponent);\nexport const NumberInput = VxeNumberInput;\nexport default VxeNumberInput;\n", "import VxeNumberInput from '../number-input';\nexport * from '../number-input';\nexport default VxeNumberInput;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAGO,IAAM,iBAAiB,OAAO,OAAO,CAAC,GAAG,sBAAyB;AAAA,EACrE,QAAQ,KAAK;AACT,QAAI,UAAU,qBAAwB,MAAM,oBAAuB;AAAA,EACvE;AACJ,CAAC;AACD,WAAW,IAAI,cAAc;AAC7B,MAAM,UAAU,oBAAuB;AAChC,IAAM,cAAc;AAC3B,IAAOA,wBAAQ;;;ACTf,IAAO,2BAAQC;", "names": ["number_input_default", "number_input_default"]}