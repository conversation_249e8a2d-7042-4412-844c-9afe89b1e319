import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:layout-dashboard',
      order: -1,
      title: '首页',
    },
    name: 'Dashboard',
    path: '/dashboard',
    children: [
      {
        name: 'Analytics',
        path: '/analytics',
        component: () => import('#/views/dashboard/analytics/index.vue'),
        meta: {
          affixTab: true,
          icon: 'lucide:area-chart',
          title: '分析页',
        },
      },
    ],
  },
  {
    meta: {
      icon: 'lucide:droplets',
      order: 1,
      title: '灌站管理',
    },
    name: 'IrrigationStation',
    path: '/irrigation',
    children: [
      {
        name: 'IrrigationOverview',
        path: '/overview',
        component: () => import('#/views/irrigation/overview/index.vue'),
        meta: {
          icon: 'lucide:monitor',
          title: '监控大屏',
        },
      },
      {
        name: 'IrrigationMap',
        path: '/map',
        component: () => import('#/views/irrigation/map/index.vue'),
        meta: {
          icon: 'lucide:map',
          title: '地图分布',
        },
      },
      {
        name: 'IrrigationDevice',
        path: '/device',
        component: () => import('#/views/irrigation/device/index.vue'),
        meta: {
          icon: 'lucide:settings',
          title: '设备管理',
        },
      },
      {
        name: 'IrrigationAlarm',
        path: '/alarm',
        component: () => import('#/views/irrigation/alarm/index.vue'),
        meta: {
          icon: 'lucide:alert-triangle',
          title: '故障报警',
        },
      },
      {
        name: 'IrrigationMobile',
        path: '/mobile',
        component: () => import('#/views/irrigation/mobile/index.vue'),
        meta: {
          icon: 'lucide:smartphone',
          title: '移动端操作',
        },
      },
      {
        name: 'IrrigationStatistics',
        path: '/statistics',
        component: () => import('#/views/irrigation/statistics/index.vue'),
        meta: {
          icon: 'lucide:bar-chart-3',
          title: '数据统计',
        },
      },
      {
        name: 'IrrigationMaintenance',
        path: '/maintenance',
        component: () => import('#/views/irrigation/maintenance/index.vue'),
        meta: {
          icon: 'lucide:wrench',
          title: '设备检修',
        },
      },
    ],
  },
];

export default routes;
