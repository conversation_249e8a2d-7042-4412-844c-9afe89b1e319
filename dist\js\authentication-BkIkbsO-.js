const o="欢迎回来",t="开箱即用的大型中后台管理系统",n="工程化、高性能、跨组件库的前端模版",c="登录成功",s="欢迎回来",e="请输入您的帐户信息以开始管理您的项目",i="快速选择账号",r="账号",a="密码",g="请输入用户名",l="请输入密码",d="请先完成验证",p="密码错误",u="记住账号",m="创建一个账号",T="创建账号",b="已经有账号了?",L="还没有账号?",w="注册",S="让您的应用程序管理变得简单而有趣",f="确认密码",h="两次输入的密码不一致",A="我同意",P="隐私政策",q="条款",y="请同意隐私政策和条款",v="去登录",E="使用 8 个或更多字符，混合字母、数字和符号",R="忘记密码?",k="输入您的电子邮件，我们将向您发送重置密码的连接",x="请输入邮箱",D="你输入的邮箱格式不正确",G="发送重置链接",Q="邮箱",U="请用手机扫描二维码登录",B="扫码后点击 '确认'，即可完成登录",C="扫码登录",H="微信登录",M="QQ登录",V="Github登录",j="Google登录",z="钉钉登录",F="请输入您的手机号码以开始管理您的项目",I="验证码",J="请输入{0}位验证码",K="手机号码",N="请输入手机号",O="手机号码格式错误",W="手机号登录",X="获取验证码",Y="{0}秒后重新获取",Z="其他登录方式",_="重新登录",$="您的登录状态已过期，请重新登录以继续。",oo={center:"居中",alignLeft:"居左",alignRight:"居右"},to={welcomeBack:o,pageTitle:t,pageDesc:n,loginSuccess:c,loginSuccessDesc:s,loginSubtitle:e,selectAccount:i,username:r,password:a,usernameTip:g,passwordTip:l,verifyRequiredTip:d,passwordErrorTip:p,rememberMe:u,createAnAccount:m,createAccount:T,alreadyHaveAccount:b,accountTip:L,signUp:w,signUpSubtitle:S,confirmPassword:f,confirmPasswordTip:h,agree:A,privacyPolicy:P,terms:q,agreeTip:y,goToLogin:v,passwordStrength:E,forgetPassword:R,forgetPasswordSubtitle:k,emailTip:x,emailValidErrorTip:D,sendResetLink:G,email:Q,qrcodeSubtitle:U,qrcodePrompt:B,qrcodeLogin:C,wechatLogin:H,qqLogin:M,githubLogin:V,googleLogin:j,dingdingLogin:z,codeSubtitle:F,code:I,codeTip:J,mobile:K,mobileTip:N,mobileErrortip:O,mobileLogin:W,sendCode:X,sendText:Y,thirdPartyLogin:Z,loginAgainTitle:_,loginAgainSubTitle:$,layout:oo};export{L as accountTip,A as agree,y as agreeTip,b as alreadyHaveAccount,I as code,F as codeSubtitle,J as codeTip,f as confirmPassword,h as confirmPasswordTip,T as createAccount,m as createAnAccount,to as default,z as dingdingLogin,Q as email,x as emailTip,D as emailValidErrorTip,R as forgetPassword,k as forgetPasswordSubtitle,V as githubLogin,v as goToLogin,j as googleLogin,oo as layout,$ as loginAgainSubTitle,_ as loginAgainTitle,e as loginSubtitle,c as loginSuccess,s as loginSuccessDesc,K as mobile,O as mobileErrortip,W as mobileLogin,N as mobileTip,n as pageDesc,t as pageTitle,a as password,p as passwordErrorTip,E as passwordStrength,l as passwordTip,P as privacyPolicy,M as qqLogin,C as qrcodeLogin,B as qrcodePrompt,U as qrcodeSubtitle,u as rememberMe,i as selectAccount,X as sendCode,G as sendResetLink,Y as sendText,w as signUp,S as signUpSubtitle,q as terms,Z as thirdPartyLogin,r as username,g as usernameTip,d as verifyRequiredTip,H as wechatLogin,o as welcomeBack};
