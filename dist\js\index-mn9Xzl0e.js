import{cm as n,cn as i,co as c,cp as e,cq as u,cr as a,cs as f,ct as s,cu as l}from"./bootstrap-CFDAkNgp.js";function t(o){return c(l(o))}n.useModal=i;n.info=function(r){return c(e(r))};n.success=function(r){return c(u(r))};n.error=function(r){return c(a(r))};n.warning=t;n.warn=t;n.confirm=function(r){return c(f(r))};n.destroyAll=function(){for(;s.length;){const r=s.pop();r&&r()}};n.install=function(o){return o.component(n.name,n),o};
