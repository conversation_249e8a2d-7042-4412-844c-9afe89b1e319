var re=(n,r,o)=>new Promise((f,s)=>{var u=T=>{try{p(o.next(T))}catch($){s($)}},m=T=>{try{p(o.throw(T))}catch($){s($)}},p=T=>T.done?f(T.value):Promise.resolve(T.value).then(u,m);p((o=o.apply(n,r)).next())});import{C as P}from"./index-DOXLVRHg.js";import{C as q,S as X,R as $e}from"./index-CHRdc_8y.js";import{g as we,m as ke,_ as K,r as _e,d as be,P as M,a as Ce,ch as Se,ci as Ie,cj as Re,ck as Be,aT as Pe,M as Le,cl as Ne,aU as Ae,j as Oe,a9 as Me,i as He,B as Fe,X as Ee,Y as ze,t as De,Z as je,b as ie,F as j,cm as de,ca as Ue,c9 as Ve,bu as We}from"./bootstrap-CFDAkNgp.js";import{a4 as fe,a5 as J,J as le,x as t,aA as Ge,P as H,T as ue,a9 as qe,av as F,ab as C,aa as U,aq as R,ac as a,a7 as l,ai as c,aj as S,aB as y}from"../jse/index-index-B2UBupFX.js";import{u as Xe,_ as Je}from"./use-echarts-Dor8gHz9.js";import{F as me,a as L}from"./index-BUPhRcAb.js";import Y from"./index-BCzosP6o.js";import I from"./index-CGqxGK2L.js";import ce from"./index-BE7dsRID.js";import{T as pe}from"./index-BhH5F5SY.js";import{B as ve}from"./index-DXEBJLLx.js";import"./index-mn9Xzl0e.js";import{T as Ye}from"./index-DEfsrzsO.js";import"./index-1BiZfdtR.js";import"./shallowequal-CNCY1mYq.js";import"./Trigger-D2zZP_An.js";import"./ResizeObserver.es-CDE7jhPe.js";import"./_arrayIncludes-B8uzE354.js";import"./Overflow--DaGju1E.js";import"./index-DQZjs6Lb.js";import"./index-tPQFuBU-.js";import"./colors-KzMfSzFw.js";import"./collapseMotion-DiwOar_A.js";import"./slide-BhgK1D9k.js";import"./useRefs-f0KzY-v7.js";import"./hasIn-Bt_d2Zq4.js";import"./isMobile-8sZ0LT6r.js";import"./useMergedState-C4x1IDb9.js";import"./isPlainObject-0t1li2J1.js";import"./Col-Bjak4A2I.js";import"./useFlexGapSupport-TvfJoknb.js";import"./debounce-CesRCMoz.js";import"./FormItemContext-CoieKSxA.js";import"./statusUtils-D62pPzYs.js";import"./BaseInput-Dslq5mxC.js";import"./index-C5ScQeGh.js";import"./SearchOutlined-DqQ4RgbY.js";import"./EyeOutlined-MQsW2UzL.js";import"./index-B2Lu6Z2W.js";import"./DownOutlined-CVWF16Fu.js";import"./CheckOutlined-CZeS3E7o.js";import"./move-IXaXzbNk.js";import"./index-DYfzhziO.js";import"./index-C5usYyko.js";import"./index-DfHX-m0D.js";import"./index-BDBY1qBK.js";import"./Checkbox-Dt0Z6J8a.js";import"./index-UTpExPeP.js";const Z=(n,r,o,f,s)=>({backgroundColor:n,border:`${f.lineWidth}px ${f.lineType} ${r}`,[`${s}-icon`]:{color:o}}),Ze=n=>{const{componentCls:r,motionDurationSlow:o,marginXS:f,marginSM:s,fontSize:u,fontSizeLG:m,lineHeight:p,borderRadiusLG:T,motionEaseInOutCirc:$,alertIconSizeLG:h,colorText:w,paddingContentVerticalSM:k,alertPaddingHorizontal:B,paddingMD:N,paddingContentHorizontalLG:A}=n;return{[r]:K(K({},_e(n)),{position:"relative",display:"flex",alignItems:"center",padding:`${k}px ${B}px`,wordWrap:"break-word",borderRadius:T,[`&${r}-rtl`]:{direction:"rtl"},[`${r}-content`]:{flex:1,minWidth:0},[`${r}-icon`]:{marginInlineEnd:f,lineHeight:0},"&-description":{display:"none",fontSize:u,lineHeight:p},"&-message":{color:w},[`&${r}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${o} ${$}, opacity ${o} ${$},
        padding-top ${o} ${$}, padding-bottom ${o} ${$},
        margin-bottom ${o} ${$}`},[`&${r}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${r}-with-description`]:{alignItems:"flex-start",paddingInline:A,paddingBlock:N,[`${r}-icon`]:{marginInlineEnd:s,fontSize:h,lineHeight:0},[`${r}-message`]:{display:"block",marginBottom:f,color:w,fontSize:m},[`${r}-description`]:{display:"block"}},[`${r}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},Ke=n=>{const{componentCls:r,colorSuccess:o,colorSuccessBorder:f,colorSuccessBg:s,colorWarning:u,colorWarningBorder:m,colorWarningBg:p,colorError:T,colorErrorBorder:$,colorErrorBg:h,colorInfo:w,colorInfoBorder:k,colorInfoBg:B}=n;return{[r]:{"&-success":Z(s,f,o,n,r),"&-info":Z(B,k,w,n,r),"&-warning":Z(p,m,u,n,r),"&-error":K(K({},Z(h,$,T,n,r)),{[`${r}-description > pre`]:{margin:0,padding:0}})}}},Qe=n=>{const{componentCls:r,iconCls:o,motionDurationMid:f,marginXS:s,fontSizeIcon:u,colorIcon:m,colorIconHover:p}=n;return{[r]:{"&-action":{marginInlineStart:s},[`${r}-close-icon`]:{marginInlineStart:s,padding:0,overflow:"hidden",fontSize:u,lineHeight:`${u}px`,backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${o}-close`]:{color:m,transition:`color ${f}`,"&:hover":{color:p}}},"&-close-text":{color:m,transition:`color ${f}`,"&:hover":{color:p}}}}},et=n=>[Ze(n),Ke(n),Qe(n)],tt=we("Alert",n=>{const{fontSizeHeading3:r}=n,o=ke(n,{alertIconSizeLG:r,alertPaddingHorizontal:12});return[et(o)]}),lt={success:Ae,info:Ne,error:Le,warning:Pe},at={success:Be,info:Re,error:Ie,warning:Se},ot=De("success","info","warning","error"),nt=()=>({type:M.oneOf(ot),closable:{type:Boolean,default:void 0},closeText:M.any,message:M.any,description:M.any,afterClose:Function,showIcon:{type:Boolean,default:void 0},prefixCls:String,banner:{type:Boolean,default:void 0},icon:M.any,closeIcon:M.any,onClose:Function}),st=fe({compatConfig:{MODE:3},name:"AAlert",inheritAttrs:!1,props:nt(),setup(n,r){let{slots:o,emit:f,attrs:s,expose:u}=r;const{prefixCls:m,direction:p}=Ce("alert",n),[T,$]=tt(m),h=J(!1),w=J(!1),k=J(),B=x=>{x.preventDefault();const _=k.value;_.style.height=`${_.offsetHeight}px`,_.style.height=`${_.offsetHeight}px`,h.value=!0,f("close",x)},N=()=>{var x;h.value=!1,w.value=!0,(x=n.afterClose)===null||x===void 0||x.call(n)},A=le(()=>{const{type:x}=n;return x!==void 0?x:n.banner?"warning":"info"});u({animationEnd:N});const Q=J({});return()=>{var x,_,O,E,z,d,e,i,v,b;const{banner:ae,closeIcon:oe=(x=o.closeIcon)===null||x===void 0?void 0:x.call(o)}=n;let{closable:ee,showIcon:D}=n;const te=(_=n.closeText)!==null&&_!==void 0?_:(O=o.closeText)===null||O===void 0?void 0:O.call(o),V=(E=n.description)!==null&&E!==void 0?E:(z=o.description)===null||z===void 0?void 0:z.call(o),ne=(d=n.message)!==null&&d!==void 0?d:(e=o.message)===null||e===void 0?void 0:e.call(o),W=(i=n.icon)!==null&&i!==void 0?i:(v=o.icon)===null||v===void 0?void 0:v.call(o),se=(b=o.action)===null||b===void 0?void 0:b.call(o);D=ae&&D===void 0?!0:D;const ge=(V?at:lt)[A.value]||null;te&&(ee=!0);const g=m.value,ye=Oe(g,{[`${g}-${A.value}`]:!0,[`${g}-closing`]:h.value,[`${g}-with-description`]:!!V,[`${g}-no-icon`]:!D,[`${g}-banner`]:!!ae,[`${g}-closable`]:ee,[`${g}-rtl`]:p.value==="rtl",[$.value]:!0}),he=ee?t("button",{type:"button",onClick:B,class:`${g}-close-icon`,tabindex:0},[te?t("span",{class:`${g}-close-text`},[te]):oe===void 0?t(Me,null,null):oe]):null,xe=W&&(He(W)?Fe(W,{class:`${g}-icon`}):t("span",{class:`${g}-icon`},[W]))||t(ge,{class:`${g}-icon`},null),Te=Ee(`${g}-motion`,{appear:!1,css:!0,onAfterLeave:N,onBeforeLeave:G=>{G.style.maxHeight=`${G.offsetHeight}px`},onLeave:G=>{G.style.maxHeight="0px"}});return T(w.value?null:t(ze,Te,{default:()=>[Ge(t("div",ie(ie({role:"alert"},s),{},{style:[s.style,Q.value],class:[s.class,ye],"data-show":!h.value,ref:k}),[D?xe:null,t("div",{class:`${g}-content`},[ne?t("div",{class:`${g}-message`},[ne]):null,V?t("div",{class:`${g}-description`},[V]):null]),se?t("div",{class:`${g}-action`},[se]):null,he]),[[je,!h.value]])]}))}}}),rt=be(st),it={class:"p-5"},dt={key:0},ut={class:"space-y-4"},mt={key:0},ct={key:1},pt={key:2},vt={key:3},ft=fe({name:"IrrigationAlarm",__name:"index",setup(n){const r=H(),o=H(!1),f=H(!1),s=H(null),u=ue({stationName:"",alarmType:"",status:"",dateRange:null}),m=ue({id:null,handleResult:"",handlePerson:"",handleTime:null,remark:""}),p=H([{id:1,stationName:"东风灌站",alarmType:"水泵故障",alarmLevel:"high",alarmTime:"2024-01-15 14:30:25",description:"主水泵电流异常，疑似轴承故障",status:"processing",handlePerson:"",handleTime:null,handleResult:"",remark:""},{id:2,stationName:"红旗灌站",alarmType:"电压异常",alarmLevel:"medium",alarmTime:"2024-01-15 13:45:12",description:"供电电压不稳定，波动范围超过正常值",status:"processing",handlePerson:"",handleTime:null,handleResult:"",remark:""},{id:3,stationName:"胜利灌站",alarmType:"流量异常",alarmLevel:"low",alarmTime:"2024-01-15 12:20:08",description:"出水流量低于设定值，可能存在管道堵塞",status:"resolved",handlePerson:"张三",handleTime:"2024-01-15 15:30:00",handleResult:"清理管道堵塞物，恢复正常流量",remark:"定期检查管道"},{id:4,stationName:"新华灌站",alarmType:"水位异常",alarmLevel:"high",alarmTime:"2024-01-15 11:15:30",description:"水位传感器显示异常低水位",status:"resolved",handlePerson:"李四",handleTime:"2024-01-15 14:20:00",handleResult:"检查水位传感器，重新校准",remark:"传感器需要定期校准"}]),T=[{title:"灌站名称",dataIndex:"stationName",key:"stationName",width:120},{title:"报警类型",dataIndex:"alarmType",key:"alarmType",width:120},{title:"报警等级",dataIndex:"alarmLevel",key:"alarmLevel",width:100},{title:"报警时间",dataIndex:"alarmTime",key:"alarmTime",width:150},{title:"状态",dataIndex:"status",key:"status",width:100},{title:"处理人",dataIndex:"handlePerson",key:"handlePerson",width:100},{title:"操作",key:"action",width:200,fixed:"right"}],$=le(()=>p.value.filter(d=>{const e=!u.stationName||d.stationName.includes(u.stationName),i=!u.alarmType||d.alarmType===u.alarmType,v=!u.status||d.status===u.status;return e&&i&&v})),h=le(()=>{const d=p.value.length,e=p.value.filter(b=>b.status==="processing").length,i=p.value.filter(b=>b.status==="resolved").length,v=p.value.filter(b=>b.alarmLevel==="high").length;return{total:d,processing:e,resolved:i,high:v,resolveRate:d>0?(i/d*100).toFixed(1):"0"}}),w=d=>{switch(d){case"high":return{color:"red",text:"高级"};case"medium":return{color:"orange",text:"中级"};case"low":return{color:"blue",text:"低级"};default:return{color:"default",text:"未知"}}},k=d=>{switch(d){case"processing":return{color:"processing",text:"处理中"};case"resolved":return{color:"success",text:"已解决"};case"ignored":return{color:"default",text:"已忽略"};default:return{color:"default",text:"未知"}}},B=()=>{Object.assign(u,{stationName:"",alarmType:"",status:"",dateRange:null})},N=d=>{s.value=d,f.value=!0},A=d=>{Object.assign(m,{id:d.id,handleResult:"",handlePerson:"",handleTime:null,remark:""}),o.value=!0},Q=()=>re(null,null,function*(){var d;try{yield(d=r.value)==null?void 0:d.validate();const e=p.value.findIndex(i=>i.id===m.id);e>-1&&(Object.assign(p.value[e],{status:"resolved",handlePerson:m.handlePerson,handleTime:new Date().toLocaleString(),handleResult:m.handleResult,remark:m.remark}),Ve.success("处理成功")),o.value=!1,x()}catch(e){console.error("表单验证失败:",e)}}),x=()=>{var d;Object.assign(m,{id:null,handleResult:"",handlePerson:"",handleTime:null,remark:""}),(d=r.value)==null||d.resetFields()},_=()=>{const d=["东风灌站","红旗灌站","胜利灌站","新华灌站","建设灌站"],e=["水泵故障","电压异常","流量异常","水位异常","温度异常"],i=["high","medium","low"],v={id:Date.now(),stationName:d[Math.floor(Math.random()*d.length)],alarmType:e[Math.floor(Math.random()*e.length)],alarmLevel:i[Math.floor(Math.random()*i.length)],alarmTime:new Date().toLocaleString(),description:"系统检测到异常，请及时处理",status:"processing",handlePerson:"",handleTime:null,handleResult:"",remark:""};p.value.unshift(v),Ue.warning({message:"新报警",description:`${v.stationName} 发生 ${v.alarmType}`,duration:5})},O=H(),{renderEcharts:E}=Xe(O);qe(()=>{E({title:{text:"7天报警趋势",left:"center"},tooltip:{trigger:"axis"},legend:{data:["高级报警","中级报警","低级报警"],top:30},xAxis:{type:"category",data:["1/9","1/10","1/11","1/12","1/13","1/14","1/15"]},yAxis:{type:"value",name:"报警数量"},series:[{name:"高级报警",type:"line",data:[2,3,1,4,2,3,2],itemStyle:{color:"#ff4d4f"}},{name:"中级报警",type:"line",data:[3,2,4,3,5,2,3],itemStyle:{color:"#faad14"}},{name:"低级报警",type:"line",data:[1,2,2,1,3,2,1],itemStyle:{color:"#1890ff"}}]}),setInterval(()=>{Math.random()>.7&&_()},3e4)});const z={handleResult:[{required:!0,message:"请输入处理结果"}],handlePerson:[{required:!0,message:"请输入处理人"}]};return(d,e)=>(C(),F("div",it,[t(l($e),{gutter:16,class:"mb-5"},{default:a(()=>[t(l(q),{span:6},{default:a(()=>[t(l(P),null,{default:a(()=>[t(l(X),{title:"总报警数",value:h.value.total,"value-style":{color:"#1890ff"}},null,8,["value"])]),_:1})]),_:1}),t(l(q),{span:6},{default:a(()=>[t(l(P),null,{default:a(()=>[t(l(X),{title:"处理中",value:h.value.processing,"value-style":{color:"#faad14"}},null,8,["value"])]),_:1})]),_:1}),t(l(q),{span:6},{default:a(()=>[t(l(P),null,{default:a(()=>[t(l(X),{title:"已解决",value:h.value.resolved,"value-style":{color:"#52c41a"}},null,8,["value"])]),_:1})]),_:1}),t(l(q),{span:6},{default:a(()=>[t(l(P),null,{default:a(()=>[t(l(X),{title:"解决率",value:h.value.resolveRate,suffix:"%","value-style":{color:"#722ed1"}},null,8,["value"])]),_:1})]),_:1})]),_:1}),h.value.processing>0?(C(),U(l(rt),{key:0,message:`当前有 ${h.value.processing} 个报警待处理`,type:"warning","show-icon":"",closable:"",class:"mb-5"},null,8,["message"])):R("",!0),t(l(P),{title:"报警趋势分析",class:"mb-5"},{default:a(()=>[t(l(Je),{ref_key:"chartRef",ref:O,style:{height:"300px"}},null,512)]),_:1}),t(l(P),{title:"报警查询",class:"mb-5"},{default:a(()=>[t(l(me),{layout:"inline",model:u},{default:a(()=>[t(l(L),{label:"灌站名称"},{default:a(()=>[t(l(Y),{value:u.stationName,"onUpdate:value":e[0]||(e[0]=i=>u.stationName=i),placeholder:"请输入灌站名称",style:{width:"200px"}},null,8,["value"])]),_:1}),t(l(L),{label:"报警类型"},{default:a(()=>[t(l(I),{value:u.alarmType,"onUpdate:value":e[1]||(e[1]=i=>u.alarmType=i),placeholder:"请选择报警类型",style:{width:"150px"},"allow-clear":""},{default:a(()=>[t(l(I).Option,{value:"水泵故障"},{default:a(()=>e[8]||(e[8]=[c("水泵故障")])),_:1,__:[8]}),t(l(I).Option,{value:"电压异常"},{default:a(()=>e[9]||(e[9]=[c("电压异常")])),_:1,__:[9]}),t(l(I).Option,{value:"流量异常"},{default:a(()=>e[10]||(e[10]=[c("流量异常")])),_:1,__:[10]}),t(l(I).Option,{value:"水位异常"},{default:a(()=>e[11]||(e[11]=[c("水位异常")])),_:1,__:[11]}),t(l(I).Option,{value:"温度异常"},{default:a(()=>e[12]||(e[12]=[c("温度异常")])),_:1,__:[12]})]),_:1},8,["value"])]),_:1}),t(l(L),{label:"处理状态"},{default:a(()=>[t(l(I),{value:u.status,"onUpdate:value":e[2]||(e[2]=i=>u.status=i),placeholder:"请选择状态",style:{width:"120px"},"allow-clear":""},{default:a(()=>[t(l(I).Option,{value:"processing"},{default:a(()=>e[13]||(e[13]=[c("处理中")])),_:1,__:[13]}),t(l(I).Option,{value:"resolved"},{default:a(()=>e[14]||(e[14]=[c("已解决")])),_:1,__:[14]}),t(l(I).Option,{value:"ignored"},{default:a(()=>e[15]||(e[15]=[c("已忽略")])),_:1,__:[15]})]),_:1},8,["value"])]),_:1}),t(l(L),null,{default:a(()=>[t(l(ce),null,{default:a(()=>[t(l(j),{type:"primary"},{default:a(()=>e[16]||(e[16]=[c("查询")])),_:1,__:[16]}),t(l(j),{onClick:B},{default:a(()=>e[17]||(e[17]=[c("重置")])),_:1,__:[17]}),t(l(j),{type:"dashed",onClick:_},{default:a(()=>e[18]||(e[18]=[c("模拟报警")])),_:1,__:[18]})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),t(l(P),{title:"报警列表"},{default:a(()=>[t(l(Ye),{columns:T,"data-source":$.value,scroll:{x:1e3},"row-key":"id"},{bodyCell:a(({column:i,record:v})=>[i.key==="alarmLevel"?(C(),U(l(pe),{key:0,color:w(v.alarmLevel).color},{default:a(()=>[c(S(w(v.alarmLevel).text),1)]),_:2},1032,["color"])):i.key==="status"?(C(),U(l(ve),{key:1,status:k(v.status).color,text:k(v.status).text},null,8,["status","text"])):i.key==="action"?(C(),U(l(ce),{key:2},{default:a(()=>[t(l(j),{type:"link",size:"small",onClick:b=>N(v)},{default:a(()=>e[19]||(e[19]=[c(" 详情 ")])),_:2,__:[19]},1032,["onClick"]),v.status==="processing"?(C(),U(l(j),{key:0,type:"link",size:"small",onClick:b=>A(v)},{default:a(()=>e[20]||(e[20]=[c(" 处理 ")])),_:2,__:[20]},1032,["onClick"])):R("",!0)]),_:2},1024)):R("",!0)]),_:1},8,["data-source"])]),_:1}),t(l(de),{open:f.value,"onUpdate:open":e[3]||(e[3]=i=>f.value=i),title:"报警详情",width:"600px",footer:null},{default:a(()=>[s.value?(C(),F("div",dt,[y("div",ut,[y("div",null,[e[21]||(e[21]=y("strong",null,"灌站名称：",-1)),c(S(s.value.stationName),1)]),y("div",null,[e[22]||(e[22]=y("strong",null,"报警类型：",-1)),c(S(s.value.alarmType),1)]),y("div",null,[e[23]||(e[23]=y("strong",null,"报警等级：",-1)),t(l(pe),{color:w(s.value.alarmLevel).color},{default:a(()=>[c(S(w(s.value.alarmLevel).text),1)]),_:1},8,["color"])]),y("div",null,[e[24]||(e[24]=y("strong",null,"报警时间：",-1)),c(S(s.value.alarmTime),1)]),y("div",null,[e[25]||(e[25]=y("strong",null,"报警描述：",-1)),c(S(s.value.description),1)]),y("div",null,[e[26]||(e[26]=y("strong",null,"处理状态：",-1)),t(l(ve),{status:k(s.value.status).color,text:k(s.value.status).text},null,8,["status","text"])]),s.value.handlePerson?(C(),F("div",mt,[e[27]||(e[27]=y("strong",null,"处理人：",-1)),c(S(s.value.handlePerson),1)])):R("",!0),s.value.handleTime?(C(),F("div",ct,[e[28]||(e[28]=y("strong",null,"处理时间：",-1)),c(S(s.value.handleTime),1)])):R("",!0),s.value.handleResult?(C(),F("div",pt,[e[29]||(e[29]=y("strong",null,"处理结果：",-1)),c(S(s.value.handleResult),1)])):R("",!0),s.value.remark?(C(),F("div",vt,[e[30]||(e[30]=y("strong",null,"备注：",-1)),c(S(s.value.remark),1)])):R("",!0)])])):R("",!0)]),_:1},8,["open"]),t(l(de),{open:o.value,"onUpdate:open":e[7]||(e[7]=i=>o.value=i),title:"处理报警",width:"600px",onOk:Q,onCancel:x},{default:a(()=>[t(l(me),{ref_key:"formRef",ref:r,model:m,rules:z,"label-col":{span:6},"wrapper-col":{span:18}},{default:a(()=>[t(l(L),{label:"处理结果",name:"handleResult"},{default:a(()=>[t(l(Y).TextArea,{value:m.handleResult,"onUpdate:value":e[4]||(e[4]=i=>m.handleResult=i),placeholder:"请输入处理结果",rows:3},null,8,["value"])]),_:1}),t(l(L),{label:"处理人",name:"handlePerson"},{default:a(()=>[t(l(Y),{value:m.handlePerson,"onUpdate:value":e[5]||(e[5]=i=>m.handlePerson=i),placeholder:"请输入处理人"},null,8,["value"])]),_:1}),t(l(L),{label:"备注"},{default:a(()=>[t(l(Y).TextArea,{value:m.remark,"onUpdate:value":e[6]||(e[6]=i=>m.remark=i),placeholder:"请输入备注信息",rows:2},null,8,["value"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open"])]))}}),ml=We(ft,[["__scopeId","data-v-4ae94e68"]]);export{ml as default};
