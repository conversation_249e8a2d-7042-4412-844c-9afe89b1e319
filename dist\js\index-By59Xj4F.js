import{P as ge,y as Xt,_ as w,Q as Yt,b as re,j as xe,n as Jt,a6 as Qt,o as Xe,a7 as Nn,a8 as Dn,F as ft,K as Se,I as nt,X as Pn,i as In,m as vt,r as $n,E as Tn,M as Kt,k as Ln,d as ot,s as lt,H as An,z as _n,g as Fn,Y as rt,a as Mn,q as Vn,h as jn,T as Bn,f as Hn}from"./bootstrap-DlHXJWd_.js";import{u as Rn}from"./move-CmFQWh0R.js";import{a5 as B,ao as Ke,a4 as Me,aF as ht,J as E,R as yt,x as _,T as Zt,q as zn,a9 as en,p as Wn,P as Fe,Y as ke,az as Un,aA as qn,n as pt,F as Gn,ax as Xn,r as Ee,al as Yn,_ as Ue,Z as Jn}from"../jse/index-index-DYNcUVMZ.js";import{L as Qn,j as Zn,u as ea,a as ta,k as na,t as Et,B as aa,e as oa,g as la,h as ra}from"./index-C_slJHcY.js";import{c as ia}from"./collapseMotion-DOIjdQkS.js";import{u as Ot}from"./useMergedState-C62ndRnY.js";import{u as sa,F as ca}from"./FormItemContext-DlTJXH8o.js";import{g as da,a as ua}from"./statusUtils-1R4B2N1T.js";import{g as fa}from"./index-DigXRWkq.js";import"./Trigger-DqFxRNhn.js";import"./vnode-wTLMd7r4.js";import"./ResizeObserver.es-CDE7jhPe.js";import"./BaseInput-IJRN1dIn.js";import"./Overflow-DVJLTYCG.js";import"./index-DHmCRrBp.js";import"./isMobile-8sZ0LT6r.js";import"./DownOutlined-DlRNa_nA.js";import"./CheckOutlined-BwvlgC3h.js";import"./SearchOutlined-B4DC4PFQ.js";import"./slide-DSq39mcs.js";function we(e){const t=B();return Ke(()=>{t.value=e()},{flush:"sync"}),t}const tn=Symbol("TreeContextKey"),va=Me({compatConfig:{MODE:3},name:"TreeContext",props:{value:{type:Object}},setup(e,t){let{slots:n}=t;return ht(tn,E(()=>e.value)),()=>{var a;return(a=n.default)===null||a===void 0?void 0:a.call(n)}}}),gt=()=>yt(tn,E(()=>({}))),nn=Symbol("KeysStateKey"),ha=e=>{ht(nn,e)},an=()=>yt(nn,{expandedKeys:B([]),selectedKeys:B([]),loadedKeys:B([]),loadingKeys:B([]),checkedKeys:B([]),halfCheckedKeys:B([]),expandedKeysSet:E(()=>new Set),selectedKeysSet:E(()=>new Set),loadedKeysSet:E(()=>new Set),loadingKeysSet:E(()=>new Set),checkedKeysSet:E(()=>new Set),halfCheckedKeysSet:E(()=>new Set),flattenNodes:B([])}),ya=e=>{let{prefixCls:t,level:n,isStart:a,isEnd:o}=e;const r=`${t}-indent-unit`,l=[];for(let c=0;c<n;c+=1)l.push(_("span",{key:c,class:{[r]:!0,[`${r}-start`]:a[c],[`${r}-end`]:o[c]}},null));return _("span",{"aria-hidden":"true",class:`${t}-indent`},[l])},on={eventKey:[String,Number],prefixCls:String,title:ge.any,data:{type:Object,default:void 0},parent:{type:Object,default:void 0},isStart:{type:Array},isEnd:{type:Array},active:{type:Boolean,default:void 0},onMousemove:{type:Function},isLeaf:{type:Boolean,default:void 0},checkable:{type:Boolean,default:void 0},selectable:{type:Boolean,default:void 0},disabled:{type:Boolean,default:void 0},disableCheckbox:{type:Boolean,default:void 0},icon:ge.any,switcherIcon:ge.any,domRef:{type:Function}},pa={prefixCls:{type:String},motion:{type:Object},focusable:{type:Boolean},activeItem:{type:Object},focused:{type:Boolean},tabindex:{type:Number},checkable:{type:Boolean},selectable:{type:Boolean},disabled:{type:Boolean},height:{type:Number},itemHeight:{type:Number},virtual:{type:Boolean},onScroll:{type:Function},onKeydown:{type:Function},onFocus:{type:Function},onBlur:{type:Function},onActiveChange:{type:Function},onContextmenu:{type:Function},onListChangeStart:{type:Function},onListChangeEnd:{type:Function}},ga=()=>({prefixCls:String,focusable:{type:Boolean,default:void 0},activeKey:[Number,String],tabindex:Number,children:ge.any,treeData:{type:Array},fieldNames:{type:Object},showLine:{type:[Boolean,Object],default:void 0},showIcon:{type:Boolean,default:void 0},icon:ge.any,selectable:{type:Boolean,default:void 0},expandAction:[String,Boolean],disabled:{type:Boolean,default:void 0},multiple:{type:Boolean,default:void 0},checkable:{type:Boolean,default:void 0},checkStrictly:{type:Boolean,default:void 0},draggable:{type:[Function,Boolean]},defaultExpandParent:{type:Boolean,default:void 0},autoExpandParent:{type:Boolean,default:void 0},defaultExpandAll:{type:Boolean,default:void 0},defaultExpandedKeys:{type:Array},expandedKeys:{type:Array},defaultCheckedKeys:{type:Array},checkedKeys:{type:[Object,Array]},defaultSelectedKeys:{type:Array},selectedKeys:{type:Array},allowDrop:{type:Function},dropIndicatorRender:{type:Function},onFocus:{type:Function},onBlur:{type:Function},onKeydown:{type:Function},onContextmenu:{type:Function},onClick:{type:Function},onDblclick:{type:Function},onScroll:{type:Function},onExpand:{type:Function},onCheck:{type:Function},onSelect:{type:Function},onLoad:{type:Function},loadData:{type:Function},loadedKeys:{type:Array},onMouseenter:{type:Function},onMouseleave:{type:Function},onRightClick:{type:Function},onDragstart:{type:Function},onDragenter:{type:Function},onDragover:{type:Function},onDragleave:{type:Function},onDragend:{type:Function},onDrop:{type:Function},onActiveChange:{type:Function},filterTreeNode:{type:Function},motion:ge.any,switcherIcon:ge.any,height:Number,itemHeight:Number,virtual:{type:Boolean,default:void 0},direction:{type:String},rootClassName:String,rootStyle:Object});var ma=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]]);return n};const Nt="open",Dt="close",ba="---",Pt=Me({compatConfig:{MODE:3},name:"ATreeNode",inheritAttrs:!1,props:on,isTreeNode:1,setup(e,t){let{attrs:n,slots:a,expose:o}=t;Xt(!("slots"in e.data),`treeData slots is deprecated, please use ${Object.keys(e.data.slots||{}).map(s=>"`v-slot:"+s+"` ")}instead`);const r=B(!1),l=gt(),{expandedKeysSet:c,selectedKeysSet:u,loadedKeysSet:i,loadingKeysSet:d,checkedKeysSet:y,halfCheckedKeysSet:m}=an(),{dragOverNodeKey:b,dropPosition:x,keyEntities:p}=l.value,k=E(()=>et(e.eventKey,{expandedKeysSet:c.value,selectedKeysSet:u.value,loadedKeysSet:i.value,loadingKeysSet:d.value,checkedKeysSet:y.value,halfCheckedKeysSet:m.value,dragOverNodeKey:b,dropPosition:x,keyEntities:p})),C=we(()=>k.value.expanded),$=we(()=>k.value.selected),P=we(()=>k.value.checked),T=we(()=>k.value.loaded),S=we(()=>k.value.loading),V=we(()=>k.value.halfChecked),H=we(()=>k.value.dragOver),L=we(()=>k.value.dragOverGapTop),F=we(()=>k.value.dragOverGapBottom),Z=we(()=>k.value.pos),J=B(),ae=E(()=>{const{eventKey:s}=e,{keyEntities:v}=l.value,{children:D}=v[s]||{};return!!(D||[]).length}),oe=E(()=>{const{isLeaf:s}=e,{loadData:v}=l.value,D=ae.value;return s===!1?!1:s||!v&&!D||v&&T.value&&!D}),fe=E(()=>oe.value?null:C.value?Nt:Dt),se=E(()=>{const{disabled:s}=e,{disabled:v}=l.value;return!!(v||s)}),pe=E(()=>{const{checkable:s}=e,{checkable:v}=l.value;return!v||s===!1?!1:v}),ce=E(()=>{const{selectable:s}=e,{selectable:v}=l.value;return typeof s=="boolean"?s:v}),Q=E(()=>{const{data:s,active:v,checkable:D,disableCheckbox:M,disabled:R,selectable:ee}=e;return w(w({active:v,checkable:D,disableCheckbox:M,disabled:R,selectable:ee},s),{dataRef:s,data:s,isLeaf:oe.value,checked:P.value,expanded:C.value,loading:S.value,selected:$.value,halfChecked:V.value})}),de=zn(),te=E(()=>{const{eventKey:s}=e,{keyEntities:v}=l.value,{parent:D}=v[s]||{};return w(w({},tt(w({},e,k.value))),{parent:D})}),le=Zt({eventData:te,eventKey:E(()=>e.eventKey),selectHandle:J,pos:Z,key:de.vnode.key});o(le);const ie=s=>{const{onNodeDoubleClick:v}=l.value;v(s,te.value)},ve=s=>{if(se.value)return;const{onNodeSelect:v}=l.value;s.preventDefault(),v(s,te.value)},W=s=>{if(se.value)return;const{disableCheckbox:v}=e,{onNodeCheck:D}=l.value;if(!pe.value||v)return;s.preventDefault();const M=!P.value;D(s,te.value,M)},ne=s=>{const{onNodeClick:v}=l.value;v(s,te.value),ce.value?ve(s):W(s)},ue=s=>{const{onNodeMouseEnter:v}=l.value;v(s,te.value)},me=s=>{const{onNodeMouseLeave:v}=l.value;v(s,te.value)},Ve=s=>{const{onNodeContextMenu:v}=l.value;v(s,te.value)},je=s=>{const{onNodeDragStart:v}=l.value;s.stopPropagation(),r.value=!0,v(s,le);try{s.dataTransfer.setData("text/plain","")}catch(D){}},Be=s=>{const{onNodeDragEnter:v}=l.value;s.preventDefault(),s.stopPropagation(),v(s,le)},He=s=>{const{onNodeDragOver:v}=l.value;s.preventDefault(),s.stopPropagation(),v(s,le)},De=s=>{const{onNodeDragLeave:v}=l.value;s.stopPropagation(),v(s,le)},Re=s=>{const{onNodeDragEnd:v}=l.value;s.stopPropagation(),r.value=!1,v(s,le)},Pe=s=>{const{onNodeDrop:v}=l.value;s.preventDefault(),s.stopPropagation(),r.value=!1,v(s,le)},Ie=s=>{const{onNodeExpand:v}=l.value;S.value||v(s,te.value)},$e=()=>{const{data:s}=e,{draggable:v}=l.value;return!!(v&&(!v.nodeDraggable||v.nodeDraggable(s)))},Te=()=>{const{draggable:s,prefixCls:v}=l.value;return s&&(s!=null&&s.icon)?_("span",{class:`${v}-draggable-icon`},[s.icon]):null},ze=()=>{var s,v,D;const{switcherIcon:M=a.switcherIcon||((s=l.value.slots)===null||s===void 0?void 0:s[(D=(v=e.data)===null||v===void 0?void 0:v.slots)===null||D===void 0?void 0:D.switcherIcon])}=e,{switcherIcon:R}=l.value,ee=M||R;return typeof ee=="function"?ee(Q.value):ee},Le=()=>{const{loadData:s,onNodeLoad:v}=l.value;S.value||s&&C.value&&!oe.value&&!ae.value&&!T.value&&v(te.value)};en(()=>{Le()}),Wn(()=>{Le()});const We=()=>{const{prefixCls:s}=l.value,v=ze();if(oe.value)return v!==!1?_("span",{class:xe(`${s}-switcher`,`${s}-switcher-noop`)},[v]):null;const D=xe(`${s}-switcher`,`${s}-switcher_${C.value?Nt:Dt}`);return v!==!1?_("span",{onClick:Ie,class:D},[v]):null},be=()=>{var s,v;const{disableCheckbox:D}=e,{prefixCls:M}=l.value,R=se.value;return pe.value?_("span",{class:xe(`${M}-checkbox`,P.value&&`${M}-checkbox-checked`,!P.value&&V.value&&`${M}-checkbox-indeterminate`,(R||D)&&`${M}-checkbox-disabled`),onClick:W},[(v=(s=l.value).customCheckable)===null||v===void 0?void 0:v.call(s)]):null},N=()=>{const{prefixCls:s}=l.value;return _("span",{class:xe(`${s}-iconEle`,`${s}-icon__${fe.value||"docu"}`,S.value&&`${s}-icon_loading`)},null)},j=()=>{const{disabled:s,eventKey:v}=e,{draggable:D,dropLevelOffset:M,dropPosition:R,prefixCls:ee,indent:f,dropIndicatorRender:h,dragOverNodeKey:g,direction:K}=l.value;return!s&&D!==!1&&g===v?h({dropPosition:R,dropLevelOffset:M,indent:f,prefixCls:ee,direction:K}):null},G=()=>{var s,v,D,M,R,ee;const{icon:f=a.icon,data:h}=e,g=a.title||((s=l.value.slots)===null||s===void 0?void 0:s[(D=(v=e.data)===null||v===void 0?void 0:v.slots)===null||D===void 0?void 0:D.title])||((M=l.value.slots)===null||M===void 0?void 0:M.title)||e.title,{prefixCls:K,showIcon:A,icon:I,loadData:O}=l.value,z=se.value,X=`${K}-node-content-wrapper`;let U;if(A){const he=f||((R=l.value.slots)===null||R===void 0?void 0:R[(ee=h==null?void 0:h.slots)===null||ee===void 0?void 0:ee.icon])||I;U=he?_("span",{class:xe(`${K}-iconEle`,`${K}-icon__customize`)},[typeof he=="function"?he(Q.value):he]):N()}else O&&S.value&&(U=N());let q;typeof g=="function"?q=g(Q.value):q=g,q=q===void 0?ba:q;const Y=_("span",{class:`${K}-title`},[q]);return _("span",{ref:J,title:typeof g=="string"?g:"",class:xe(`${X}`,`${X}-${fe.value||"normal"}`,!z&&($.value||r.value)&&`${K}-node-selected`),onMouseenter:ue,onMouseleave:me,onContextmenu:Ve,onClick:ne,onDblclick:ie},[U,Y,j()])};return()=>{const s=w(w({},e),n),{eventKey:v,isLeaf:D,isStart:M,isEnd:R,domRef:ee,active:f,data:h,onMousemove:g,selectable:K}=s,A=ma(s,["eventKey","isLeaf","isStart","isEnd","domRef","active","data","onMousemove","selectable"]),{prefixCls:I,filterTreeNode:O,keyEntities:z,dropContainerKey:X,dropTargetKey:U,draggingNodeKey:q}=l.value,Y=se.value,he=Yt(A,{aria:!0,data:!0}),{level:Oe}=z[v]||{},Ce=R[R.length-1],ye=$e(),Ae=!Y&&ye,Je=q===v,at=K!==void 0?{"aria-selected":!!K}:void 0;return _("div",re(re({ref:ee,class:xe(n.class,`${I}-treenode`,{[`${I}-treenode-disabled`]:Y,[`${I}-treenode-switcher-${C.value?"open":"close"}`]:!D,[`${I}-treenode-checkbox-checked`]:P.value,[`${I}-treenode-checkbox-indeterminate`]:V.value,[`${I}-treenode-selected`]:$.value,[`${I}-treenode-loading`]:S.value,[`${I}-treenode-active`]:f,[`${I}-treenode-leaf-last`]:Ce,[`${I}-treenode-draggable`]:Ae,dragging:Je,"drop-target":U===v,"drop-container":X===v,"drag-over":!Y&&H.value,"drag-over-gap-top":!Y&&L.value,"drag-over-gap-bottom":!Y&&F.value,"filter-node":O&&O(te.value)}),style:n.style,draggable:Ae,"aria-grabbed":Je,onDragstart:Ae?je:void 0,onDragenter:ye?Be:void 0,onDragover:ye?He:void 0,onDragleave:ye?De:void 0,onDrop:ye?Pe:void 0,onDragend:ye?Re:void 0,onMousemove:g},at),he),[_(ya,{prefixCls:I,level:Oe,isStart:M,isEnd:R},null),Te(),We(),be(),G()])}}});function Ne(e,t){if(!e)return[];const n=e.slice(),a=n.indexOf(t);return a>=0&&n.splice(a,1),n}function _e(e,t){const n=(e||[]).slice();return n.indexOf(t)===-1&&n.push(t),n}function mt(e){return e.split("-")}function ln(e,t){return`${e}-${t}`}function Sa(e){return e&&e.type&&e.type.isTreeNode}function Ca(e,t){const n=[],a=t[e];function o(){(arguments.length>0&&arguments[0]!==void 0?arguments[0]:[]).forEach(l=>{let{key:c,children:u}=l;n.push(c),o(u)})}return o(a.children),n}function ka(e){if(e.parent){const t=mt(e.pos);return Number(t[t.length-1])===e.parent.children.length-1}return!1}function wa(e){const t=mt(e.pos);return Number(t[t.length-1])===0}function It(e,t,n,a,o,r,l,c,u,i){var d;const{clientX:y,clientY:m}=e,{top:b,height:x}=e.target.getBoundingClientRect(),k=((i==="rtl"?-1:1)*(((o==null?void 0:o.x)||0)-y)-12)/a;let C=c[n.eventKey];if(m<b+x/2){const Z=l.findIndex(oe=>oe.key===C.key),J=Z<=0?0:Z-1,ae=l[J].key;C=c[ae]}const $=C.key,P=C,T=C.key;let S=0,V=0;if(!u.has($))for(let Z=0;Z<k&&ka(C);Z+=1)C=C.parent,V+=1;const H=t.eventData,L=C.node;let F=!0;return wa(C)&&C.level===0&&m<b+x/2&&r({dragNode:H,dropNode:L,dropPosition:-1})&&C.key===n.eventKey?S=-1:(P.children||[]).length&&u.has(T)?r({dragNode:H,dropNode:L,dropPosition:0})?S=0:F=!1:V===0?k>-1.5?r({dragNode:H,dropNode:L,dropPosition:1})?S=1:F=!1:r({dragNode:H,dropNode:L,dropPosition:0})?S=0:r({dragNode:H,dropNode:L,dropPosition:1})?S=1:F=!1:r({dragNode:H,dropNode:L,dropPosition:1})?S=1:F=!1,{dropPosition:S,dropLevelOffset:V,dropTargetKey:C.key,dropTargetPos:C.pos,dragOverNodeKey:T,dropContainerKey:S===0?null:((d=C.parent)===null||d===void 0?void 0:d.key)||null,dropAllowed:F}}function $t(e,t){if(!e)return;const{multiple:n}=t;return n?e.slice():e.length?[e[0]]:e}function it(e){if(!e)return null;let t;if(Array.isArray(e))t={checkedKeys:e,halfCheckedKeys:void 0};else if(typeof e=="object")t={checkedKeys:e.checked||void 0,halfCheckedKeys:e.halfChecked||void 0};else return null;return t}function Tt(e,t){const n=new Set;function a(o){if(n.has(o))return;const r=t[o];if(!r)return;n.add(o);const{parent:l,node:c}=r;c.disabled||l&&a(l.key)}return(e||[]).forEach(o=>{a(o)}),[...n]}var xa=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]]);return n};function Ze(e,t){return e!=null?e:t}function bt(e){const{title:t,_title:n,key:a,children:o}=e||{},r=t||"title";return{title:r,_title:n||[r],key:a||"key",children:o||"children"}}function Ka(e){function t(){let n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return Jt(n).map(o=>{var r,l,c,u;if(!Sa(o))return null;const i=o.children||{},d=o.key,y={};for(const[Z,J]of Object.entries(o.props))y[Qt(Z)]=J;const{isLeaf:m,checkable:b,selectable:x,disabled:p,disableCheckbox:k}=y,C={isLeaf:m||m===""||void 0,checkable:b||b===""||void 0,selectable:x||x===""||void 0,disabled:p||p===""||void 0,disableCheckbox:k||k===""||void 0},$=w(w({},y),C),{title:P=(r=i.title)===null||r===void 0?void 0:r.call(i,$),icon:T=(l=i.icon)===null||l===void 0?void 0:l.call(i,$),switcherIcon:S=(c=i.switcherIcon)===null||c===void 0?void 0:c.call(i,$)}=y,V=xa(y,["title","icon","switcherIcon"]),H=(u=i.default)===null||u===void 0?void 0:u.call(i),L=w(w(w({},V),{title:P,icon:T,switcherIcon:S,key:d,isLeaf:m}),C),F=t(H);return F.length&&(L.children=F),L})}return t(e)}function Ea(e,t,n){const{_title:a,key:o,children:r}=bt(n),l=new Set(t===!0?[]:t),c=[];function u(i){let d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return i.map((y,m)=>{const b=ln(d?d.pos:"0",m),x=Ze(y[o],b);let p;for(let C=0;C<a.length;C+=1){const $=a[C];if(y[$]!==void 0){p=y[$];break}}const k=w(w({},Xe(y,[...a,o,r])),{title:p,key:x,parent:d,pos:b,children:null,data:y,isStart:[...d?d.isStart:[],m===0],isEnd:[...d?d.isEnd:[],m===i.length-1]});return c.push(k),t===!0||l.has(x)?k.children=u(y[r]||[],k):k.children=[],k})}return u(e),c}function Oa(e,t,n){let a={};typeof n=="object"?a=n:a={externalGetKey:n},a=a||{};const{childrenPropName:o,externalGetKey:r,fieldNames:l}=a,{key:c,children:u}=bt(l),i=o||u;let d;r?typeof r=="string"?d=m=>m[r]:typeof r=="function"&&(d=m=>r(m)):d=(m,b)=>Ze(m[c],b);function y(m,b,x,p){const k=m?m[i]:e,C=m?ln(x.pos,b):"0",$=m?[...p,m]:[];if(m){const P=d(m,C),T={node:m,index:b,pos:C,key:P,parentPos:x.node?x.pos:null,level:x.level+1,nodes:$};t(T)}k&&k.forEach((P,T)=>{y(P,T,{node:m,pos:C,level:x?x.level+1:-1},$)})}y(null)}function rn(e){let{initWrapper:t,processEntity:n,onProcessFinished:a,externalGetKey:o,childrenPropName:r,fieldNames:l}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},c=arguments.length>2?arguments[2]:void 0;const u=o||c,i={},d={};let y={posEntities:i,keyEntities:d};return t&&(y=t(y)||y),Oa(e,m=>{const{node:b,index:x,pos:p,key:k,parentPos:C,level:$,nodes:P}=m,T={node:b,nodes:P,index:x,key:k,pos:p,level:$},S=Ze(k,p);i[p]=T,d[S]=T,T.parent=i[C],T.parent&&(T.parent.children=T.parent.children||[],T.parent.children.push(T)),n&&n(T,y)},{externalGetKey:u,childrenPropName:r,fieldNames:l}),a&&a(y),y}function et(e,t){let{expandedKeysSet:n,selectedKeysSet:a,loadedKeysSet:o,loadingKeysSet:r,checkedKeysSet:l,halfCheckedKeysSet:c,dragOverNodeKey:u,dropPosition:i,keyEntities:d}=t;const y=d[e];return{eventKey:e,expanded:n.has(e),selected:a.has(e),loaded:o.has(e),loading:r.has(e),checked:l.has(e),halfChecked:c.has(e),pos:String(y?y.pos:""),parent:y.parent,dragOver:u===e&&i===0,dragOverGapTop:u===e&&i===-1,dragOverGapBottom:u===e&&i===1}}function tt(e){const{data:t,expanded:n,selected:a,checked:o,loaded:r,loading:l,halfChecked:c,dragOver:u,dragOverGapTop:i,dragOverGapBottom:d,pos:y,active:m,eventKey:b}=e,x=w(w({dataRef:t},t),{expanded:n,selected:a,checked:o,loaded:r,loading:l,halfChecked:c,dragOver:u,dragOverGapTop:i,dragOverGapBottom:d,pos:y,active:m,eventKey:b,key:b});return"props"in x||Object.defineProperty(x,"props",{get(){return e}}),x}function sn(e,t){const n=new Set;return e.forEach(a=>{t.has(a)||n.add(a)}),n}function Na(e){const{disabled:t,disableCheckbox:n,checkable:a}=e||{};return!!(t||n)||a===!1}function Da(e,t,n,a){const o=new Set(e),r=new Set;for(let c=0;c<=n;c+=1)(t.get(c)||new Set).forEach(i=>{const{key:d,node:y,children:m=[]}=i;o.has(d)&&!a(y)&&m.filter(b=>!a(b.node)).forEach(b=>{o.add(b.key)})});const l=new Set;for(let c=n;c>=0;c-=1)(t.get(c)||new Set).forEach(i=>{const{parent:d,node:y}=i;if(a(y)||!i.parent||l.has(i.parent.key))return;if(a(i.parent.node)){l.add(d.key);return}let m=!0,b=!1;(d.children||[]).filter(x=>!a(x.node)).forEach(x=>{let{key:p}=x;const k=o.has(p);m&&!k&&(m=!1),!b&&(k||r.has(p))&&(b=!0)}),m&&o.add(d.key),b&&r.add(d.key),l.add(d.key)});return{checkedKeys:Array.from(o),halfCheckedKeys:Array.from(sn(r,o))}}function Pa(e,t,n,a,o){const r=new Set(e);let l=new Set(t);for(let u=0;u<=a;u+=1)(n.get(u)||new Set).forEach(d=>{const{key:y,node:m,children:b=[]}=d;!r.has(y)&&!l.has(y)&&!o(m)&&b.filter(x=>!o(x.node)).forEach(x=>{r.delete(x.key)})});l=new Set;const c=new Set;for(let u=a;u>=0;u-=1)(n.get(u)||new Set).forEach(d=>{const{parent:y,node:m}=d;if(o(m)||!d.parent||c.has(d.parent.key))return;if(o(d.parent.node)){c.add(y.key);return}let b=!0,x=!1;(y.children||[]).filter(p=>!o(p.node)).forEach(p=>{let{key:k}=p;const C=r.has(k);b&&!C&&(b=!1),!x&&(C||l.has(k))&&(x=!0)}),b||r.delete(y.key),x&&l.add(y.key),c.add(y.key)});return{checkedKeys:Array.from(r),halfCheckedKeys:Array.from(sn(l,r))}}function Ge(e,t,n,a,o,r){let l;l=Na;const c=new Set(e.filter(i=>!!n[i]));let u;return t===!0?u=Da(c,o,a,l):u=Pa(c,t.halfCheckedKeys,o,a,l),u}function cn(e){const t=Fe(0),n=B();return Ke(()=>{const a=new Map;let o=0;const r=e.value||{};for(const l in r)if(Object.prototype.hasOwnProperty.call(r,l)){const c=r[l],{level:u}=c;let i=a.get(u);i||(i=new Set,a.set(u,i)),i.add(c),o=Math.max(o,u)}t.value=o,n.value=a}),{maxLevel:t,levelEntities:n}}var Lt=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]]);return n};const Ia=Me({compatConfig:{MODE:3},name:"MotionTreeNode",inheritAttrs:!1,props:w(w({},on),{active:Boolean,motion:Object,motionNodes:{type:Array},onMotionStart:Function,onMotionEnd:Function,motionType:String}),setup(e,t){let{attrs:n,slots:a}=t;const o=B(!0),r=gt(),l=B(!1),c=E(()=>e.motion?e.motion:ia()),u=(i,d)=>{var y,m,b,x;d==="appear"?(m=(y=c.value)===null||y===void 0?void 0:y.onAfterEnter)===null||m===void 0||m.call(y,i):d==="leave"&&((x=(b=c.value)===null||b===void 0?void 0:b.onAfterLeave)===null||x===void 0||x.call(b,i)),l.value||e.onMotionEnd(),l.value=!0};return ke(()=>e.motionNodes,()=>{e.motionNodes&&e.motionType==="hide"&&o.value&&pt(()=>{o.value=!1})},{immediate:!0,flush:"post"}),en(()=>{e.motionNodes&&e.onMotionStart()}),Un(()=>{e.motionNodes&&u()}),()=>{const{motion:i,motionNodes:d,motionType:y,active:m,eventKey:b}=e,x=Lt(e,["motion","motionNodes","motionType","active","eventKey"]);return d?_(Nn,re(re({},c.value),{},{appear:y==="show",onAfterAppear:p=>u(p,"appear"),onAfterLeave:p=>u(p,"leave")}),{default:()=>[qn(_("div",{class:`${r.value.prefixCls}-treenode-motion`},[d.map(p=>{const k=Lt(p.data,[]),{title:C,key:$,isStart:P,isEnd:T}=p;return delete k.children,_(Pt,re(re({},k),{},{title:C,active:m,data:p.data,key:$,eventKey:$,isStart:P,isEnd:T}),a)})]),[[Dn,o.value]])]}):_(Pt,re(re({class:n.class,style:n.style},x),{},{active:m,eventKey:b}),a)}}});function $a(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];const n=e.length,a=t.length;if(Math.abs(n-a)!==1)return{add:!1,key:null};function o(r,l){const c=new Map;r.forEach(i=>{c.set(i,!0)});const u=l.filter(i=>!c.has(i));return u.length===1?u[0]:null}return n<a?{add:!0,key:o(e,t)}:{add:!1,key:o(t,e)}}function At(e,t,n){const a=e.findIndex(l=>l.key===n),o=e[a+1],r=t.findIndex(l=>l.key===n);if(o){const l=t.findIndex(c=>c.key===o.key);return t.slice(r+1,l)}return t.slice(r+1)}var _t=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]]);return n};const Ft={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0},Ta=()=>{},qe=`RC_TREE_MOTION_${Math.random()}`,ct={key:qe},dn={key:qe,level:0,index:0,pos:"0",node:ct,nodes:[ct]},Mt={parent:null,children:[],pos:dn.pos,data:ct,title:null,key:qe,isStart:[],isEnd:[]};function Vt(e,t,n,a){return t===!1||!n?e:e.slice(0,Math.ceil(n/a)+1)}function jt(e){const{key:t,pos:n}=e;return Ze(t,n)}function La(e){let t=String(e.key),n=e;for(;n.parent;)n=n.parent,t=`${n.key} > ${t}`;return t}const Aa=Me({compatConfig:{MODE:3},name:"NodeList",inheritAttrs:!1,props:pa,setup(e,t){let{expose:n,attrs:a}=t;const o=Fe(),r=Fe(),{expandedKeys:l,flattenNodes:c}=an();n({scrollTo:p=>{o.value.scrollTo(p)},getIndentWidth:()=>r.value.offsetWidth});const u=B(c.value),i=B([]),d=Fe(null);function y(){u.value=c.value,i.value=[],d.value=null,e.onListChangeEnd()}const m=gt();ke([()=>l.value.slice(),c],(p,k)=>{let[C,$]=p,[P,T]=k;const S=$a(P,C);if(S.key!==null){const{virtual:V,height:H,itemHeight:L}=e;if(S.add){const F=T.findIndex(ae=>{let{key:oe}=ae;return oe===S.key}),Z=Vt(At(T,$,S.key),V,H,L),J=T.slice();J.splice(F+1,0,Mt),u.value=J,i.value=Z,d.value="show"}else{const F=$.findIndex(ae=>{let{key:oe}=ae;return oe===S.key}),Z=Vt(At($,T,S.key),V,H,L),J=$.slice();J.splice(F+1,0,Mt),u.value=J,i.value=Z,d.value="hide"}}else T!==$&&(u.value=$)}),ke(()=>m.value.dragging,p=>{p||y()});const b=E(()=>e.motion===void 0?u.value:c.value),x=()=>{e.onActiveChange(null)};return()=>{const p=w(w({},e),a),{prefixCls:k,selectable:C,checkable:$,disabled:P,motion:T,height:S,itemHeight:V,virtual:H,focusable:L,activeItem:F,focused:Z,tabindex:J,onKeydown:ae,onFocus:oe,onBlur:fe,onListChangeStart:se,onListChangeEnd:pe}=p,ce=_t(p,["prefixCls","selectable","checkable","disabled","motion","height","itemHeight","virtual","focusable","activeItem","focused","tabindex","onKeydown","onFocus","onBlur","onListChangeStart","onListChangeEnd"]);return _(Gn,null,[Z&&F&&_("span",{style:Ft,"aria-live":"assertive"},[La(F)]),_("div",null,[_("input",{style:Ft,disabled:L===!1||P,tabindex:L!==!1?J:null,onKeydown:ae,onFocus:oe,onBlur:fe,value:"",onChange:Ta,"aria-label":"for screen reader"},null)]),_("div",{class:`${k}-treenode`,"aria-hidden":!0,style:{position:"absolute",pointerEvents:"none",visibility:"hidden",height:0,overflow:"hidden"}},[_("div",{class:`${k}-indent`},[_("div",{ref:r,class:`${k}-indent-unit`},null)])]),_(Qn,re(re({},Xe(ce,["onActiveChange"])),{},{data:b.value,itemKey:jt,height:S,fullHeight:!1,virtual:H,itemHeight:V,prefixCls:`${k}-list`,ref:o,onVisibleChange:(Q,de)=>{const te=new Set(Q);de.filter(ie=>!te.has(ie)).some(ie=>jt(ie)===qe)&&y()}}),{default:Q=>{const{pos:de}=Q,te=_t(Q.data,[]),{title:le,key:ie,isStart:ve,isEnd:W}=Q,ne=Ze(ie,de);return delete te.key,delete te.children,_(Ia,re(re({},te),{},{eventKey:ne,title:le,active:!!F&&ie===F.key,data:Q.data,isStart:ve,isEnd:W,motion:T,motionNodes:ie===qe?i.value:null,motionType:d.value,onMotionStart:se,onMotionEnd:y,onMousemove:x}),null)}})])}}});function _a(e){let{dropPosition:t,dropLevelOffset:n,indent:a}=e;const o={pointerEvents:"none",position:"absolute",right:0,backgroundColor:"red",height:"2px"};switch(t){case-1:o.top=0,o.left=`${-n*a}px`;break;case 1:o.bottom=0,o.left=`${-n*a}px`;break;case 0:o.bottom=0,o.left=`${a}`;break}return _("div",{style:o},null)}const Fa=10,Ma=Me({compatConfig:{MODE:3},name:"Tree",inheritAttrs:!1,props:ft(ga(),{prefixCls:"vc-tree",showLine:!1,showIcon:!0,selectable:!0,multiple:!1,checkable:!1,disabled:!1,checkStrictly:!1,draggable:!1,expandAction:!1,defaultExpandParent:!0,autoExpandParent:!1,defaultExpandAll:!1,defaultExpandedKeys:[],defaultCheckedKeys:[],defaultSelectedKeys:[],dropIndicatorRender:_a,allowDrop:()=>!0}),setup(e,t){let{attrs:n,slots:a,expose:o}=t;const r=B(!1);let l={};const c=B(),u=B([]),i=B([]),d=B([]),y=B([]),m=B([]),b=B([]),x={},p=Zt({draggingNodeKey:null,dragChildrenKeys:[],dropTargetKey:null,dropPosition:null,dropContainerKey:null,dropLevelOffset:null,dropTargetPos:null,dropAllowed:!0,dragOverNodeKey:null}),k=B([]);ke([()=>e.treeData,()=>e.children],()=>{k.value=e.treeData!==void 0?e.treeData.slice():Ka(Ee(e.children))},{immediate:!0,deep:!0});const C=B({}),$=B(!1),P=B(null),T=B(!1),S=E(()=>bt(e.fieldNames)),V=B();let H=null,L=null,F=null;const Z=E(()=>({expandedKeysSet:J.value,selectedKeysSet:ae.value,loadedKeysSet:oe.value,loadingKeysSet:fe.value,checkedKeysSet:se.value,halfCheckedKeysSet:pe.value,dragOverNodeKey:p.dragOverNodeKey,dropPosition:p.dropPosition,keyEntities:C.value})),J=E(()=>new Set(b.value)),ae=E(()=>new Set(u.value)),oe=E(()=>new Set(y.value)),fe=E(()=>new Set(m.value)),se=E(()=>new Set(i.value)),pe=E(()=>new Set(d.value));Ke(()=>{if(k.value){const f=rn(k.value,{fieldNames:S.value});C.value=w({[qe]:dn},f.keyEntities)}});let ce=!1;ke([()=>e.expandedKeys,()=>e.autoExpandParent,C],(f,h)=>{let[g,K]=f,[A,I]=h,O=b.value;if(e.expandedKeys!==void 0||ce&&K!==I)O=e.autoExpandParent||!ce&&e.defaultExpandParent?Tt(e.expandedKeys,C.value):e.expandedKeys;else if(!ce&&e.defaultExpandAll){const z=w({},C.value);delete z[qe],O=Object.keys(z).map(X=>z[X].key)}else!ce&&e.defaultExpandedKeys&&(O=e.autoExpandParent||e.defaultExpandParent?Tt(e.defaultExpandedKeys,C.value):e.defaultExpandedKeys);O&&(b.value=O),ce=!0},{immediate:!0});const Q=B([]);Ke(()=>{Q.value=Ea(k.value,b.value,S.value)}),Ke(()=>{e.selectable&&(e.selectedKeys!==void 0?u.value=$t(e.selectedKeys,e):!ce&&e.defaultSelectedKeys&&(u.value=$t(e.defaultSelectedKeys,e)))});const{maxLevel:de,levelEntities:te}=cn(C);Ke(()=>{if(e.checkable){let f;if(e.checkedKeys!==void 0?f=it(e.checkedKeys)||{}:!ce&&e.defaultCheckedKeys?f=it(e.defaultCheckedKeys)||{}:k.value&&(f=it(e.checkedKeys)||{checkedKeys:i.value,halfCheckedKeys:d.value}),f){let{checkedKeys:h=[],halfCheckedKeys:g=[]}=f;e.checkStrictly||({checkedKeys:h,halfCheckedKeys:g}=Ge(h,!0,C.value,de.value,te.value)),i.value=h,d.value=g}}}),Ke(()=>{e.loadedKeys&&(y.value=e.loadedKeys)});const le=()=>{w(p,{dragOverNodeKey:null,dropPosition:null,dropLevelOffset:null,dropTargetKey:null,dropContainerKey:null,dropTargetPos:null,dropAllowed:!1})},ie=f=>{V.value.scrollTo(f)};ke(()=>e.activeKey,()=>{e.activeKey!==void 0&&(P.value=e.activeKey)},{immediate:!0}),ke(P,f=>{pt(()=>{f!==null&&ie({key:f})})},{immediate:!0,flush:"post"});const ve=f=>{e.expandedKeys===void 0&&(b.value=f)},W=()=>{p.draggingNodeKey!==null&&w(p,{draggingNodeKey:null,dropPosition:null,dropContainerKey:null,dropTargetKey:null,dropLevelOffset:null,dropAllowed:!0,dragOverNodeKey:null}),H=null,F=null},ne=(f,h)=>{const{onDragend:g}=e;p.dragOverNodeKey=null,W(),g==null||g({event:f,node:h.eventData}),L=null},ue=f=>{ne(f,null),window.removeEventListener("dragend",ue)},me=(f,h)=>{const{onDragstart:g}=e,{eventKey:K,eventData:A}=h;L=h,H={x:f.clientX,y:f.clientY};const I=Ne(b.value,K);p.draggingNodeKey=K,p.dragChildrenKeys=Ca(K,C.value),c.value=V.value.getIndentWidth(),ve(I),window.addEventListener("dragend",ue),g&&g({event:f,node:A})},Ve=(f,h)=>{const{onDragenter:g,onExpand:K,allowDrop:A,direction:I}=e,{pos:O,eventKey:z}=h;if(F!==z&&(F=z),!L){le();return}const{dropPosition:X,dropLevelOffset:U,dropTargetKey:q,dropContainerKey:Y,dropTargetPos:he,dropAllowed:Oe,dragOverNodeKey:Ce}=It(f,L,h,c.value,H,A,Q.value,C.value,J.value,I);if(p.dragChildrenKeys.indexOf(q)!==-1||!Oe){le();return}if(l||(l={}),Object.keys(l).forEach(ye=>{clearTimeout(l[ye])}),L.eventKey!==h.eventKey&&(l[O]=window.setTimeout(()=>{if(p.draggingNodeKey===null)return;let ye=b.value.slice();const Ae=C.value[h.eventKey];Ae&&(Ae.children||[]).length&&(ye=_e(b.value,h.eventKey)),ve(ye),K&&K(ye,{node:h.eventData,expanded:!0,nativeEvent:f})},800)),L.eventKey===q&&U===0){le();return}w(p,{dragOverNodeKey:Ce,dropPosition:X,dropLevelOffset:U,dropTargetKey:q,dropContainerKey:Y,dropTargetPos:he,dropAllowed:Oe}),g&&g({event:f,node:h.eventData,expandedKeys:b.value})},je=(f,h)=>{const{onDragover:g,allowDrop:K,direction:A}=e;if(!L)return;const{dropPosition:I,dropLevelOffset:O,dropTargetKey:z,dropContainerKey:X,dropAllowed:U,dropTargetPos:q,dragOverNodeKey:Y}=It(f,L,h,c.value,H,K,Q.value,C.value,J.value,A);p.dragChildrenKeys.indexOf(z)!==-1||!U||(L.eventKey===z&&O===0?p.dropPosition===null&&p.dropLevelOffset===null&&p.dropTargetKey===null&&p.dropContainerKey===null&&p.dropTargetPos===null&&p.dropAllowed===!1&&p.dragOverNodeKey===null||le():I===p.dropPosition&&O===p.dropLevelOffset&&z===p.dropTargetKey&&X===p.dropContainerKey&&q===p.dropTargetPos&&U===p.dropAllowed&&Y===p.dragOverNodeKey||w(p,{dropPosition:I,dropLevelOffset:O,dropTargetKey:z,dropContainerKey:X,dropTargetPos:q,dropAllowed:U,dragOverNodeKey:Y}),g&&g({event:f,node:h.eventData}))},Be=(f,h)=>{F===h.eventKey&&!f.currentTarget.contains(f.relatedTarget)&&(le(),F=null);const{onDragleave:g}=e;g&&g({event:f,node:h.eventData})},He=function(f,h){let g=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;var K;const{dragChildrenKeys:A,dropPosition:I,dropTargetKey:O,dropTargetPos:z,dropAllowed:X}=p;if(!X)return;const{onDrop:U}=e;if(p.dragOverNodeKey=null,W(),O===null)return;const q=w(w({},et(O,Ee(Z.value))),{active:((K=D.value)===null||K===void 0?void 0:K.key)===O,data:C.value[O].node});A.indexOf(O);const Y=mt(z),he={event:f,node:tt(q),dragNode:L?L.eventData:null,dragNodesKeys:[L.eventKey].concat(A),dropToGap:I!==0,dropPosition:I+Number(Y[Y.length-1])};g||U==null||U(he),L=null},De=(f,h)=>{const{expanded:g,key:K}=h,A=Q.value.filter(O=>O.key===K)[0],I=tt(w(w({},et(K,Z.value)),{data:A.data}));ve(g?Ne(b.value,K):_e(b.value,K)),j(f,I)},Re=(f,h)=>{const{onClick:g,expandAction:K}=e;K==="click"&&De(f,h),g&&g(f,h)},Pe=(f,h)=>{const{onDblclick:g,expandAction:K}=e;(K==="doubleclick"||K==="dblclick")&&De(f,h),g&&g(f,h)},Ie=(f,h)=>{let g=u.value;const{onSelect:K,multiple:A}=e,{selected:I}=h,O=h[S.value.key],z=!I;z?A?g=_e(g,O):g=[O]:g=Ne(g,O);const X=C.value,U=g.map(q=>{const Y=X[q];return Y?Y.node:null}).filter(q=>q);e.selectedKeys===void 0&&(u.value=g),K&&K(g,{event:"select",selected:z,node:h,selectedNodes:U,nativeEvent:f})},$e=(f,h,g)=>{const{checkStrictly:K,onCheck:A}=e,I=h[S.value.key];let O;const z={event:"check",node:h,checked:g,nativeEvent:f},X=C.value;if(K){const U=g?_e(i.value,I):Ne(i.value,I),q=Ne(d.value,I);O={checked:U,halfChecked:q},z.checkedNodes=U.map(Y=>X[Y]).filter(Y=>Y).map(Y=>Y.node),e.checkedKeys===void 0&&(i.value=U)}else{let{checkedKeys:U,halfCheckedKeys:q}=Ge([...i.value,I],!0,X,de.value,te.value);if(!g){const Y=new Set(U);Y.delete(I),{checkedKeys:U,halfCheckedKeys:q}=Ge(Array.from(Y),{halfCheckedKeys:q},X,de.value,te.value)}O=U,z.checkedNodes=[],z.checkedNodesPositions=[],z.halfCheckedKeys=q,U.forEach(Y=>{const he=X[Y];if(!he)return;const{node:Oe,pos:Ce}=he;z.checkedNodes.push(Oe),z.checkedNodesPositions.push({node:Oe,pos:Ce})}),e.checkedKeys===void 0&&(i.value=U,d.value=q)}A&&A(O,z)},Te=f=>{const h=f[S.value.key],g=new Promise((K,A)=>{const{loadData:I,onLoad:O}=e;if(!I||oe.value.has(h)||fe.value.has(h))return null;I(f).then(()=>{const X=_e(y.value,h),U=Ne(m.value,h);O&&O(X,{event:"load",node:f}),e.loadedKeys===void 0&&(y.value=X),m.value=U,K()}).catch(X=>{const U=Ne(m.value,h);if(m.value=U,x[h]=(x[h]||0)+1,x[h]>=Fa){const q=_e(y.value,h);e.loadedKeys===void 0&&(y.value=q),K()}A(X)}),m.value=_e(m.value,h)});return g.catch(()=>{}),g},ze=(f,h)=>{const{onMouseenter:g}=e;g&&g({event:f,node:h})},Le=(f,h)=>{const{onMouseleave:g}=e;g&&g({event:f,node:h})},We=(f,h)=>{const{onRightClick:g}=e;g&&(f.preventDefault(),g({event:f,node:h}))},be=f=>{const{onFocus:h}=e;$.value=!0,h&&h(f)},N=f=>{const{onBlur:h}=e;$.value=!1,v(null),h&&h(f)},j=(f,h)=>{let g=b.value;const{onExpand:K,loadData:A}=e,{expanded:I}=h,O=h[S.value.key];if(T.value)return;g.indexOf(O);const z=!I;if(z?g=_e(g,O):g=Ne(g,O),ve(g),K&&K(g,{node:h,expanded:z,nativeEvent:f}),z&&A){const X=Te(h);X&&X.then(()=>{}).catch(U=>{const q=Ne(b.value,O);ve(q),Promise.reject(U)})}},G=()=>{T.value=!0},s=()=>{setTimeout(()=>{T.value=!1})},v=f=>{const{onActiveChange:h}=e;P.value!==f&&(e.activeKey!==void 0&&(P.value=f),f!==null&&ie({key:f}),h&&h(f))},D=E(()=>P.value===null?null:Q.value.find(f=>{let{key:h}=f;return h===P.value})||null),M=f=>{let h=Q.value.findIndex(K=>{let{key:A}=K;return A===P.value});h===-1&&f<0&&(h=Q.value.length),h=(h+f+Q.value.length)%Q.value.length;const g=Q.value[h];if(g){const{key:K}=g;v(K)}else v(null)},R=E(()=>tt(w(w({},et(P.value,Z.value)),{data:D.value.data,active:!0}))),ee=f=>{const{onKeydown:h,checkable:g,selectable:K}=e;switch(f.which){case Se.UP:{M(-1),f.preventDefault();break}case Se.DOWN:{M(1),f.preventDefault();break}}const A=D.value;if(A&&A.data){const I=A.data.isLeaf===!1||!!(A.data.children||[]).length,O=R.value;switch(f.which){case Se.LEFT:{I&&J.value.has(P.value)?j({},O):A.parent&&v(A.parent.key),f.preventDefault();break}case Se.RIGHT:{I&&!J.value.has(P.value)?j({},O):A.children&&A.children.length&&v(A.children[0].key),f.preventDefault();break}case Se.ENTER:case Se.SPACE:{g&&!O.disabled&&O.checkable!==!1&&!O.disableCheckbox?$e({},O,!se.value.has(P.value)):!g&&K&&!O.disabled&&O.selectable!==!1&&Ie({},O);break}}}h&&h(f)};return o({onNodeExpand:j,scrollTo:ie,onKeydown:ee,selectedKeys:E(()=>u.value),checkedKeys:E(()=>i.value),halfCheckedKeys:E(()=>d.value),loadedKeys:E(()=>y.value),loadingKeys:E(()=>m.value),expandedKeys:E(()=>b.value)}),Xn(()=>{window.removeEventListener("dragend",ue),r.value=!0}),ha({expandedKeys:b,selectedKeys:u,loadedKeys:y,loadingKeys:m,checkedKeys:i,halfCheckedKeys:d,expandedKeysSet:J,selectedKeysSet:ae,loadedKeysSet:oe,loadingKeysSet:fe,checkedKeysSet:se,halfCheckedKeysSet:pe,flattenNodes:Q}),()=>{const{draggingNodeKey:f,dropLevelOffset:h,dropContainerKey:g,dropTargetKey:K,dropPosition:A,dragOverNodeKey:I}=p,{prefixCls:O,showLine:z,focusable:X,tabindex:U=0,selectable:q,showIcon:Y,icon:he=a.icon,switcherIcon:Oe,draggable:Ce,checkable:ye,checkStrictly:Ae,disabled:Je,motion:at,loadData:hn,filterTreeNode:yn,height:pn,itemHeight:gn,virtual:mn,dropIndicatorRender:bn,onContextmenu:Sn,onScroll:Cn,direction:kn,rootClassName:wn,rootStyle:xn}=e,{class:Kn,style:En}=n,On=Yt(w(w({},e),n),{aria:!0,data:!0});let Qe;return Ce?typeof Ce=="object"?Qe=Ce:typeof Ce=="function"?Qe={nodeDraggable:Ce}:Qe={}:Qe=!1,_(va,{value:{prefixCls:O,selectable:q,showIcon:Y,icon:he,switcherIcon:Oe,draggable:Qe,draggingNodeKey:f,checkable:ye,customCheckable:a.checkable,checkStrictly:Ae,disabled:Je,keyEntities:C.value,dropLevelOffset:h,dropContainerKey:g,dropTargetKey:K,dropPosition:A,dragOverNodeKey:I,dragging:f!==null,indent:c.value,direction:kn,dropIndicatorRender:bn,loadData:hn,filterTreeNode:yn,onNodeClick:Re,onNodeDoubleClick:Pe,onNodeExpand:j,onNodeSelect:Ie,onNodeCheck:$e,onNodeLoad:Te,onNodeMouseEnter:ze,onNodeMouseLeave:Le,onNodeContextMenu:We,onNodeDragStart:me,onNodeDragEnter:Ve,onNodeDragOver:je,onNodeDragLeave:Be,onNodeDragEnd:ne,onNodeDrop:He,slots:a}},{default:()=>[_("div",{role:"tree",class:xe(O,Kn,wn,{[`${O}-show-line`]:z,[`${O}-focused`]:$.value,[`${O}-active-focused`]:P.value!==null}),style:xn},[_(Aa,re({ref:V,prefixCls:O,style:En,disabled:Je,selectable:q,checkable:!!ye,motion:at,height:pn,itemHeight:gn,virtual:mn,focusable:X,focused:$.value,tabindex:U,activeItem:D.value,onFocus:be,onBlur:N,onKeydown:ee,onActiveChange:v,onListChangeStart:G,onListChangeEnd:s,onContextmenu:Sn,onScroll:Cn},On),null)])]})}}});var Va={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file",theme:"outlined"};function Bt(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},a=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(o){return Object.getOwnPropertyDescriptor(n,o).enumerable}))),a.forEach(function(o){ja(e,o,n[o])})}return e}function ja(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var St=function(t,n){var a=Bt({},t,n.attrs);return _(nt,Bt({},a,{icon:Va}),null)};St.displayName="FileOutlined";St.inheritAttrs=!1;var Ba={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h368c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"minus-square",theme:"outlined"};function Ht(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},a=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(o){return Object.getOwnPropertyDescriptor(n,o).enumerable}))),a.forEach(function(o){Ha(e,o,n[o])})}return e}function Ha(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Ct=function(t,n){var a=Ht({},t,n.attrs);return _(nt,Ht({},a,{icon:Ba}),null)};Ct.displayName="MinusSquareOutlined";Ct.inheritAttrs=!1;var Ra={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"plus-square",theme:"outlined"};function Rt(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},a=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(o){return Object.getOwnPropertyDescriptor(n,o).enumerable}))),a.forEach(function(o){za(e,o,n[o])})}return e}function za(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var kt=function(t,n){var a=Rt({},t,n.attrs);return _(nt,Rt({},a,{icon:Ra}),null)};kt.displayName="PlusSquareOutlined";kt.inheritAttrs=!1;var Wa={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"filled"};function zt(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},a=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(o){return Object.getOwnPropertyDescriptor(n,o).enumerable}))),a.forEach(function(o){Ua(e,o,n[o])})}return e}function Ua(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var wt=function(t,n){var a=zt({},t,n.attrs);return _(nt,zt({},a,{icon:Wa}),null)};wt.displayName="CaretDownFilled";wt.inheritAttrs=!1;function qa(e,t,n,a,o){const{isLeaf:r,expanded:l,loading:c}=n;let u=t;if(c)return _(Pn,{class:`${e}-switcher-loading-icon`},null);let i;o&&typeof o=="object"&&(i=o.showLeafIcon);let d=null;const y=`${e}-switcher-icon`;return r?o?i&&a?a(n):(typeof o=="object"&&!i?d=_("span",{class:`${e}-switcher-leaf-line`},null):d=_(St,{class:`${e}-switcher-line-icon`},null),d):null:(d=_(wt,{class:y},null),o&&(d=l?_(Ct,{class:`${e}-switcher-line-icon`},null):_(kt,{class:`${e}-switcher-line-icon`},null)),typeof t=="function"?u=t(w(w({},n),{defaultIcon:d,switcherCls:y})):In(u)&&(u=Yn(u,{class:y})),u||d)}const Ga=new Tn("ant-tree-node-fx-do-not-use",{"0%":{opacity:0},"100%":{opacity:1}}),Xa=(e,t)=>({[`.${e}-switcher-icon`]:{display:"inline-block",fontSize:10,verticalAlign:"baseline",svg:{transition:`transform ${t.motionDurationSlow}`}}}),Ya=(e,t)=>({[`.${e}-drop-indicator`]:{position:"absolute",zIndex:1,height:2,backgroundColor:t.colorPrimary,borderRadius:1,pointerEvents:"none","&:after":{position:"absolute",top:-3,insetInlineStart:-6,width:8,height:8,backgroundColor:"transparent",border:`${t.lineWidthBold}px solid ${t.colorPrimary}`,borderRadius:"50%",content:'""'}}}),Ja=(e,t)=>{const{treeCls:n,treeNodeCls:a,treeNodePadding:o,treeTitleHeight:r}=t,l=(r-t.fontSizeLG)/2,c=t.paddingXS;return{[n]:w(w({},$n(t)),{background:t.colorBgContainer,borderRadius:t.borderRadius,transition:`background-color ${t.motionDurationSlow}`,[`&${n}-rtl`]:{[`${n}-switcher`]:{"&_close":{[`${n}-switcher-icon`]:{svg:{transform:"rotate(90deg)"}}}}},[`&-focused:not(:hover):not(${n}-active-focused)`]:w({},Kt(t)),[`${n}-list-holder-inner`]:{alignItems:"flex-start"},[`&${n}-block-node`]:{[`${n}-list-holder-inner`]:{alignItems:"stretch",[`${n}-node-content-wrapper`]:{flex:"auto"},[`${a}.dragging`]:{position:"relative","&:after":{position:"absolute",top:0,insetInlineEnd:0,bottom:o,insetInlineStart:0,border:`1px solid ${t.colorPrimary}`,opacity:0,animationName:Ga,animationDuration:t.motionDurationSlow,animationPlayState:"running",animationFillMode:"forwards",content:'""',pointerEvents:"none"}}}},[`${a}`]:{display:"flex",alignItems:"flex-start",padding:`0 0 ${o}px 0`,outline:"none","&-rtl":{direction:"rtl"},"&-disabled":{[`${n}-node-content-wrapper`]:{color:t.colorTextDisabled,cursor:"not-allowed","&:hover":{background:"transparent"}}},[`&-active ${n}-node-content-wrapper`]:w({},Kt(t)),[`&:not(${a}-disabled).filter-node ${n}-title`]:{color:"inherit",fontWeight:500},"&-draggable":{[`${n}-draggable-icon`]:{width:r,lineHeight:`${r}px`,textAlign:"center",visibility:"visible",opacity:.2,transition:`opacity ${t.motionDurationSlow}`,[`${a}:hover &`]:{opacity:.45}},[`&${a}-disabled`]:{[`${n}-draggable-icon`]:{visibility:"hidden"}}}},[`${n}-indent`]:{alignSelf:"stretch",whiteSpace:"nowrap",userSelect:"none","&-unit":{display:"inline-block",width:r}},[`${n}-draggable-icon`]:{visibility:"hidden"},[`${n}-switcher`]:w(w({},Xa(e,t)),{position:"relative",flex:"none",alignSelf:"stretch",width:r,margin:0,lineHeight:`${r}px`,textAlign:"center",cursor:"pointer",userSelect:"none","&-noop":{cursor:"default"},"&_close":{[`${n}-switcher-icon`]:{svg:{transform:"rotate(-90deg)"}}},"&-loading-icon":{color:t.colorPrimary},"&-leaf-line":{position:"relative",zIndex:1,display:"inline-block",width:"100%",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:r/2,bottom:-o,marginInlineStart:-1,borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&:after":{position:"absolute",width:r/2*.8,height:r/2,borderBottom:`1px solid ${t.colorBorder}`,content:'""'}}}),[`${n}-checkbox`]:{top:"initial",marginInlineEnd:c,marginBlockStart:l},[`${n}-node-content-wrapper, ${n}-checkbox + span`]:{position:"relative",zIndex:"auto",minHeight:r,margin:0,padding:`0 ${t.paddingXS/2}px`,color:"inherit",lineHeight:`${r}px`,background:"transparent",borderRadius:t.borderRadius,cursor:"pointer",transition:`all ${t.motionDurationMid}, border 0s, line-height 0s, box-shadow 0s`,"&:hover":{backgroundColor:t.controlItemBgHover},[`&${n}-node-selected`]:{backgroundColor:t.controlItemBgActive},[`${n}-iconEle`]:{display:"inline-block",width:r,height:r,lineHeight:`${r}px`,textAlign:"center",verticalAlign:"top","&:empty":{display:"none"}}},[`${n}-unselectable ${n}-node-content-wrapper:hover`]:{backgroundColor:"transparent"},[`${n}-node-content-wrapper`]:w({lineHeight:`${r}px`,userSelect:"none"},Ya(e,t)),[`${a}.drop-container`]:{"> [draggable]":{boxShadow:`0 0 0 2px ${t.colorPrimary}`}},"&-show-line":{[`${n}-indent`]:{"&-unit":{position:"relative",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:r/2,bottom:-o,borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&-end":{"&:before":{display:"none"}}}},[`${n}-switcher`]:{background:"transparent","&-line-icon":{verticalAlign:"-0.15em"}}},[`${a}-leaf-last`]:{[`${n}-switcher`]:{"&-leaf-line":{"&:before":{top:"auto !important",bottom:"auto !important",height:`${r/2}px !important`}}}}})}},Qa=e=>{const{treeCls:t,treeNodeCls:n,treeNodePadding:a}=e;return{[`${t}${t}-directory`]:{[n]:{position:"relative","&:before":{position:"absolute",top:0,insetInlineEnd:0,bottom:a,insetInlineStart:0,transition:`background-color ${e.motionDurationMid}`,content:'""',pointerEvents:"none"},"&:hover":{"&:before":{background:e.controlItemBgHover}},"> *":{zIndex:1},[`${t}-switcher`]:{transition:`color ${e.motionDurationMid}`},[`${t}-node-content-wrapper`]:{borderRadius:0,userSelect:"none","&:hover":{background:"transparent"},[`&${t}-node-selected`]:{color:e.colorTextLightSolid,background:"transparent"}},"&-selected":{"\n            &:hover::before,\n            &::before\n          ":{background:e.colorPrimary},[`${t}-switcher`]:{color:e.colorTextLightSolid},[`${t}-node-content-wrapper`]:{color:e.colorTextLightSolid,background:"transparent"}}}}}},Za=(e,t)=>{const n=`.${e}`,a=`${n}-treenode`,o=t.paddingXS/2,r=t.controlHeightSM,l=vt(t,{treeCls:n,treeNodeCls:a,treeNodePadding:o,treeTitleHeight:r});return[Ja(e,l),Qa(l)]};function eo(e){return Array.isArray(e)?e:e!==void 0?[e]:[]}function to(e){const{label:t,value:n,children:a}=e||{},o=n||"value";return{_title:t?[t]:["title","label"],value:o,key:o,children:a||"children"}}function dt(e){return e.disabled||e.disableCheckbox||e.checkable===!1}function no(e,t){const n=[];function a(o){o.forEach(r=>{n.push(r[t.value]);const l=r[t.children];l&&a(l)})}return a(e),n}function Wt(e){return e==null}const un=Symbol("TreeSelectContextPropsKey");function ao(e){return ht(un,e)}function oo(){return yt(un,{})}const lo={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0},ro=Me({compatConfig:{MODE:3},name:"OptionList",inheritAttrs:!1,setup(e,t){let{slots:n,expose:a}=t;const o=ea(),r=Zn(),l=oo(),c=Fe(),u=Rn(()=>l.treeData,[()=>o.open,()=>l.treeData],S=>S[0]),i=E(()=>{const{checkable:S,halfCheckedKeys:V,checkedKeys:H}=r;return S?{checked:H,halfChecked:V}:null});ke(()=>o.open,()=>{pt(()=>{var S;o.open&&!o.multiple&&r.checkedKeys.length&&((S=c.value)===null||S===void 0||S.scrollTo({key:r.checkedKeys[0]}))})},{immediate:!0,flush:"post"});const d=E(()=>String(o.searchValue).toLowerCase()),y=S=>d.value?String(S[r.treeNodeFilterProp]).toLowerCase().includes(d.value):!1,m=B(r.treeDefaultExpandedKeys),b=B(null);ke(()=>o.searchValue,()=>{o.searchValue&&(b.value=no(Ee(l.treeData),Ee(l.fieldNames)))},{immediate:!0});const x=E(()=>r.treeExpandedKeys?r.treeExpandedKeys.slice():o.searchValue?b.value:m.value),p=S=>{var V;m.value=S,b.value=S,(V=r.onTreeExpand)===null||V===void 0||V.call(r,S)},k=S=>{S.preventDefault()},C=(S,V)=>{let{node:H}=V;var L,F;const{checkable:Z,checkedKeys:J}=r;Z&&dt(H)||((L=l.onSelect)===null||L===void 0||L.call(l,H.key,{selected:!J.includes(H.key)}),o.multiple||(F=o.toggleOpen)===null||F===void 0||F.call(o,!1))},$=Fe(null),P=E(()=>r.keyEntities[$.value]),T=S=>{$.value=S};return a({scrollTo:function(){for(var S,V,H=arguments.length,L=new Array(H),F=0;F<H;F++)L[F]=arguments[F];return(V=(S=c.value)===null||S===void 0?void 0:S.scrollTo)===null||V===void 0?void 0:V.call(S,...L)},onKeydown:S=>{var V;const{which:H}=S;switch(H){case Se.UP:case Se.DOWN:case Se.LEFT:case Se.RIGHT:(V=c.value)===null||V===void 0||V.onKeydown(S);break;case Se.ENTER:{if(P.value){const{selectable:L,value:F}=P.value.node||{};L!==!1&&C(null,{node:{key:$.value},selected:!r.checkedKeys.includes(F)})}break}case Se.ESC:o.toggleOpen(!1)}},onKeyup:()=>{}}),()=>{var S;const{prefixCls:V,multiple:H,searchValue:L,open:F,notFoundContent:Z=(S=n.notFoundContent)===null||S===void 0?void 0:S.call(n)}=o,{listHeight:J,listItemHeight:ae,virtual:oe,dropdownMatchSelectWidth:fe,treeExpandAction:se}=l,{checkable:pe,treeDefaultExpandAll:ce,treeIcon:Q,showTreeIcon:de,switcherIcon:te,treeLine:le,loadData:ie,treeLoadedKeys:ve,treeMotion:W,onTreeLoad:ne,checkedKeys:ue}=r;if(u.value.length===0)return _("div",{role:"listbox",class:`${V}-empty`,onMousedown:k},[Z]);const me={fieldNames:l.fieldNames};return ve&&(me.loadedKeys=ve),x.value&&(me.expandedKeys=x.value),_("div",{onMousedown:k},[P.value&&F&&_("span",{style:lo,"aria-live":"assertive"},[P.value.node.value]),_(Ma,re(re({ref:c,focusable:!1,prefixCls:`${V}-tree`,treeData:u.value,height:J,itemHeight:ae,virtual:oe!==!1&&fe!==!1,multiple:H,icon:Q,showIcon:de,switcherIcon:te,showLine:le,loadData:L?null:ie,motion:W,activeKey:$.value,checkable:pe,checkStrictly:!0,checkedKeys:i.value,selectedKeys:pe?[]:ue,defaultExpandAll:ce},me),{},{onActiveChange:T,onSelect:C,onCheck:C,onExpand:p,onLoad:ne,filterTreeNode:y,expandAction:se}),w(w({},n),{checkable:r.customSlots.treeCheckable}))])}}}),io="SHOW_ALL",fn="SHOW_PARENT",xt="SHOW_CHILD";function Ut(e,t,n,a){const o=new Set(e);return t===xt?e.filter(r=>{const l=n[r];return!(l&&l.children&&l.children.some(c=>{let{node:u}=c;return o.has(u[a.value])})&&l.children.every(c=>{let{node:u}=c;return dt(u)||o.has(u[a.value])}))}):t===fn?e.filter(r=>{const l=n[r],c=l?l.parent:null;return!(c&&!dt(c.node)&&o.has(c.key))}):e}const Ye=()=>null;Ye.inheritAttrs=!1;Ye.displayName="ATreeSelectNode";Ye.isTreeSelectNode=!0;var so=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]]);return n};function co(e){return e&&e.type&&e.type.isTreeSelectNode}function uo(e){function t(){let n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return Jt(n).map(a=>{var o,r,l;if(!co(a))return null;const c=a.children||{},u=a.key,i={};for(const[H,L]of Object.entries(a.props))i[Qt(H)]=L;const{isLeaf:d,checkable:y,selectable:m,disabled:b,disableCheckbox:x}=i,p={isLeaf:d||d===""||void 0,checkable:y||y===""||void 0,selectable:m||m===""||void 0,disabled:b||b===""||void 0,disableCheckbox:x||x===""||void 0},k=w(w({},i),p),{title:C=(o=c.title)===null||o===void 0?void 0:o.call(c,k),switcherIcon:$=(r=c.switcherIcon)===null||r===void 0?void 0:r.call(c,k)}=i,P=so(i,["title","switcherIcon"]),T=(l=c.default)===null||l===void 0?void 0:l.call(c),S=w(w(w({},P),{title:C,switcherIcon:$,key:u,isLeaf:d}),p),V=t(T);return V.length&&(S.children=V),S})}return t(e)}function ut(e){if(!e)return e;const t=w({},e);return"props"in t||Object.defineProperty(t,"props",{get(){return t}}),t}function fo(e,t,n,a,o,r){let l=null,c=null;function u(){function i(d){let y=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"0",m=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;return d.map((b,x)=>{const p=`${y}-${x}`,k=b[r.value],C=n.includes(k),$=i(b[r.children]||[],p,C),P=_(Ye,b,{default:()=>[$.map(T=>T.node)]});if(t===k&&(l=P),C){const T={pos:p,node:P,children:$};return m||c.push(T),T}return null}).filter(b=>b)}c||(c=[],i(a),c.sort((d,y)=>{let{node:{props:{value:m}}}=d,{node:{props:{value:b}}}=y;const x=n.indexOf(m),p=n.indexOf(b);return x-p}))}Object.defineProperty(e,"triggerNode",{get(){return u(),l}}),Object.defineProperty(e,"allCheckedNodes",{get(){return u(),o?c:c.map(i=>{let{node:d}=i;return d})}})}function vo(e,t){let{id:n,pId:a,rootPId:o}=t;const r={},l=[];return e.map(u=>{const i=w({},u),d=i[n];return r[d]=i,i.key=i.key||d,i}).forEach(u=>{const i=u[a],d=r[i];d&&(d.children=d.children||[],d.children.push(u)),(i===o||!d&&o===null)&&l.push(u)}),l}function ho(e,t,n){const a=B();return ke([n,e,t],()=>{const o=n.value;e.value?a.value=n.value?vo(Ee(e.value),w({id:"id",pId:"pId",rootPId:null},o!==!0?o:{})):Ee(e.value).slice():a.value=uo(Ee(t.value))},{immediate:!0,deep:!0}),a}const yo=e=>{const t=B({valueLabels:new Map}),n=B();return ke(e,()=>{n.value=Ee(e.value)},{immediate:!0}),[E(()=>{const{valueLabels:o}=t.value,r=new Map,l=n.value.map(c=>{var u;const{value:i}=c,d=(u=c.label)!==null&&u!==void 0?u:o.get(i);return r.set(i,d),w(w({},c),{label:d})});return t.value.valueLabels=r,l})]},po=(e,t)=>{const n=B(new Map),a=B({});return Ke(()=>{const o=t.value,r=rn(e.value,{fieldNames:o,initWrapper:l=>w(w({},l),{valueEntities:new Map}),processEntity:(l,c)=>{const u=l.node[o.value];c.valueEntities.set(u,l)}});n.value=r.valueEntities,a.value=r.keyEntities}),{valueEntities:n,keyEntities:a}},go=(e,t,n,a,o,r)=>{const l=B([]),c=B([]);return Ke(()=>{let u=e.value.map(y=>{let{value:m}=y;return m}),i=t.value.map(y=>{let{value:m}=y;return m});const d=u.filter(y=>!a.value[y]);n.value&&({checkedKeys:u,halfCheckedKeys:i}=Ge(u,!0,a.value,o.value,r.value)),l.value=Array.from(new Set([...d,...u])),c.value=i}),[l,c]},mo=(e,t,n)=>{let{treeNodeFilterProp:a,filterTreeNode:o,fieldNames:r}=n;return E(()=>{const{children:l}=r.value,c=t.value,u=a==null?void 0:a.value;if(!c||o.value===!1)return e.value;let i;if(typeof o.value=="function")i=o.value;else{const y=c.toUpperCase();i=(m,b)=>{const x=b[u];return String(x).toUpperCase().includes(y)}}function d(y){let m=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;const b=[];for(let x=0,p=y.length;x<p;x++){const k=y[x],C=k[l],$=m||i(c,ut(k)),P=d(C||[],$);($||P.length)&&b.push(w(w({},k),{[l]:P}))}return b}return d(e.value)})};function vn(){return w(w({},Xe(oa(),["mode"])),{prefixCls:String,id:String,value:{type:[String,Number,Object,Array]},defaultValue:{type:[String,Number,Object,Array]},onChange:{type:Function},searchValue:String,inputValue:String,onSearch:{type:Function},autoClearSearchValue:{type:Boolean,default:void 0},filterTreeNode:{type:[Boolean,Function],default:void 0},treeNodeFilterProp:String,onSelect:Function,onDeselect:Function,showCheckedStrategy:{type:String},treeNodeLabelProp:String,fieldNames:{type:Object},multiple:{type:Boolean,default:void 0},treeCheckable:{type:Boolean,default:void 0},treeCheckStrictly:{type:Boolean,default:void 0},labelInValue:{type:Boolean,default:void 0},treeData:{type:Array},treeDataSimpleMode:{type:[Boolean,Object],default:void 0},loadData:{type:Function},treeLoadedKeys:{type:Array},onTreeLoad:{type:Function},treeDefaultExpandAll:{type:Boolean,default:void 0},treeExpandedKeys:{type:Array},treeDefaultExpandedKeys:{type:Array},onTreeExpand:{type:Function},virtual:{type:Boolean,default:void 0},listHeight:Number,listItemHeight:Number,onDropdownVisibleChange:{type:Function},treeLine:{type:[Boolean,Object],default:void 0},treeIcon:ge.any,showTreeIcon:{type:Boolean,default:void 0},switcherIcon:ge.any,treeMotion:ge.any,children:Array,treeExpandAction:String,showArrow:{type:Boolean,default:void 0},showSearch:{type:Boolean,default:void 0},open:{type:Boolean,default:void 0},defaultOpen:{type:Boolean,default:void 0},disabled:{type:Boolean,default:void 0},placeholder:ge.any,maxTagPlaceholder:{type:Function},dropdownPopupAlign:ge.any,customSlots:Object})}function bo(e){return!e||typeof e!="object"}const So=Me({compatConfig:{MODE:3},name:"TreeSelect",inheritAttrs:!1,props:ft(vn(),{treeNodeFilterProp:"value",autoClearSearchValue:!0,showCheckedStrategy:xt,listHeight:200,listItemHeight:20,prefixCls:"vc-tree-select"}),setup(e,t){let{attrs:n,expose:a,slots:o}=t;const r=ta(Ue(e,"id")),l=E(()=>e.treeCheckable&&!e.treeCheckStrictly),c=E(()=>e.treeCheckable||e.treeCheckStrictly),u=E(()=>e.treeCheckStrictly||e.labelInValue),i=E(()=>c.value||e.multiple),d=E(()=>to(e.fieldNames)),[y,m]=Ot("",{value:E(()=>e.searchValue!==void 0?e.searchValue:e.inputValue),postState:N=>N||""}),b=N=>{var j;m(N),(j=e.onSearch)===null||j===void 0||j.call(e,N)},x=ho(Ue(e,"treeData"),Ue(e,"children"),Ue(e,"treeDataSimpleMode")),{keyEntities:p,valueEntities:k}=po(x,d),C=N=>{const j=[],G=[];return N.forEach(s=>{k.value.has(s)?G.push(s):j.push(s)}),{missingRawValues:j,existRawValues:G}},$=mo(x,y,{fieldNames:d,treeNodeFilterProp:Ue(e,"treeNodeFilterProp"),filterTreeNode:Ue(e,"filterTreeNode")}),P=N=>{if(N){if(e.treeNodeLabelProp)return N[e.treeNodeLabelProp];const{_title:j}=d.value;for(let G=0;G<j.length;G+=1){const s=N[j[G]];if(s!==void 0)return s}}},T=N=>eo(N).map(G=>bo(G)?{value:G}:G),S=N=>T(N).map(G=>{let{label:s}=G;const{value:v,halfChecked:D}=G;let M;const R=k.value.get(v);return R&&(s=s!=null?s:P(R.node),M=R.node.disabled),{label:s,value:v,halfChecked:D,disabled:M}}),[V,H]=Ot(e.defaultValue,{value:Ue(e,"value")}),L=E(()=>T(V.value)),F=B([]),Z=B([]);Ke(()=>{const N=[],j=[];L.value.forEach(G=>{G.halfChecked?j.push(G):N.push(G)}),F.value=N,Z.value=j});const J=E(()=>F.value.map(N=>N.value)),{maxLevel:ae,levelEntities:oe}=cn(p),[fe,se]=go(F,Z,l,p,ae,oe),pe=E(()=>{const G=Ut(fe.value,e.showCheckedStrategy,p.value,d.value).map(D=>{var M,R,ee;return(ee=(R=(M=p.value[D])===null||M===void 0?void 0:M.node)===null||R===void 0?void 0:R[d.value.value])!==null&&ee!==void 0?ee:D}).map(D=>{const M=F.value.find(R=>R.value===D);return{value:D,label:M==null?void 0:M.label}}),s=S(G),v=s[0];return!i.value&&v&&Wt(v.value)&&Wt(v.label)?[]:s.map(D=>{var M;return w(w({},D),{label:(M=D.label)!==null&&M!==void 0?M:D.value})})}),[ce]=yo(pe),Q=(N,j,G)=>{const s=S(N);if(H(s),e.autoClearSearchValue&&m(""),e.onChange){let v=N;l.value&&(v=Ut(N,e.showCheckedStrategy,p.value,d.value).map(A=>{const I=k.value.get(A);return I?I.node[d.value.value]:A}));const{triggerValue:D,selected:M}=j||{triggerValue:void 0,selected:void 0};let R=v;if(e.treeCheckStrictly){const K=Z.value.filter(A=>!v.includes(A.value));R=[...R,...K]}const ee=S(R),f={preValue:F.value,triggerValue:D};let h=!0;(e.treeCheckStrictly||G==="selection"&&!M)&&(h=!1),fo(f,D,N,x.value,h,d.value),c.value?f.checked=M:f.selected=M;const g=u.value?ee:ee.map(K=>K.value);e.onChange(i.value?g:g[0],u.value?null:ee.map(K=>K.label),f)}},de=(N,j)=>{let{selected:G,source:s}=j;var v,D,M;const R=Ee(p.value),ee=Ee(k.value),f=R[N],h=f==null?void 0:f.node,g=(v=h==null?void 0:h[d.value.value])!==null&&v!==void 0?v:N;if(!i.value)Q([g],{selected:!0,triggerValue:g},"option");else{let K=G?[...J.value,g]:fe.value.filter(A=>A!==g);if(l.value){const{missingRawValues:A,existRawValues:I}=C(K),O=I.map(X=>ee.get(X).key);let z;G?{checkedKeys:z}=Ge(O,!0,R,ae.value,oe.value):{checkedKeys:z}=Ge(O,{halfCheckedKeys:se.value},R,ae.value,oe.value),K=[...A,...z.map(X=>R[X].node[d.value.value])]}Q(K,{selected:G,triggerValue:g},s||"option")}G||!i.value?(D=e.onSelect)===null||D===void 0||D.call(e,g,ut(h)):(M=e.onDeselect)===null||M===void 0||M.call(e,g,ut(h))},te=N=>{if(e.onDropdownVisibleChange){const j={};Object.defineProperty(j,"documentClickClose",{get(){return!1}}),e.onDropdownVisibleChange(N,j)}},le=(N,j)=>{const G=N.map(s=>s.value);if(j.type==="clear"){Q(G,{},"selection");return}j.values.length&&de(j.values[0].value,{selected:!1,source:"selection"})},{treeNodeFilterProp:ie,loadData:ve,treeLoadedKeys:W,onTreeLoad:ne,treeDefaultExpandAll:ue,treeExpandedKeys:me,treeDefaultExpandedKeys:Ve,onTreeExpand:je,virtual:Be,listHeight:He,listItemHeight:De,treeLine:Re,treeIcon:Pe,showTreeIcon:Ie,switcherIcon:$e,treeMotion:Te,customSlots:ze,dropdownMatchSelectWidth:Le,treeExpandAction:We}=Jn(e);na(Et({checkable:c,loadData:ve,treeLoadedKeys:W,onTreeLoad:ne,checkedKeys:fe,halfCheckedKeys:se,treeDefaultExpandAll:ue,treeExpandedKeys:me,treeDefaultExpandedKeys:Ve,onTreeExpand:je,treeIcon:Pe,treeMotion:Te,showTreeIcon:Ie,switcherIcon:$e,treeLine:Re,treeNodeFilterProp:ie,keyEntities:p,customSlots:ze})),ao(Et({virtual:Be,listHeight:He,listItemHeight:De,treeData:$,fieldNames:d,onSelect:de,dropdownMatchSelectWidth:Le,treeExpandAction:We}));const be=Fe();return a({focus(){var N;(N=be.value)===null||N===void 0||N.focus()},blur(){var N;(N=be.value)===null||N===void 0||N.blur()},scrollTo(N){var j;(j=be.value)===null||j===void 0||j.scrollTo(N)}}),()=>{var N;const j=Xe(e,["id","prefixCls","customSlots","value","defaultValue","onChange","onSelect","onDeselect","searchValue","inputValue","onSearch","autoClearSearchValue","filterTreeNode","treeNodeFilterProp","showCheckedStrategy","treeNodeLabelProp","multiple","treeCheckable","treeCheckStrictly","labelInValue","fieldNames","treeDataSimpleMode","treeData","children","loadData","treeLoadedKeys","onTreeLoad","treeDefaultExpandAll","treeExpandedKeys","treeDefaultExpandedKeys","onTreeExpand","virtual","listHeight","listItemHeight","onDropdownVisibleChange","treeLine","treeIcon","showTreeIcon","switcherIcon","treeMotion"]);return _(aa,re(re(re({ref:be},n),j),{},{id:r,prefixCls:e.prefixCls,mode:i.value?"multiple":void 0,displayValues:ce.value,onDisplayValuesChange:le,searchValue:y.value,onSearch:b,OptionList:ro,emptyOptions:!x.value.length,onDropdownVisibleChange:te,tagRender:e.tagRender||o.tagRender,dropdownMatchSelectWidth:(N=e.dropdownMatchSelectWidth)!==null&&N!==void 0?N:!0}),o)}}}),Co=e=>{const{componentCls:t,treePrefixCls:n,colorBgElevated:a}=e,o=`.${n}`;return[{[`${t}-dropdown`]:[{padding:`${e.paddingXS}px ${e.paddingXS/2}px`},Za(n,vt(e,{colorBgContainer:a})),{[o]:{borderRadius:0,"&-list-holder-inner":{alignItems:"stretch",[`${o}-treenode`]:{[`${o}-node-content-wrapper`]:{flex:"auto"}}}}},fa(`${n}-checkbox`,e),{"&-rtl":{direction:"rtl",[`${o}-switcher${o}-switcher_close`]:{[`${o}-switcher-icon svg`]:{transform:"rotate(90deg)"}}}}]}]};function ko(e,t){return Ln("TreeSelect",n=>{const a=vt(n,{treePrefixCls:t.value});return[Co(a)]})(e)}const qt=(e,t,n)=>n!==void 0?n:`${e}-${t}`;function wo(){return w(w({},Xe(vn(),["showTreeIcon","treeMotion","inputIcon","getInputElement","treeLine","customSlots"])),{suffixIcon:ge.any,size:lt(),bordered:Fn(),treeLine:_n([Boolean,Object]),replaceFields:An(),placement:lt(),status:lt(),popupClassName:String,dropdownClassName:String,"onUpdate:value":ot(),"onUpdate:treeExpandedKeys":ot(),"onUpdate:searchValue":ot()})}const st=Me({compatConfig:{MODE:3},name:"ATreeSelect",inheritAttrs:!1,props:ft(wo(),{choiceTransitionName:"",listHeight:256,treeIcon:!1,listItemHeight:26,bordered:!0}),slots:Object,setup(e,t){let{attrs:n,slots:a,expose:o,emit:r}=t;Xt(!(e.treeData===void 0&&a.default)),rt(e.multiple!==!1||!e.treeCheckable,"TreeSelect","`multiple` will always be `true` when `treeCheckable` is true"),rt(e.replaceFields===void 0,"TreeSelect","`replaceFields` is deprecated, please use fieldNames instead"),rt(!e.dropdownClassName,"TreeSelect","`dropdownClassName` is deprecated. Please use `popupClassName` instead.");const l=sa(),c=ca.useInject(),u=E(()=>da(c.status,e.status)),{prefixCls:i,renderEmpty:d,direction:y,virtual:m,dropdownMatchSelectWidth:b,size:x,getPopupContainer:p,getPrefixCls:k,disabled:C}=Mn("select",e),{compactSize:$,compactItemClassnames:P}=Vn(i,y),T=E(()=>$.value||x.value),S=jn(),V=E(()=>{var W;return(W=C.value)!==null&&W!==void 0?W:S.value}),H=E(()=>k()),L=E(()=>e.placement!==void 0?e.placement:y.value==="rtl"?"bottomRight":"bottomLeft"),F=E(()=>qt(H.value,Bn(L.value),e.transitionName)),Z=E(()=>qt(H.value,"",e.choiceTransitionName)),J=E(()=>k("select-tree",e.prefixCls)),ae=E(()=>k("tree-select",e.prefixCls)),[oe,fe]=la(i),[se]=ko(ae,J),pe=E(()=>xe(e.popupClassName||e.dropdownClassName,`${ae.value}-dropdown`,{[`${ae.value}-dropdown-rtl`]:y.value==="rtl"},fe.value)),ce=E(()=>!!(e.treeCheckable||e.multiple)),Q=E(()=>e.showArrow!==void 0?e.showArrow:e.loading||!ce.value),de=Fe();o({focus(){var W,ne;(ne=(W=de.value).focus)===null||ne===void 0||ne.call(W)},blur(){var W,ne;(ne=(W=de.value).blur)===null||ne===void 0||ne.call(W)}});const te=function(){for(var W=arguments.length,ne=new Array(W),ue=0;ue<W;ue++)ne[ue]=arguments[ue];r("update:value",ne[0]),r("change",...ne),l.onFieldChange()},le=W=>{r("update:treeExpandedKeys",W),r("treeExpand",W)},ie=W=>{r("update:searchValue",W),r("search",W)},ve=W=>{r("blur",W),l.onFieldBlur()};return()=>{var W,ne,ue;const{notFoundContent:me=(W=a.notFoundContent)===null||W===void 0?void 0:W.call(a),prefixCls:Ve,bordered:je,listHeight:Be,listItemHeight:He,multiple:De,treeIcon:Re,treeLine:Pe,showArrow:Ie,switcherIcon:$e=(ne=a.switcherIcon)===null||ne===void 0?void 0:ne.call(a),fieldNames:Te=e.replaceFields,id:ze=l.id.value,placeholder:Le=(ue=a.placeholder)===null||ue===void 0?void 0:ue.call(a)}=e,{isFormItemInput:We,hasFeedback:be,feedbackIcon:N}=c,{suffixIcon:j,removeIcon:G,clearIcon:s}=ra(w(w({},e),{multiple:ce.value,showArrow:Q.value,hasFeedback:be,feedbackIcon:N,prefixCls:i.value}),a);let v;me!==void 0?v=me:v=d("Select");const D=Xe(e,["suffixIcon","itemIcon","removeIcon","clearIcon","switcherIcon","bordered","status","onUpdate:value","onUpdate:treeExpandedKeys","onUpdate:searchValue"]),M=xe(!Ve&&ae.value,{[`${i.value}-lg`]:T.value==="large",[`${i.value}-sm`]:T.value==="small",[`${i.value}-rtl`]:y.value==="rtl",[`${i.value}-borderless`]:!je,[`${i.value}-in-form-item`]:We},ua(i.value,u.value,be),P.value,n.class,fe.value),R={};return e.treeData===void 0&&a.default&&(R.children=Hn(a.default())),oe(se(_(So,re(re(re(re({},n),D),{},{disabled:V.value,virtual:m.value,dropdownMatchSelectWidth:b.value,id:ze,fieldNames:Te,ref:de,prefixCls:i.value,class:M,listHeight:Be,listItemHeight:He,treeLine:!!Pe,inputIcon:j,multiple:De,removeIcon:G,clearIcon:s,switcherIcon:ee=>qa(J.value,$e,ee,a.leafIcon,Pe),showTreeIcon:Re,notFoundContent:v,getPopupContainer:p==null?void 0:p.value,treeMotion:null,dropdownClassName:pe.value,choiceTransitionName:Z.value,onChange:te,onBlur:ve,onSearch:ie,onTreeExpand:le},R),{},{transitionName:F.value,customSlots:w(w({},a),{treeCheckable:()=>_("span",{class:`${i.value}-tree-checkbox-inner`},null)}),maxTagPlaceholder:e.maxTagPlaceholder||a.maxTagPlaceholder,placement:L.value,showArrow:be||Ie,placeholder:Le}),w(w({},a),{treeCheckable:()=>_("span",{class:`${i.value}-tree-checkbox-inner`},null)}))))}}}),Gt=Ye,zo=w(st,{TreeNode:Ye,SHOW_ALL:io,SHOW_PARENT:fn,SHOW_CHILD:xt,install:e=>(e.component(st.name,st),e.component(Gt.displayName,Gt),e)});export{Gt as TreeSelectNode,zo as default,wo as treeSelectProps};
