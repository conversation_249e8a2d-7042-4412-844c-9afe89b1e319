<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue';
import type { EchartsUIType } from '@vben/plugins/echarts';
import { EchartsUI, useEcharts } from '@vben/plugins/echarts';
import {
  Card,
  Row,
  Col,
  Statistic,
  Select,
  DatePicker,
  Button,
  Table,
  Progress,
  Tag,
  Space,
  Tabs,
  TabPane,
} from 'ant-design-vue';
import type { TableColumnsType } from 'ant-design-vue';

defineOptions({ name: 'IrrigationStatistics' });

// 查询条件
const queryForm = reactive({
  timeRange: 'month', // week, month, quarter, year
  stationType: 'all', // all, large, medium, small, micro
  dateRange: null,
});

// 图表引用
const flowTrendRef = ref<EchartsUIType>();
const powerDistributionRef = ref<EchartsUIType>();
const efficiencyAnalysisRef = ref<EchartsUIType>();
const maintenanceStatRef = ref<EchartsUIType>();

const { renderEcharts: renderFlowTrend } = useEcharts(flowTrendRef);
const { renderEcharts: renderPowerDistribution } = useEcharts(powerDistributionRef);
const { renderEcharts: renderEfficiencyAnalysis } = useEcharts(efficiencyAnalysisRef);
const { renderEcharts: renderMaintenanceStat } = useEcharts(maintenanceStatRef);

// 统计数据
const statisticsData = reactive({
  totalStations: 154,
  onlineStations: 142,
  totalFlow: 12856.8,
  totalPower: 68245.6,
  totalIrrigationArea: 125680,
  avgEfficiency: 87.5,
  maintenanceRate: 92.3,
  faultRate: 2.1,
});

// 运行效率数据
const efficiencyData = ref([
  { stationName: '东风灌站', efficiency: 95.2, runningTime: 8.5, targetTime: 9.0, status: 'excellent' },
  { stationName: '红旗灌站', efficiency: 78.5, runningTime: 6.2, targetTime: 8.0, status: 'good' },
  { stationName: '胜利灌站', efficiency: 92.1, runningTime: 7.8, targetTime: 8.5, status: 'excellent' },
  { stationName: '新华灌站', efficiency: 65.3, runningTime: 5.1, targetTime: 8.0, status: 'poor' },
  { stationName: '建设灌站', efficiency: 88.7, runningTime: 7.5, targetTime: 8.5, status: 'good' },
]);

// 维修记录数据
const maintenanceData = ref([
  {
    id: 1,
    stationName: '东风灌站',
    deviceName: '主水泵',
    maintenanceType: '定期保养',
    maintenanceDate: '2024-01-10',
    cost: 2500,
    duration: 4,
    result: '正常',
  },
  {
    id: 2,
    stationName: '红旗灌站',
    deviceName: '变频器',
    maintenanceType: '故障维修',
    maintenanceDate: '2024-01-08',
    cost: 8500,
    duration: 12,
    result: '已修复',
  },
  {
    id: 3,
    stationName: '胜利灌站',
    deviceName: '流量计',
    maintenanceType: '校准检测',
    maintenanceDate: '2024-01-05',
    cost: 1200,
    duration: 2,
    result: '正常',
  },
]);

// 表格列定义
const efficiencyColumns: TableColumnsType = [
  { title: '灌站名称', dataIndex: 'stationName', key: 'stationName' },
  { title: '运行效率(%)', dataIndex: 'efficiency', key: 'efficiency' },
  { title: '运行时间(h)', dataIndex: 'runningTime', key: 'runningTime' },
  { title: '目标时间(h)', dataIndex: 'targetTime', key: 'targetTime' },
  { title: '状态', dataIndex: 'status', key: 'status' },
];

const maintenanceColumns: TableColumnsType = [
  { title: '灌站名称', dataIndex: 'stationName', key: 'stationName' },
  { title: '设备名称', dataIndex: 'deviceName', key: 'deviceName' },
  { title: '维修类型', dataIndex: 'maintenanceType', key: 'maintenanceType' },
  { title: '维修日期', dataIndex: 'maintenanceDate', key: 'maintenanceDate' },
  { title: '费用(元)', dataIndex: 'cost', key: 'cost' },
  { title: '耗时(h)', dataIndex: 'duration', key: 'duration' },
  { title: '结果', dataIndex: 'result', key: 'result' },
];

// 获取效率状态标签
const getEfficiencyTag = (status: string) => {
  switch (status) {
    case 'excellent':
      return { color: 'green', text: '优秀' };
    case 'good':
      return { color: 'blue', text: '良好' };
    case 'poor':
      return { color: 'red', text: '较差' };
    default:
      return { color: 'default', text: '未知' };
  }
};

// 计算统计指标
const computedStats = computed(() => {
  const onlineRate = ((statisticsData.onlineStations / statisticsData.totalStations) * 100).toFixed(1);
  const avgFlowPerStation = (statisticsData.totalFlow / statisticsData.onlineStations).toFixed(1);
  const avgPowerPerStation = (statisticsData.totalPower / statisticsData.onlineStations).toFixed(1);
  
  return {
    onlineRate,
    avgFlowPerStation,
    avgPowerPerStation,
  };
});

// 渲染图表
const renderCharts = () => {
  // 流量趋势图
  renderFlowTrend({
    title: {
      text: '30天流量趋势',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      formatter: '{b}: {c} m³/h',
    },
    xAxis: {
      type: 'category',
      data: Array.from({ length: 30 }).map((_, i) => `${i + 1}日`),
    },
    yAxis: {
      type: 'value',
      name: '流量(m³/h)',
    },
    series: [
      {
        name: '总流量',
        type: 'line',
        smooth: true,
        data: Array.from({ length: 30 }).map(() => 
          10000 + Math.random() * 5000
        ),
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(24, 144, 255, 0.6)' },
              { offset: 1, color: 'rgba(24, 144, 255, 0.1)' },
            ],
          },
        },
      },
    ],
  });

  // 功率分布图
  renderPowerDistribution({
    title: {
      text: '灌站功率分布',
      left: 'center',
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}座 ({d}%)',
    },
    series: [
      {
        name: '功率分布',
        type: 'pie',
        radius: ['40%', '70%'],
        data: [
          { value: 25, name: '大型灌站(>300kW)' },
          { value: 45, name: '中型灌站(150-300kW)' },
          { value: 58, name: '小型灌站(50-150kW)' },
          { value: 26, name: '微型灌站(<50kW)' },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  });

  // 效率分析图
  renderEfficiencyAnalysis({
    title: {
      text: '灌站运行效率分析',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
    },
    xAxis: {
      type: 'category',
      data: efficiencyData.value.map(item => item.stationName),
    },
    yAxis: {
      type: 'value',
      name: '效率(%)',
      max: 100,
    },
    series: [
      {
        name: '运行效率',
        type: 'bar',
        data: efficiencyData.value.map(item => ({
          value: item.efficiency,
          itemStyle: {
            color: item.efficiency >= 90 ? '#52c41a' : 
                   item.efficiency >= 80 ? '#1890ff' : '#ff4d4f',
          },
        })),
      },
    ],
  });

  // 维修统计图
  renderMaintenanceStat({
    title: {
      text: '月度维修统计',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: ['维修次数', '维修费用'],
      top: 30,
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月'],
    },
    yAxis: [
      {
        type: 'value',
        name: '维修次数',
        position: 'left',
      },
      {
        type: 'value',
        name: '费用(万元)',
        position: 'right',
      },
    ],
    series: [
      {
        name: '维修次数',
        type: 'bar',
        data: [12, 8, 15, 10, 6, 9],
        itemStyle: { color: '#1890ff' },
      },
      {
        name: '维修费用',
        type: 'line',
        yAxisIndex: 1,
        data: [8.5, 6.2, 12.3, 7.8, 4.5, 6.9],
        itemStyle: { color: '#ff4d4f' },
      },
    ],
  });
};

// 刷新数据
const refreshData = () => {
  // 模拟数据刷新
  statisticsData.totalFlow += (Math.random() - 0.5) * 1000;
  statisticsData.totalPower += (Math.random() - 0.5) * 5000;
  statisticsData.avgEfficiency += (Math.random() - 0.5) * 2;
  
  renderCharts();
};

onMounted(() => {
  renderCharts();
});
</script>

<template>
  <div class="p-5">
    <!-- 查询条件 -->
    <Card title="查询条件" class="mb-5">
      <Row :gutter="16" align="middle">
        <Col :span="4">
          <span class="mr-2">时间范围：</span>
          <Select v-model:value="queryForm.timeRange" style="width: 120px">
            <Select.Option value="week">近一周</Select.Option>
            <Select.Option value="month">近一月</Select.Option>
            <Select.Option value="quarter">近一季</Select.Option>
            <Select.Option value="year">近一年</Select.Option>
          </Select>
        </Col>
        <Col :span="4">
          <span class="mr-2">灌站类型：</span>
          <Select v-model:value="queryForm.stationType" style="width: 120px">
            <Select.Option value="all">全部</Select.Option>
            <Select.Option value="large">大型</Select.Option>
            <Select.Option value="medium">中型</Select.Option>
            <Select.Option value="small">小型</Select.Option>
            <Select.Option value="micro">微型</Select.Option>
          </Select>
        </Col>
        <Col :span="4">
          <Button type="primary" @click="refreshData">刷新数据</Button>
        </Col>
      </Row>
    </Card>

    <!-- 总体统计 -->
    <Row :gutter="16" class="mb-5">
      <Col :span="4">
        <Card>
          <Statistic
            title="总灌站数"
            :value="statisticsData.totalStations"
            suffix="座"
            :value-style="{ color: '#1890ff' }"
          />
        </Card>
      </Col>
      <Col :span="4">
        <Card>
          <Statistic
            title="在线率"
            :value="computedStats.onlineRate"
            suffix="%"
            :value-style="{ color: '#52c41a' }"
          />
        </Card>
      </Col>
      <Col :span="4">
        <Card>
          <Statistic
            title="总流量"
            :value="statisticsData.totalFlow"
            suffix="m³/h"
            :precision="1"
            :value-style="{ color: '#722ed1' }"
          />
        </Card>
      </Col>
      <Col :span="4">
        <Card>
          <Statistic
            title="总功率"
            :value="statisticsData.totalPower"
            suffix="kW"
            :precision="1"
            :value-style="{ color: '#faad14' }"
          />
        </Card>
      </Col>
      <Col :span="4">
        <Card>
          <Statistic
            title="平均效率"
            :value="statisticsData.avgEfficiency"
            suffix="%"
            :precision="1"
            :value-style="{ color: '#13c2c2' }"
          />
        </Card>
      </Col>
      <Col :span="4">
        <Card>
          <Statistic
            title="故障率"
            :value="statisticsData.faultRate"
            suffix="%"
            :precision="1"
            :value-style="{ color: '#ff4d4f' }"
          />
        </Card>
      </Col>
    </Row>

    <!-- 图表分析 -->
    <Tabs default-active-key="1" class="mb-5">
      <TabPane key="1" tab="运行分析">
        <Row :gutter="16">
          <Col :span="12">
            <Card title="流量趋势分析">
              <EchartsUI ref="flowTrendRef" style="height: 350px" />
            </Card>
          </Col>
          <Col :span="12">
            <Card title="功率分布统计">
              <EchartsUI ref="powerDistributionRef" style="height: 350px" />
            </Card>
          </Col>
        </Row>
      </TabPane>
      
      <TabPane key="2" tab="效率分析">
        <Row :gutter="16">
          <Col :span="12">
            <Card title="运行效率对比">
              <EchartsUI ref="efficiencyAnalysisRef" style="height: 350px" />
            </Card>
          </Col>
          <Col :span="12">
            <Card title="效率详细数据">
              <Table
                :columns="efficiencyColumns"
                :data-source="efficiencyData"
                :pagination="false"
                size="small"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'efficiency'">
                    <div class="flex items-center">
                      <Progress
                        :percent="record.efficiency"
                        size="small"
                        :stroke-color="record.efficiency >= 90 ? '#52c41a' : 
                                       record.efficiency >= 80 ? '#1890ff' : '#ff4d4f'"
                        class="mr-2"
                        style="width: 60px"
                      />
                      <span>{{ record.efficiency }}%</span>
                    </div>
                  </template>
                  <template v-else-if="column.key === 'status'">
                    <Tag :color="getEfficiencyTag(record.status).color">
                      {{ getEfficiencyTag(record.status).text }}
                    </Tag>
                  </template>
                </template>
              </Table>
            </Card>
          </Col>
        </Row>
      </TabPane>
      
      <TabPane key="3" tab="维修分析">
        <Row :gutter="16">
          <Col :span="12">
            <Card title="维修统计趋势">
              <EchartsUI ref="maintenanceStatRef" style="height: 350px" />
            </Card>
          </Col>
          <Col :span="12">
            <Card title="维修记录详情">
              <Table
                :columns="maintenanceColumns"
                :data-source="maintenanceData"
                :pagination="false"
                size="small"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'cost'">
                    <span class="font-medium">¥{{ record.cost.toLocaleString() }}</span>
                  </template>
                  <template v-else-if="column.key === 'result'">
                    <Tag :color="record.result === '正常' || record.result === '已修复' ? 'green' : 'red'">
                      {{ record.result }}
                    </Tag>
                  </template>
                </template>
              </Table>
            </Card>
          </Col>
        </Row>
      </TabPane>
    </Tabs>

    <!-- 关键指标 -->
    <Card title="关键指标分析">
      <Row :gutter="16">
        <Col :span="8">
          <div class="text-center p-4">
            <div class="text-lg text-gray-600 mb-2">平均单站流量</div>
            <div class="text-2xl font-bold text-blue-500">
              {{ computedStats.avgFlowPerStation }} m³/h
            </div>
            <Progress
              :percent="75"
              stroke-color="#1890ff"
              class="mt-2"
            />
          </div>
        </Col>
        <Col :span="8">
          <div class="text-center p-4">
            <div class="text-lg text-gray-600 mb-2">平均单站功率</div>
            <div class="text-2xl font-bold text-green-500">
              {{ computedStats.avgPowerPerStation }} kW
            </div>
            <Progress
              :percent="68"
              stroke-color="#52c41a"
              class="mt-2"
            />
          </div>
        </Col>
        <Col :span="8">
          <div class="text-center p-4">
            <div class="text-lg text-gray-600 mb-2">维修完成率</div>
            <div class="text-2xl font-bold text-purple-500">
              {{ statisticsData.maintenanceRate }}%
            </div>
            <Progress
              :percent="statisticsData.maintenanceRate"
              stroke-color="#722ed1"
              class="mt-2"
            />
          </div>
        </Col>
      </Row>
    </Card>
  </div>
</template>

<style scoped>
.ant-statistic-content {
  font-size: 20px;
  font-weight: bold;
}

.ant-progress-line {
  margin-bottom: 0;
}
</style>
