import{aN as V,g as k,P as F,v as G,a as J,aO as L,j as W,_ as p,n as Y,b as P,aP as $}from"./bootstrap-DlHXJWd_.js";import{a5 as q,a9 as H,a4 as K,J as o,P as j,Y as Q,x as m,F as U}from"../jse/index-index-DYNcUVMZ.js";const X=()=>{const e=q(!1);return H(()=>{e.value=V()}),e},Z={small:8,middle:16,large:24},ee=()=>({prefixCls:String,size:{type:[String,Number,Array]},direction:F.oneOf(G("horizontal","vertical")).def("horizontal"),align:F.oneOf(G("start","end","center","baseline")),wrap:k()});function ae(e){return typeof e=="string"?Z[e]:e||0}const d=K({compatConfig:{MODE:3},name:"ASpace",inheritAttrs:!1,props:ee(),slots:Object,setup(e,I){let{slots:i,attrs:f}=I;const{prefixCls:l,space:g,direction:h}=J("space",e),[B,R]=L(l),z=X(),n=o(()=>{var a,t,s;return(s=(a=e.size)!==null&&a!==void 0?a:(t=g==null?void 0:g.value)===null||t===void 0?void 0:t.size)!==null&&s!==void 0?s:"small"}),y=j(),r=j();Q(n,()=>{[y.value,r.value]=(Array.isArray(n.value)?n.value:[n.value,n.value]).map(a=>ae(a))},{immediate:!0});const b=o(()=>e.align===void 0&&e.direction==="horizontal"?"center":e.align),D=o(()=>W(l.value,R.value,`${l.value}-${e.direction}`,{[`${l.value}-rtl`]:h.value==="rtl",[`${l.value}-align-${b.value}`]:b.value})),E=o(()=>h.value==="rtl"?"marginLeft":"marginRight"),M=o(()=>{const a={};return z.value&&(a.columnGap=`${y.value}px`,a.rowGap=`${r.value}px`),p(p({},a),e.wrap&&{flexWrap:"wrap",marginBottom:`${-r.value}px`})});return()=>{var a,t;const{wrap:s,direction:T="horizontal"}=e,C=(a=i.default)===null||a===void 0?void 0:a.call(i),w=Y(C),_=w.length;if(_===0)return null;const c=(t=i.split)===null||t===void 0?void 0:t.call(i),A=`${l.value}-item`,N=y.value,S=_-1;return m("div",P(P({},f),{},{class:[D.value,f.class],style:[M.value,f.style]}),[w.map((O,u)=>{let x=C.indexOf(O);x===-1&&(x=`$$space-${u}`);let v={};return z.value||(T==="vertical"?u<S&&(v={marginBottom:`${N/(c?2:1)}px`}):v=p(p({},u<S&&{[E.value]:`${N/(c?2:1)}px`}),s&&{paddingBottom:`${r.value}px`})),B(m(U,{key:x},[m("div",{class:A,style:v},[O]),u<S&&c&&m("span",{class:`${A}-split`,style:v},[c])]))})])}}});d.Compact=$;d.install=function(e){return e.component(d.name,d),e.component($.name,$),e};export{$ as Compact,d as default,ee as spaceProps};
