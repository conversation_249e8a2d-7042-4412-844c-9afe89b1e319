import{K as ce,b as U,_ as p,O as at,Q as nt,h as Ze,o as be,P as ue,g as ot,m as qe,G as _e,s as Ae,k as rt,H as st,v as it,R as ct,x as Oe,a as ut,J as dt,S as vt,T as ft,j as Ue,f as ht}from"./bootstrap-CFDAkNgp.js";import{u as pt}from"./move-IXaXzbNk.js";import{T as mt,c as gt,a as He,u as yt,g as bt,r as Ct}from"./index-DYfzhziO.js";import{u as St,a as wt,b as xt,c as It,t as ze,B as Vt,d as kt,e as Tt,g as Nt}from"./index-B2Lu6Z2W.js";import{R as Lt,aF as Et,a4 as je,P as ge,J as y,Y as ye,a5 as R,x as z,n as Dt,r as te,ao as $e,_ as ne,Z as Pt}from"../jse/index-index-B2UBupFX.js";import{u as Ge}from"./useMergedState-C4x1IDb9.js";import{u as Kt,F as _t}from"./FormItemContext-CoieKSxA.js";import{g as At,a as Ot}from"./statusUtils-D62pPzYs.js";import{g as Ft}from"./index-C5usYyko.js";import"./collapseMotion-DiwOar_A.js";import"./Trigger-D2zZP_An.js";import"./ResizeObserver.es-CDE7jhPe.js";import"./BaseInput-Dslq5mxC.js";import"./Overflow--DaGju1E.js";import"./index-DQZjs6Lb.js";import"./isMobile-8sZ0LT6r.js";import"./DownOutlined-CVWF16Fu.js";import"./CheckOutlined-CZeS3E7o.js";import"./SearchOutlined-DqQ4RgbY.js";import"./slide-BhgK1D9k.js";function Ht(e){return Array.isArray(e)?e:e!==void 0?[e]:[]}function Mt(e){const{label:i,value:a,children:t}=e||{},l=a||"value";return{_title:i?[i]:["title","label"],value:l,key:l,children:t||"children"}}function Me(e){return e.disabled||e.disableCheckbox||e.checkable===!1}function Rt(e,i){const a=[];function t(l){l.forEach(n=>{a.push(n[i.value]);const o=n[i.children];o&&t(o)})}return t(e),a}function Je(e){return e==null}const et=Symbol("TreeSelectContextPropsKey");function jt(e){return Et(et,e)}function $t(){return Lt(et,{})}const Bt={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0},Wt=je({compatConfig:{MODE:3},name:"OptionList",inheritAttrs:!1,setup(e,i){let{slots:a,expose:t}=i;const l=wt(),n=St(),o=$t(),c=ge(),v=pt(()=>o.treeData,[()=>l.open,()=>o.treeData],d=>d[0]),r=y(()=>{const{checkable:d,halfCheckedKeys:b,checkedKeys:E}=n;return d?{checked:E,halfChecked:b}:null});ye(()=>l.open,()=>{Dt(()=>{var d;l.open&&!l.multiple&&n.checkedKeys.length&&((d=c.value)===null||d===void 0||d.scrollTo({key:n.checkedKeys[0]}))})},{immediate:!0,flush:"post"});const u=y(()=>String(l.searchValue).toLowerCase()),g=d=>u.value?String(d[n.treeNodeFilterProp]).toLowerCase().includes(u.value):!1,V=R(n.treeDefaultExpandedKeys),S=R(null);ye(()=>l.searchValue,()=>{l.searchValue&&(S.value=Rt(te(o.treeData),te(o.fieldNames)))},{immediate:!0});const I=y(()=>n.treeExpandedKeys?n.treeExpandedKeys.slice():l.searchValue?S.value:V.value),w=d=>{var b;V.value=d,S.value=d,(b=n.onTreeExpand)===null||b===void 0||b.call(n,d)},k=d=>{d.preventDefault()},O=(d,b)=>{let{node:E}=b;var K,N;const{checkable:W,checkedKeys:G}=n;W&&Me(E)||((K=o.onSelect)===null||K===void 0||K.call(o,E.key,{selected:!G.includes(E.key)}),l.multiple||(N=l.toggleOpen)===null||N===void 0||N.call(l,!1))},_=ge(null),A=y(()=>n.keyEntities[_.value]),F=d=>{_.value=d};return t({scrollTo:function(){for(var d,b,E=arguments.length,K=new Array(E),N=0;N<E;N++)K[N]=arguments[N];return(b=(d=c.value)===null||d===void 0?void 0:d.scrollTo)===null||b===void 0?void 0:b.call(d,...K)},onKeydown:d=>{var b;const{which:E}=d;switch(E){case ce.UP:case ce.DOWN:case ce.LEFT:case ce.RIGHT:(b=c.value)===null||b===void 0||b.onKeydown(d);break;case ce.ENTER:{if(A.value){const{selectable:K,value:N}=A.value.node||{};K!==!1&&O(null,{node:{key:_.value},selected:!n.checkedKeys.includes(N)})}break}case ce.ESC:l.toggleOpen(!1)}},onKeyup:()=>{}}),()=>{var d;const{prefixCls:b,multiple:E,searchValue:K,open:N,notFoundContent:W=(d=a.notFoundContent)===null||d===void 0?void 0:d.call(a)}=l,{listHeight:G,listItemHeight:j,virtual:le,dropdownMatchSelectWidth:J,treeExpandAction:oe}=o,{checkable:re,treeDefaultExpandAll:se,treeIcon:ae,showTreeIcon:X,switcherIcon:ve,treeLine:fe,loadData:he,treeLoadedKeys:ie,treeMotion:m,onTreeLoad:D,checkedKeys:H}=n;if(v.value.length===0)return z("div",{role:"listbox",class:`${b}-empty`,onMousedown:k},[W]);const Y={fieldNames:o.fieldNames};return ie&&(Y.loadedKeys=ie),I.value&&(Y.expandedKeys=I.value),z("div",{onMousedown:k},[A.value&&N&&z("span",{style:Bt,"aria-live":"assertive"},[A.value.node.value]),z(mt,U(U({ref:c,focusable:!1,prefixCls:`${b}-tree`,treeData:v.value,height:G,itemHeight:j,virtual:le!==!1&&J!==!1,multiple:E,icon:ae,showIcon:X,switcherIcon:ve,showLine:fe,loadData:K?null:he,motion:m,activeKey:_.value,checkable:re,checkStrictly:!0,checkedKeys:r.value,selectedKeys:re?[]:H,defaultExpandAll:se},Y),{},{onActiveChange:F,onSelect:O,onCheck:O,onExpand:w,onLoad:D,filterTreeNode:g,expandAction:oe}),p(p({},a),{checkable:n.customSlots.treeCheckable}))])}}}),Ut="SHOW_ALL",tt="SHOW_PARENT",Be="SHOW_CHILD";function Xe(e,i,a,t){const l=new Set(e);return i===Be?e.filter(n=>{const o=a[n];return!(o&&o.children&&o.children.some(c=>{let{node:v}=c;return l.has(v[t.value])})&&o.children.every(c=>{let{node:v}=c;return Me(v)||l.has(v[t.value])}))}):i===tt?e.filter(n=>{const o=a[n],c=o?o.parent:null;return!(c&&!Me(c.node)&&l.has(c.key))}):e}const de=()=>null;de.inheritAttrs=!1;de.displayName="ATreeSelectNode";de.isTreeSelectNode=!0;var zt=function(e,i){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&i.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,t=Object.getOwnPropertySymbols(e);l<t.length;l++)i.indexOf(t[l])<0&&Object.prototype.propertyIsEnumerable.call(e,t[l])&&(a[t[l]]=e[t[l]]);return a};function Gt(e){return e&&e.type&&e.type.isTreeSelectNode}function Jt(e){function i(){let a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return at(a).map(t=>{var l,n,o;if(!Gt(t))return null;const c=t.children||{},v=t.key,r={};for(const[E,K]of Object.entries(t.props))r[nt(E)]=K;const{isLeaf:u,checkable:g,selectable:V,disabled:S,disableCheckbox:I}=r,w={isLeaf:u||u===""||void 0,checkable:g||g===""||void 0,selectable:V||V===""||void 0,disabled:S||S===""||void 0,disableCheckbox:I||I===""||void 0},k=p(p({},r),w),{title:O=(l=c.title)===null||l===void 0?void 0:l.call(c,k),switcherIcon:_=(n=c.switcherIcon)===null||n===void 0?void 0:n.call(c,k)}=r,A=zt(r,["title","switcherIcon"]),F=(o=c.default)===null||o===void 0?void 0:o.call(c),d=p(p(p({},A),{title:O,switcherIcon:_,key:v,isLeaf:u}),w),b=i(F);return b.length&&(d.children=b),d})}return i(e)}function Re(e){if(!e)return e;const i=p({},e);return"props"in i||Object.defineProperty(i,"props",{get(){return i}}),i}function Xt(e,i,a,t,l,n){let o=null,c=null;function v(){function r(u){let g=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"0",V=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;return u.map((S,I)=>{const w=`${g}-${I}`,k=S[n.value],O=a.includes(k),_=r(S[n.children]||[],w,O),A=z(de,S,{default:()=>[_.map(F=>F.node)]});if(i===k&&(o=A),O){const F={pos:w,node:A,children:_};return V||c.push(F),F}return null}).filter(S=>S)}c||(c=[],r(t),c.sort((u,g)=>{let{node:{props:{value:V}}}=u,{node:{props:{value:S}}}=g;const I=a.indexOf(V),w=a.indexOf(S);return I-w}))}Object.defineProperty(e,"triggerNode",{get(){return v(),o}}),Object.defineProperty(e,"allCheckedNodes",{get(){return v(),l?c:c.map(r=>{let{node:u}=r;return u})}})}function Yt(e,i){let{id:a,pId:t,rootPId:l}=i;const n={},o=[];return e.map(v=>{const r=p({},v),u=r[a];return n[u]=r,r.key=r.key||u,r}).forEach(v=>{const r=v[t],u=n[r];u&&(u.children=u.children||[],u.children.push(v)),(r===l||!u&&l===null)&&o.push(v)}),o}function Qt(e,i,a){const t=R();return ye([a,e,i],()=>{const l=a.value;e.value?t.value=a.value?Yt(te(e.value),p({id:"id",pId:"pId",rootPId:null},l!==!0?l:{})):te(e.value).slice():t.value=Jt(te(i.value))},{immediate:!0,deep:!0}),t}const Zt=e=>{const i=R({valueLabels:new Map}),a=R();return ye(e,()=>{a.value=te(e.value)},{immediate:!0}),[y(()=>{const{valueLabels:l}=i.value,n=new Map,o=a.value.map(c=>{var v;const{value:r}=c,u=(v=c.label)!==null&&v!==void 0?v:l.get(r);return n.set(r,u),p(p({},c),{label:u})});return i.value.valueLabels=n,o})]},qt=(e,i)=>{const a=R(new Map),t=R({});return $e(()=>{const l=i.value,n=gt(e.value,{fieldNames:l,initWrapper:o=>p(p({},o),{valueEntities:new Map}),processEntity:(o,c)=>{const v=o.node[l.value];c.valueEntities.set(v,o)}});a.value=n.valueEntities,t.value=n.keyEntities}),{valueEntities:a,keyEntities:t}},el=(e,i,a,t,l,n)=>{const o=R([]),c=R([]);return $e(()=>{let v=e.value.map(g=>{let{value:V}=g;return V}),r=i.value.map(g=>{let{value:V}=g;return V});const u=v.filter(g=>!t.value[g]);a.value&&({checkedKeys:v,halfCheckedKeys:r}=He(v,!0,t.value,l.value,n.value)),o.value=Array.from(new Set([...u,...v])),c.value=r}),[o,c]},tl=(e,i,a)=>{let{treeNodeFilterProp:t,filterTreeNode:l,fieldNames:n}=a;return y(()=>{const{children:o}=n.value,c=i.value,v=t==null?void 0:t.value;if(!c||l.value===!1)return e.value;let r;if(typeof l.value=="function")r=l.value;else{const g=c.toUpperCase();r=(V,S)=>{const I=S[v];return String(I).toUpperCase().includes(g)}}function u(g){let V=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;const S=[];for(let I=0,w=g.length;I<w;I++){const k=g[I],O=k[o],_=V||r(c,Re(k)),A=u(O||[],_);(_||A.length)&&S.push(p(p({},k),{[o]:A}))}return S}return u(e.value)})};function lt(){return p(p({},be(kt(),["mode"])),{prefixCls:String,id:String,value:{type:[String,Number,Object,Array]},defaultValue:{type:[String,Number,Object,Array]},onChange:{type:Function},searchValue:String,inputValue:String,onSearch:{type:Function},autoClearSearchValue:{type:Boolean,default:void 0},filterTreeNode:{type:[Boolean,Function],default:void 0},treeNodeFilterProp:String,onSelect:Function,onDeselect:Function,showCheckedStrategy:{type:String},treeNodeLabelProp:String,fieldNames:{type:Object},multiple:{type:Boolean,default:void 0},treeCheckable:{type:Boolean,default:void 0},treeCheckStrictly:{type:Boolean,default:void 0},labelInValue:{type:Boolean,default:void 0},treeData:{type:Array},treeDataSimpleMode:{type:[Boolean,Object],default:void 0},loadData:{type:Function},treeLoadedKeys:{type:Array},onTreeLoad:{type:Function},treeDefaultExpandAll:{type:Boolean,default:void 0},treeExpandedKeys:{type:Array},treeDefaultExpandedKeys:{type:Array},onTreeExpand:{type:Function},virtual:{type:Boolean,default:void 0},listHeight:Number,listItemHeight:Number,onDropdownVisibleChange:{type:Function},treeLine:{type:[Boolean,Object],default:void 0},treeIcon:ue.any,showTreeIcon:{type:Boolean,default:void 0},switcherIcon:ue.any,treeMotion:ue.any,children:Array,treeExpandAction:String,showArrow:{type:Boolean,default:void 0},showSearch:{type:Boolean,default:void 0},open:{type:Boolean,default:void 0},defaultOpen:{type:Boolean,default:void 0},disabled:{type:Boolean,default:void 0},placeholder:ue.any,maxTagPlaceholder:{type:Function},dropdownPopupAlign:ue.any,customSlots:Object})}function ll(e){return!e||typeof e!="object"}const al=je({compatConfig:{MODE:3},name:"TreeSelect",inheritAttrs:!1,props:Ze(lt(),{treeNodeFilterProp:"value",autoClearSearchValue:!0,showCheckedStrategy:Be,listHeight:200,listItemHeight:20,prefixCls:"vc-tree-select"}),setup(e,i){let{attrs:a,expose:t,slots:l}=i;const n=xt(ne(e,"id")),o=y(()=>e.treeCheckable&&!e.treeCheckStrictly),c=y(()=>e.treeCheckable||e.treeCheckStrictly),v=y(()=>e.treeCheckStrictly||e.labelInValue),r=y(()=>c.value||e.multiple),u=y(()=>Mt(e.fieldNames)),[g,V]=Ge("",{value:y(()=>e.searchValue!==void 0?e.searchValue:e.inputValue),postState:s=>s||""}),S=s=>{var f;V(s),(f=e.onSearch)===null||f===void 0||f.call(e,s)},I=Qt(ne(e,"treeData"),ne(e,"children"),ne(e,"treeDataSimpleMode")),{keyEntities:w,valueEntities:k}=qt(I,u),O=s=>{const f=[],h=[];return s.forEach(x=>{k.value.has(x)?h.push(x):f.push(x)}),{missingRawValues:f,existRawValues:h}},_=tl(I,g,{fieldNames:u,treeNodeFilterProp:ne(e,"treeNodeFilterProp"),filterTreeNode:ne(e,"filterTreeNode")}),A=s=>{if(s){if(e.treeNodeLabelProp)return s[e.treeNodeLabelProp];const{_title:f}=u.value;for(let h=0;h<f.length;h+=1){const x=s[f[h]];if(x!==void 0)return x}}},F=s=>Ht(s).map(h=>ll(h)?{value:h}:h),d=s=>F(s).map(h=>{let{label:x}=h;const{value:P,halfChecked:L}=h;let C;const T=k.value.get(P);return T&&(x=x!=null?x:A(T.node),C=T.node.disabled),{label:x,value:P,halfChecked:L,disabled:C}}),[b,E]=Ge(e.defaultValue,{value:ne(e,"value")}),K=y(()=>F(b.value)),N=R([]),W=R([]);$e(()=>{const s=[],f=[];K.value.forEach(h=>{h.halfChecked?f.push(h):s.push(h)}),N.value=s,W.value=f});const G=y(()=>N.value.map(s=>s.value)),{maxLevel:j,levelEntities:le}=yt(w),[J,oe]=el(N,W,o,w,j,le),re=y(()=>{const h=Xe(J.value,e.showCheckedStrategy,w.value,u.value).map(L=>{var C,T,M;return(M=(T=(C=w.value[L])===null||C===void 0?void 0:C.node)===null||T===void 0?void 0:T[u.value.value])!==null&&M!==void 0?M:L}).map(L=>{const C=N.value.find(T=>T.value===L);return{value:L,label:C==null?void 0:C.label}}),x=d(h),P=x[0];return!r.value&&P&&Je(P.value)&&Je(P.label)?[]:x.map(L=>{var C;return p(p({},L),{label:(C=L.label)!==null&&C!==void 0?C:L.value})})}),[se]=Zt(re),ae=(s,f,h)=>{const x=d(s);if(E(x),e.autoClearSearchValue&&V(""),e.onChange){let P=s;o.value&&(P=Xe(s,e.showCheckedStrategy,w.value,u.value).map(ee=>{const me=k.value.get(ee);return me?me.node[u.value.value]:ee}));const{triggerValue:L,selected:C}=f||{triggerValue:void 0,selected:void 0};let T=P;if(e.treeCheckStrictly){const B=W.value.filter(ee=>!P.includes(ee.value));T=[...T,...B]}const M=d(T),Z={preValue:N.value,triggerValue:L};let q=!0;(e.treeCheckStrictly||h==="selection"&&!C)&&(q=!1),Xt(Z,L,s,I.value,q,u.value),c.value?Z.checked=C:Z.selected=C;const $=v.value?M:M.map(B=>B.value);e.onChange(r.value?$:$[0],v.value?null:M.map(B=>B.label),Z)}},X=(s,f)=>{let{selected:h,source:x}=f;var P,L,C;const T=te(w.value),M=te(k.value),Z=T[s],q=Z==null?void 0:Z.node,$=(P=q==null?void 0:q[u.value.value])!==null&&P!==void 0?P:s;if(!r.value)ae([$],{selected:!0,triggerValue:$},"option");else{let B=h?[...G.value,$]:J.value.filter(ee=>ee!==$);if(o.value){const{missingRawValues:ee,existRawValues:me}=O(B),We=me.map(Ke=>M.get(Ke).key);let Pe;h?{checkedKeys:Pe}=He(We,!0,T,j.value,le.value):{checkedKeys:Pe}=He(We,{halfCheckedKeys:oe.value},T,j.value,le.value),B=[...ee,...Pe.map(Ke=>T[Ke].node[u.value.value])]}ae(B,{selected:h,triggerValue:$},x||"option")}h||!r.value?(L=e.onSelect)===null||L===void 0||L.call(e,$,Re(q)):(C=e.onDeselect)===null||C===void 0||C.call(e,$,Re(q))},ve=s=>{if(e.onDropdownVisibleChange){const f={};Object.defineProperty(f,"documentClickClose",{get(){return!1}}),e.onDropdownVisibleChange(s,f)}},fe=(s,f)=>{const h=s.map(x=>x.value);if(f.type==="clear"){ae(h,{},"selection");return}f.values.length&&X(f.values[0].value,{selected:!1,source:"selection"})},{treeNodeFilterProp:he,loadData:ie,treeLoadedKeys:m,onTreeLoad:D,treeDefaultExpandAll:H,treeExpandedKeys:Y,treeDefaultExpandedKeys:Ce,onTreeExpand:Se,virtual:we,listHeight:xe,listItemHeight:Ie,treeLine:Ve,treeIcon:pe,showTreeIcon:ke,switcherIcon:Te,treeMotion:Ne,customSlots:Le,dropdownMatchSelectWidth:Ee,treeExpandAction:De}=Pt(e);It(ze({checkable:c,loadData:ie,treeLoadedKeys:m,onTreeLoad:D,checkedKeys:J,halfCheckedKeys:oe,treeDefaultExpandAll:H,treeExpandedKeys:Y,treeDefaultExpandedKeys:Ce,onTreeExpand:Se,treeIcon:pe,treeMotion:Ne,showTreeIcon:ke,switcherIcon:Te,treeLine:Ve,treeNodeFilterProp:he,keyEntities:w,customSlots:Le})),jt(ze({virtual:we,listHeight:xe,listItemHeight:Ie,treeData:_,fieldNames:u,onSelect:X,dropdownMatchSelectWidth:Ee,treeExpandAction:De}));const Q=ge();return t({focus(){var s;(s=Q.value)===null||s===void 0||s.focus()},blur(){var s;(s=Q.value)===null||s===void 0||s.blur()},scrollTo(s){var f;(f=Q.value)===null||f===void 0||f.scrollTo(s)}}),()=>{var s;const f=be(e,["id","prefixCls","customSlots","value","defaultValue","onChange","onSelect","onDeselect","searchValue","inputValue","onSearch","autoClearSearchValue","filterTreeNode","treeNodeFilterProp","showCheckedStrategy","treeNodeLabelProp","multiple","treeCheckable","treeCheckStrictly","labelInValue","fieldNames","treeDataSimpleMode","treeData","children","loadData","treeLoadedKeys","onTreeLoad","treeDefaultExpandAll","treeExpandedKeys","treeDefaultExpandedKeys","onTreeExpand","virtual","listHeight","listItemHeight","onDropdownVisibleChange","treeLine","treeIcon","showTreeIcon","switcherIcon","treeMotion"]);return z(Vt,U(U(U({ref:Q},a),f),{},{id:n,prefixCls:e.prefixCls,mode:r.value?"multiple":void 0,displayValues:se.value,onDisplayValuesChange:fe,searchValue:g.value,onSearch:S,OptionList:Wt,emptyOptions:!I.value.length,onDropdownVisibleChange:ve,tagRender:e.tagRender||l.tagRender,dropdownMatchSelectWidth:(s=e.dropdownMatchSelectWidth)!==null&&s!==void 0?s:!0}),l)}}}),nl=e=>{const{componentCls:i,treePrefixCls:a,colorBgElevated:t}=e,l=`.${a}`;return[{[`${i}-dropdown`]:[{padding:`${e.paddingXS}px ${e.paddingXS/2}px`},bt(a,qe(e,{colorBgContainer:t})),{[l]:{borderRadius:0,"&-list-holder-inner":{alignItems:"stretch",[`${l}-treenode`]:{[`${l}-node-content-wrapper`]:{flex:"auto"}}}}},Ft(`${a}-checkbox`,e),{"&-rtl":{direction:"rtl",[`${l}-switcher${l}-switcher_close`]:{[`${l}-switcher-icon svg`]:{transform:"rotate(90deg)"}}}}]}]};function ol(e,i){return ot("TreeSelect",a=>{const t=qe(a,{treePrefixCls:i.value});return[nl(t)]})(e)}const Ye=(e,i,a)=>a!==void 0?a:`${e}-${i}`;function rl(){return p(p({},be(lt(),["showTreeIcon","treeMotion","inputIcon","getInputElement","treeLine","customSlots"])),{suffixIcon:ue.any,size:Ae(),bordered:it(),treeLine:st([Boolean,Object]),replaceFields:rt(),placement:Ae(),status:Ae(),popupClassName:String,dropdownClassName:String,"onUpdate:value":_e(),"onUpdate:treeExpandedKeys":_e(),"onUpdate:searchValue":_e()})}const Fe=je({compatConfig:{MODE:3},name:"ATreeSelect",inheritAttrs:!1,props:Ze(rl(),{choiceTransitionName:"",listHeight:256,treeIcon:!1,listItemHeight:26,bordered:!0}),slots:Object,setup(e,i){let{attrs:a,slots:t,expose:l,emit:n}=i;ct(!(e.treeData===void 0&&t.default)),Oe(e.multiple!==!1||!e.treeCheckable,"TreeSelect","`multiple` will always be `true` when `treeCheckable` is true"),Oe(e.replaceFields===void 0,"TreeSelect","`replaceFields` is deprecated, please use fieldNames instead"),Oe(!e.dropdownClassName,"TreeSelect","`dropdownClassName` is deprecated. Please use `popupClassName` instead.");const o=Kt(),c=_t.useInject(),v=y(()=>At(c.status,e.status)),{prefixCls:r,renderEmpty:u,direction:g,virtual:V,dropdownMatchSelectWidth:S,size:I,getPopupContainer:w,getPrefixCls:k,disabled:O}=ut("select",e),{compactSize:_,compactItemClassnames:A}=dt(r,g),F=y(()=>_.value||I.value),d=vt(),b=y(()=>{var m;return(m=O.value)!==null&&m!==void 0?m:d.value}),E=y(()=>k()),K=y(()=>e.placement!==void 0?e.placement:g.value==="rtl"?"bottomRight":"bottomLeft"),N=y(()=>Ye(E.value,ft(K.value),e.transitionName)),W=y(()=>Ye(E.value,"",e.choiceTransitionName)),G=y(()=>k("select-tree",e.prefixCls)),j=y(()=>k("tree-select",e.prefixCls)),[le,J]=Tt(r),[oe]=ol(j,G),re=y(()=>Ue(e.popupClassName||e.dropdownClassName,`${j.value}-dropdown`,{[`${j.value}-dropdown-rtl`]:g.value==="rtl"},J.value)),se=y(()=>!!(e.treeCheckable||e.multiple)),ae=y(()=>e.showArrow!==void 0?e.showArrow:e.loading||!se.value),X=ge();l({focus(){var m,D;(D=(m=X.value).focus)===null||D===void 0||D.call(m)},blur(){var m,D;(D=(m=X.value).blur)===null||D===void 0||D.call(m)}});const ve=function(){for(var m=arguments.length,D=new Array(m),H=0;H<m;H++)D[H]=arguments[H];n("update:value",D[0]),n("change",...D),o.onFieldChange()},fe=m=>{n("update:treeExpandedKeys",m),n("treeExpand",m)},he=m=>{n("update:searchValue",m),n("search",m)},ie=m=>{n("blur",m),o.onFieldBlur()};return()=>{var m,D,H;const{notFoundContent:Y=(m=t.notFoundContent)===null||m===void 0?void 0:m.call(t),prefixCls:Ce,bordered:Se,listHeight:we,listItemHeight:xe,multiple:Ie,treeIcon:Ve,treeLine:pe,showArrow:ke,switcherIcon:Te=(D=t.switcherIcon)===null||D===void 0?void 0:D.call(t),fieldNames:Ne=e.replaceFields,id:Le=o.id.value,placeholder:Ee=(H=t.placeholder)===null||H===void 0?void 0:H.call(t)}=e,{isFormItemInput:De,hasFeedback:Q,feedbackIcon:s}=c,{suffixIcon:f,removeIcon:h,clearIcon:x}=Nt(p(p({},e),{multiple:se.value,showArrow:ae.value,hasFeedback:Q,feedbackIcon:s,prefixCls:r.value}),t);let P;Y!==void 0?P=Y:P=u("Select");const L=be(e,["suffixIcon","itemIcon","removeIcon","clearIcon","switcherIcon","bordered","status","onUpdate:value","onUpdate:treeExpandedKeys","onUpdate:searchValue"]),C=Ue(!Ce&&j.value,{[`${r.value}-lg`]:F.value==="large",[`${r.value}-sm`]:F.value==="small",[`${r.value}-rtl`]:g.value==="rtl",[`${r.value}-borderless`]:!Se,[`${r.value}-in-form-item`]:De},Ot(r.value,v.value,Q),A.value,a.class,J.value),T={};return e.treeData===void 0&&t.default&&(T.children=ht(t.default())),le(oe(z(al,U(U(U(U({},a),L),{},{disabled:b.value,virtual:V.value,dropdownMatchSelectWidth:S.value,id:Le,fieldNames:Ne,ref:X,prefixCls:r.value,class:C,listHeight:we,listItemHeight:xe,treeLine:!!pe,inputIcon:f,multiple:Ie,removeIcon:h,clearIcon:x,switcherIcon:M=>Ct(G.value,Te,M,t.leafIcon,pe),showTreeIcon:Ve,notFoundContent:P,getPopupContainer:w==null?void 0:w.value,treeMotion:null,dropdownClassName:re.value,choiceTransitionName:W.value,onChange:ve,onBlur:ie,onSearch:he,onTreeExpand:fe},T),{},{transitionName:N.value,customSlots:p(p({},t),{treeCheckable:()=>z("span",{class:`${r.value}-tree-checkbox-inner`},null)}),maxTagPlaceholder:e.maxTagPlaceholder||t.maxTagPlaceholder,placement:K.value,showArrow:Q||ke,placeholder:Ee}),p(p({},t),{treeCheckable:()=>z("span",{class:`${r.value}-tree-checkbox-inner`},null)}))))}}}),Qe=de,Tl=p(Fe,{TreeNode:de,SHOW_ALL:Ut,SHOW_PARENT:tt,SHOW_CHILD:Be,install:e=>(e.component(Fe.name,Fe),e.component(Qe.displayName,Qe),e)});export{Qe as TreeSelectNode,Tl as default,rl as treeSelectProps};
