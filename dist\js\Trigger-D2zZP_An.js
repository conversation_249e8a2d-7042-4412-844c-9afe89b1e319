import{P as O,_ as P,ad as fe,Y as Ct,b as $,f as ce,j as xt,z,ae as I,af as Be,ag as pe,ah as Ve,ai as $t,V as at,aj as Fe,ak as wt,al as de,am as Le,U as Ie,an as Ht,ao as Rt,A as We,B as he,ap as U,X as ze,Z as Xe,aq as Bt,a7 as G,p as Ye,O as je,ar as Ue,as as Vt,at as Ge,au as ke,av as Ke,q as et,aw as qe}from"./bootstrap-CFDAkNgp.js";import{x as C,aA as ve,bw as Ze,a4 as J,P as W,a5 as x,Y as V,a9 as ge,az as Je,J as K,n as Tt,p as Qe,ax as tn,_ as en,F as nn,aF as on,R as rn}from"../jse/index-index-B2UBupFX.js";import{i as sn}from"./ResizeObserver.es-CDE7jhPe.js";function an(){return""}function un(t){return t?t.ownerDocument:window.document}function me(){}const ln=()=>({action:O.oneOfType([O.string,O.arrayOf(O.string)]).def([]),showAction:O.any.def([]),hideAction:O.any.def([]),getPopupClassNameFromAlign:O.any.def(an),onPopupVisibleChange:Function,afterPopupVisibleChange:O.func.def(me),popup:O.any,arrow:O.bool.def(!0),popupStyle:{type:Object,default:void 0},prefixCls:O.string.def("rc-trigger-popup"),popupClassName:O.string.def(""),popupPlacement:String,builtinPlacements:O.object,popupTransitionName:String,popupAnimation:O.any,mouseEnterDelay:O.number.def(0),mouseLeaveDelay:O.number.def(.1),zIndex:Number,focusDelay:O.number.def(0),blurDelay:O.number.def(.15),getPopupContainer:Function,getDocument:O.func.def(un),forceRender:{type:Boolean,default:void 0},destroyPopupOnHide:{type:Boolean,default:!1},mask:{type:Boolean,default:!1},maskClosable:{type:Boolean,default:!0},popupAlign:O.object.def(()=>({})),popupVisible:{type:Boolean,default:void 0},defaultPopupVisible:{type:Boolean,default:!1},maskTransitionName:String,maskAnimation:String,stretch:String,alignPoint:{type:Boolean,default:void 0},autoDestroy:{type:Boolean,default:!1},mobile:Object,getTriggerDOMNode:Function}),Mt={visible:Boolean,prefixCls:String,zIndex:Number,destroyPopupOnHide:Boolean,forceRender:Boolean,arrow:{type:Boolean,default:!0},animation:[String,Object],transitionName:String,stretch:{type:String},align:{type:Object},point:{type:Object},getRootDomNode:{type:Function},getClassNameFromAlign:{type:Function},onAlign:{type:Function},onMouseenter:{type:Function},onMouseleave:{type:Function},onMousedown:{type:Function},onTouchstart:{type:Function}},fn=P(P({},Mt),{mobile:{type:Object}}),cn=P(P({},Mt),{mask:Boolean,mobile:{type:Object},maskAnimation:String,maskTransitionName:String});function ye(t){const{prefixCls:e,visible:n,zIndex:i,mask:o,maskAnimation:r,maskTransitionName:s}=t;if(!o)return null;let a={};return(s||r)&&(a=fe({prefixCls:e,transitionName:s,animation:r})),C(Ct,$({appear:!0},a),{default:()=>[ve(C("div",{style:{zIndex:i},class:`${e}-mask`},null),[[Ze("if"),n]])]})}ye.displayName="Mask";const pn=J({compatConfig:{MODE:3},name:"MobilePopupInner",inheritAttrs:!1,props:fn,emits:["mouseenter","mouseleave","mousedown","touchstart","align"],setup(t,e){let{expose:n,slots:i}=e;const o=W();return n({forceAlign:()=>{},getElement:()=>o.value}),()=>{var r;const{zIndex:s,visible:a,prefixCls:l,mobile:{popupClassName:u,popupStyle:h,popupMotion:p={},popupRender:c}={}}=t,f=P({zIndex:s},h);let d=ce((r=i.default)===null||r===void 0?void 0:r.call(i));d.length>1&&(d=C("div",{class:`${l}-content`},[d])),c&&(d=c(d));const g=xt(l,u);return C(Ct,$({ref:o},p),{default:()=>[a?C("div",{class:g,style:f},[d]):null]})}}});var dn=function(t,e,n,i){function o(r){return r instanceof n?r:new n(function(s){s(r)})}return new(n||(n=Promise))(function(r,s){function a(h){try{u(i.next(h))}catch(p){s(p)}}function l(h){try{u(i.throw(h))}catch(p){s(p)}}function u(h){h.done?r(h.value):o(h.value).then(a,l)}u((i=i.apply(t,e||[])).next())})};const Ft=["measure","align",null,"motion"],hn=(t,e)=>{const n=x(null),i=x(),o=x(!1);function r(l){o.value||(n.value=l)}function s(){z.cancel(i.value)}function a(l){s(),i.value=z(()=>{let u=n.value;switch(n.value){case"align":u="motion";break;case"motion":u="stable";break}r(u),l==null||l()})}return V(t,()=>{r("measure")},{immediate:!0,flush:"post"}),ge(()=>{V(n,()=>{switch(n.value){case"measure":e();break}n.value&&(i.value=z(()=>dn(void 0,void 0,void 0,function*(){const l=Ft.indexOf(n.value),u=Ft[l+1];u&&l!==-1&&r(u)})))},{immediate:!0,flush:"post"})}),Je(()=>{o.value=!0,s()}),[n,a]},vn=t=>{const e=x({width:0,height:0});function n(o){e.value={width:o.offsetWidth,height:o.offsetHeight}}return[K(()=>{const o={};if(t.value){const{width:r,height:s}=e.value;t.value.indexOf("height")!==-1&&s?o.height=`${s}px`:t.value.indexOf("minHeight")!==-1&&s&&(o.minHeight=`${s}px`),t.value.indexOf("width")!==-1&&r?o.width=`${r}px`:t.value.indexOf("minWidth")!==-1&&r&&(o.minWidth=`${r}px`)}return o}),n]};function Lt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(o){return Object.getOwnPropertyDescriptor(t,o).enumerable})),n.push.apply(n,i)}return n}function It(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Lt(Object(n),!0).forEach(function(i){gn(t,i,n[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Lt(Object(n)).forEach(function(i){Object.defineProperty(t,i,Object.getOwnPropertyDescriptor(n,i))})}return t}function bt(t){"@babel/helpers - typeof";return bt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},bt(t)}function gn(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var j,mn={Webkit:"-webkit-",Moz:"-moz-",ms:"-ms-",O:"-o-"};function ut(){if(j!==void 0)return j;j="";var t=document.createElement("p").style,e="Transform";for(var n in mn)n+e in t&&(j=n);return j}function we(){return ut()?"".concat(ut(),"TransitionProperty"):"transitionProperty"}function ft(){return ut()?"".concat(ut(),"Transform"):"transform"}function Wt(t,e){var n=we();n&&(t.style[n]=e,n!=="transitionProperty"&&(t.style.transitionProperty=e))}function ht(t,e){var n=ft();n&&(t.style[n]=e,n!=="transform"&&(t.style.transform=e))}function yn(t){return t.style.transitionProperty||t.style[we()]}function wn(t){var e=window.getComputedStyle(t,null),n=e.getPropertyValue("transform")||e.getPropertyValue(ft());if(n&&n!=="none"){var i=n.replace(/[^0-9\-.,]/g,"").split(",");return{x:parseFloat(i[12]||i[4],0),y:parseFloat(i[13]||i[5],0)}}return{x:0,y:0}}var Tn=/matrix\((.*)\)/,bn=/matrix3d\((.*)\)/;function Pn(t,e){var n=window.getComputedStyle(t,null),i=n.getPropertyValue("transform")||n.getPropertyValue(ft());if(i&&i!=="none"){var o,r=i.match(Tn);if(r)r=r[1],o=r.split(",").map(function(a){return parseFloat(a,10)}),o[4]=e.x,o[5]=e.y,ht(t,"matrix(".concat(o.join(","),")"));else{var s=i.match(bn)[1];o=s.split(",").map(function(a){return parseFloat(a,10)}),o[12]=e.x,o[13]=e.y,ht(t,"matrix3d(".concat(o.join(","),")"))}}else ht(t,"translateX(".concat(e.x,"px) translateY(").concat(e.y,"px) translateZ(0)"))}var On=/[\-+]?(?:\d*\.|)\d+(?:[eE][\-+]?\d+|)/.source,Q;function zt(t){var e=t.style.display;t.style.display="none",t.offsetHeight,t.style.display=e}function X(t,e,n){var i=n;if(bt(e)==="object"){for(var o in e)e.hasOwnProperty(o)&&X(t,o,e[o]);return}if(typeof i!="undefined"){typeof i=="number"&&(i="".concat(i,"px")),t.style[e]=i;return}return Q(t,e)}function _n(t){var e,n,i,o=t.ownerDocument,r=o.body,s=o&&o.documentElement;return e=t.getBoundingClientRect(),n=Math.floor(e.left),i=Math.floor(e.top),n-=s.clientLeft||r.clientLeft||0,i-=s.clientTop||r.clientTop||0,{left:n,top:i}}function Te(t,e){var n=t["page".concat(e?"Y":"X","Offset")],i="scroll".concat(e?"Top":"Left");if(typeof n!="number"){var o=t.document;n=o.documentElement[i],typeof n!="number"&&(n=o.body[i])}return n}function be(t){return Te(t)}function Pe(t){return Te(t,!0)}function q(t){var e=_n(t),n=t.ownerDocument,i=n.defaultView||n.parentWindow;return e.left+=be(i),e.top+=Pe(i),e}function St(t){return t!=null&&t==t.window}function Oe(t){return St(t)?t.document:t.nodeType===9?t:t.ownerDocument}function Cn(t,e,n){var i=n,o="",r=Oe(t);return i=i||r.defaultView.getComputedStyle(t,null),i&&(o=i.getPropertyValue(e)||i[e]),o}var xn=new RegExp("^(".concat(On,")(?!px)[a-z%]+$"),"i"),Mn=/^(top|right|bottom|left)$/,vt="currentStyle",gt="runtimeStyle",B="left",Sn="px";function An(t,e){var n=t[vt]&&t[vt][e];if(xn.test(n)&&!Mn.test(e)){var i=t.style,o=i[B],r=t[gt][B];t[gt][B]=t[vt][B],i[B]=e==="fontSize"?"1em":n||0,n=i.pixelLeft+Sn,i[B]=o,t[gt][B]=r}return n===""?"auto":n}typeof window!="undefined"&&(Q=window.getComputedStyle?Cn:An);function nt(t,e){return t==="left"?e.useCssRight?"right":t:e.useCssBottom?"bottom":t}function Xt(t){if(t==="left")return"right";if(t==="right")return"left";if(t==="top")return"bottom";if(t==="bottom")return"top"}function Yt(t,e,n){X(t,"position")==="static"&&(t.style.position="relative");var i=-999,o=-999,r=nt("left",n),s=nt("top",n),a=Xt(r),l=Xt(s);r!=="left"&&(i=999),s!=="top"&&(o=999);var u="",h=q(t);("left"in e||"top"in e)&&(u=yn(t)||"",Wt(t,"none")),"left"in e&&(t.style[a]="",t.style[r]="".concat(i,"px")),"top"in e&&(t.style[l]="",t.style[s]="".concat(o,"px")),zt(t);var p=q(t),c={};for(var f in e)if(e.hasOwnProperty(f)){var d=nt(f,n),g=f==="left"?i:o,w=h[f]-p[f];d===f?c[d]=g+w:c[d]=g-w}X(t,c),zt(t),("left"in e||"top"in e)&&Wt(t,u);var m={};for(var y in e)if(e.hasOwnProperty(y)){var b=nt(y,n),_=e[y]-h[y];y===b?m[b]=c[b]+_:m[b]=c[b]-_}X(t,m)}function Dn(t,e){var n=q(t),i=wn(t),o={x:i.x,y:i.y};"left"in e&&(o.x=i.x+e.left-n.left),"top"in e&&(o.y=i.y+e.top-n.top),Pn(t,o)}function En(t,e,n){if(n.ignoreShake){var i=q(t),o=i.left.toFixed(0),r=i.top.toFixed(0),s=e.left.toFixed(0),a=e.top.toFixed(0);if(o===s&&r===a)return}n.useCssRight||n.useCssBottom?Yt(t,e,n):n.useCssTransform&&ft()in document.body.style?Dn(t,e):Yt(t,e,n)}function At(t,e){for(var n=0;n<t.length;n++)e(t[n])}function _e(t){return Q(t,"boxSizing")==="border-box"}var Nn=["margin","border","padding"],Pt=-1,$n=2,Ot=1,Hn=0;function Rn(t,e,n){var i={},o=t.style,r;for(r in e)e.hasOwnProperty(r)&&(i[r]=o[r],o[r]=e[r]);n.call(t);for(r in e)e.hasOwnProperty(r)&&(o[r]=i[r])}function k(t,e,n){var i=0,o,r,s;for(r=0;r<e.length;r++)if(o=e[r],o)for(s=0;s<n.length;s++){var a=void 0;o==="border"?a="".concat(o).concat(n[s],"Width"):a=o+n[s],i+=parseFloat(Q(t,a))||0}return i}var D={getParent:function(e){var n=e;do n.nodeType===11&&n.host?n=n.host:n=n.parentNode;while(n&&n.nodeType!==1&&n.nodeType!==9);return n}};At(["Width","Height"],function(t){D["doc".concat(t)]=function(e){var n=e.document;return Math.max(n.documentElement["scroll".concat(t)],n.body["scroll".concat(t)],D["viewport".concat(t)](n))},D["viewport".concat(t)]=function(e){var n="client".concat(t),i=e.document,o=i.body,r=i.documentElement,s=r[n];return i.compatMode==="CSS1Compat"&&s||o&&o[n]||s}});function jt(t,e,n){var i=n;if(St(t))return e==="width"?D.viewportWidth(t):D.viewportHeight(t);if(t.nodeType===9)return e==="width"?D.docWidth(t):D.docHeight(t);var o=e==="width"?["Left","Right"]:["Top","Bottom"],r=Math.floor(e==="width"?t.getBoundingClientRect().width:t.getBoundingClientRect().height),s=_e(t),a=0;(r==null||r<=0)&&(r=void 0,a=Q(t,e),(a==null||Number(a)<0)&&(a=t.style[e]||0),a=Math.floor(parseFloat(a))||0),i===void 0&&(i=s?Ot:Pt);var l=r!==void 0||s,u=r||a;return i===Pt?l?u-k(t,["border","padding"],o):a:l?i===Ot?u:u+(i===$n?-k(t,["border"],o):k(t,["margin"],o)):a+k(t,Nn.slice(i),o)}var Bn={position:"absolute",visibility:"hidden",display:"block"};function Ut(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var i,o=e[0];return o.offsetWidth!==0?i=jt.apply(void 0,e):Rn(o,Bn,function(){i=jt.apply(void 0,e)}),i}At(["width","height"],function(t){var e=t.charAt(0).toUpperCase()+t.slice(1);D["outer".concat(e)]=function(i,o){return i&&Ut(i,t,o?Hn:Ot)};var n=t==="width"?["Left","Right"]:["Top","Bottom"];D[t]=function(i,o){var r=o;if(r!==void 0){if(i){var s=_e(i);return s&&(r+=k(i,["padding","border"],n)),X(i,t,r)}return}return i&&Ut(i,t,Pt)}});function Ce(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}var v={getWindow:function(e){if(e&&e.document&&e.setTimeout)return e;var n=e.ownerDocument||e;return n.defaultView||n.parentWindow},getDocument:Oe,offset:function(e,n,i){if(typeof n!="undefined")En(e,n,i||{});else return q(e)},isWindow:St,each:At,css:X,clone:function(e){var n,i={};for(n in e)e.hasOwnProperty(n)&&(i[n]=e[n]);var o=e.overflow;if(o)for(n in e)e.hasOwnProperty(n)&&(i.overflow[n]=e.overflow[n]);return i},mix:Ce,getWindowScrollLeft:function(e){return be(e)},getWindowScrollTop:function(e){return Pe(e)},merge:function(){for(var e={},n=0;n<arguments.length;n++)v.mix(e,n<0||arguments.length<=n?void 0:arguments[n]);return e},viewportWidth:0,viewportHeight:0};Ce(v,D);var mt=v.getParent;function _t(t){if(v.isWindow(t)||t.nodeType===9)return null;var e=v.getDocument(t),n=e.body,i,o=v.css(t,"position"),r=o==="fixed"||o==="absolute";if(!r)return t.nodeName.toLowerCase()==="html"?null:mt(t);for(i=mt(t);i&&i!==n&&i.nodeType!==9;i=mt(i))if(o=v.css(i,"position"),o!=="static")return i;return null}var Gt=v.getParent;function Vn(t){if(v.isWindow(t)||t.nodeType===9)return!1;var e=v.getDocument(t),n=e.body,i=null;for(i=Gt(t);i&&i!==n&&i!==e;i=Gt(i)){var o=v.css(i,"position");if(o==="fixed")return!0}return!1}function Dt(t,e){for(var n={left:0,right:1/0,top:0,bottom:1/0},i=_t(t),o=v.getDocument(t),r=o.defaultView||o.parentWindow,s=o.body,a=o.documentElement;i;){if((navigator.userAgent.indexOf("MSIE")===-1||i.clientWidth!==0)&&i!==s&&i!==a&&v.css(i,"overflow")!=="visible"){var l=v.offset(i);l.left+=i.clientLeft,l.top+=i.clientTop,n.top=Math.max(n.top,l.top),n.right=Math.min(n.right,l.left+i.clientWidth),n.bottom=Math.min(n.bottom,l.top+i.clientHeight),n.left=Math.max(n.left,l.left)}else if(i===s||i===a)break;i=_t(i)}var u=null;if(!v.isWindow(t)&&t.nodeType!==9){u=t.style.position;var h=v.css(t,"position");h==="absolute"&&(t.style.position="fixed")}var p=v.getWindowScrollLeft(r),c=v.getWindowScrollTop(r),f=v.viewportWidth(r),d=v.viewportHeight(r),g=a.scrollWidth,w=a.scrollHeight,m=window.getComputedStyle(s);if(m.overflowX==="hidden"&&(g=r.innerWidth),m.overflowY==="hidden"&&(w=r.innerHeight),t.style&&(t.style.position=u),e||Vn(t))n.left=Math.max(n.left,p),n.top=Math.max(n.top,c),n.right=Math.min(n.right,p+f),n.bottom=Math.min(n.bottom,c+d);else{var y=Math.max(g,p+f);n.right=Math.min(n.right,y);var b=Math.max(w,c+d);n.bottom=Math.min(n.bottom,b)}return n.top>=0&&n.left>=0&&n.bottom>n.top&&n.right>n.left?n:null}function Fn(t,e,n,i){var o=v.clone(t),r={width:e.width,height:e.height};return i.adjustX&&o.left<n.left&&(o.left=n.left),i.resizeWidth&&o.left>=n.left&&o.left+r.width>n.right&&(r.width-=o.left+r.width-n.right),i.adjustX&&o.left+r.width>n.right&&(o.left=Math.max(n.right-r.width,n.left)),i.adjustY&&o.top<n.top&&(o.top=n.top),i.resizeHeight&&o.top>=n.top&&o.top+r.height>n.bottom&&(r.height-=o.top+r.height-n.bottom),i.adjustY&&o.top+r.height>n.bottom&&(o.top=Math.max(n.bottom-r.height,n.top)),v.mix(o,r)}function Et(t){var e,n,i;if(!v.isWindow(t)&&t.nodeType!==9)e=v.offset(t),n=v.outerWidth(t),i=v.outerHeight(t);else{var o=v.getWindow(t);e={left:v.getWindowScrollLeft(o),top:v.getWindowScrollTop(o)},n=v.viewportWidth(o),i=v.viewportHeight(o)}return e.width=n,e.height=i,e}function kt(t,e){var n=e.charAt(0),i=e.charAt(1),o=t.width,r=t.height,s=t.left,a=t.top;return n==="c"?a+=r/2:n==="b"&&(a+=r),i==="c"?s+=o/2:i==="r"&&(s+=o),{left:s,top:a}}function it(t,e,n,i,o){var r=kt(e,n[1]),s=kt(t,n[0]),a=[s.left-r.left,s.top-r.top];return{left:Math.round(t.left-a[0]+i[0]-o[0]),top:Math.round(t.top-a[1]+i[1]-o[1])}}function Kt(t,e,n){return t.left<n.left||t.left+e.width>n.right}function qt(t,e,n){return t.top<n.top||t.top+e.height>n.bottom}function Ln(t,e,n){return t.left>n.right||t.left+e.width<n.left}function In(t,e,n){return t.top>n.bottom||t.top+e.height<n.top}function ot(t,e,n){var i=[];return v.each(t,function(o){i.push(o.replace(e,function(r){return n[r]}))}),i}function rt(t,e){return t[e]=-t[e],t}function Zt(t,e){var n;return/%$/.test(t)?n=parseInt(t.substring(0,t.length-1),10)/100*e:n=parseInt(t,10),n||0}function Jt(t,e){t[0]=Zt(t[0],e.width),t[1]=Zt(t[1],e.height)}function xe(t,e,n,i){var o=n.points,r=n.offset||[0,0],s=n.targetOffset||[0,0],a=n.overflow,l=n.source||t;r=[].concat(r),s=[].concat(s),a=a||{};var u={},h=0,p=!!(a&&a.alwaysByViewport),c=Dt(l,p),f=Et(l);Jt(r,f),Jt(s,e);var d=it(f,e,o,r,s),g=v.merge(f,d);if(c&&(a.adjustX||a.adjustY)&&i){if(a.adjustX&&Kt(d,f,c)){var w=ot(o,/[lr]/gi,{l:"r",r:"l"}),m=rt(r,0),y=rt(s,0),b=it(f,e,w,m,y);Ln(b,f,c)||(h=1,o=w,r=m,s=y)}if(a.adjustY&&qt(d,f,c)){var _=ot(o,/[tb]/gi,{t:"b",b:"t"}),A=rt(r,1),T=rt(s,1),E=it(f,e,_,A,T);In(E,f,c)||(h=1,o=_,r=A,s=T)}h&&(d=it(f,e,o,r,s),v.mix(g,d));var M=Kt(d,f,c),S=qt(d,f,c);if(M||S){var R=o;M&&(R=ot(o,/[lr]/gi,{l:"r",r:"l"})),S&&(R=ot(o,/[tb]/gi,{t:"b",b:"t"})),o=R,r=n.offset||[0,0],s=n.targetOffset||[0,0]}u.adjustX=a.adjustX&&M,u.adjustY=a.adjustY&&S,(u.adjustX||u.adjustY)&&(g=Fn(d,f,c,u))}return g.width!==f.width&&v.css(l,"width",v.width(l)+g.width-f.width),g.height!==f.height&&v.css(l,"height",v.height(l)+g.height-f.height),v.offset(l,{left:g.left,top:g.top},{useCssRight:n.useCssRight,useCssBottom:n.useCssBottom,useCssTransform:n.useCssTransform,ignoreShake:n.ignoreShake}),{points:o,offset:r,targetOffset:s,overflow:u}}function Wn(t,e){var n=Dt(t,e),i=Et(t);return!n||i.left+i.width<=n.left||i.top+i.height<=n.top||i.left>=n.right||i.top>=n.bottom}function Nt(t,e,n){var i=n.target||e,o=Et(i),r=!Wn(i,n.overflow&&n.overflow.alwaysByViewport);return xe(t,o,n,r)}Nt.__getOffsetParent=_t;Nt.__getVisibleRectForElement=Dt;function zn(t,e,n){var i,o,r=v.getDocument(t),s=r.defaultView||r.parentWindow,a=v.getWindowScrollLeft(s),l=v.getWindowScrollTop(s),u=v.viewportWidth(s),h=v.viewportHeight(s);"pageX"in e?i=e.pageX:i=a+e.clientX,"pageY"in e?o=e.pageY:o=l+e.clientY;var p={left:i,top:o,width:0,height:0},c=i>=0&&i<=a+u&&o>=0&&o<=l+h,f=[n.points[0],"cc"];return xe(t,p,It(It({},n),{},{points:f}),c)}function Xn(t,e){return t===e?!0:!t||!e?!1:"pageX"in e&&"pageY"in e?t.pageX===e.pageX&&t.pageY===e.pageY:"clientX"in e&&"clientY"in e?t.clientX===e.clientX&&t.clientY===e.clientY:!1}function Yn(t,e){t!==document.activeElement&&I(e,t)&&typeof t.focus=="function"&&t.focus()}function Qt(t,e){let n=null,i=null;function o(s){let[{target:a}]=s;if(!document.documentElement.contains(a))return;const{width:l,height:u}=a.getBoundingClientRect(),h=Math.floor(l),p=Math.floor(u);(n!==h||i!==p)&&Promise.resolve().then(()=>{e({width:h,height:p})}),n=h,i=p}const r=new sn(o);return t&&r.observe(t),()=>{r.disconnect()}}const jn=(t,e)=>{let n=!1,i=null;function o(){clearTimeout(i)}function r(s){if(!n||s===!0){if(t()===!1)return;n=!0,o(),i=setTimeout(()=>{n=!1},e.value)}else o(),i=setTimeout(()=>{n=!1,r()},e.value)}return[r,()=>{n=!1,o()}]};function Un(){this.__data__=[],this.size=0}function Me(t,e){return t===e||t!==t&&e!==e}function ct(t,e){for(var n=t.length;n--;)if(Me(t[n][0],e))return n;return-1}var Gn=Array.prototype,kn=Gn.splice;function Kn(t){var e=this.__data__,n=ct(e,t);if(n<0)return!1;var i=e.length-1;return n==i?e.pop():kn.call(e,n,1),--this.size,!0}function qn(t){var e=this.__data__,n=ct(e,t);return n<0?void 0:e[n][1]}function Zn(t){return ct(this.__data__,t)>-1}function Jn(t,e){var n=this.__data__,i=ct(n,t);return i<0?(++this.size,n.push([t,e])):n[i][1]=e,this}function N(t){var e=-1,n=t==null?0:t.length;for(this.clear();++e<n;){var i=t[e];this.set(i[0],i[1])}}N.prototype.clear=Un;N.prototype.delete=Kn;N.prototype.get=qn;N.prototype.has=Zn;N.prototype.set=Jn;function Qn(){this.__data__=new N,this.size=0}function ti(t){var e=this.__data__,n=e.delete(t);return this.size=e.size,n}function ei(t){return this.__data__.get(t)}function ni(t){return this.__data__.has(t)}var Z=Be(Object,"create");function ii(){this.__data__=Z?Z(null):{},this.size=0}function oi(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}var ri="__lodash_hash_undefined__",si=Object.prototype,ai=si.hasOwnProperty;function ui(t){var e=this.__data__;if(Z){var n=e[t];return n===ri?void 0:n}return ai.call(e,t)?e[t]:void 0}var li=Object.prototype,fi=li.hasOwnProperty;function ci(t){var e=this.__data__;return Z?e[t]!==void 0:fi.call(e,t)}var pi="__lodash_hash_undefined__";function di(t,e){var n=this.__data__;return this.size+=this.has(t)?0:1,n[t]=Z&&e===void 0?pi:e,this}function F(t){var e=-1,n=t==null?0:t.length;for(this.clear();++e<n;){var i=t[e];this.set(i[0],i[1])}}F.prototype.clear=ii;F.prototype.delete=oi;F.prototype.get=ui;F.prototype.has=ci;F.prototype.set=di;function hi(){this.size=0,this.__data__={hash:new F,map:new(pe||N),string:new F}}function vi(t){var e=typeof t;return e=="string"||e=="number"||e=="symbol"||e=="boolean"?t!=="__proto__":t===null}function pt(t,e){var n=t.__data__;return vi(e)?n[typeof e=="string"?"string":"hash"]:n.map}function gi(t){var e=pt(this,t).delete(t);return this.size-=e?1:0,e}function mi(t){return pt(this,t).get(t)}function yi(t){return pt(this,t).has(t)}function wi(t,e){var n=pt(this,t),i=n.size;return n.set(t,e),this.size+=n.size==i?0:1,this}function L(t){var e=-1,n=t==null?0:t.length;for(this.clear();++e<n;){var i=t[e];this.set(i[0],i[1])}}L.prototype.clear=hi;L.prototype.delete=gi;L.prototype.get=mi;L.prototype.has=yi;L.prototype.set=wi;var Ti=200;function bi(t,e){var n=this.__data__;if(n instanceof N){var i=n.__data__;if(!pe||i.length<Ti-1)return i.push([t,e]),this.size=++n.size,this;n=this.__data__=new L(i)}return n.set(t,e),this.size=n.size,this}function H(t){var e=this.__data__=new N(t);this.size=e.size}H.prototype.clear=Qn;H.prototype.delete=ti;H.prototype.get=ei;H.prototype.has=ni;H.prototype.set=bi;var Pi="__lodash_hash_undefined__";function Oi(t){return this.__data__.set(t,Pi),this}function _i(t){return this.__data__.has(t)}function lt(t){var e=-1,n=t==null?0:t.length;for(this.__data__=new L;++e<n;)this.add(t[e])}lt.prototype.add=lt.prototype.push=Oi;lt.prototype.has=_i;function Ci(t,e){for(var n=-1,i=t==null?0:t.length;++n<i;)if(e(t[n],n,t))return!0;return!1}function xi(t,e){return t.has(e)}var Mi=1,Si=2;function Se(t,e,n,i,o,r){var s=n&Mi,a=t.length,l=e.length;if(a!=l&&!(s&&l>a))return!1;var u=r.get(t),h=r.get(e);if(u&&h)return u==e&&h==t;var p=-1,c=!0,f=n&Si?new lt:void 0;for(r.set(t,e),r.set(e,t);++p<a;){var d=t[p],g=e[p];if(i)var w=s?i(g,d,p,e,t,r):i(d,g,p,t,e,r);if(w!==void 0){if(w)continue;c=!1;break}if(f){if(!Ci(e,function(m,y){if(!xi(f,y)&&(d===m||o(d,m,n,i,r)))return f.push(y)})){c=!1;break}}else if(!(d===g||o(d,g,n,i,r))){c=!1;break}}return r.delete(t),r.delete(e),c}var te=Ve.Uint8Array;function Ai(t){var e=-1,n=Array(t.size);return t.forEach(function(i,o){n[++e]=[o,i]}),n}function Di(t){var e=-1,n=Array(t.size);return t.forEach(function(i){n[++e]=i}),n}var Ei=1,Ni=2,$i="[object Boolean]",Hi="[object Date]",Ri="[object Error]",Bi="[object Map]",Vi="[object Number]",Fi="[object RegExp]",Li="[object Set]",Ii="[object String]",Wi="[object Symbol]",zi="[object ArrayBuffer]",Xi="[object DataView]",ee=$t?$t.prototype:void 0,yt=ee?ee.valueOf:void 0;function Yi(t,e,n,i,o,r,s){switch(n){case Xi:if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case zi:return!(t.byteLength!=e.byteLength||!r(new te(t),new te(e)));case $i:case Hi:case Vi:return Me(+t,+e);case Ri:return t.name==e.name&&t.message==e.message;case Fi:case Ii:return t==e+"";case Bi:var a=Ai;case Li:var l=i&Ei;if(a||(a=Di),t.size!=e.size&&!l)return!1;var u=s.get(t);if(u)return u==e;i|=Ni,s.set(t,e);var h=Se(a(t),a(e),i,o,r,s);return s.delete(t),h;case Wi:if(yt)return yt.call(t)==yt.call(e)}return!1}function ji(t,e){for(var n=-1,i=e.length,o=t.length;++n<i;)t[o+n]=e[n];return t}function Ui(t,e,n){var i=e(t);return at(t)?i:ji(i,n(t))}function Gi(t,e){for(var n=-1,i=t==null?0:t.length,o=0,r=[];++n<i;){var s=t[n];e(s,n,t)&&(r[o++]=s)}return r}function ki(){return[]}var Ki=Object.prototype,qi=Ki.propertyIsEnumerable,ne=Object.getOwnPropertySymbols,Zi=ne?function(t){return t==null?[]:(t=Object(t),Gi(ne(t),function(e){return qi.call(t,e)}))}:ki;function Ji(t,e){for(var n=-1,i=Array(t);++n<t;)i[n]=e(n);return i}var Qi=9007199254740991,to=/^(?:0|[1-9]\d*)$/;function eo(t,e){var n=typeof t;return e=e==null?Qi:e,!!e&&(n=="number"||n!="symbol"&&to.test(t))&&t>-1&&t%1==0&&t<e}var no=Object.prototype,io=no.hasOwnProperty;function oo(t,e){var n=at(t),i=!n&&Fe(t),o=!n&&!i&&wt(t),r=!n&&!i&&!o&&de(t),s=n||i||o||r,a=s?Ji(t.length,String):[],l=a.length;for(var u in t)(e||io.call(t,u))&&!(s&&(u=="length"||o&&(u=="offset"||u=="parent")||r&&(u=="buffer"||u=="byteLength"||u=="byteOffset")||eo(u,l)))&&a.push(u);return a}function ro(t){return Ie(t)?oo(t):Le(t)}function ie(t){return Ui(t,ro,Zi)}var so=1,ao=Object.prototype,uo=ao.hasOwnProperty;function lo(t,e,n,i,o,r){var s=n&so,a=ie(t),l=a.length,u=ie(e),h=u.length;if(l!=h&&!s)return!1;for(var p=l;p--;){var c=a[p];if(!(s?c in e:uo.call(e,c)))return!1}var f=r.get(t),d=r.get(e);if(f&&d)return f==e&&d==t;var g=!0;r.set(t,e),r.set(e,t);for(var w=s;++p<l;){c=a[p];var m=t[c],y=e[c];if(i)var b=s?i(y,m,c,e,t,r):i(m,y,c,t,e,r);if(!(b===void 0?m===y||o(m,y,n,i,r):b)){g=!1;break}w||(w=c=="constructor")}if(g&&!w){var _=t.constructor,A=e.constructor;_!=A&&"constructor"in t&&"constructor"in e&&!(typeof _=="function"&&_ instanceof _&&typeof A=="function"&&A instanceof A)&&(g=!1)}return r.delete(t),r.delete(e),g}var fo=1,oe="[object Arguments]",re="[object Array]",st="[object Object]",co=Object.prototype,se=co.hasOwnProperty;function po(t,e,n,i,o,r){var s=at(t),a=at(e),l=s?re:Ht(t),u=a?re:Ht(e);l=l==oe?st:l,u=u==oe?st:u;var h=l==st,p=u==st,c=l==u;if(c&&wt(t)){if(!wt(e))return!1;s=!0,h=!1}if(c&&!h)return r||(r=new H),s||de(t)?Se(t,e,n,i,o,r):Yi(t,e,l,n,i,o,r);if(!(n&fo)){var f=h&&se.call(t,"__wrapped__"),d=p&&se.call(e,"__wrapped__");if(f||d){var g=f?t.value():t,w=d?e.value():e;return r||(r=new H),o(g,w,n,i,r)}}return c?(r||(r=new H),lo(t,e,n,i,o,r)):!1}function Ae(t,e,n,i,o){return t===e?!0:t==null||e==null||!Rt(t)&&!Rt(e)?t!==t&&e!==e:po(t,e,n,i,Ae,o)}function ho(t,e){return Ae(t,e)}const vo={align:Object,target:[Object,Function],onAlign:Function,monitorBufferTime:Number,monitorWindowResize:Boolean,disabled:Boolean};function ae(t){return typeof t!="function"?null:t()}function ue(t){return typeof t!="object"||!t?null:t}const go=J({compatConfig:{MODE:3},name:"Align",props:vo,emits:["align"],setup(t,e){let{expose:n,slots:i}=e;const o=W({}),r=W(),[s,a]=jn(()=>{const{disabled:c,target:f,align:d,onAlign:g}=t;if(!c&&f&&r.value){const w=r.value;let m;const y=ae(f),b=ue(f);o.value.element=y,o.value.point=b,o.value.align=d;const{activeElement:_}=document;return y&&We(y)?m=Nt(w,y,d):b&&(m=zn(w,b,d)),Yn(_,w),g&&m&&g(w,m),!0}return!1},K(()=>t.monitorBufferTime)),l=W({cancel:()=>{}}),u=W({cancel:()=>{}}),h=()=>{const c=t.target,f=ae(c),d=ue(c);r.value!==u.value.element&&(u.value.cancel(),u.value.element=r.value,u.value.cancel=Qt(r.value,s)),(o.value.element!==f||!Xn(o.value.point,d)||!ho(o.value.align,t.align))&&(s(),l.value.element!==f&&(l.value.cancel(),l.value.element=f,l.value.cancel=Qt(f,s)))};ge(()=>{Tt(()=>{h()})}),Qe(()=>{Tt(()=>{h()})}),V(()=>t.disabled,c=>{c?a():s()},{immediate:!0,flush:"post"});const p=W(null);return V(()=>t.monitorWindowResize,c=>{c?p.value||(p.value=U(window,"resize",s)):p.value&&(p.value.remove(),p.value=null)},{flush:"post"}),tn(()=>{l.value.cancel(),u.value.cancel(),p.value&&p.value.remove(),a()}),n({forceAlign:()=>s(!0)}),()=>{const c=i==null?void 0:i.default();return c?he(c[0],{ref:r},!0,!0):null}}}),mo=J({compatConfig:{MODE:3},name:"PopupInner",inheritAttrs:!1,props:Mt,emits:["mouseenter","mouseleave","mousedown","touchstart","align"],setup(t,e){let{expose:n,attrs:i,slots:o}=e;const r=x(),s=x(),a=x(),[l,u]=vn(en(t,"stretch")),h=()=>{t.stretch&&u(t.getRootDomNode())},p=x(!1);let c;V(()=>t.visible,T=>{clearTimeout(c),T?c=setTimeout(()=>{p.value=t.visible}):p.value=!1},{immediate:!0});const[f,d]=hn(p,h),g=x(),w=()=>t.point?t.point:t.getRootDomNode,m=()=>{var T;(T=r.value)===null||T===void 0||T.forceAlign()},y=(T,E)=>{var M;const S=t.getClassNameFromAlign(E),R=a.value;a.value!==S&&(a.value=S),f.value==="align"&&(R!==S?Promise.resolve().then(()=>{m()}):d(()=>{var Y;(Y=g.value)===null||Y===void 0||Y.call(g)}),(M=t.onAlign)===null||M===void 0||M.call(t,T,E))},b=K(()=>{const T=typeof t.animation=="object"?t.animation:fe(t);return["onAfterEnter","onAfterLeave"].forEach(E=>{const M=T[E];T[E]=S=>{d(),f.value="stable",M==null||M(S)}}),T}),_=()=>new Promise(T=>{g.value=T});V([b,f],()=>{!b.value&&f.value==="motion"&&d()},{immediate:!0}),n({forceAlign:m,getElement:()=>s.value.$el||s.value});const A=K(()=>{var T;return!(!((T=t.align)===null||T===void 0)&&T.points&&(f.value==="align"||f.value==="stable"))});return()=>{var T;const{zIndex:E,align:M,prefixCls:S,destroyPopupOnHide:R,onMouseenter:Y,onMouseleave:De,onTouchstart:Ee=()=>{},onMousedown:Ne}=t,dt=f.value,$e=[P(P({},l.value),{zIndex:E,opacity:dt==="motion"||dt==="stable"||!p.value?null:0,pointerEvents:!p.value&&dt!=="stable"?"none":null}),i.style];let tt=ce((T=o.default)===null||T===void 0?void 0:T.call(o,{visible:t.visible}));tt.length>1&&(tt=C("div",{class:`${S}-content`},[tt]));const He=xt(S,i.class,a.value,!t.arrow&&`${S}-arrow-hidden`),Re=p.value||!t.visible?ze(b.value.name,b.value):{};return C(Ct,$($({ref:s},Re),{},{onBeforeEnter:_}),{default:()=>!R||t.visible?ve(C(go,{target:w(),key:"popup",ref:r,monitorWindowResize:!0,disabled:A.value,align:M,onAlign:y},{default:()=>C("div",{class:He,onMouseenter:Y,onMouseleave:De,onMousedown:Bt(Ne,["capture"]),[G?"onTouchstartPassive":"onTouchstart"]:Bt(Ee,["capture"]),style:$e},[tt])}),[[Xe,p.value]]):null})}}}),yo=J({compatConfig:{MODE:3},name:"Popup",inheritAttrs:!1,props:cn,setup(t,e){let{attrs:n,slots:i,expose:o}=e;const r=x(!1),s=x(!1),a=x(),l=x();return V([()=>t.visible,()=>t.mobile],()=>{r.value=t.visible,t.visible&&t.mobile&&(s.value=!0)},{immediate:!0,flush:"post"}),o({forceAlign:()=>{var u;(u=a.value)===null||u===void 0||u.forceAlign()},getElement:()=>{var u;return(u=a.value)===null||u===void 0?void 0:u.getElement()}}),()=>{const u=P(P(P({},t),n),{visible:r.value}),h=s.value?C(pn,$($({},u),{},{mobile:t.mobile,ref:a}),{default:i.default}):C(mo,$($({},u),{},{ref:a}),{default:i.default});return C("div",{ref:l},[C(ye,u,null),h])}}});function wo(t,e,n){return n?t[0]===e[0]:t[0]===e[0]&&t[1]===e[1]}function le(t,e,n){const i=t[e]||{};return P(P({},i),n)}function To(t,e,n,i){const{points:o}=n,r=Object.keys(t);for(let s=0;s<r.length;s+=1){const a=r[s];if(wo(t[a].points,o,i))return`${e}-placement-${a}`}return""}const bo={methods:{setState(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0,n=typeof t=="function"?t(this.$data,this.$props):t;if(this.getDerivedStateFromProps){const i=this.getDerivedStateFromProps(Ye(this),P(P({},this.$data),n));if(i===null)return;n=P(P({},n),i||{})}P(this.$data,n),this._.isMounted&&this.$forceUpdate(),Tt(()=>{e&&e()})},__emit(){const t=[].slice.call(arguments,0);let e=t[0];e=`on${e[0].toUpperCase()}${e.substring(1)}`;const n=this.$props[e]||this.$attrs[e];if(t.length&&n)if(Array.isArray(n))for(let i=0,o=n.length;i<o;i++)n[i](...t.slice(1));else n(...t.slice(1))}}},Po=["onClick","onMousedown","onTouchstart","onMouseenter","onMouseleave","onFocus","onBlur","onContextmenu"],Mo=J({compatConfig:{MODE:3},name:"Trigger",mixins:[bo],inheritAttrs:!1,props:ln(),setup(t){const e=K(()=>{const{popupPlacement:o,popupAlign:r,builtinPlacements:s}=t;return o&&s?le(s,o,r):r}),n=x(null),i=o=>{n.value=o};return{vcTriggerContext:rn("vcTriggerContext",{}),popupRef:n,setPopupRef:i,triggerRef:x(null),align:e,focusTime:null,clickOutsideHandler:null,contextmenuOutsideHandler1:null,contextmenuOutsideHandler2:null,touchOutsideHandler:null,attachId:null,delayTimer:null,hasPopupMouseDown:!1,preClickTime:null,preTouchTime:null,mouseDownTimeout:null,childOriginEvents:{}}},data(){const t=this.$props;let e;return this.popupVisible!==void 0?e=!!t.popupVisible:e=!!t.defaultPopupVisible,Po.forEach(n=>{this[`fire${n}`]=i=>{this.fireEvents(n,i)}}),{prevPopupVisible:e,sPopupVisible:e,point:null}},watch:{popupVisible(t){t!==void 0&&(this.prevPopupVisible=this.sPopupVisible,this.sPopupVisible=t)}},created(){on("vcTriggerContext",{onPopupMouseDown:this.onPopupMouseDown,onPopupMouseenter:this.onPopupMouseenter,onPopupMouseleave:this.onPopupMouseleave}),qe(this)},deactivated(){this.setPopupVisible(!1)},mounted(){this.$nextTick(()=>{this.updatedCal()})},updated(){this.$nextTick(()=>{this.updatedCal()})},beforeUnmount(){this.clearDelayTimer(),this.clearOutsideHandler(),clearTimeout(this.mouseDownTimeout),z.cancel(this.attachId)},methods:{updatedCal(){const t=this.$props;if(this.$data.sPopupVisible){let n;!this.clickOutsideHandler&&(this.isClickToHide()||this.isContextmenuToShow())&&(n=t.getDocument(this.getRootDomNode()),this.clickOutsideHandler=U(n,"mousedown",this.onDocumentClick)),this.touchOutsideHandler||(n=n||t.getDocument(this.getRootDomNode()),this.touchOutsideHandler=U(n,"touchstart",this.onDocumentClick,G?{passive:!1}:!1)),!this.contextmenuOutsideHandler1&&this.isContextmenuToShow()&&(n=n||t.getDocument(this.getRootDomNode()),this.contextmenuOutsideHandler1=U(n,"scroll",this.onContextmenuClose)),!this.contextmenuOutsideHandler2&&this.isContextmenuToShow()&&(this.contextmenuOutsideHandler2=U(window,"blur",this.onContextmenuClose))}else this.clearOutsideHandler()},onMouseenter(t){const{mouseEnterDelay:e}=this.$props;this.fireEvents("onMouseenter",t),this.delaySetPopupVisible(!0,e,e?null:t)},onMouseMove(t){this.fireEvents("onMousemove",t),this.setPoint(t)},onMouseleave(t){this.fireEvents("onMouseleave",t),this.delaySetPopupVisible(!1,this.$props.mouseLeaveDelay)},onPopupMouseenter(){const{vcTriggerContext:t={}}=this;t.onPopupMouseenter&&t.onPopupMouseenter(),this.clearDelayTimer()},onPopupMouseleave(t){var e;if(t&&t.relatedTarget&&!t.relatedTarget.setTimeout&&I((e=this.popupRef)===null||e===void 0?void 0:e.getElement(),t.relatedTarget))return;this.isMouseLeaveToHide()&&this.delaySetPopupVisible(!1,this.$props.mouseLeaveDelay);const{vcTriggerContext:n={}}=this;n.onPopupMouseleave&&n.onPopupMouseleave(t)},onFocus(t){this.fireEvents("onFocus",t),this.clearDelayTimer(),this.isFocusToShow()&&(this.focusTime=Date.now(),this.delaySetPopupVisible(!0,this.$props.focusDelay))},onMousedown(t){this.fireEvents("onMousedown",t),this.preClickTime=Date.now()},onTouchstart(t){this.fireEvents("onTouchstart",t),this.preTouchTime=Date.now()},onBlur(t){I(t.target,t.relatedTarget||document.activeElement)||(this.fireEvents("onBlur",t),this.clearDelayTimer(),this.isBlurToHide()&&this.delaySetPopupVisible(!1,this.$props.blurDelay))},onContextmenu(t){t.preventDefault(),this.fireEvents("onContextmenu",t),this.setPopupVisible(!0,t)},onContextmenuClose(){this.isContextmenuToShow()&&this.close()},onClick(t){if(this.fireEvents("onClick",t),this.focusTime){let n;if(this.preClickTime&&this.preTouchTime?n=Math.min(this.preClickTime,this.preTouchTime):this.preClickTime?n=this.preClickTime:this.preTouchTime&&(n=this.preTouchTime),Math.abs(n-this.focusTime)<20)return;this.focusTime=0}this.preClickTime=0,this.preTouchTime=0,this.isClickToShow()&&(this.isClickToHide()||this.isBlurToHide())&&t&&t.preventDefault&&t.preventDefault(),t&&t.domEvent&&t.domEvent.preventDefault();const e=!this.$data.sPopupVisible;(this.isClickToHide()&&!e||e&&this.isClickToShow())&&this.setPopupVisible(!this.$data.sPopupVisible,t)},onPopupMouseDown(){const{vcTriggerContext:t={}}=this;this.hasPopupMouseDown=!0,clearTimeout(this.mouseDownTimeout),this.mouseDownTimeout=setTimeout(()=>{this.hasPopupMouseDown=!1},0),t.onPopupMouseDown&&t.onPopupMouseDown(...arguments)},onDocumentClick(t){if(this.$props.mask&&!this.$props.maskClosable)return;const e=t.target,n=this.getRootDomNode(),i=this.getPopupDomNode();(!I(n,e)||this.isContextMenuOnly())&&!I(i,e)&&!this.hasPopupMouseDown&&this.delaySetPopupVisible(!1,.1)},getPopupDomNode(){var t;return((t=this.popupRef)===null||t===void 0?void 0:t.getElement())||null},getRootDomNode(){var t,e,n,i;const{getTriggerDOMNode:o}=this.$props;if(o){const r=((e=(t=this.triggerRef)===null||t===void 0?void 0:t.$el)===null||e===void 0?void 0:e.nodeName)==="#comment"?null:et(this.triggerRef);return et(o(r))}try{const r=((i=(n=this.triggerRef)===null||n===void 0?void 0:n.$el)===null||i===void 0?void 0:i.nodeName)==="#comment"?null:et(this.triggerRef);if(r)return r}catch(r){}return et(this)},handleGetPopupClassFromAlign(t){const e=[],n=this.$props,{popupPlacement:i,builtinPlacements:o,prefixCls:r,alignPoint:s,getPopupClassNameFromAlign:a}=n;return i&&o&&e.push(To(o,r,t,s)),a&&e.push(a(t)),e.join(" ")},getPopupAlign(){const t=this.$props,{popupPlacement:e,popupAlign:n,builtinPlacements:i}=t;return e&&i?le(i,e,n):n},getComponent(){const t={};this.isMouseEnterToShow()&&(t.onMouseenter=this.onPopupMouseenter),this.isMouseLeaveToHide()&&(t.onMouseleave=this.onPopupMouseleave),t.onMousedown=this.onPopupMouseDown,t[G?"onTouchstartPassive":"onTouchstart"]=this.onPopupMouseDown;const{handleGetPopupClassFromAlign:e,getRootDomNode:n,$attrs:i}=this,{prefixCls:o,destroyPopupOnHide:r,popupClassName:s,popupAnimation:a,popupTransitionName:l,popupStyle:u,mask:h,maskAnimation:p,maskTransitionName:c,zIndex:f,stretch:d,alignPoint:g,mobile:w,arrow:m,forceRender:y}=this.$props,{sPopupVisible:b,point:_}=this.$data,A=P(P({prefixCls:o,arrow:m,destroyPopupOnHide:r,visible:b,point:g?_:null,align:this.align,animation:a,getClassNameFromAlign:e,stretch:d,getRootDomNode:n,mask:h,zIndex:f,transitionName:l,maskAnimation:p,maskTransitionName:c,class:s,style:u,onAlign:i.onPopupAlign||me},t),{ref:this.setPopupRef,mobile:w,forceRender:y});return C(yo,A,{default:this.$slots.popup||(()=>Ke(this,"popup"))})},attachParent(t){z.cancel(this.attachId);const{getPopupContainer:e,getDocument:n}=this.$props,i=this.getRootDomNode();let o;e?(i||e.length===0)&&(o=e(i)):o=n(this.getRootDomNode()).body,o?o.appendChild(t):this.attachId=z(()=>{this.attachParent(t)})},getContainer(){const{$props:t}=this,{getDocument:e}=t,n=e(this.getRootDomNode()).createElement("div");return n.style.position="absolute",n.style.top="0",n.style.left="0",n.style.width="100%",this.attachParent(n),n},setPopupVisible(t,e){const{alignPoint:n,sPopupVisible:i,onPopupVisibleChange:o}=this;this.clearDelayTimer(),i!==t&&(ke(this,"popupVisible")||this.setState({sPopupVisible:t,prevPopupVisible:i}),o&&o(t)),n&&e&&t&&this.setPoint(e)},setPoint(t){const{alignPoint:e}=this.$props;!e||!t||this.setState({point:{pageX:t.pageX,pageY:t.pageY}})},handlePortalUpdate(){this.prevPopupVisible!==this.sPopupVisible&&this.afterPopupVisibleChange(this.sPopupVisible)},delaySetPopupVisible(t,e,n){const i=e*1e3;if(this.clearDelayTimer(),i){const o=n?{pageX:n.pageX,pageY:n.pageY}:null;this.delayTimer=setTimeout(()=>{this.setPopupVisible(t,o),this.clearDelayTimer()},i)}else this.setPopupVisible(t,n)},clearDelayTimer(){this.delayTimer&&(clearTimeout(this.delayTimer),this.delayTimer=null)},clearOutsideHandler(){this.clickOutsideHandler&&(this.clickOutsideHandler.remove(),this.clickOutsideHandler=null),this.contextmenuOutsideHandler1&&(this.contextmenuOutsideHandler1.remove(),this.contextmenuOutsideHandler1=null),this.contextmenuOutsideHandler2&&(this.contextmenuOutsideHandler2.remove(),this.contextmenuOutsideHandler2=null),this.touchOutsideHandler&&(this.touchOutsideHandler.remove(),this.touchOutsideHandler=null)},createTwoChains(t){let e=()=>{};const n=Vt(this);return this.childOriginEvents[t]&&n[t]?this[`fire${t}`]:(e=this.childOriginEvents[t]||n[t]||e,e)},isClickToShow(){const{action:t,showAction:e}=this.$props;return t.indexOf("click")!==-1||e.indexOf("click")!==-1},isContextMenuOnly(){const{action:t}=this.$props;return t==="contextmenu"||t.length===1&&t[0]==="contextmenu"},isContextmenuToShow(){const{action:t,showAction:e}=this.$props;return t.indexOf("contextmenu")!==-1||e.indexOf("contextmenu")!==-1},isClickToHide(){const{action:t,hideAction:e}=this.$props;return t.indexOf("click")!==-1||e.indexOf("click")!==-1},isMouseEnterToShow(){const{action:t,showAction:e}=this.$props;return t.indexOf("hover")!==-1||e.indexOf("mouseenter")!==-1},isMouseLeaveToHide(){const{action:t,hideAction:e}=this.$props;return t.indexOf("hover")!==-1||e.indexOf("mouseleave")!==-1},isFocusToShow(){const{action:t,showAction:e}=this.$props;return t.indexOf("focus")!==-1||e.indexOf("focus")!==-1},isBlurToHide(){const{action:t,hideAction:e}=this.$props;return t.indexOf("focus")!==-1||e.indexOf("blur")!==-1},forcePopupAlign(){var t;this.$data.sPopupVisible&&((t=this.popupRef)===null||t===void 0||t.forceAlign())},fireEvents(t,e){this.childOriginEvents[t]&&this.childOriginEvents[t](e);const n=this.$props[t]||this.$attrs[t];n&&n(e)},close(){this.setPopupVisible(!1)}},render(){const{$attrs:t}=this,e=je(Ue(this)),{alignPoint:n,getPopupContainer:i}=this.$props,o=e[0];this.childOriginEvents=Vt(o);const r={key:"trigger"};this.isContextmenuToShow()?r.onContextmenu=this.onContextmenu:r.onContextmenu=this.createTwoChains("onContextmenu"),this.isClickToHide()||this.isClickToShow()?(r.onClick=this.onClick,r.onMousedown=this.onMousedown,r[G?"onTouchstartPassive":"onTouchstart"]=this.onTouchstart):(r.onClick=this.createTwoChains("onClick"),r.onMousedown=this.createTwoChains("onMousedown"),r[G?"onTouchstartPassive":"onTouchstart"]=this.createTwoChains("onTouchstart")),this.isMouseEnterToShow()?(r.onMouseenter=this.onMouseenter,n&&(r.onMousemove=this.onMouseMove)):r.onMouseenter=this.createTwoChains("onMouseenter"),this.isMouseLeaveToHide()?r.onMouseleave=this.onMouseleave:r.onMouseleave=this.createTwoChains("onMouseleave"),this.isFocusToShow()||this.isBlurToHide()?(r.onFocus=this.onFocus,r.onBlur=this.onBlur):(r.onFocus=this.createTwoChains("onFocus"),r.onBlur=u=>{u&&(!u.relatedTarget||!I(u.target,u.relatedTarget))&&this.createTwoChains("onBlur")(u)});const s=xt(o&&o.props&&o.props.class,t.class);s&&(r.class=s);const a=he(o,P(P({},r),{ref:"triggerRef"}),!0,!0),l=C(Ge,{key:"portal",getContainer:i&&(()=>i(this.getRootDomNode())),didUpdate:this.handlePortalUpdate,visible:this.$data.sPopupVisible},{default:this.getComponent});return C(nn,null,[a,l])}});export{bo as B,L as M,lt as S,Mo as T,te as U,oo as a,ki as b,xi as c,ji as d,Ui as e,H as f,Zi as g,ie as h,Ae as i,ho as j,ro as k,Me as l,eo as m,Di as s};
