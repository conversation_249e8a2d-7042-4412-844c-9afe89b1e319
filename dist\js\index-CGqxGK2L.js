import{j as fe,o as me,b as W,i as Ne,W as $e,K,f as je,_ as P,h as Fe,D as we,P as se,G as He,s as de,v as Be,H as Ce,a as Ge,J as Ke,S as We,a5 as ze,T as Ue,a6 as Je}from"./bootstrap-CFDAkNgp.js";import{a as Xe,L as Ye,f as qe,T as Qe,i as ge,b as Ze,h as et,j as tt,k as lt,t as nt,B as ot,d as at,e as it,g as st}from"./index-B2Lu6Z2W.js";import{u as ut}from"./move-IXaXzbNk.js";import{aF as rt,R as ct,a4 as Se,J as b,T as dt,Y as he,x as L,F as vt,r as Ae,n as Ve,a5 as ie,ao as be,_ as q,P as Me}from"../jse/index-index-B2UBupFX.js";import{u as _e}from"./useMergedState-C4x1IDb9.js";import{u as ft,F as mt}from"./FormItemContext-CoieKSxA.js";import{g as pt,a as gt}from"./statusUtils-D62pPzYs.js";import"./Trigger-D2zZP_An.js";import"./ResizeObserver.es-CDE7jhPe.js";import"./BaseInput-Dslq5mxC.js";import"./Overflow--DaGju1E.js";import"./index-DQZjs6Lb.js";import"./isMobile-8sZ0LT6r.js";import"./DownOutlined-CVWF16Fu.js";import"./CheckOutlined-CZeS3E7o.js";import"./SearchOutlined-DqQ4RgbY.js";import"./slide-BhgK1D9k.js";function ht(){return/(mac\sos|macintosh)/i.test(navigator.appVersion)}const Te=Symbol("SelectContextKey");function bt(e){return rt(Te,e)}function St(){return ct(Te,{})}var Ot=function(e,S){var g={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&S.indexOf(a)<0&&(g[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,a=Object.getOwnPropertySymbols(e);l<a.length;l++)S.indexOf(a[l])<0&&Object.prototype.propertyIsEnumerable.call(e,a[l])&&(g[a[l]]=e[a[l]]);return g};function xe(e){return typeof e=="string"||typeof e=="number"}const yt=Se({compatConfig:{MODE:3},name:"OptionList",inheritAttrs:!1,setup(e,S){let{expose:g,slots:a}=S;const l=Xe(),u=St(),f=b(()=>`${l.prefixCls}-item`),v=ut(()=>u.flattenOptions,[()=>l.open,()=>u.flattenOptions],o=>o[0]),p=qe(),h=o=>{o.preventDefault()},m=o=>{p.current&&p.current.scrollTo(typeof o=="number"?{index:o}:o)},y=function(o){let O=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1;const V=v.value.length;for(let d=0;d<V;d+=1){const N=(o+d*O+V)%V,{group:$,data:j}=v.value[N];if(!$&&!j.disabled)return N}return-1},M=dt({activeIndex:y(0)}),_=function(o){let O=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;M.activeIndex=o;const V={source:O?"keyboard":"mouse"},d=v.value[o];if(!d){u.onActiveValue(null,-1,V);return}u.onActiveValue(d.value,o,V)};he([()=>v.value.length,()=>l.searchValue],()=>{_(u.defaultActiveFirstOption!==!1?y(0):-1)},{immediate:!0});const I=o=>u.rawValues.has(o)&&l.mode!=="combobox";he([()=>l.open,()=>l.searchValue],()=>{if(!l.multiple&&l.open&&u.rawValues.size===1){const o=Array.from(u.rawValues)[0],O=Ae(v.value).findIndex(V=>{let{data:d}=V;return d[u.fieldNames.value]===o});O!==-1&&(_(O),Ve(()=>{m(O)}))}l.open&&Ve(()=>{var o;(o=p.current)===null||o===void 0||o.scrollTo(void 0)})},{immediate:!0,flush:"post"});const w=o=>{o!==void 0&&u.onSelect(o,{selected:!u.rawValues.has(o)}),l.multiple||l.toggleOpen(!1)},C=o=>typeof o.label=="function"?o.label():o.label;function D(o){const O=v.value[o];if(!O)return null;const V=O.data||{},{value:d}=V,{group:N}=O,$=$e(V,!0),j=C(O);return O?L("div",W(W({"aria-label":typeof j=="string"&&!N?j:null},$),{},{key:o,role:N?"presentation":"option",id:`${l.id}_list_${o}`,"aria-selected":I(d)}),[d]):null}return g({onKeydown:o=>{const{which:O,ctrlKey:V}=o;switch(O){case K.N:case K.P:case K.UP:case K.DOWN:{let d=0;if(O===K.UP?d=-1:O===K.DOWN?d=1:ht()&&V&&(O===K.N?d=1:O===K.P&&(d=-1)),d!==0){const N=y(M.activeIndex+d,d);m(N),_(N,!0)}break}case K.ENTER:{const d=v.value[M.activeIndex];d&&!d.data.disabled?w(d.value):w(void 0),l.open&&o.preventDefault();break}case K.ESC:l.toggleOpen(!1),l.open&&o.stopPropagation()}},onKeyup:()=>{},scrollTo:o=>{m(o)}}),()=>{const{id:o,notFoundContent:O,onPopupScroll:V}=l,{menuItemSelectedIcon:d,fieldNames:N,virtual:$,listHeight:j,listItemHeight:ne}=u,z=a.option,{activeIndex:R}=M,ue=Object.keys(N).map(T=>N[T]);return v.value.length===0?L("div",{role:"listbox",id:`${o}_list`,class:`${f.value}-empty`,onMousedown:h},[O]):L(vt,null,[L("div",{role:"listbox",id:`${o}_list`,style:{height:0,width:0,overflow:"hidden"}},[D(R-1),D(R),D(R+1)]),L(Ye,{itemKey:"key",ref:p,data:v.value,height:j,itemHeight:ne,fullHeight:!1,onMousedown:h,onScroll:V,virtual:$},{default:(T,Z)=>{var ee;const{group:oe,groupOption:re,data:r,value:F}=T,{key:k}=r,B=typeof T.label=="function"?T.label():T.label;if(oe){const le=(ee=r.title)!==null&&ee!==void 0?ee:xe(B)&&B;return L("div",{class:fe(f.value,`${f.value}-group`),title:le},[z?z(r):B!==void 0?B:k])}const{disabled:U,title:ae,children:te,style:ce,class:t,className:n}=r,i=Ot(r,["disabled","title","children","style","class","className"]),c=me(i,ue),s=I(F),x=`${f.value}-option`,J=fe(f.value,x,t,n,{[`${x}-grouped`]:re,[`${x}-active`]:R===Z&&!U,[`${x}-disabled`]:U,[`${x}-selected`]:s}),H=C(T),G=!d||typeof d=="function"||s,A=typeof H=="number"?H:H||F;let ve=xe(A)?A.toString():void 0;return ae!==void 0&&(ve=ae),L("div",W(W({},c),{},{"aria-selected":s,class:J,title:ve,onMousemove:le=>{i.onMousemove&&i.onMousemove(le),!(R===Z||U)&&_(Z)},onClick:le=>{U||w(F),i.onClick&&i.onClick(le)},style:ce}),[L("div",{class:`${x}-content`},[z?z(r):A]),Ne(d)||s,G&&L(Qe,{class:`${f.value}-option-state`,customizeIcon:d,customizeIconProps:{isSelected:s}},{default:()=>[s?"✓":null]})])}})])}}});var It=function(e,S){var g={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&S.indexOf(a)<0&&(g[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,a=Object.getOwnPropertySymbols(e);l<a.length;l++)S.indexOf(a[l])<0&&Object.prototype.propertyIsEnumerable.call(e,a[l])&&(g[a[l]]=e[a[l]]);return g};function wt(e){const S=e,{key:g,children:a}=S,l=S.props,{value:u,disabled:f}=l,v=It(l,["value","disabled"]),p=a==null?void 0:a.default;return P({key:g,value:u!==void 0?u:g,children:p,disabled:f||f===""},v)}function De(e){let S=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return je(e).map((a,l)=>{var u;if(!Ne(a)||!a.type)return null;const{type:{isSelectOptGroup:f},key:v,children:p,props:h}=a;if(S||!f)return wt(a);const m=p&&p.default?p.default():void 0,y=(h==null?void 0:h.label)||((u=p.label)===null||u===void 0?void 0:u.call(p))||v;return P(P({key:`__RC_SELECT_GRP__${v===null?l:String(v)}__`},h),{label:y,options:De(m||[])})}).filter(a=>a)}function Ct(e,S,g){const a=ie(),l=ie(),u=ie(),f=ie([]);return he([e,S],()=>{e.value?f.value=Ae(e.value).slice():f.value=De(S.value)},{immediate:!0,deep:!0}),be(()=>{const v=f.value,p=new Map,h=new Map,m=g.value;function y(M){let _=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;for(let I=0;I<M.length;I+=1){const w=M[I];!w[m.options]||_?(p.set(w[m.value],w),h.set(w[m.label],w)):y(w[m.options],!0)}}y(v),a.value=v,l.value=p,u.value=h}),{options:a,valueOptions:l,labelOptions:u}}function Ee(e){return Array.isArray(e)?e:e!==void 0?[e]:[]}function pe(e,S){return Ee(e).join("").toUpperCase().includes(S)}const Vt=(e,S,g,a,l)=>b(()=>{const u=g.value,f=l==null?void 0:l.value,v=a==null?void 0:a.value;if(!u||v===!1)return e.value;const{options:p,label:h,value:m}=S.value,y=[],M=typeof v=="function",_=u.toUpperCase(),I=M?v:(C,D)=>f?pe(D[f],_):D[p]?pe(D[h!=="children"?h:"label"],_):pe(D[m],_),w=M?C=>ge(C):C=>C;return e.value.forEach(C=>{if(C[p]){if(I(u,w(C)))y.push(C);else{const Q=C[p].filter(Y=>I(u,w(Y)));Q.length&&y.push(P(P({},C),{[p]:Q}))}return}I(u,w(C))&&y.push(C)}),y}),_t=(e,S)=>{const g=ie({values:new Map,options:new Map});return[b(()=>{const{values:u,options:f}=g.value,v=e.value.map(m=>{var y;return m.label===void 0?P(P({},m),{label:(y=u.get(m.value))===null||y===void 0?void 0:y.label}):m}),p=new Map,h=new Map;return v.forEach(m=>{p.set(m.value,m),h.set(m.value,S.value.get(m.value)||f.get(m.value))}),g.value.values=p,g.value.options=h,v}),u=>S.value.get(u)||g.value.options.get(u)]},xt=["inputValue"];function Re(){return P(P({},at()),{prefixCls:String,id:String,backfill:{type:Boolean,default:void 0},fieldNames:Object,inputValue:String,searchValue:String,onSearch:Function,autoClearSearchValue:{type:Boolean,default:void 0},onSelect:Function,onDeselect:Function,filterOption:{type:[Boolean,Function],default:void 0},filterSort:Function,optionFilterProp:String,optionLabelProp:String,options:Array,defaultActiveFirstOption:{type:Boolean,default:void 0},virtual:{type:Boolean,default:void 0},listHeight:Number,listItemHeight:Number,menuItemSelectedIcon:se.any,mode:String,labelInValue:{type:Boolean,default:void 0},value:se.any,defaultValue:se.any,onChange:Function,children:Array})}function Pt(e){return!e||typeof e!="object"}const Nt=Se({compatConfig:{MODE:3},name:"VcSelect",inheritAttrs:!1,props:Fe(Re(),{prefixCls:"vc-select",autoClearSearchValue:!0,listHeight:200,listItemHeight:20,dropdownMatchSelectWidth:!0}),setup(e,S){let{expose:g,attrs:a,slots:l}=S;const u=Ze(q(e,"id")),f=b(()=>et(e.mode)),v=b(()=>!!(!e.options&&e.children)),p=b(()=>e.filterOption===void 0&&e.mode==="combobox"?!1:e.filterOption),h=b(()=>tt(e.fieldNames,v.value)),[m,y]=_e("",{value:b(()=>e.searchValue!==void 0?e.searchValue:e.inputValue),postState:t=>t||""}),M=Ct(q(e,"options"),q(e,"children"),h),{valueOptions:_,labelOptions:I,options:w}=M,C=t=>Ee(t).map(i=>{var c,s;let x,J,H,G;Pt(i)?x=i:(H=i.key,J=i.label,x=(c=i.value)!==null&&c!==void 0?c:H);const A=_.value.get(x);return A&&(J===void 0&&(J=A==null?void 0:A[e.optionLabelProp||h.value.label]),H===void 0&&(H=(s=A==null?void 0:A.key)!==null&&s!==void 0?s:x),G=A==null?void 0:A.disabled),{label:J,value:x,key:H,disabled:G,option:A}}),[D,Q]=_e(e.defaultValue,{value:q(e,"value")}),Y=b(()=>{var t;const n=C(D.value);return e.mode==="combobox"&&!(!((t=n[0])===null||t===void 0)&&t.value)?[]:n}),[E,o]=_t(Y,_),O=b(()=>{if(!e.mode&&E.value.length===1){const t=E.value[0];if(t.value===null&&(t.label===null||t.label===void 0))return[]}return E.value.map(t=>{var n;return P(P({},t),{label:(n=typeof t.label=="function"?t.label():t.label)!==null&&n!==void 0?n:t.value})})}),V=b(()=>new Set(E.value.map(t=>t.value)));be(()=>{var t;if(e.mode==="combobox"){const n=(t=E.value[0])===null||t===void 0?void 0:t.value;n!=null&&y(String(n))}},{flush:"post"});const d=(t,n)=>{const i=n!=null?n:t;return{[h.value.value]:t,[h.value.label]:i}},N=ie();be(()=>{if(e.mode!=="tags"){N.value=w.value;return}const t=w.value.slice(),n=i=>_.value.has(i);[...E.value].sort((i,c)=>i.value<c.value?-1:1).forEach(i=>{const c=i.value;n(c)||t.push(d(c,i.label))}),N.value=t});const $=Vt(N,h,m,p,q(e,"optionFilterProp")),j=b(()=>e.mode!=="tags"||!m.value||$.value.some(t=>t[e.optionFilterProp||"value"]===m.value)?$.value:[d(m.value),...$.value]),ne=b(()=>e.filterSort?[...j.value].sort((t,n)=>e.filterSort(t,n)):j.value),z=b(()=>lt(ne.value,{fieldNames:h.value,childrenAsData:v.value})),R=t=>{const n=C(t);if(Q(n),e.onChange&&(n.length!==E.value.length||n.some((i,c)=>{var s;return((s=E.value[c])===null||s===void 0?void 0:s.value)!==(i==null?void 0:i.value)}))){const i=e.labelInValue?n.map(s=>P(P({},s),{originLabel:s.label,label:typeof s.label=="function"?s.label():s.label})):n.map(s=>s.value),c=n.map(s=>ge(o(s.value)));e.onChange(f.value?i:i[0],f.value?c:c[0])}},[ue,T]=we(null),[Z,ee]=we(0),oe=b(()=>e.defaultActiveFirstOption!==void 0?e.defaultActiveFirstOption:e.mode!=="combobox"),re=function(t,n){let{source:i="keyboard"}=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};ee(n),e.backfill&&e.mode==="combobox"&&t!==null&&i==="keyboard"&&T(String(t))},r=(t,n)=>{const i=()=>{var c;const s=o(t),x=s==null?void 0:s[h.value.label];return[e.labelInValue?{label:typeof x=="function"?x():x,originLabel:x,value:t,key:(c=s==null?void 0:s.key)!==null&&c!==void 0?c:t}:t,ge(s)]};if(n&&e.onSelect){const[c,s]=i();e.onSelect(c,s)}else if(!n&&e.onDeselect){const[c,s]=i();e.onDeselect(c,s)}},F=(t,n)=>{let i;const c=f.value?n.selected:!0;c?i=f.value?[...E.value,t]:[t]:i=E.value.filter(s=>s.value!==t),R(i),r(t,c),e.mode==="combobox"?T(""):(!f.value||e.autoClearSearchValue)&&(y(""),T(""))},k=(t,n)=>{R(t),(n.type==="remove"||n.type==="clear")&&n.values.forEach(i=>{r(i.value,!1)})},B=(t,n)=>{var i;if(y(t),T(null),n.source==="submit"){const c=(t||"").trim();if(c){const s=Array.from(new Set([...V.value,c]));R(s),r(c,!0),y("")}return}n.source!=="blur"&&(e.mode==="combobox"&&R(t),(i=e.onSearch)===null||i===void 0||i.call(e,t))},U=t=>{let n=t;e.mode!=="tags"&&(n=t.map(c=>{const s=I.value.get(c);return s==null?void 0:s.value}).filter(c=>c!==void 0));const i=Array.from(new Set([...V.value,...n]));R(i),i.forEach(c=>{r(c,!0)})},ae=b(()=>e.virtual!==!1&&e.dropdownMatchSelectWidth!==!1);bt(nt(P(P({},M),{flattenOptions:z,onActiveValue:re,defaultActiveFirstOption:oe,onSelect:F,menuItemSelectedIcon:q(e,"menuItemSelectedIcon"),rawValues:V,fieldNames:h,virtual:ae,listHeight:q(e,"listHeight"),listItemHeight:q(e,"listItemHeight"),childrenAsData:v})));const te=Me();g({focus(){var t;(t=te.value)===null||t===void 0||t.focus()},blur(){var t;(t=te.value)===null||t===void 0||t.blur()},scrollTo(t){var n;(n=te.value)===null||n===void 0||n.scrollTo(t)}});const ce=b(()=>me(e,["id","mode","prefixCls","backfill","fieldNames","inputValue","searchValue","onSearch","autoClearSearchValue","onSelect","onDeselect","dropdownMatchSelectWidth","filterOption","filterSort","optionFilterProp","optionLabelProp","options","children","defaultActiveFirstOption","menuItemSelectedIcon","virtual","listHeight","listItemHeight","value","defaultValue","labelInValue","onChange"]));return()=>L(ot,W(W(W({},ce.value),a),{},{id:u,prefixCls:e.prefixCls,ref:te,omitDomProps:xt,mode:e.mode,displayValues:O.value,onDisplayValuesChange:k,searchValue:m.value,onSearch:B,onSearchSplit:U,dropdownMatchSelectWidth:e.dropdownMatchSelectWidth,OptionList:yt,emptyOptions:!z.value.length,activeValue:ue.value,activeDescendantId:`${u}_list_${Z.value}`}),l)}}),Oe=()=>null;Oe.isSelectOption=!0;Oe.displayName="ASelectOption";const ye=()=>null;ye.isSelectOptGroup=!0;ye.displayName="ASelectOptGroup";const Ft=()=>P(P({},me(Re(),["inputIcon","mode","getInputElement","getRawInputElement","backfill"])),{value:Ce([Array,Object,String,Number]),defaultValue:Ce([Array,Object,String,Number]),notFoundContent:se.any,suffixIcon:se.any,itemIcon:se.any,size:de(),mode:de(),bordered:Be(!0),transitionName:String,choiceTransitionName:de(""),popupClassName:String,dropdownClassName:String,placement:de(),status:de(),"onUpdate:value":He()}),Pe="SECRET_COMBOBOX_MODE_DO_NOT_USE",X=Se({compatConfig:{MODE:3},name:"ASelect",Option:Oe,OptGroup:ye,inheritAttrs:!1,props:Fe(Ft(),{listHeight:256,listItemHeight:24}),SECRET_COMBOBOX_MODE_DO_NOT_USE:Pe,slots:Object,setup(e,S){let{attrs:g,emit:a,slots:l,expose:u}=S;const f=Me(),v=ft(),p=mt.useInject(),h=b(()=>pt(p.status,e.status)),m=()=>{var r;(r=f.value)===null||r===void 0||r.focus()},y=()=>{var r;(r=f.value)===null||r===void 0||r.blur()},M=r=>{var F;(F=f.value)===null||F===void 0||F.scrollTo(r)},_=b(()=>{const{mode:r}=e;if(r!=="combobox")return r===Pe?"combobox":r}),{prefixCls:I,direction:w,renderEmpty:C,size:D,getPrefixCls:Q,getPopupContainer:Y,disabled:E,select:o}=Ge("select",e),{compactSize:O,compactItemClassnames:V}=Ke(I,w),d=b(()=>O.value||D.value),N=We(),$=b(()=>{var r;return(r=E.value)!==null&&r!==void 0?r:N.value}),[j,ne]=it(I),z=b(()=>Q()),R=b(()=>e.placement!==void 0?e.placement:w.value==="rtl"?"bottomRight":"bottomLeft"),ue=b(()=>ze(z.value,Ue(R.value),e.transitionName)),T=b(()=>fe({[`${I.value}-lg`]:d.value==="large",[`${I.value}-sm`]:d.value==="small",[`${I.value}-rtl`]:w.value==="rtl",[`${I.value}-borderless`]:!e.bordered,[`${I.value}-in-form-item`]:p.isFormItemInput},gt(I.value,h.value,p.hasFeedback),V.value,ne.value)),Z=function(){for(var r=arguments.length,F=new Array(r),k=0;k<r;k++)F[k]=arguments[k];a("update:value",F[0]),a("change",...F),v.onFieldChange()},ee=r=>{a("blur",r),v.onFieldBlur()};u({blur:y,focus:m,scrollTo:M});const oe=b(()=>_.value==="multiple"||_.value==="tags"),re=b(()=>e.showArrow!==void 0?e.showArrow:e.loading||!(oe.value||_.value==="combobox"));return()=>{var r,F,k,B;const{notFoundContent:U,listHeight:ae=256,listItemHeight:te=24,popupClassName:ce,dropdownClassName:t,virtual:n,dropdownMatchSelectWidth:i,id:c=v.id.value,placeholder:s=(r=l.placeholder)===null||r===void 0?void 0:r.call(l),showArrow:x}=e,{hasFeedback:J,feedbackIcon:H}=p;let G;U!==void 0?G=U:l.notFoundContent?G=l.notFoundContent():_.value==="combobox"?G=null:G=(C==null?void 0:C("Select"))||L(Je,{componentName:"Select"},null);const{suffixIcon:A,itemIcon:ve,removeIcon:le,clearIcon:ke}=st(P(P({},e),{multiple:oe.value,prefixCls:I.value,hasFeedback:J,feedbackIcon:H,showArrow:re.value}),l),Ie=me(e,["prefixCls","suffixIcon","itemIcon","removeIcon","clearIcon","size","bordered","status"]),Le=fe(ce||t,{[`${I.value}-dropdown-${w.value}`]:w.value==="rtl"},ne.value);return j(L(Nt,W(W(W({ref:f,virtual:n,dropdownMatchSelectWidth:i},Ie),g),{},{showSearch:(F=e.showSearch)!==null&&F!==void 0?F:(k=o==null?void 0:o.value)===null||k===void 0?void 0:k.showSearch,placeholder:s,listHeight:ae,listItemHeight:te,mode:_.value,prefixCls:I.value,direction:w.value,inputIcon:A,menuItemSelectedIcon:ve,removeIcon:le,clearIcon:ke,notFoundContent:G,class:[T.value,g.class],getPopupContainer:Y==null?void 0:Y.value,dropdownClassName:Le,onChange:Z,onBlur:ee,id:c,dropdownRender:Ie.dropdownRender||l.dropdownRender,transitionName:ue.value,children:(B=l.default)===null||B===void 0?void 0:B.call(l),tagRender:e.tagRender||l.tagRender,optionLabelRender:l.optionLabel,maxTagPlaceholder:e.maxTagPlaceholder||l.maxTagPlaceholder,showArrow:J||x,disabled:$.value}),{option:l.option}))}}});X.install=function(e){return e.component(X.name,X),e.component(X.Option.displayName,X.Option),e.component(X.OptGroup.displayName,X.OptGroup),e};const Jt=X.Option,Xt=X.OptGroup;export{Xt as SelectOptGroup,Jt as SelectOption,X as default,Ft as selectProps};
