import {
  filterEmpty,
  warning_default2 as warning_default
} from "./chunk-MFV6O37K.js";
import {
  _extends
} from "./chunk-LHAI6UAP.js";
import {
  Comment,
  Fragment,
  cloneVNode,
  isVNode,
  render
} from "./chunk-ZCM5A7SR.js";

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/vnode.js
function cloneElement(vnode) {
  let nodeProps = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
  let override = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;
  let mergeRef = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false;
  let ele = vnode;
  if (Array.isArray(vnode)) {
    ele = filterEmpty(vnode)[0];
  }
  if (!ele) {
    return null;
  }
  const node = cloneVNode(ele, nodeProps, mergeRef);
  node.props = override ? _extends(_extends({}, node.props), nodeProps) : node.props;
  warning_default(typeof node.props.class !== "object", "class must be string");
  return node;
}
function cloneVNodes(vnodes) {
  let nodeProps = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
  let override = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;
  return vnodes.map((vnode) => cloneElement(vnode, nodeProps, override));
}
function deepCloneElement(vnode) {
  let nodeProps = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
  let override = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;
  let mergeRef = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false;
  if (Array.isArray(vnode)) {
    return vnode.map((item) => deepCloneElement(item, nodeProps, override, mergeRef));
  } else {
    if (!isVNode(vnode)) {
      return vnode;
    }
    const cloned = cloneElement(vnode, nodeProps, override, mergeRef);
    if (Array.isArray(cloned.children)) {
      cloned.children = deepCloneElement(cloned.children);
    }
    return cloned;
  }
}
function triggerVNodeUpdate(vm, attrs, dom) {
  render(cloneVNode(vm, _extends({}, attrs)), dom);
}
var ensureValidVNode = (slot) => {
  return (slot || []).some((child) => {
    if (!isVNode(child)) return true;
    if (child.type === Comment) return false;
    if (child.type === Fragment && !ensureValidVNode(child.children)) return false;
    return true;
  }) ? slot : null;
};
function customRenderSlot(slots, name, props, fallback) {
  var _a;
  const slot = (_a = slots[name]) === null || _a === void 0 ? void 0 : _a.call(slots, props);
  if (ensureValidVNode(slot)) {
    return slot;
  }
  return fallback === null || fallback === void 0 ? void 0 : fallback();
}

export {
  cloneElement,
  cloneVNodes,
  deepCloneElement,
  triggerVNodeUpdate,
  customRenderSlot
};
//# sourceMappingURL=chunk-H4KWW7KX.js.map
