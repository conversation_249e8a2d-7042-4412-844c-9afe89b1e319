var Ql=Object.defineProperty,eo=Object.defineProperties;var to=Object.getOwnPropertyDescriptors;var _t=Object.getOwnPropertySymbols;var Ta=Object.prototype.hasOwnProperty,Ma=Object.prototype.propertyIsEnumerable;var Wt=(l,a,t)=>a in l?Ql(l,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[a]=t,Q=(l,a)=>{for(var t in a||(a={}))Ta.call(a,t)&&Wt(l,t,a[t]);if(_t)for(var t of _t(a))Ma.call(a,t)&&Wt(l,t,a[t]);return l},xe=(l,a)=>eo(l,to(a));var ke=(l,a)=>{var t={};for(var o in l)Ta.call(l,o)&&a.indexOf(o)<0&&(t[o]=l[o]);if(l!=null&&_t)for(var o of _t(l))a.indexOf(o)<0&&Ma.call(l,o)&&(t[o]=l[o]);return t};var ht=(l,a,t)=>Wt(l,typeof a!="symbol"?a+"":a,t);var Y=(l,a,t)=>new Promise((o,s)=>{var n=d=>{try{i(t.next(d))}catch(f){s(f)}},r=d=>{try{i(t.throw(d))}catch(f){s(f)}},i=d=>d.done?o(d.value):Promise.resolve(d.value).then(n,r);i((t=t.apply(l,a)).next())});import{c7 as ao,aR as be,bX as qa,c8 as lo,c9 as Yt,bs as lt,ca as oo,cb as no,bl as Oe,cc as so,cd as ro,ce as io,cf as uo,cg as co,ch as po,ci as fo,cj as mo,ck as ho,cl as bo,cm as vo,cn as go,co as yo,cp as wo,cq as xo,bG as ko,bH as Co,bp as Ya,a7 as at,bI as So,bJ as _o,bK as To,cr as Mo,cs as $o,bk as $e,aU as Pe,ct as Bo,bS as Ue,az as Me,aj as Lt,b6 as Jt,cu as Vo,cv as Eo,cw as Lo,cx as zo,cy as Po,cz as Ao,bC as Ze,cA as Io,bR as Uo,u as ot,aT as nt,bB as Ge,bx as Ja,b9 as Ho,bL as Xa,bM as Oo,bN as Za,bO as $a,bz as Do,aS as g,a8 as ze,cB as Wo,bj as Qa,cC as el,c6 as st,cD as tl,cE as Ro,b5 as rt,cF as No,cG as Fo,cH as Ko,cI as jo,cJ as Go,cK as qo,cL as Yo,cM as al,cN as Jo,b2 as Xo,bv as Zo,cO as Qo,cP as en,cQ as tn,cR as an,cS as ln,cT as ll,cU as on,cV as tt,cW as nn,cX as sn,cY as rn,cZ as dn}from"./bootstrap-DlHXJWd_.js";import{bo as un,bp as cn,bU as ol,a1 as I,r as pn,a4 as V,av as k,ab as u,aV as R,a7 as e,aW as se,a8 as E,aa as y,ac as c,x as m,J as w,ad as fe,af as He,ag as Qe,aq as A,aj as _,R as zt,P as X,ar as fn,aw as ce,a_ as ve,b4 as mn,a$ as x,Y as pe,F as Z,aC as ue,i as vt,a5 as Vt,bV as Pt,bt as hn,a9 as qe,aB as $,ai as z,ah as Le,bW as bn,a2 as et,ax as St,O as vn,bX as gn,an as yn,bK as wn,aF as At,bn as xn,T as pt,k as Ba,n as je,bY as kn,M as nl,bZ as Tt,aA as Ee,b_ as Va,aZ as Gt,bi as Cn,b$ as sl,c0 as wt,c1 as Et,c2 as Sn,c3 as Mt,c4 as _n,U as Xt,as as Ne,b2 as it,be as Tn,c5 as Mn,c6 as $n,c7 as Bn,c8 as Ea,c9 as Vn,ca as La,ae as rl,bx as za,aJ as En,ao as il,cb as Ln,cc as zn,b6 as ut,cd as Pa,ce as Pn,ba as Aa,q as It,az as dl,aI as qt,_ as An,bO as In,bT as Ia,aD as ul,N as Un,a3 as Ua}from"../jse/index-index-DYNcUVMZ.js";import{_ as xt}from"./avatar.vue_vue_type_script_setup_true_lang-CiaVJ8BJ.js";import{_ as Hn,S as On,c as Dn,u as cl,s as pl,a as ft}from"./use-vben-form-D_KgkLrU.js";import{_ as Zt,a as Qt,b as ea,c as gt,d as Wn,e as fl,f as ml,S as Rn,M as Nn,g as Fn,h as Kn,i as jn,j as Gn}from"./theme-toggle.vue_vue_type_script_setup_true_lang-BkcvcZZW.js";import{C as qn,X as ct,u as kt,d as Yn}from"./use-modal-C76TZrr_.js";import{a as Jn,b as Xn,_ as Zn}from"./TabsList.vue_vue_type_script_setup_true_lang-DsT_45_5.js";import{_ as Qn}from"./render-content.vue_vue_type_script_lang-D2VaNJ52.js";import{R as ta}from"./rotate-cw-C3yB0h8-.js";function aa(l,a){for(const t of l){if(t.path===a)return t;const o=t.children&&aa(t.children,a);if(o)return o}return null}function yt(l,a,t=0){var r;const o=aa(l,a),s=(r=o==null?void 0:o.parents)==null?void 0:r[t],n=s?l.find(i=>i.path===s):void 0;return{findMenu:o,rootMenu:n,rootMenuPath:s}}const mt=ao("core-tabbar",{actions:{_bulkCloseByKeys(l){return Y(this,null,function*(){const a=new Set(l);this.tabs=this.tabs.filter(t=>!a.has(Fe(t))),yield this.updateCacheTabs()})},_close(l){if(Ye(l))return;const a=this.tabs.findIndex(t=>Je(t,l));a!==-1&&this.tabs.splice(a,1)},_goToDefaultTab(l){return Y(this,null,function*(){if(this.getTabs.length<=0)return;const a=this.getTabs[0];a&&(yield this._goToTab(a,l))})},_goToTab(l,a){return Y(this,null,function*(){const{params:t,path:o,query:s}=l,n={params:t||{},path:o,query:s||{}};yield a.replace(n)})},addTab(l){var o,s;let a=es(l);if(a.key||(a.key=Ke(l)),!ts(a))return a;const t=this.tabs.findIndex(n=>Je(n,a));if(t===-1){const n=I.tabbar.maxCount,r=(s=(o=l==null?void 0:l.meta)==null?void 0:o.maxNumOfOpenTab)!=null?s:-1;if(r>0&&this.tabs.filter(i=>i.name===l.name).length>=r){const i=this.tabs.findIndex(d=>d.name===l.name);i!==-1&&this.tabs.splice(i,1)}else if(n>0&&this.tabs.length>=n){const i=this.tabs.findIndex(d=>!Reflect.has(d.meta,"affixTab")||!d.meta.affixTab);i!==-1&&this.tabs.splice(i,1)}this.tabs.push(a)}else{const n=pn(this.tabs)[t],r=xe(Q(Q({},n),a),{meta:Q(Q({},n==null?void 0:n.meta),a.meta)});if(n){const i=n.meta;Reflect.has(i,"affixTab")&&(r.meta.affixTab=i.affixTab),Reflect.has(i,"newTabTitle")&&(r.meta.newTabTitle=i.newTabTitle)}a=r,this.tabs.splice(t,1,r)}return this.updateCacheTabs(),a},closeAllTabs(l){return Y(this,null,function*(){const a=this.tabs.filter(t=>Ye(t));this.tabs=a.length>0?a:[...this.tabs].splice(0,1),yield this._goToDefaultTab(l),this.updateCacheTabs()})},closeLeftTabs(l){return Y(this,null,function*(){const a=this.tabs.findIndex(s=>Je(s,l));if(a<1)return;const t=this.tabs.slice(0,a),o=[];for(const s of t)Ye(s)||o.push(s.key);yield this._bulkCloseByKeys(o)})},closeOtherTabs(l){return Y(this,null,function*(){const a=this.tabs.map(o=>Fe(o)),t=[];for(const o of a)if(o!==Fe(l)){const s=this.tabs.find(n=>Fe(n)===o);if(!s)continue;Ye(s)||t.push(s.key)}yield this._bulkCloseByKeys(t)})},closeRightTabs(l){return Y(this,null,function*(){const a=this.tabs.findIndex(t=>Je(t,l));if(a!==-1&&a<this.tabs.length-1){const t=this.tabs.slice(a+1),o=[];for(const s of t)Ye(s)||o.push(s.key);yield this._bulkCloseByKeys(o)}})},closeTab(l,a){return Y(this,null,function*(){const{currentRoute:t}=a;if(Ke(t.value)!==Fe(l)){this._close(l),this.updateCacheTabs();return}const o=this.getTabs.findIndex(r=>Fe(r)===Ke(t.value)),s=this.getTabs[o-1],n=this.getTabs[o+1];n?(this._close(l),yield this._goToTab(n,a)):s?(this._close(l),yield this._goToTab(s,a)):console.error("Failed to close the tab; only one tab remains open.")})},closeTabByKey(l,a){return Y(this,null,function*(){const t=decodeURIComponent(l),o=this.tabs.findIndex(n=>Fe(n)===t);if(o===-1)return;const s=this.tabs[o];s&&(yield this.closeTab(s,a))})},getTabByKey(l){return this.getTabs.find(a=>Fe(a)===l)},openTabInNewWindow(l){return Y(this,null,function*(){ol(l.fullPath||l.path)})},pinTab(l){return Y(this,null,function*(){var n;const a=this.tabs.findIndex(r=>Je(r,l));if(a===-1)return;const t=this.tabs[a];l.meta.affixTab=!0,l.meta.title=(n=t==null?void 0:t.meta)==null?void 0:n.title,this.tabs.splice(a,1,l);const s=this.tabs.filter(r=>Ye(r)).findIndex(r=>Je(r,l));yield this.sortTabs(a,s)})},refresh(l){return Y(this,null,function*(){if(typeof l=="string")return yield this.refreshByName(l);const{currentRoute:a}=l,{name:t}=a.value;this.excludeCachedTabs.add(t),this.renderRouteView=!1,un(),yield new Promise(o=>setTimeout(o,200)),this.excludeCachedTabs.delete(t),this.renderRouteView=!0,cn()})},refreshByName(l){return Y(this,null,function*(){this.excludeCachedTabs.add(l),yield new Promise(a=>setTimeout(a,200)),this.excludeCachedTabs.delete(l)})},resetTabTitle(l){return Y(this,null,function*(){var t;if((t=l==null?void 0:l.meta)!=null&&t.newTabTitle)return;const a=this.tabs.find(o=>Je(o,l));a&&(a.meta.newTabTitle=void 0,yield this.updateCacheTabs())})},setAffixTabs(l){for(const a of l)a.meta.affixTab=!0,this.addTab(as(a))},setMenuList(l){this.menuList=l},setTabTitle(l,a){return Y(this,null,function*(){const t=this.tabs.find(o=>Je(o,l));t&&(t.meta.newTabTitle=a,yield this.updateCacheTabs())})},setUpdateTime(){this.updateTime=Date.now()},sortTabs(l,a){return Y(this,null,function*(){const t=this.tabs[l];t&&(this.tabs.splice(l,1),this.tabs.splice(a,0,t),this.dragEndIndex=this.dragEndIndex+1)})},toggleTabPin(l){return Y(this,null,function*(){var t,o;yield((o=(t=l==null?void 0:l.meta)==null?void 0:t.affixTab)!=null?o:!1)?this.unpinTab(l):this.pinTab(l)})},unpinTab(l){return Y(this,null,function*(){var n;const a=this.tabs.findIndex(r=>Je(r,l));if(a===-1)return;const t=this.tabs[a];l.meta.affixTab=!1,l.meta.title=(n=t==null?void 0:t.meta)==null?void 0:n.title,this.tabs.splice(a,1,l);const s=this.tabs.filter(r=>Ye(r)).length;yield this.sortTabs(a,s)})},updateCacheTabs(){return Y(this,null,function*(){var a;const l=new Set;for(const t of this.tabs){if(!((a=t.meta)==null?void 0:a.keepAlive))continue;(t.matched||[]).forEach((n,r)=>{r>0&&l.add(n.name)});const s=t.name;l.add(s)}this.cachedTabs=l})}},getters:{affixTabs(){return this.tabs.filter(a=>Ye(a)).sort((a,t)=>{var n,r,i,d;const o=(r=(n=a.meta)==null?void 0:n.affixTabOrder)!=null?r:0,s=(d=(i=t.meta)==null?void 0:i.affixTabOrder)!=null?d:0;return o-s})},getCachedTabs(){return[...this.cachedTabs]},getExcludeCachedTabs(){return[...this.excludeCachedTabs]},getMenuList(){return this.menuList},getTabs(){const l=this.tabs.filter(a=>!Ye(a));return[...this.affixTabs,...l].filter(Boolean)}},persist:[{pick:["tabs"],storage:sessionStorage}],state:()=>({cachedTabs:new Set,dragEndIndex:0,excludeCachedTabs:new Set,menuList:["close","affix","maximize","reload","open-in-new-window","close-left","close-right","close-other","close-all"],renderRouteView:!0,tabs:[],updateTime:Date.now()})});function es(l){if(!l)return l;const s=l,{matched:a,meta:t}=s,o=ke(s,["matched","meta"]);return xe(Q({},o),{matched:a?a.map(n=>({meta:n.meta,name:n.name,path:n.path})):void 0,meta:xe(Q({},t),{newTabTitle:t.newTabTitle})})}function Ye(l){var a,t;return(t=(a=l==null?void 0:l.meta)==null?void 0:a.affixTab)!=null?t:!1}function ts(l){var t;const a=(t=l==null?void 0:l.matched)!=null?t:[];return!l.meta.hideInTab&&a.every(o=>!o.meta.hideInTab)}function Ke(l){const{fullPath:a,path:t,meta:{fullPathKey:o}={},query:s={}}=l,n=Array.isArray(s.pageKey)?s.pageKey[0]:s.pageKey;let r;n?r=n:r=o===!1?t:a!=null?a:t;try{return decodeURIComponent(r)}catch(i){return r}}function Fe(l){var a;return(a=l.key)!=null?a:Ke(l)}function Je(l,a){return Fe(l)===Fe(a)}function as(l){return{meta:l.meta,name:l.name,path:l.path,key:Ke(l)}}const ls=be("arrow-down",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);const os=be("arrow-left-to-line",[["path",{d:"M3 19V5",key:"rwsyhb"}],["path",{d:"m13 6-6 6 6 6",key:"1yhaz7"}],["path",{d:"M7 12h14",key:"uoisry"}]]);const ns=be("arrow-right-left",[["path",{d:"m16 3 4 4-4 4",key:"1x1c3m"}],["path",{d:"M20 7H4",key:"zbl0bi"}],["path",{d:"m8 21-4-4 4-4",key:"h9nckh"}],["path",{d:"M4 17h16",key:"g4d7ey"}]]);const ss=be("arrow-right-to-line",[["path",{d:"M17 12H3",key:"8awo09"}],["path",{d:"m11 18 6-6-6-6",key:"8c2y43"}],["path",{d:"M21 5v14",key:"nzette"}]]);const rs=be("arrow-up-to-line",[["path",{d:"M5 3h14",key:"7usisc"}],["path",{d:"m18 13-6-6-6 6",key:"1kf1n9"}],["path",{d:"M12 7v14",key:"1akyts"}]]);const is=be("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]);const ds=be("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]);const us=be("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);const cs=be("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);const ps=be("corner-down-left",[["polyline",{points:"9 10 4 15 9 20",key:"r3jprv"}],["path",{d:"M20 4v7a4 4 0 0 1-4 4H4",key:"6o5b7l"}]]);const fs=be("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);const ms=be("fold-horizontal",[["path",{d:"M2 12h6",key:"1wqiqv"}],["path",{d:"M22 12h-6",key:"1eg9hc"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 8v2",key:"1woqiv"}],["path",{d:"M12 14v2",key:"8jcxud"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m19 9-3 3 3 3",key:"12ol22"}],["path",{d:"m5 15 3-3-3-3",key:"1kdhjc"}]]);const hl=be("fullscreen",[["path",{d:"M3 7V5a2 2 0 0 1 2-2h2",key:"aa7l1z"}],["path",{d:"M17 3h2a2 2 0 0 1 2 2v2",key:"4qcy5o"}],["path",{d:"M21 17v2a2 2 0 0 1-2 2h-2",key:"6vwrx8"}],["path",{d:"M7 21H5a2 2 0 0 1-2-2v-2",key:"ioqczr"}],["rect",{width:"10",height:"8",x:"7",y:"8",rx:"1",key:"vys8me"}]]);const bl=be("lock-keyhole",[["circle",{cx:"12",cy:"16",r:"1",key:"1au0dj"}],["rect",{x:"3",y:"10",width:"18",height:"12",rx:"2",key:"6s8ecr"}],["path",{d:"M7 10V7a5 5 0 0 1 10 0v3",key:"1pqi11"}]]);const hs=be("log-out",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);const bs=be("mail-check",[["path",{d:"M22 13V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v12c0 1.1.9 2 2 2h8",key:"12jkf8"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}],["path",{d:"m16 19 2 2 4-4",key:"1b14m6"}]]);const vs=be("maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]]);const vl=be("minimize-2",[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]);const gs=be("minimize",[["path",{d:"M8 3v3a2 2 0 0 1-2 2H3",key:"hohbtr"}],["path",{d:"M21 8h-3a2 2 0 0 1-2-2V3",key:"5jw1f3"}],["path",{d:"M3 16h3a2 2 0 0 1 2 2v3",key:"198tvr"}],["path",{d:"M16 21v-3a2 2 0 0 1 2-2h3",key:"ph8mxp"}]]);const gl=be("pin-off",[["path",{d:"M12 17v5",key:"bb1du9"}],["path",{d:"M15 9.34V7a1 1 0 0 1 1-1 2 2 0 0 0 0-4H7.89",key:"znwnzq"}],["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M9 9v1.76a2 2 0 0 1-1.11 1.79l-1.78.9A2 2 0 0 0 5 15.24V16a1 1 0 0 0 1 1h11",key:"c9qhm2"}]]);const Ut=be("pin",[["path",{d:"M12 17v5",key:"bb1du9"}],["path",{d:"M9 10.76a2 2 0 0 1-1.11 1.79l-1.78.9A2 2 0 0 0 5 15.24V16a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-.76a2 2 0 0 0-1.11-1.79l-1.78-.9A2 2 0 0 1 15 10.76V7a1 1 0 0 1 1-1 2 2 0 0 0 0-4H8a2 2 0 0 0 0 4 1 1 0 0 1 1 1z",key:"1nkz8b"}]]);const ys=be("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);const ws=be("search-x",[["path",{d:"m13.5 8.5-5 5",key:"1cs55j"}],["path",{d:"m8.5 8.5 5 5",key:"a8mexj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);const Ha=be("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);const yl=be("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);const xs=be("user-round-pen",[["path",{d:"M2 21a8 8 0 0 1 10.821-7.487",key:"1c8h7z"}],["path",{d:"M21.378 16.626a1 1 0 0 0-3.004-3.004l-4.01 4.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1817ys"}],["circle",{cx:"10",cy:"8",r:"5",key:"o932ke"}]]),ks=qa("inline-flex items-center rounded-md border border-border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{defaultVariants:{variant:"default"},variants:{variant:{default:"border-transparent bg-accent hover:bg-accent text-primary-foreground shadow",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive-hover",outline:"text-foreground",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80"}}}),Cs=V({__name:"Badge",props:{class:{},variant:{}},setup(l){const a=l;return(t,o)=>(u(),k("div",{class:R(e(se)(e(ks)({variant:t.variant}),a.class))},[E(t.$slots,"default")],2))}}),Ss=V({__name:"Breadcrumb",props:{class:{}},setup(l){const a=l;return(t,o)=>(u(),k("nav",{class:R(a.class),"aria-label":"breadcrumb",role:"navigation"},[E(t.$slots,"default")],2))}}),_s=V({__name:"BreadcrumbItem",props:{class:{}},setup(l){const a=l;return(t,o)=>(u(),k("li",{class:R(e(se)("hover:text-foreground inline-flex items-center gap-1.5",a.class))},[E(t.$slots,"default")],2))}}),Ts=V({__name:"BreadcrumbLink",props:{asChild:{type:Boolean},as:{default:"a"},class:{}},setup(l){const a=l;return(t,o)=>(u(),y(e(lo),{as:t.as,"as-child":t.asChild,class:R(e(se)("hover:text-foreground transition-colors",a.class))},{default:c(()=>[E(t.$slots,"default")]),_:3},8,["as","as-child","class"]))}}),Ms=V({__name:"BreadcrumbList",props:{class:{}},setup(l){const a=l;return(t,o)=>(u(),k("ol",{class:R(e(se)("text-muted-foreground flex flex-wrap items-center gap-1.5 break-words text-sm sm:gap-2.5",a.class))},[E(t.$slots,"default")],2))}}),$s=V({__name:"BreadcrumbPage",props:{class:{}},setup(l){const a=l;return(t,o)=>(u(),k("span",{class:R(e(se)("text-foreground font-normal",a.class)),"aria-current":"page","aria-disabled":"true",role:"link"},[E(t.$slots,"default")],2))}}),Bs=V({__name:"BreadcrumbSeparator",props:{class:{}},setup(l){const a=l;return(t,o)=>(u(),k("li",{class:R(e(se)("[&>svg]:size-3.5",a.class)),"aria-hidden":"true",role:"presentation"},[E(t.$slots,"default",{},()=>[m(e(Yt))])],2))}}),Vs=V({__name:"DropdownMenuLabel",props:{asChild:{type:Boolean},as:{},class:{},inset:{type:Boolean}},setup(l){const a=l,t=w(()=>{const r=a,{class:s}=r;return ke(r,["class"])}),o=lt(t);return(s,n)=>(u(),y(e(oo),fe(e(o),{class:e(se)("px-2 py-1.5 text-sm font-semibold",s.inset&&"pl-8",a.class)}),{default:c(()=>[E(s.$slots,"default")]),_:3},16,["class"]))}}),$t=V({__name:"DropdownMenuSeparator",props:{asChild:{type:Boolean},as:{},class:{}},setup(l){const a=l,t=w(()=>{const n=a,{class:o}=n;return ke(n,["class"])});return(o,s)=>(u(),y(e(no),fe(t.value,{class:e(se)("bg-border -mx-1 my-1 h-px",a.class)}),null,16,["class"]))}}),Oa=V({__name:"DropdownMenuShortcut",props:{class:{}},setup(l){const a=l;return(t,o)=>(u(),k("span",{class:R(e(se)("ml-auto text-xs tracking-widest opacity-60",a.class))},[E(t.$slots,"default")],2))}}),Es=V({__name:"HoverCard",props:{defaultOpen:{type:Boolean},open:{type:Boolean},openDelay:{},closeDelay:{}},emits:["update:open"],setup(l,{emit:a}){const s=Oe(l,a);return(n,r)=>(u(),y(e(so),He(Qe(e(s))),{default:c(()=>[E(n.$slots,"default")]),_:3},16))}}),Ls=V({__name:"HoverCardContent",props:{forceMount:{type:Boolean},side:{},sideOffset:{default:4},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(l){const a=l,t=w(()=>{const r=a,{class:s}=r;return ke(r,["class"])}),o=lt(t);return(s,n)=>(u(),y(e(ro),null,{default:c(()=>[m(e(io),fe(e(o),{class:e(se)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 border-border z-popup w-64 rounded-md border p-4 shadow-md outline-none",a.class)}),{default:c(()=>[E(s.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),zs=V({__name:"HoverCardTrigger",props:{asChild:{type:Boolean},as:{}},setup(l){const a=l;return(t,o)=>(u(),y(e(uo),He(Qe(a)),{default:c(()=>[E(t.$slots,"default")]),_:3},16))}}),Ps=V({__name:"NumberField",props:{defaultValue:{},modelValue:{},min:{},max:{},step:{},formatOptions:{},locale:{},disabled:{type:Boolean},required:{type:Boolean},name:{},id:{},asChild:{type:Boolean},as:{},class:{}},emits:["update:modelValue"],setup(l,{emit:a}){const t=l,o=a,s=w(()=>{const d=t,{class:r}=d;return ke(d,["class"])}),n=Oe(s,o);return(r,i)=>(u(),y(e(co),fe(e(n),{class:e(se)("grid gap-1.5",t.class)}),{default:c(()=>[E(r.$slots,"default")]),_:3},16,["class"]))}}),As=V({__name:"NumberFieldContent",props:{class:{}},setup(l){const a=l;return(t,o)=>(u(),k("div",{class:R(e(se)("relative [&>[data-slot=input]]:has-[[data-slot=decrement]]:pl-5 [&>[data-slot=input]]:has-[[data-slot=increment]]:pr-5",a.class))},[E(t.$slots,"default")],2))}}),Is=V({__name:"NumberFieldDecrement",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(l){const a=l,t=w(()=>{const r=a,{class:s}=r;return ke(r,["class"])}),o=lt(t);return(s,n)=>(u(),y(e(fo),fe({"data-slot":"decrement"},e(o),{class:e(se)("absolute left-0 top-1/2 -translate-y-1/2 p-3 disabled:cursor-not-allowed disabled:opacity-20",a.class)}),{default:c(()=>[E(s.$slots,"default",{},()=>[m(e(po),{class:"h-4 w-4"})])]),_:3},16,["class"]))}}),Us=V({__name:"NumberFieldIncrement",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(l){const a=l,t=w(()=>{const r=a,{class:s}=r;return ke(r,["class"])}),o=lt(t);return(s,n)=>(u(),y(e(mo),fe({"data-slot":"increment"},e(o),{class:e(se)("absolute right-0 top-1/2 -translate-y-1/2 p-3 disabled:cursor-not-allowed disabled:opacity-20",a.class)}),{default:c(()=>[E(s.$slots,"default",{},()=>[m(e(ys),{class:"h-4 w-4"})])]),_:3},16,["class"]))}}),Hs=V({__name:"NumberFieldInput",setup(l){return(a,t)=>(u(),y(e(ho),{class:R(e(se)("border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent py-1 text-center text-sm shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50")),"data-slot":"input"},null,8,["class"]))}}),wl=V({__name:"ScrollBar",props:{orientation:{default:"vertical"},forceMount:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(l){const a=l,t=w(()=>{const n=a,{class:o}=n;return ke(n,["class"])});return(o,s)=>(u(),y(e(bo),fe(t.value,{class:e(se)("flex touch-none select-none transition-colors",o.orientation==="vertical"&&"h-full w-2.5 border-l border-l-transparent p-px",o.orientation==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent p-px",a.class)}),{default:c(()=>[m(e(vo),{class:"bg-border relative flex-1 rounded-full"})]),_:1},16,["class"]))}}),Os=V({__name:"ScrollArea",props:{type:{},dir:{},scrollHideDelay:{},asChild:{type:Boolean},as:{},class:{},onScroll:{type:Function,default:()=>{}},viewportProps:{}},setup(l){const a=l,t=w(()=>{const n=a,{class:o}=n;return ke(n,["class"])});return(o,s)=>(u(),y(e(go),fe(t.value,{class:e(se)("relative overflow-hidden",a.class)}),{default:c(()=>[m(e(yo),{"as-child":"",class:"h-full w-full rounded-[inherit] focus:outline-none",onScroll:o.onScroll},{default:c(()=>[E(o.$slots,"default")]),_:3},8,["onScroll"]),m(wl),m(e(wo))]),_:3},16,["class"]))}}),Ds=V({__name:"Separator",props:{orientation:{},decorative:{type:Boolean},asChild:{type:Boolean},as:{},class:{},label:{}},setup(l){const a=l,t=w(()=>{const n=a,{class:o}=n;return ke(n,["class"])});return(o,s)=>(u(),y(e(xo),fe(t.value,{class:e(se)("bg-border relative shrink-0",a.orientation==="vertical"?"h-full w-px":"h-px w-full",a.class)}),{default:c(()=>[a.label?(u(),k("span",{key:0,class:R(e(se)("text-muted-foreground bg-background absolute left-1/2 top-1/2 flex -translate-x-1/2 -translate-y-1/2 items-center justify-center text-xs",a.orientation==="vertical"?"w-[1px] px-1 py-2":"h-[1px] px-2 py-1"))},_(a.label),3)):A("",!0)]),_:1},16,["class"]))}}),Ws=qa("bg-background shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500 border-border",{defaultVariants:{side:"right"},variants:{side:{bottom:"inset-x-0 bottom-0 border-t border-border data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left ",right:"inset-y-0 right-0 w-3/4 border-l  data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right",top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top"}}}),Rs=V({__name:"Sheet",props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(l,{emit:a}){const s=Oe(l,a);return(n,r)=>(u(),y(e(ko),He(Qe(e(s))),{default:c(()=>[E(n.$slots,"default")]),_:3},16))}}),Da=V({__name:"SheetClose",props:{asChild:{type:Boolean},as:{}},setup(l){const a=l;return(t,o)=>(u(),y(e(Co),He(Qe(a)),{default:c(()=>[E(t.$slots,"default")]),_:3},16))}}),Ns=["data-dismissable-drawer"],Fs=V({__name:"SheetOverlay",setup(l){Ya();const a=zt("DISMISSABLE_DRAWER_ID");return(t,o)=>(u(),k("div",{"data-dismissable-drawer":e(a),class:"bg-overlay z-popup inset-0"},null,8,Ns))}}),Ks=V({inheritAttrs:!1,__name:"SheetContent",props:{appendTo:{default:"body"},class:{},modal:{type:Boolean},open:{type:Boolean},overlayBlur:{},side:{},zIndex:{},forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus","close","closed","opened"],setup(l,{emit:a}){const t=l,o=a,s=w(()=>{const H=t,{class:p,modal:v,open:h,side:b}=H;return ke(H,["class","modal","open","side"])});function n(){return t.appendTo==="body"||t.appendTo===document.body||!t.appendTo}const r=w(()=>n()?"fixed":"absolute"),i=Oe(s,o),d=X(null);function f(p){var v;p.target===((v=d.value)==null?void 0:v.$el)&&(t.open?o("opened"):o("closed"))}return(p,v)=>(u(),y(fn,{defer:"",to:p.appendTo},[m(at,{name:"fade"},{default:c(()=>[p.open&&p.modal?(u(),y(Fs,{key:0,style:ce(xe(Q({},p.zIndex?{zIndex:p.zIndex}:{}),{position:r.value,backdropFilter:p.overlayBlur&&p.overlayBlur>0?`blur(${p.overlayBlur}px)`:"none"}))},null,8,["style"])):A("",!0)]),_:1}),m(e(So),fe({ref_key:"contentRef",ref:d,class:e(se)("z-popup",e(Ws)({side:p.side}),t.class),style:xe(Q({},p.zIndex?{zIndex:p.zIndex}:{}),{position:r.value}),onAnimationend:f},Q(Q({},e(i)),p.$attrs)),{default:c(()=>[E(p.$slots,"default")]),_:3},16,["class","style"])],8,["to"]))}}),Rt=V({__name:"SheetDescription",props:{asChild:{type:Boolean},as:{},class:{}},setup(l){const a=l,t=w(()=>{const n=a,{class:o}=n;return ke(n,["class"])});return(o,s)=>(u(),y(e(_o),fe({class:e(se)("text-muted-foreground text-sm",a.class)},t.value),{default:c(()=>[E(o.$slots,"default")]),_:3},16,["class"]))}}),js=V({__name:"SheetFooter",props:{class:{}},setup(l){const a=l;return(t,o)=>(u(),k("div",{class:R(e(se)("flex flex-row flex-col-reverse justify-end gap-x-2",a.class))},[E(t.$slots,"default")],2))}}),Gs=V({__name:"SheetHeader",props:{class:{}},setup(l){const a=l;return(t,o)=>(u(),k("div",{class:R(e(se)("flex flex-col text-center sm:text-left",a.class))},[E(t.$slots,"default")],2))}}),Nt=V({__name:"SheetTitle",props:{asChild:{type:Boolean},as:{},class:{}},setup(l){const a=l,t=w(()=>{const n=a,{class:o}=n;return ke(n,["class"])});return(o,s)=>(u(),y(e(To),fe({class:e(se)("text-foreground font-medium",a.class)},t.value),{default:c(()=>[E(o.$slots,"default")]),_:3},16,["class"]))}}),qs=V({__name:"Switch",props:{defaultChecked:{type:Boolean},checked:{type:Boolean},disabled:{type:Boolean},required:{type:Boolean},name:{},id:{},value:{},asChild:{type:Boolean},as:{},class:{}},emits:["update:checked"],setup(l,{emit:a}){const t=l,o=a,s=w(()=>{const d=t,{class:r}=d;return ke(d,["class"])}),n=Oe(s,o);return(r,i)=>(u(),y(e($o),fe(e(n),{class:e(se)("focus-visible:ring-ring focus-visible:ring-offset-background data-[state=checked]:bg-primary data-[state=unchecked]:bg-input peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t.class)}),{default:c(()=>[m(e(Mo),{class:R(e(se)("bg-background pointer-events-none block h-4 w-4 rounded-full shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0"))},null,8,["class"])]),_:1},16,["class"]))}}),Ys=V({name:"VbenButtonGroup",__name:"button-group",props:{border:{type:Boolean,default:!1},gap:{default:0},size:{default:"middle"}},setup(l){return(a,t)=>(u(),k("div",{class:R(e(se)("vben-button-group rounded-md",`size-${a.size}`,a.gap?"with-gap":"no-gap",a.$attrs.class)),style:ce({gap:a.gap?`${a.gap}px`:"0px"})},[E(a.$slots,"default",{},void 0,!0)],6))}}),Js=$e(Ys,[["__scopeId","data-v-ba11c217"]]),Xs={key:0,class:"icon-wrapper"},Zs=V({__name:"check-button-group",props:ve({allowClear:{type:Boolean,default:!1},beforeChange:{},btnClass:{},gap:{default:0},maxCount:{default:0},multiple:{type:Boolean,default:!1},options:{},showIcon:{type:Boolean,default:!0},size:{default:"middle"},disabled:{type:Boolean}},{modelValue:{},modelModifiers:{}}),emits:ve(["btnClick"],["update:modelValue"]),setup(l,{emit:a}){const t=l,o=a,s=w(()=>xe(Q({},mn(t,["options","btnClass","size","disabled"])),{class:se(t.btnClass)})),n=x(l,"modelValue"),r=X([]),i=X([]);pe(()=>t.multiple,f=>{f?n.value=r.value:n.value=r.value.length>0?r.value[0]:void 0}),pe(()=>n.value,f=>{if(Array.isArray(f)){const p=f.filter(v=>v!==void 0);p.length>0?r.value=t.multiple?[...p]:[p[0]]:r.value=[]}else r.value=f===void 0?[]:[f]},{deep:!0,immediate:!0});function d(f){return Y(this,null,function*(){if(t.beforeChange&&vt(t.beforeChange))try{if(i.value.push(f),(yield t.beforeChange(f,!r.value.includes(f)))===!1)return}finally{i.value.splice(i.value.indexOf(f),1)}if(t.multiple)r.value.includes(f)?r.value=r.value.filter(p=>p!==f):(t.maxCount>0&&r.value.length>=t.maxCount&&(r.value=r.value.slice(0,t.maxCount-1)),r.value.push(f)),n.value=r.value;else if(t.allowClear&&r.value.includes(f)){r.value=[],n.value=void 0,o("btnClick",void 0);return}else r.value=[f],n.value=f;o("btnClick",f)})}return(f,p)=>(u(),y(Js,{size:t.size,gap:t.gap,class:"vben-check-button-group"},{default:c(()=>[(u(!0),k(Z,null,ue(t.options,(v,h)=>(u(),y(Pe,fe({key:h,class:e(se)("border",t.btnClass),disabled:t.disabled||i.value.includes(v.value)||!t.multiple&&i.value.length>0},{ref_for:!0},s.value,{variant:r.value.includes(v.value)?"default":"outline",onClick:b=>d(v.value),type:"button"}),{default:c(()=>[t.showIcon?(u(),k("div",Xs,[E(f.$slots,"icon",{loading:i.value.includes(v.value),checked:r.value.includes(v.value)},()=>[i.value.includes(v.value)?(u(),y(e(Bo),{key:0,class:"animate-spin"})):r.value.includes(v.value)?(u(),y(e(qn),{key:1})):(u(),y(e(us),{key:2}))],!0)])):A("",!0),E(f.$slots,"option",{label:v.label,value:v.value,data:v},()=>[m(e(Qn),{content:v.label},null,8,["content"])],!0)]),_:2},1040,["class","disabled","variant","onClick"]))),128))]),_:3},8,["size","gap"]))}}),Qs=$e(Zs,[["__scopeId","data-v-9a0233de"]]),er=l=>{const a=Vt(),t=Vt(),o=X(!1),s=()=>{var i;a.value&&(o.value=a.value.scrollTop>=((i=l==null?void 0:l.visibilityHeight)!=null?i:0))},n=()=>{var i;(i=a.value)==null||i.scrollTo({behavior:"smooth",top:0})},r=Pt(s,300,!0);return hn(t,"scroll",r),qe(()=>{var i;if(t.value=document,a.value=document.documentElement,l.target){if(a.value=(i=document.querySelector(l.target))!=null?i:void 0,!a.value)throw new Error(`target does not exist: ${l.target}`);t.value=a.value}s()}),{handleClick:n,visible:o}},tr=V({name:"BackTop",__name:"back-top",props:{bottom:{default:20},isGroup:{type:Boolean,default:!1},right:{default:24},target:{default:""},visibilityHeight:{default:200}},setup(l){const a=l,t=w(()=>({bottom:`${a.bottom}px`,right:`${a.right}px`})),{handleClick:o,visible:s}=er(a);return(n,r)=>(u(),y(at,{name:"fade-down"},{default:c(()=>[e(s)?(u(),y(e(Pe),{key:0,style:ce(t.value),class:"dark:bg-accent dark:hover:bg-heavy bg-background hover:bg-heavy data shadow-float z-popup fixed bottom-10 size-10 rounded-full duration-500",size:"icon",variant:"icon",onClick:e(o)},{default:c(()=>[m(e(rs),{class:"size-4"})]),_:1},8,["style","onClick"])):A("",!0)]),_:1}))}}),ar={class:"flex"},lr=["onClick"],or={class:"flex-center z-10 h-full"},nr=V({name:"Breadcrumb",__name:"breadcrumb-background",props:{breadcrumbs:{},showIcon:{type:Boolean},styleType:{}},emits:["select"],setup(l,{emit:a}){const t=a;function o(s,n){!n||s===l.breadcrumbs.length-1||t("select",n)}return(s,n)=>(u(),k("ul",ar,[m(Lt,{name:"breadcrumb-transition"},{default:c(()=>[(u(!0),k(Z,null,ue(s.breadcrumbs,(r,i)=>(u(),k("li",{key:`${r.path}-${r.title}-${i}`},[$("a",{href:"javascript:void 0",onClick:Me(d=>o(i,r.path),["stop"])},[$("span",or,[s.showIcon?(u(),y(e(Ue),{key:0,icon:r.icon,class:"mr-1 size-4 flex-shrink-0"},null,8,["icon"])):A("",!0),$("span",{class:R({"text-foreground font-normal":i===s.breadcrumbs.length-1})},_(r.title),3)])],8,lr)]))),128))]),_:1})]))}}),sr=$e(nr,[["__scopeId","data-v-da1498bb"]]),rr={key:0},ir={class:"flex-center"},dr={class:"flex-center"},ur=V({name:"Breadcrumb",__name:"breadcrumb",props:{breadcrumbs:{},showIcon:{type:Boolean,default:!1},styleType:{}},emits:["select"],setup(l,{emit:a}){const t=a;function o(s){s&&t("select",s)}return(s,n)=>(u(),y(e(Ss),null,{default:c(()=>[m(e(Ms),null,{default:c(()=>[m(Lt,{name:"breadcrumb-transition"},{default:c(()=>[(u(!0),k(Z,null,ue(s.breadcrumbs,(r,i)=>(u(),y(e(_s),{key:`${r.path}-${r.title}-${i}`},{default:c(()=>{var d,f;return[(f=(d=r.items)==null?void 0:d.length)!=null&&f?(u(),k("div",rr,[m(e(Zt),null,{default:c(()=>[m(e(Qt),{class:"flex items-center gap-1"},{default:c(()=>[s.showIcon?(u(),y(e(Ue),{key:0,icon:r.icon,class:"size-5"},null,8,["icon"])):A("",!0),z(" "+_(r.title)+" ",1),m(e(Jt),{class:"size-4"})]),_:2},1024),m(e(ea),{align:"start"},{default:c(()=>[(u(!0),k(Z,null,ue(r.items,p=>(u(),y(e(gt),{key:`sub-${p.path}`,onClick:Me(v=>o(p.path),["stop"])},{default:c(()=>[z(_(p.title),1)]),_:2},1032,["onClick"]))),128))]),_:2},1024)]),_:2},1024)])):i!==s.breadcrumbs.length-1?(u(),y(e(Ts),{key:1,href:"javascript:void 0",onClick:Me(p=>o(r.path),["stop"])},{default:c(()=>[$("div",ir,[s.showIcon?(u(),y(e(Ue),{key:0,class:R([{"size-5":r.isHome},"mr-1 size-4"]),icon:r.icon},null,8,["class","icon"])):A("",!0),z(" "+_(r.title),1)])]),_:2},1032,["onClick"])):(u(),y(e($s),{key:2},{default:c(()=>[$("div",dr,[s.showIcon?(u(),y(e(Ue),{key:0,class:R([{"size-5":r.isHome},"mr-1 size-4"]),icon:r.icon},null,8,["class","icon"])):A("",!0),z(" "+_(r.title),1)])]),_:2},1024)),i<s.breadcrumbs.length-1&&!r.isHome?(u(),y(e(Bs),{key:3})):A("",!0)]}),_:2},1024))),128))]),_:1})]),_:1})]),_:1}))}}),cr=V({__name:"breadcrumb-view",props:{class:{},breadcrumbs:{},showIcon:{type:Boolean},styleType:{}},emits:["select"],setup(l,{emit:a}){const s=Oe(l,a);return(n,r)=>(u(),k(Z,null,[n.styleType==="normal"?(u(),y(ur,fe({key:0},e(s),{class:"vben-breadcrumb"}),null,16)):A("",!0),n.styleType==="background"?(u(),y(sr,fe({key:1},e(s),{class:"vben-breadcrumb"}),null,16)):A("",!0)],64))}}),pr=$e(cr,[["__scopeId","data-v-4cd036dd"]]),fr=V({__name:"ContextMenu",props:{dir:{},modal:{type:Boolean,default:!1}},emits:["update:open"],setup(l,{emit:a}){const s=Oe(l,a);return(n,r)=>(u(),y(e(Vo),He(Qe(e(s))),{default:c(()=>[E(n.$slots,"default")]),_:3},16))}}),mr=V({__name:"ContextMenuContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},sticky:{},hideWhenDetached:{type:Boolean},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","closeAutoFocus"],setup(l,{emit:a}){const t=l,o=a,s=w(()=>{const d=t,{class:r}=d;return ke(d,["class"])}),n=Oe(s,o);return(r,i)=>(u(),y(e(Eo),null,{default:c(()=>[m(e(Lo),fe(e(n),{class:e(se)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 border-border z-popup min-w-32 overflow-hidden rounded-md border p-1 shadow-md",t.class)}),{default:c(()=>[E(r.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),hr=V({__name:"ContextMenuItem",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{},class:{},inset:{type:Boolean}},emits:["select"],setup(l,{emit:a}){const t=l,o=a,s=w(()=>{const d=t,{class:r}=d;return ke(d,["class"])}),n=Oe(s,o);return(r,i)=>(u(),y(e(zo),fe(e(n),{class:e(se)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r.inset&&"pl-8",t.class)}),{default:c(()=>[E(r.$slots,"default")]),_:3},16,["class"]))}}),br=V({__name:"ContextMenuSeparator",props:{asChild:{type:Boolean},as:{},class:{}},setup(l){const a=l,t=w(()=>{const n=a,{class:o}=n;return ke(n,["class"])});return(o,s)=>(u(),y(e(Po),fe(t.value,{class:e(se)("bg-border -mx-1 my-1 h-px",a.class)}),null,16,["class"]))}}),vr=V({__name:"ContextMenuShortcut",props:{class:{}},setup(l){const a=l;return(t,o)=>(u(),k("span",{class:R(e(se)("text-muted-foreground ml-auto text-xs tracking-widest",a.class))},[E(t.$slots,"default")],2))}}),gr=V({__name:"ContextMenuTrigger",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{}},setup(l){const t=lt(l);return(o,s)=>(u(),y(e(Ao),He(Qe(e(t))),{default:c(()=>[E(o.$slots,"default")]),_:3},16))}}),xl=V({__name:"context-menu",props:{dir:{},modal:{type:Boolean},class:{},contentClass:{},contentProps:{},handlerData:{},itemClass:{},menus:{type:Function}},emits:["update:open"],setup(l,{emit:a}){const t=l,o=a,s=w(()=>{const b=t,{class:d,contentClass:f,contentProps:p,itemClass:v}=b;return ke(b,["class","contentClass","contentProps","itemClass"])}),n=Oe(s,o),r=w(()=>{var d;return(d=t.menus)==null?void 0:d.call(t,t.handlerData)});function i(d){var f;d.disabled||(f=d==null?void 0:d.handler)==null||f.call(d,t.handlerData)}return(d,f)=>(u(),y(e(fr),He(Qe(e(n))),{default:c(()=>[m(e(gr),{"as-child":""},{default:c(()=>[E(d.$slots,"default")]),_:3}),m(e(mr),fe({class:d.contentClass},d.contentProps,{class:"side-content z-popup"}),{default:c(()=>[(u(!0),k(Z,null,ue(r.value,p=>(u(),k(Z,{key:p.key},[m(e(hr),{class:R([d.itemClass,"cursor-pointer"]),disabled:p.disabled,inset:p.inset||!p.icon,onClick:v=>i(p)},{default:c(()=>[p.icon?(u(),y(Le(p.icon),{key:0,class:"mr-2 size-4 text-lg"})):A("",!0),z(" "+_(p.text)+" ",1),p.shortcut?(u(),y(e(vr),{key:1},{default:c(()=>[z(_(p.shortcut),1)]),_:2},1024)):A("",!0)]),_:2},1032,["class","disabled","inset","onClick"]),p.separator?(u(),y(e(br),{key:0})):A("",!0)],64))),128))]),_:1},16,["class"])]),_:3},16))}}),yr=V({name:"DropdownMenu",__name:"dropdown-menu",props:{menus:{}},setup(l){const a=l;function t(o){var s;o.disabled||(s=o==null?void 0:o.handler)==null||s.call(o,a)}return(o,s)=>(u(),y(e(Zt),null,{default:c(()=>[m(e(Qt),{class:"flex h-full items-center gap-1"},{default:c(()=>[E(o.$slots,"default")]),_:3}),m(e(ea),{align:"start"},{default:c(()=>[m(e(Wn),null,{default:c(()=>[(u(!0),k(Z,null,ue(o.menus,n=>(u(),k(Z,{key:n.value},[m(e(gt),{disabled:n.disabled,class:"data-[state=checked]:bg-accent data-[state=checked]:text-accent-foreground text-foreground/80 mb-1 cursor-pointer",onClick:r=>t(n)},{default:c(()=>[n.icon?(u(),y(Le(n.icon),{key:0,class:"mr-2 size-4"})):A("",!0),z(" "+_(n.label),1)]),_:2},1032,["disabled","onClick"]),n.separator?(u(),y(e($t),{key:0,class:"bg-border"})):A("",!0)],64))),128))]),_:1})]),_:1})]),_:3}))}}),wr=V({name:"FullScreen",__name:"full-screen",setup(l){const{isFullscreen:a,toggle:t}=bn();return a.value=!!(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement),(o,s)=>(u(),y(e(Ze),{onClick:e(t)},{default:c(()=>[e(a)?(u(),y(e(gs),{key:0,class:"text-foreground size-4"})):(u(),y(e(vs),{key:1,class:"text-foreground size-4"}))]),_:1},8,["onClick"]))}}),xr={class:"h-full cursor-pointer"},kr=V({__name:"hover-card",props:{class:{},contentClass:{},contentProps:{},defaultOpen:{type:Boolean},open:{type:Boolean},openDelay:{},closeDelay:{}},emits:["update:open"],setup(l,{emit:a}){const t=l,o=a,s=w(()=>{const p=t,{class:r,contentClass:i,contentProps:d}=p;return ke(p,["class","contentClass","contentProps"])}),n=Oe(s,o);return(r,i)=>(u(),y(e(Es),He(Qe(e(n))),{default:c(()=>[m(e(zs),{"as-child":"",class:"h-full"},{default:c(()=>[$("div",xr,[E(r.$slots,"trigger")])]),_:3}),m(e(Ls),fe({class:r.contentClass},r.contentProps,{class:"side-content z-popup"}),{default:c(()=>[E(r.$slots,"default")]),_:3},16,["class"])]),_:3},16))}}),Cr=["href"],Sr={class:"text-foreground truncate text-nowrap font-semibold"},Wa=V({name:"VbenLogo",__name:"logo",props:{collapsed:{type:Boolean,default:!1},fit:{default:"cover"},href:{default:"javascript:void 0"},logoSize:{default:32},src:{default:""},text:{},theme:{default:"light"}},setup(l){return(a,t)=>(u(),k("div",{class:R([a.theme,"flex h-full items-center text-lg"])},[$("a",{class:R([a.$attrs.class,"flex h-full items-center gap-2 overflow-hidden px-3 text-lg leading-normal transition-all duration-500"]),href:a.href},[a.src?(u(),y(e(xt),{key:0,alt:a.text,src:a.src,size:a.logoSize,fit:a.fit,class:"relative rounded-none bg-transparent"},null,8,["alt","src","size","fit"])):A("",!0),a.collapsed?A("",!0):E(a.$slots,"text",{key:1},()=>[$("span",Sr,_(a.text),1)])],10,Cr)],2))}}),Ra=1,_r=V({__name:"scrollbar",props:{class:{default:""},horizontal:{type:Boolean,default:!1},scrollBarClass:{},shadow:{type:Boolean,default:!1},shadowBorder:{type:Boolean,default:!1},shadowBottom:{type:Boolean,default:!0},shadowLeft:{type:Boolean,default:!1},shadowRight:{type:Boolean,default:!1},shadowTop:{type:Boolean,default:!0}},emits:["scrollAt"],setup(l,{emit:a}){const t=l,o=a,s=X(!0),n=X(!1),r=X(!1),i=X(!0),d=w(()=>t.shadow&&t.shadowTop),f=w(()=>t.shadow&&t.shadowBottom),p=w(()=>t.shadow&&t.shadowLeft),v=w(()=>t.shadow&&t.shadowRight),h=w(()=>({"both-shadow":!i.value&&!n.value&&p.value&&v.value,"left-shadow":!i.value&&p.value,"right-shadow":!n.value&&v.value}));function b(S){var K,P,B,N,J,oe;const H=S.target,O=(K=H==null?void 0:H.scrollTop)!=null?K:0,C=(P=H==null?void 0:H.scrollLeft)!=null?P:0,W=(B=H==null?void 0:H.clientHeight)!=null?B:0,F=(N=H==null?void 0:H.clientWidth)!=null?N:0,U=(J=H==null?void 0:H.scrollHeight)!=null?J:0,L=(oe=H==null?void 0:H.scrollWidth)!=null?oe:0;s.value=O<=0,i.value=C<=0,r.value=Math.abs(O)+W>=U-Ra,n.value=Math.abs(C)+F>=L-Ra,o("scrollAt",{bottom:r.value,left:i.value,right:n.value,top:s.value})}return(S,H)=>(u(),y(e(Os),{class:R([[e(se)(t.class),h.value],"vben-scrollbar relative"]),"on-scroll":b},{default:c(()=>[d.value?(u(),k("div",{key:0,class:R([{"opacity-100":!s.value,"border-border border-t":S.shadowBorder&&!s.value},"scrollbar-top-shadow pointer-events-none absolute top-0 z-10 h-12 w-full opacity-0 transition-opacity duration-300 ease-in-out will-change-[opacity]"])},null,2)):A("",!0),E(S.$slots,"default",{},void 0,!0),f.value?(u(),k("div",{key:1,class:R([{"opacity-100":!s.value&&!r.value,"border-border border-b":S.shadowBorder&&!s.value&&!r.value},"scrollbar-bottom-shadow pointer-events-none absolute bottom-0 z-10 h-12 w-full opacity-0 transition-opacity duration-300 ease-in-out will-change-[opacity]"])},null,2)):A("",!0),S.horizontal?(u(),y(e(wl),{key:2,class:R(S.scrollBarClass),orientation:"horizontal"},null,8,["class"])):A("",!0)]),_:3},8,["class"]))}}),Ct=$e(_r,[["__scopeId","data-v-c94474ed"]]),Tr={class:"bg-background text-foreground inline-flex h-full w-full items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"},Mr=V({__name:"tabs-indicator",props:{asChild:{type:Boolean},as:{},class:{}},setup(l){const a=l,t=w(()=>{const r=a,{class:s}=r;return ke(r,["class"])}),o=lt(t);return(s,n)=>(u(),y(e(Io),fe(e(o),{class:e(se)("absolute bottom-0 left-0 z-10 h-full w-1/2 translate-x-[--radix-tabs-indicator-position] rounded-full px-0 py-1 pr-1 transition-[width,transform] duration-300",a.class)}),{default:c(()=>[$("div",Tr,[E(s.$slots,"default")])]),_:3},16,["class"]))}}),$r=V({__name:"segmented",props:ve({defaultValue:{default:""},tabs:{default:()=>[]}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const a=l,t=x(l,"modelValue"),o=w(()=>{var r;return a.defaultValue||((r=a.tabs[0])==null?void 0:r.value)}),s=w(()=>({"grid-template-columns":`repeat(${a.tabs.length}, minmax(0, 1fr))`})),n=w(()=>({width:`${(100/a.tabs.length).toFixed(0)}%`}));return(r,i)=>(u(),y(e(Zn),{modelValue:t.value,"onUpdate:modelValue":i[0]||(i[0]=d=>t.value=d),"default-value":o.value},{default:c(()=>[m(e(Jn),{style:ce(s.value),class:"bg-accent relative grid w-full"},{default:c(()=>[m(Mr,{style:ce(n.value)},null,8,["style"]),(u(!0),k(Z,null,ue(r.tabs,d=>(u(),y(e(Uo),{key:d.value,value:d.value,class:"z-20 inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium disabled:pointer-events-none disabled:opacity-50"},{default:c(()=>[z(_(d.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["style"]),(u(!0),k(Z,null,ue(r.tabs,d=>(u(),y(e(Xn),{key:d.value,value:d.value},{default:c(()=>[E(r.$slots,d.value)]),_:2},1032,["value"]))),128))]),_:3},8,["modelValue","default-value"]))}});function kl(){const{contentIsMaximize:l}=ot();function a(){const t=l.value;et({header:{hidden:!t},sidebar:{hidden:!t}})}return{contentIsMaximize:l,toggleMaximize:a}}const Ft=500,Kt=0;function Br(l,a=Ft){const t=typeof a=="number"||vt(a)?{enterDelay:Kt,leaveDelay:a}:Q({enterDelay:Kt,leaveDelay:Ft},a),o=X(!1),s=X(),n=X(),r=X([]),i=w(()=>{const C=e(l);return C===null?[]:Array.isArray(C)?C:[C]}),d=X([]);function f(){r.value.forEach(C=>C.stop()),r.value=[],d.value=i.value.map(C=>{if(!C)return X(!1);const W=w(()=>{const L=e(C);return L instanceof Element?L:L==null?void 0:L.$el}),F=vn(),U=F.run(()=>gn(W))||X(!1);return r.value.push(F),U})}const p=w(()=>{const C=e(l);return C===null?0:Array.isArray(C)?C.length:1});f();const v=pe(p,f,{deep:!1}),h=w(()=>d.value.every(C=>!C.value));function b(){s.value&&(clearTimeout(s.value),s.value=void 0),n.value&&(clearTimeout(n.value),n.value=void 0)}function S(C){var W,F;if(b(),C){const U=(W=t.enterDelay)!=null?W:Kt,L=vt(U)?U():U;L<=0?o.value=!0:s.value=setTimeout(()=>{o.value=!0,s.value=void 0},L)}else{const U=(F=t.leaveDelay)!=null?F:Ft,L=vt(U)?U():U;L<=0?o.value=!1:n.value=setTimeout(()=>{o.value=!1,n.value=void 0},L)}}const H=pe(h,C=>{S(!C)},{immediate:!0}),O={enable(){H.resume()},disable(){H.pause()}};return St(()=>{b(),v(),r.value.forEach(C=>C.stop())}),[o,O]}function Cl(){const l=nt(),a=mt();function t(){return Y(this,null,function*(){yield a.refresh(l)})}return{refresh:t}}function Sl(){const l=nt(),a=Ge(),t=mt();function o(C){return Y(this,null,function*(){yield t.closeLeftTabs(C||a)})}function s(){return Y(this,null,function*(){yield t.closeAllTabs(l)})}function n(C){return Y(this,null,function*(){yield t.closeRightTabs(C||a)})}function r(C){return Y(this,null,function*(){yield t.closeOtherTabs(C||a)})}function i(C){return Y(this,null,function*(){yield t.closeTab(C||a,l)})}function d(C){return Y(this,null,function*(){yield t.pinTab(C||a)})}function f(C){return Y(this,null,function*(){yield t.unpinTab(C||a)})}function p(C){return Y(this,null,function*(){yield t.toggleTabPin(C||a)})}function v(C){return Y(this,null,function*(){yield t.refresh(C||l)})}function h(C){return Y(this,null,function*(){yield t.openTabInNewWindow(C||a)})}function b(C){return Y(this,null,function*(){yield t.closeTabByKey(C,l)})}function S(C){return Y(this,null,function*(){t.setUpdateTime(),yield t.setTabTitle(a,C)})}function H(){return Y(this,null,function*(){t.setUpdateTime(),yield t.resetTabTitle(a)})}function O(C=a){var de;const W=t.getTabs,F=t.affixTabs,U=W.findIndex(ge=>ge.path===C.path),L=W.length<=1,{meta:K}=C,P=(de=K==null?void 0:K.affixTab)!=null?de:!1,B=a.path===C.path,N=U===0||U-F.length<=0||!B,J=!B||U===W.length-1,oe=L||!B||W.length-F.length<=1;return{disabledCloseAll:L,disabledCloseCurrent:!!P||L,disabledCloseLeft:N,disabledCloseOther:oe,disabledCloseRight:J,disabledRefresh:!B}}return{closeAllTabs:s,closeCurrentTab:i,closeLeftTabs:o,closeOtherTabs:r,closeRightTabs:n,closeTabByKey:b,getTabDisableState:O,openTabInNewWindow:h,pinTab:d,refreshTab:v,resetTabTitle:H,setTabTitle:S,toggleTabPin:p,unpinTab:f}}const Vr={class:"flex items-center"},Er={class:"flex-center"},Lr=V({__name:"drawer",props:{drawerApi:{default:void 0},appendToMain:{type:Boolean,default:!1},cancelText:{},class:{},closable:{type:Boolean},closeIconPlacement:{default:"right"},closeOnClickModal:{type:Boolean},closeOnPressEscape:{type:Boolean},confirmLoading:{type:Boolean},confirmText:{},contentClass:{},description:{},destroyOnClose:{type:Boolean,default:!1},footer:{type:Boolean},footerClass:{},header:{type:Boolean},headerClass:{},loading:{type:Boolean},modal:{type:Boolean},openAutoFocus:{type:Boolean},overlayBlur:{},placement:{},showCancelButton:{type:Boolean},showConfirmButton:{type:Boolean},submitting:{type:Boolean,default:!1},title:{},titleTooltip:{},zIndex:{default:1e3}},setup(l){var re,me;const a=l,t=Ja.getComponents(),o=yn();At("DISMISSABLE_DRAWER_ID",o);const s=X(),{$t:n}=Ho(),{isMobile:r}=Xa(),i=(me=(re=a.drawerApi)==null?void 0:re.useStore)==null?void 0:me.call(re),{appendToMain:d,cancelText:f,class:p,closable:v,closeIconPlacement:h,closeOnClickModal:b,closeOnPressEscape:S,confirmLoading:H,confirmText:O,contentClass:C,description:W,destroyOnClose:F,footer:U,footerClass:L,header:K,headerClass:P,loading:B,modal:N,openAutoFocus:J,overlayBlur:oe,placement:de,showCancelButton:ge,showConfirmButton:G,submitting:ee,title:he,titleTooltip:Ce,zIndex:Be}=Oo(a,i);wn(()=>{var q;d.value||(q=a.drawerApi)==null||q.close()});function Ae(q){(!b.value||ee.value)&&q.preventDefault()}function _e(q){(!S.value||ee.value)&&q.preventDefault()}function j(q){const we=q.target,D=we==null?void 0:we.dataset.dismissableDrawer;(ee.value||!b.value||D!==o)&&q.preventDefault()}function ne(q){J.value||q==null||q.preventDefault()}function le(q){q.preventDefault(),q.stopPropagation()}const ye=w(()=>d.value?`#${Za}>div:not(.absolute)>div`:void 0),Se=X(!1),Ve=X(!0);pe(()=>{var q;return(q=i==null?void 0:i.value)==null?void 0:q.isOpen},q=>{Ve.value=!1,q&&!e(Se)&&(Se.value=!0)});function De(){var q;Ve.value=!0,(q=a.drawerApi)==null||q.onClosed()}const Ie=w(()=>!e(F)&&e(Se));return(q,we)=>{var D;return u(),y(e(Rs),{modal:!1,open:(D=e(i))==null?void 0:D.isOpen,"onUpdate:open":we[3]||(we[3]=()=>{var te;return(te=q.drawerApi)==null?void 0:te.close()})},{default:c(()=>{var te;return[m(e(Ks),{"append-to":ye.value,class:R(e(se)("flex w-[520px] flex-col",e(p),{"!w-full":e(r)||e(de)==="bottom"||e(de)==="top","max-h-[100vh]":e(de)==="bottom"||e(de)==="top",hidden:Ve.value})),modal:e(N),open:(te=e(i))==null?void 0:te.isOpen,side:e(de),"z-index":e(Be),"force-mount":Ie.value,"overlay-blur":e(oe),onCloseAutoFocus:le,onClosed:De,onEscapeKeyDown:_e,onFocusOutside:le,onInteractOutside:Ae,onOpenAutoFocus:ne,onOpened:we[2]||(we[2]=()=>{var ie;return(ie=q.drawerApi)==null?void 0:ie.onOpened()}),onPointerDownOutside:j},{default:c(()=>[e(K)?(u(),y(e(Gs),{key:0,class:R(e(se)("!flex flex-row items-center justify-between border-b px-6 py-5",e(P),{"px-4 py-3":e(v),"pl-2":e(v)&&e(h)==="left"}))},{default:c(()=>[$("div",Vr,[e(v)&&e(h)==="left"?(u(),y(e(Da),{key:0,"as-child":"",disabled:e(ee),class:"data-[state=open]:bg-secondary ml-[2px] cursor-pointer rounded-full opacity-80 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none"},{default:c(()=>[E(q.$slots,"close-icon",{},()=>[m(e(Ze),null,{default:c(()=>[m(e(ct),{class:"size-4"})]),_:1})])]),_:3},8,["disabled"])):A("",!0),e(v)&&e(h)==="left"?(u(),y(e(Ds),{key:1,class:"ml-1 mr-2 h-8",decorative:"",orientation:"vertical"})):A("",!0),e(he)?(u(),y(e(Nt),{key:2,class:"text-left"},{default:c(()=>[E(q.$slots,"title",{},()=>[z(_(e(he))+" ",1),e(Ce)?(u(),y(e(Hn),{key:0,"trigger-class":"pb-1"},{default:c(()=>[z(_(e(Ce)),1)]),_:1})):A("",!0)])]),_:3})):A("",!0),e(W)?(u(),y(e(Rt),{key:3,class:"mt-1 text-xs"},{default:c(()=>[E(q.$slots,"description",{},()=>[z(_(e(W)),1)])]),_:3})):A("",!0)]),!e(he)||!e(W)?(u(),y(e($a),{key:0},{default:c(()=>[e(he)?A("",!0):(u(),y(e(Nt),{key:0})),e(W)?A("",!0):(u(),y(e(Rt),{key:1}))]),_:1})):A("",!0),$("div",Er,[E(q.$slots,"extra"),e(v)&&e(h)==="right"?(u(),y(e(Da),{key:0,"as-child":"",disabled:e(ee),class:"data-[state=open]:bg-secondary ml-[2px] cursor-pointer rounded-full opacity-80 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none"},{default:c(()=>[E(q.$slots,"close-icon",{},()=>[m(e(Ze),null,{default:c(()=>[m(e(ct),{class:"size-4"})]),_:1})])]),_:3},8,["disabled"])):A("",!0)])]),_:3},8,["class"])):(u(),y(e($a),{key:1},{default:c(()=>[m(e(Nt)),m(e(Rt))]),_:1})),$("div",{ref_key:"wrapperRef",ref:s,class:R(e(se)("relative flex-1 overflow-y-auto p-3",e(C),{"pointer-events-none":e(B)||e(ee)}))},[E(q.$slots,"default")],2),e(B)||e(ee)?(u(),y(e(Do),{key:2,spinning:""})):A("",!0),e(U)?(u(),y(e(js),{key:3,class:R(e(se)("w-full flex-row items-center justify-end border-t p-2 px-3",e(L)))},{default:c(()=>[E(q.$slots,"prepend-footer"),E(q.$slots,"footer",{},()=>[e(ge)?(u(),y(Le(e(t).DefaultButton||e(Pe)),{key:0,variant:"ghost",disabled:e(ee),onClick:we[0]||(we[0]=()=>{var ie;return(ie=q.drawerApi)==null?void 0:ie.onCancel()})},{default:c(()=>[E(q.$slots,"cancelText",{},()=>[z(_(e(f)||e(n)("cancel")),1)])]),_:3},8,["disabled"])):A("",!0),E(q.$slots,"center-footer"),e(G)?(u(),y(Le(e(t).PrimaryButton||e(Pe)),{key:1,loading:e(H)||e(ee),onClick:we[1]||(we[1]=()=>{var ie;return(ie=q.drawerApi)==null?void 0:ie.onConfirm()})},{default:c(()=>[E(q.$slots,"confirmText",{},()=>[z(_(e(O)||e(n)("confirm")),1)])]),_:3},8,["loading"])):A("",!0)]),E(q.$slots,"append-footer")]),_:3},8,["class"])):A("",!0)]),_:3},8,["append-to","class","modal","open","side","z-index","force-mount","overlay-blur"])]}),_:3},8,["open"])}}});class zr{constructor(a={}){ht(this,"sharedData",{payload:{}});ht(this,"store");ht(this,"api");ht(this,"state");const v=a,{connectedComponent:t,onBeforeClose:o,onCancel:s,onClosed:n,onConfirm:r,onOpenChange:i,onOpened:d}=v,f=ke(v,["connectedComponent","onBeforeClose","onCancel","onClosed","onConfirm","onOpenChange","onOpened"]),p={class:"",closable:!0,closeIconPlacement:"right",closeOnClickModal:!0,closeOnPressEscape:!0,confirmLoading:!1,contentClass:"",footer:!0,header:!0,isOpen:!1,loading:!1,modal:!0,openAutoFocus:!1,placement:"right",showCancelButton:!0,showConfirmButton:!0,submitting:!1,title:""};this.store=new On(Q(Q({},p),f),{onUpdate:()=>{var b,S,H;const h=this.store.state;(h==null?void 0:h.isOpen)===((b=this.state)==null?void 0:b.isOpen)?this.state=h:(this.state=h,(H=(S=this.api).onOpenChange)==null||H.call(S,!!(h!=null&&h.isOpen)))}}),this.state=this.store.state,this.api={onBeforeClose:o,onCancel:s,onClosed:n,onConfirm:r,onOpenChange:i,onOpened:d},xn(this)}close(){return Y(this,null,function*(){var t,o,s;((s=yield(o=(t=this.api).onBeforeClose)==null?void 0:o.call(t))!=null?s:!0)&&this.store.setState(n=>xe(Q({},n),{isOpen:!1,submitting:!1}))})}getData(){var a,t;return(t=(a=this.sharedData)==null?void 0:a.payload)!=null?t:{}}lock(a=!0){return this.setState({submitting:a})}onCancel(){var a,t;this.api.onCancel?(t=(a=this.api).onCancel)==null||t.call(a):this.close()}onClosed(){var a,t;this.state.isOpen||(t=(a=this.api).onClosed)==null||t.call(a)}onConfirm(){var a,t;(t=(a=this.api).onConfirm)==null||t.call(a)}onOpened(){var a,t;this.state.isOpen&&((t=(a=this.api).onOpened)==null||t.call(a))}open(){this.store.setState(a=>xe(Q({},a),{isOpen:!0}))}setData(a){return this.sharedData.payload=a,this}setState(a){return vt(a)?this.store.setState(a):this.store.setState(t=>Q(Q({},t),a)),this}unlock(){return this.lock(!1)}}const Na=Symbol("VBEN_DRAWER_INJECT"),Pr={};function _l(l={}){var d;const{connectedComponent:a}=l;if(a){const f=pt({}),p=X(!0);return[V((h,{attrs:b,slots:S})=>(At(Na,{extendApi(O){Object.setPrototypeOf(f,O)},options:l,reCreateDrawer(){return Y(this,null,function*(){p.value=!1,yield je(),p.value=!0})}}),Ar(f,Q(Q(Q({},h),b),S)),()=>Ba(p.value?a:"div",Q(Q({},h),b),S)),{name:"VbenParentDrawer",inheritAttrs:!1}),f]}const t=zt(Na,{}),o=Q(Q(Q({},Pr),t.options),l);o.onOpenChange=f=>{var p,v,h;(p=l.onOpenChange)==null||p.call(l,f),(h=(v=t.options)==null?void 0:v.onOpenChange)==null||h.call(v,f)};const s=o.onClosed;o.onClosed=()=>{var f;s==null||s(),o.destroyOnClose&&((f=t.reCreateDrawer)==null||f.call(t))};const n=new zr(o),r=n;r.useStore=f=>Dn(n.store,f);const i=V((f,{attrs:p,slots:v})=>()=>Ba(Lr,xe(Q(Q({},f),p),{drawerApi:r}),v),{name:"VbenDrawer",inheritAttrs:!1});return(d=t.extendApi)==null||d.call(t,r),[i,r]}function Ar(l,a){return Y(this,null,function*(){var s;if(!a||Object.keys(a).length===0)return;yield je();const t=(s=l==null?void 0:l.store)==null?void 0:s.state;if(!t)return;const o=new Set(Object.keys(t));for(const n of Object.keys(a))o.has(n)&&!["class"].includes(n)&&console.warn(`[Vben Drawer]: When 'connectedComponent' exists, do not set props or slots '${n}', which will increase complexity. If you need to modify the props of Drawer, please use useVbenDrawer or api.`)})}const Ir=V({__name:"breadcrumb",props:{hideWhenOnlyOne:{type:Boolean},showHome:{type:Boolean,default:!1},showIcon:{type:Boolean,default:!1},type:{default:"normal"}},setup(l){const a=l,t=Ge(),o=nt(),s=w(()=>{const r=t.matched,i=[];for(const d of r){const{meta:f,path:p}=d,{hideChildrenInMenu:v,hideInBreadcrumb:h,icon:b,name:S,title:H}=f||{};h||v||!p||i.push({icon:b,path:p||t.path,title:H?g(H||S):""})}return a.showHome&&i.unshift({icon:"mdi:home-outline",isHome:!0,path:"/"}),a.hideWhenOnlyOne&&i.length===1?[]:i});function n(r){o.push(r)}return(r,i)=>(u(),y(e(pr),{breadcrumbs:s.value,"show-icon":r.showIcon,"style-type":r.type,class:"ml-2",onSelect:n},null,8,["breadcrumbs","show-icon","style-type"]))}}),Ur=V({name:"CheckUpdates",__name:"check-updates",props:{checkUpdatesInterval:{default:1},checkUpdateUrl:{default:"/"}},setup(l){const a=l;let t=!1;const o=X(""),s=X(""),n=X(),[r,i]=kt({closable:!1,closeOnPressEscape:!1,closeOnClickModal:!1,onConfirm(){s.value=o.value,window.location.reload()}});function d(){return Y(this,null,function*(){try{if(location.hostname==="localhost"||location.hostname==="127.0.0.1")return null;const S=yield fetch(a.checkUpdateUrl,{cache:"no-cache",method:"HEAD",redirect:"manual"});return S.headers.get("etag")||S.headers.get("last-modified")}catch(S){return console.error("Failed to fetch version tag"),null}})}function f(){return Y(this,null,function*(){const S=yield d();if(S){if(!s.value){s.value=S;return}s.value!==S&&S&&(clearInterval(n.value),p(S))}})}function p(S){o.value=S,i.open()}function v(){a.checkUpdatesInterval<=0||(n.value=setInterval(f,a.checkUpdatesInterval*60*1e3))}function h(){document.hidden?b():t||(t=!0,f().finally(()=>{t=!1,v()}))}function b(){clearInterval(n.value)}return qe(()=>{v(),document.addEventListener("visibilitychange",h)}),St(()=>{b(),document.removeEventListener("visibilitychange",h)}),(S,H)=>(u(),y(e(r),{"cancel-text":e(g)("common.cancel"),"confirm-text":e(g)("common.refresh"),"fullscreen-button":!1,title:e(g)("ui.widgets.checkUpdatesTitle"),centered:"","content-class":"px-8 min-h-10","footer-class":"border-none mb-3 mr-3","header-class":"border-none"},{default:c(()=>[z(_(e(g)("ui.widgets.checkUpdatesDescription")),1)]),_:1},8,["cancel-text","confirm-text","title"]))}}),Hr={class:"!flex h-full justify-center px-2 sm:max-h-[450px]"},Or={key:0,class:"text-muted-foreground text-center"},Dr={class:"mb-10 mt-6 text-xs"},Wr={class:"text-foreground text-sm font-medium"},Rr={key:1,class:"text-muted-foreground text-center"},Nr={class:"my-10 text-xs"},Fr={class:"w-full"},Kr={key:0,class:"text-muted-foreground mb-2 text-xs"},jr=["data-index","data-search-item"],Gr={class:"flex-1"},qr=["onClick"],Yr=V({name:"SearchPanel",__name:"search-panel",props:{keyword:{default:""},menus:{default:()=>[]}},emits:["close"],setup(l,{emit:a}){const t=l,o=a,s=nt(),n=kn(`__search-history-${location.hostname}__`,[]),r=X(-1),i=Vt([]),d=X([]),f=Pt(p,200);function p(L){if(L=L.trim(),!L){d.value=[];return}const K=U(L),P=[];Cn(i.value,B=>{var N;K.test((N=B.name)==null?void 0:N.toLowerCase())&&P.push(B)}),d.value=P,P.length>0&&(r.value=0),r.value=0}function v(){const L=document.querySelector(`[data-search-item="${r.value}"]`);L&&L.scrollIntoView({block:"nearest"})}function h(){return Y(this,null,function*(){if(d.value.length===0)return;const L=d.value,K=r.value;if(L.length===0||K<0)return;const P=L[K];P&&(n.value=Va([...n.value,P],"path"),H(),yield je(),Gt(P.path)?window.open(P.path,"_blank"):s.push({path:P.path,replace:!0}))})}function b(){d.value.length!==0&&(r.value--,r.value<0&&(r.value=d.value.length-1),v())}function S(){d.value.length!==0&&(r.value++,r.value>d.value.length-1&&(r.value=0),v())}function H(){d.value=[],o("close")}function O(L){var P;const K=(P=L.target)==null?void 0:P.dataset.index;r.value=Number(K)}function C(L){t.keyword?d.value.splice(L,1):n.value.splice(L,1),r.value=Math.max(r.value-1,0),v()}const W=new Set(["$","(",")","*","+",".","?","[","\\","]","^","{","|","}"]);function F(L){return W.has(L)?`\\${L}`:L}function U(L){const K=[...L].map(P=>F(P)).join(".*");return new RegExp(`.*${K}.*`)}return pe(()=>t.keyword,L=>{L?f(L):d.value=[...n.value]}),qe(()=>{i.value=nl(t.menus,L=>xe(Q({},L),{name:g(L==null?void 0:L.name)})),n.value.length>0&&(d.value=n.value),Tt("Enter",h),Tt("ArrowUp",b),Tt("ArrowDown",S),Tt("Escape",H)}),(L,K)=>(u(),y(e(Ct),null,{default:c(()=>[$("div",Hr,[L.keyword&&d.value.length===0?(u(),k("div",Or,[m(e(ws),{class:"mx-auto mt-4 size-12"}),$("p",Dr,[z(_(e(g)("ui.widgets.search.noResults"))+" ",1),$("span",Wr,' "'+_(L.keyword)+'" ',1)])])):A("",!0),!L.keyword&&d.value.length===0?(u(),k("div",Rr,[$("p",Nr,_(e(g)("ui.widgets.search.noRecent")),1)])):A("",!0),Ee($("ul",Fr,[e(n).length>0&&!L.keyword?(u(),k("li",Kr,_(e(g)("ui.widgets.search.recent")),1)):A("",!0),(u(!0),k(Z,null,ue(e(Va)(d.value,"path"),(P,B)=>(u(),k("li",{key:P.path,class:R([r.value===B?"active bg-primary text-primary-foreground":"","bg-accent flex-center group mb-3 w-full cursor-pointer rounded-lg px-4 py-4"]),"data-index":B,"data-search-item":B,onClick:h,onMouseenter:O},[m(e(Ue),{icon:P.icon,class:"mr-2 size-5 flex-shrink-0",fallback:""},null,8,["icon"]),$("span",Gr,_(P.name),1),$("div",{class:"flex-center dark:hover:bg-accent hover:text-primary-foreground rounded-full p-1 hover:scale-110",onClick:Me(N=>C(B),["stop"])},[m(e(ct),{class:"size-4"})],8,qr)],42,jr))),128))],512),[[ze,d.value.length>0]])])]),_:1}))}}),Jr={class:"flex items-center"},Xr=["placeholder"],Zr={class:"flex w-full justify-start text-xs"},Qr={class:"mr-2 flex items-center"},ei={class:"mr-2 flex items-center"},ti={class:"flex items-center"},ai={class:"text-muted-foreground group-hover:text-foreground hidden text-xs duration-300 md:block"},li={key:0,class:"bg-background border-foreground/60 text-muted-foreground group-hover:text-foreground relative hidden rounded-sm rounded-r-xl px-1.5 py-1 text-xs leading-none group-hover:opacity-100 md:block"},oi={key:1},ni=V({name:"GlobalSearch",__name:"global-search",props:{enableShortcutKey:{type:Boolean,default:!0},menus:{default:()=>[]}},setup(l){const a=l,t=X(""),o=X(),[s,n]=kt({onCancel(){n.close()},onOpenChange(b){b||(t.value="")}}),r=n.useStore(b=>b.isOpen);function i(){n.close(),t.value=""}const d=sl(),f=wt()?d["ctrl+k"]:d["cmd+k"];Et(f,()=>{a.enableShortcutKey&&n.open()}),Et(r,()=>{je(()=>{var b;(b=o.value)==null||b.focus()})});const p=b=>{var S;((S=b.key)==null?void 0:S.toLowerCase())==="k"&&(b.metaKey||b.ctrlKey)&&b.preventDefault()},v=()=>{a.enableShortcutKey?window.addEventListener("keydown",p):window.removeEventListener("keydown",p)},h=()=>{r.value?n.close():n.open()};return pe(()=>a.enableShortcutKey,v),qe(()=>{v(),St(()=>{window.removeEventListener("keydown",p)})}),(b,S)=>(u(),k("div",null,[m(e(s),{"fullscreen-button":!1,class:"w-[600px]","header-class":"py-2 border-b"},{title:c(()=>[$("div",Jr,[m(e(Ha),{class:"text-muted-foreground mr-2 size-4"}),Ee($("input",{ref_key:"searchInputRef",ref:o,"onUpdate:modelValue":S[0]||(S[0]=H=>t.value=H),placeholder:e(g)("ui.widgets.search.searchNavigate"),class:"ring-none placeholder:text-muted-foreground w-[80%] rounded-md border border-none bg-transparent p-2 pl-0 text-sm font-normal outline-none ring-0 ring-offset-transparent focus-visible:ring-transparent"},null,8,Xr),[[Wo,t.value]])])]),footer:c(()=>[$("div",Zr,[$("div",Qr,[m(e(ps),{class:"mr-1 size-3"}),z(" "+_(e(g)("ui.widgets.search.select")),1)]),$("div",ei,[m(e(is),{class:"mr-1 size-3"}),m(e(ls),{class:"mr-1 size-3"}),z(" "+_(e(g)("ui.widgets.search.navigate")),1)]),$("div",ti,[m(e(Yn),{class:"mr-1 size-3"}),z(" "+_(e(g)("ui.widgets.search.close")),1)])])]),default:c(()=>[m(Yr,{keyword:t.value,menus:b.menus,onClose:i},null,8,["keyword","menus"])]),_:1}),$("div",{class:"md:bg-accent group flex h-8 cursor-pointer items-center gap-3 rounded-2xl border-none bg-none px-2 py-0.5 outline-none",onClick:S[1]||(S[1]=H=>h())},[m(e(Ha),{class:"text-muted-foreground group-hover:text-foreground size-4 group-hover:opacity-100"}),$("span",ai,_(e(g)("ui.widgets.search.title")),1),b.enableShortcutKey?(u(),k("span",li,[z(_(e(wt)()?"Ctrl":"⌘")+" ",1),S[2]||(S[2]=$("kbd",null,"K",-1))])):(u(),k("span",oi))])]))}}),si=["onKeydown"],ri={class:"w-full"},ii={class:"ml-2 flex w-full flex-col items-center"},di={class:"text-foreground my-6 flex items-center font-medium"},ui=V({name:"LockScreenModal",__name:"lock-screen-modal",props:{avatar:{default:""},text:{default:""}},emits:["submit"],setup(l,{emit:a}){const t=a,[o,{resetForm:s,validate:n,getValues:r}]=cl(pt({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:w(()=>[{component:"VbenInputPassword",componentProps:{placeholder:g("ui.widgets.lockScreen.placeholder")},fieldName:"lockScreenPassword",formFieldProps:{validateOnBlur:!1},label:g("authentication.password"),rules:pl().min(1,{message:g("ui.widgets.lockScreen.placeholder")})}]),showDefaultActions:!1})),[i]=kt({onConfirm(){d()},onOpenChange(f){f&&s()}});function d(){return Y(this,null,function*(){const{valid:f}=yield n(),p=yield r();f&&t("submit",p==null?void 0:p.lockScreenPassword)})}return(f,p)=>(u(),y(e(i),{footer:!1,"fullscreen-button":!1,title:e(g)("ui.widgets.lockScreen.title")},{default:c(()=>[$("div",{class:"mb-10 flex w-full flex-col items-center px-10",onKeydown:Qa(Me(d,["prevent"]),["enter"])},[$("div",ri,[$("div",ii,[m(e(xt),{src:f.avatar,class:"size-20","dot-class":"bottom-0 right-1 border-2 size-4 bg-green-500"},null,8,["src"]),$("div",di,_(f.text),1)]),m(e(o)),m(e(Pe),{class:"mt-1 w-full",onClick:d},{default:c(()=>[z(_(e(g)("ui.widgets.lockScreen.screenButton")),1)]),_:1})])],40,si)]),_:1},8,["title"]))}}),ci={class:"bg-background fixed z-[2000] size-full"},pi={class:"size-full"},fi={class:"flex h-full w-full items-center justify-center"},mi={class:"flex w-full justify-center gap-4 px-4 sm:gap-6 md:gap-8"},hi={class:"bg-accent relative flex h-[140px] w-[140px] items-center justify-center rounded-xl text-[36px] sm:h-[160px] sm:w-[160px] sm:text-[42px] md:h-[200px] md:w-[200px] md:text-[72px]"},bi={class:"absolute left-3 top-3 text-xs font-semibold sm:text-sm md:text-xl"},vi={class:"bg-accent flex h-[140px] w-[140px] items-center justify-center rounded-xl text-[36px] sm:h-[160px] sm:w-[160px] sm:text-[42px] md:h-[200px] md:w-[200px] md:text-[72px]"},gi=["onKeydown"],yi={class:"flex-col-center mb-10 w-[90%] max-w-[300px] px-4"},wi={class:"enter-x mb-2 w-full items-center"},xi={class:"enter-y absolute bottom-5 w-full text-center text-xl md:text-2xl xl:text-xl 2xl:text-3xl"},ki={key:0,class:"enter-x mb-2 text-2xl md:text-3xl"},Ci={class:"text-base md:text-lg"},Si={class:"text-xl md:text-3xl"},zc=V({name:"LockScreen",__name:"lock-screen",props:{avatar:{default:""}},emits:["toLogin"],setup(l){const{locale:a}=el(),t=st(),o=Sn(),s=Mt(o,"A"),n=Mt(o,"HH"),r=Mt(o,"mm"),i=Mt(o,"YYYY-MM-DD dddd",{locales:a.value}),d=X(!1),{lockScreenPassword:f}=tl(t),[p,{form:v,validate:h}]=cl(pt({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:w(()=>[{component:"VbenInputPassword",componentProps:{placeholder:g("ui.widgets.lockScreen.placeholder")},fieldName:"password",label:g("authentication.password"),rules:pl().min(1,{message:g("authentication.passwordTip")})}]),showDefaultActions:!1})),b=w(()=>{var O;return(f==null?void 0:f.value)===((O=v==null?void 0:v.values)==null?void 0:O.password)});function S(){return Y(this,null,function*(){const{valid:O}=yield h();O&&(b.value?t.unlockScreen():v.setFieldError("password",g("authentication.passwordErrorTip")))})}function H(){d.value=!d.value}return Ya(),(O,C)=>(u(),k("div",ci,[m(at,{name:"slide-left"},{default:c(()=>[Ee($("div",pi,[$("div",{class:"flex-col-center text-foreground/80 hover:text-foreground group fixed left-1/2 top-6 z-[2001] -translate-x-1/2 cursor-pointer text-xl font-semibold",onClick:H},[m(e(bl),{class:"size-5 transition-all duration-300 group-hover:scale-125"}),$("span",null,_(e(g)("ui.widgets.lockScreen.unlock")),1)]),$("div",fi,[$("div",mi,[$("div",hi,[$("span",bi,_(e(s)),1),z(" "+_(e(n)),1)]),$("div",vi,_(e(r)),1)])])],512),[[ze,!d.value]])]),_:1}),m(at,{name:"slide-right"},{default:c(()=>[d.value?(u(),k("div",{key:0,class:"flex-center size-full",onKeydown:Qa(Me(S,["prevent"]),["enter"])},[$("div",yi,[m(e(xt),{src:O.avatar,class:"enter-x mb-6 size-20"},null,8,["src"]),$("div",wi,[m(e(p))]),m(e(Pe),{class:"enter-x w-full",onClick:S},{default:c(()=>[z(_(e(g)("ui.widgets.lockScreen.entry")),1)]),_:1}),m(e(Pe),{class:"enter-x my-2 w-full",variant:"ghost",onClick:C[0]||(C[0]=W=>O.$emit("toLogin"))},{default:c(()=>[z(_(e(g)("ui.widgets.lockScreen.backToLogin")),1)]),_:1}),m(e(Pe),{class:"enter-x mr-2 w-full",variant:"ghost",onClick:H},{default:c(()=>[z(_(e(g)("common.back")),1)]),_:1})])],40,gi)):A("",!0)]),_:1}),$("div",xi,[d.value?(u(),k("div",ki,[z(_(e(n))+":"+_(e(r))+" ",1),$("span",Ci,_(e(s)),1)])):A("",!0),$("div",Si,_(e(i)),1)])]))}}),_i={key:0,class:"bg-primary absolute right-0.5 top-0.5 h-2 w-2 rounded"},Ti={class:"relative"},Mi={class:"flex items-center justify-between p-4 py-3"},$i={class:"text-foreground"},Bi={class:"!flex max-h-[360px] w-full flex-col"},Vi=["onClick"],Ei={key:0,class:"bg-primary absolute right-2 top-2 h-2 w-2 rounded"},Li={class:"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full"},zi=["src"],Pi={class:"flex flex-col gap-1 leading-none"},Ai={class:"font-semibold"},Ii={class:"text-muted-foreground my-1 line-clamp-2 text-xs"},Ui={class:"text-muted-foreground line-clamp-2 text-xs"},Hi={key:1,class:"flex-center text-muted-foreground min-h-[150px] w-full"},Oi={class:"border-border flex items-center justify-between border-t px-4 py-3"},Di=V({name:"NotificationPopup",__name:"notification",props:{dot:{type:Boolean,default:!1},notifications:{default:()=>[]}},emits:["clear","makeAll","read","viewAll"],setup(l,{emit:a}){const t=a,[o,s]=_n();function n(){o.value=!1}function r(){t("viewAll"),n()}function i(){t("makeAll")}function d(){t("clear")}function f(p){t("read",p)}return(p,v)=>(u(),y(e(Ro),{open:e(o),"onUpdate:open":v[1]||(v[1]=h=>Xt(o)?o.value=h:null),"content-class":"relative right-2 w-[360px] p-0"},{trigger:c(()=>[$("div",{class:"flex-center mr-2 h-full",onClick:v[0]||(v[0]=Me(h=>e(s)(),["stop"]))},[m(e(Ze),{class:"bell-button text-foreground relative"},{default:c(()=>[p.dot?(u(),k("span",_i)):A("",!0),m(e(ds),{class:"size-4"})]),_:1})])]),default:c(()=>[$("div",Ti,[$("div",Mi,[$("div",$i,_(e(g)("ui.widgets.notifications")),1),m(e(Ze),{disabled:p.notifications.length<=0,tooltip:e(g)("ui.widgets.markAllAsRead"),onClick:i},{default:c(()=>[m(e(bs),{class:"size-4"})]),_:1},8,["disabled","tooltip"])]),p.notifications.length>0?(u(),y(e(Ct),{key:0},{default:c(()=>[$("ul",Bi,[(u(!0),k(Z,null,ue(p.notifications,h=>(u(),k("li",{key:h.title,class:"hover:bg-accent border-border relative flex w-full cursor-pointer items-start gap-5 border-t px-3 py-3",onClick:b=>f(h)},[h.isRead?A("",!0):(u(),k("span",Ei)),$("span",Li,[$("img",{src:h.avatar,class:"aspect-square h-full w-full object-cover",role:"img"},null,8,zi)]),$("div",Pi,[$("p",Ai,_(h.title),1),$("p",Ii,_(h.message),1),$("p",Ui,_(h.date),1)])],8,Vi))),128))])]),_:1})):(u(),k("div",Hi,_(e(g)("common.noData")),1)),$("div",Oi,[m(e(Pe),{disabled:p.notifications.length<=0,size:"sm",variant:"ghost",onClick:d},{default:c(()=>[z(_(e(g)("ui.widgets.clearNotifications")),1)]),_:1},8,["disabled"]),m(e(Pe),{size:"sm",onClick:r},{default:c(()=>[z(_(e(g)("ui.widgets.viewAll")),1)]),_:1})])])]),_:1},8,["open"]))}}),Pc=$e(Di,[["__scopeId","data-v-d7a4acd4"]]),Wi={class:"flex flex-col py-4"},Ri={class:"mb-3 font-semibold leading-none tracking-tight"},Te=V({name:"PreferenceBlock",__name:"block",props:{title:{default:""}},setup(l){return(a,t)=>(u(),k("div",Wi,[$("h3",Ri,_(a.title),1),E(a.$slots,"default")]))}}),Ni={class:"flex items-center text-sm"},Fi={key:0,class:"ml-auto mr-2 text-xs opacity-60"},ae=V({name:"PreferenceSwitchItem",__name:"switch-item",props:ve({disabled:{type:Boolean,default:!1},tip:{default:""}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const a=x(l,"modelValue"),t=Ne();function o(){a.value=!a.value}return(s,n)=>(u(),k("div",{class:R([{"pointer-events-none opacity-50":s.disabled},"hover:bg-accent my-1 flex w-full items-center justify-between rounded-md px-2 py-2.5"]),onClick:o},[$("span",Ni,[E(s.$slots,"default"),e(t).tip||s.tip?(u(),y(e(rt),{key:0,side:"bottom"},{trigger:c(()=>[m(e(ft),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[E(s.$slots,"tip",{},()=>[s.tip?(u(!0),k(Z,{key:0},ue(s.tip.split(`
`),(r,i)=>(u(),k("p",{key:i},_(r),1))),128)):A("",!0)])]),_:3})):A("",!0)]),s.$slots.shortcut?(u(),k("span",Fi,[E(s.$slots,"shortcut")])):A("",!0),m(e(qs),{checked:a.value,"onUpdate:checked":n[0]||(n[0]=r=>a.value=r),onClick:n[1]||(n[1]=Me(()=>{},["stop"]))},null,8,["checked"])],2))}}),Ki={key:0,class:"mb-2 mt-3 flex justify-between gap-3 px-2"},ji=["onClick"],Gi=V({name:"PreferenceAnimation",__name:"animation",props:{transitionProgress:{type:Boolean,default:!1},transitionProgressModifiers:{},transitionName:{},transitionNameModifiers:{},transitionEnable:{type:Boolean},transitionEnableModifiers:{},transitionLoading:{type:Boolean},transitionLoadingModifiers:{}},emits:["update:transitionProgress","update:transitionName","update:transitionEnable","update:transitionLoading"],setup(l){const a=x(l,"transitionProgress"),t=x(l,"transitionName"),o=x(l,"transitionEnable"),s=x(l,"transitionLoading"),n=["fade","fade-slide","fade-up","fade-down"];function r(i){t.value=i}return(i,d)=>(u(),k(Z,null,[m(ae,{modelValue:a.value,"onUpdate:modelValue":d[0]||(d[0]=f=>a.value=f)},{default:c(()=>[z(_(e(g)("preferences.animation.progress")),1)]),_:1},8,["modelValue"]),m(ae,{modelValue:s.value,"onUpdate:modelValue":d[1]||(d[1]=f=>s.value=f)},{default:c(()=>[z(_(e(g)("preferences.animation.loading")),1)]),_:1},8,["modelValue"]),m(ae,{modelValue:o.value,"onUpdate:modelValue":d[2]||(d[2]=f=>o.value=f)},{default:c(()=>[z(_(e(g)("preferences.animation.transition")),1)]),_:1},8,["modelValue"]),o.value?(u(),k("div",Ki,[(u(),k(Z,null,ue(n,f=>$("div",{key:f,class:R([{"outline-box-active":t.value===f},"outline-box p-2"]),onClick:p=>r(f)},[$("div",{class:R([`${f}-slow`,"bg-accent h-10 w-12 rounded-md"])},null,2)],10,ji)),64))])):A("",!0)],64))}}),qi={class:"flex items-center text-sm"},Ht=V({name:"PreferenceSelectItem",__name:"select-item",props:ve({disabled:{type:Boolean,default:!1},items:{default:()=>[]},placeholder:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const a=x(l,"modelValue"),t=Ne();return(o,s)=>(u(),k("div",{class:R([{"hover:bg-accent":!e(t).tip,"pointer-events-none opacity-50":o.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[$("span",qi,[E(o.$slots,"default"),e(t).tip?(u(),y(e(rt),{key:0,side:"bottom"},{trigger:c(()=>[m(e(ft),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[E(o.$slots,"tip")]),_:3})):A("",!0)]),m(e(No),{modelValue:a.value,"onUpdate:modelValue":s[0]||(s[0]=n=>a.value=n)},{default:c(()=>[m(e(Fo),{class:"h-8 w-[165px]"},{default:c(()=>[m(e(Ko),{placeholder:o.placeholder},null,8,["placeholder"])]),_:1}),m(e(jo),null,{default:c(()=>[(u(!0),k(Z,null,ue(o.items,n=>(u(),y(e(Go),{key:n.value,value:n.value},{default:c(()=>[z(_(n.label),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue"])],2))}}),Yi=V({name:"PreferenceGeneralConfig",__name:"general",props:{appLocale:{},appLocaleModifiers:{},appDynamicTitle:{type:Boolean},appDynamicTitleModifiers:{},appWatermark:{type:Boolean},appWatermarkModifiers:{},appEnableCheckUpdates:{type:Boolean},appEnableCheckUpdatesModifiers:{}},emits:["update:appLocale","update:appDynamicTitle","update:appWatermark","update:appEnableCheckUpdates"],setup(l){const a=x(l,"appLocale"),t=x(l,"appDynamicTitle"),o=x(l,"appWatermark"),s=x(l,"appEnableCheckUpdates");return(n,r)=>(u(),k(Z,null,[m(Ht,{modelValue:a.value,"onUpdate:modelValue":r[0]||(r[0]=i=>a.value=i),items:e(qo)},{default:c(()=>[z(_(e(g)("preferences.language")),1)]),_:1},8,["modelValue","items"]),m(ae,{modelValue:t.value,"onUpdate:modelValue":r[1]||(r[1]=i=>t.value=i)},{default:c(()=>[z(_(e(g)("preferences.dynamicTitle")),1)]),_:1},8,["modelValue"]),m(ae,{modelValue:o.value,"onUpdate:modelValue":r[2]||(r[2]=i=>o.value=i)},{default:c(()=>[z(_(e(g)("preferences.watermark")),1)]),_:1},8,["modelValue"]),m(ae,{modelValue:s.value,"onUpdate:modelValue":r[3]||(r[3]=i=>s.value=i)},{default:c(()=>[z(_(e(g)("preferences.checkUpdates")),1)]),_:1},8,["modelValue"])],64))}}),Ji={class:"text-sm"},la=V({name:"PreferenceToggleItem",__name:"toggle-item",props:ve({disabled:{type:Boolean,default:!1},items:{default:()=>[]}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const a=x(l,"modelValue");return(t,o)=>(u(),k("div",{class:R([{"pointer-events-none opacity-50":t.disabled},"hover:bg-accent flex w-full items-center justify-between rounded-md px-2 py-2"]),disabled:""},[$("span",Ji,[E(t.$slots,"default")]),m(e(fl),{modelValue:a.value,"onUpdate:modelValue":o[0]||(o[0]=s=>a.value=s),class:"gap-2",size:"sm",type:"single",variant:"outline"},{default:c(()=>[(u(!0),k(Z,null,ue(t.items,s=>(u(),y(e(ml),{key:s.value,value:s.value,class:"data-[state=on]:bg-primary data-[state=on]:text-primary-foreground h-7 rounded-sm"},{default:c(()=>[z(_(s.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])],2))}}),Xi=V({name:"PreferenceBreadcrumbConfig",__name:"breadcrumb",props:ve({disabled:{type:Boolean}},{breadcrumbEnable:{type:Boolean},breadcrumbEnableModifiers:{},breadcrumbShowIcon:{type:Boolean},breadcrumbShowIconModifiers:{},breadcrumbStyleType:{},breadcrumbStyleTypeModifiers:{},breadcrumbShowHome:{type:Boolean},breadcrumbShowHomeModifiers:{},breadcrumbHideOnlyOne:{type:Boolean},breadcrumbHideOnlyOneModifiers:{}}),emits:["update:breadcrumbEnable","update:breadcrumbShowIcon","update:breadcrumbStyleType","update:breadcrumbShowHome","update:breadcrumbHideOnlyOne"],setup(l){const a=l,t=x(l,"breadcrumbEnable"),o=x(l,"breadcrumbShowIcon"),s=x(l,"breadcrumbStyleType"),n=x(l,"breadcrumbShowHome"),r=x(l,"breadcrumbHideOnlyOne"),i=[{label:g("preferences.normal"),value:"normal"},{label:g("preferences.breadcrumb.background"),value:"background"}],d=w(()=>!t.value||a.disabled);return(f,p)=>(u(),k(Z,null,[m(ae,{modelValue:t.value,"onUpdate:modelValue":p[0]||(p[0]=v=>t.value=v),disabled:f.disabled},{default:c(()=>[z(_(e(g)("preferences.breadcrumb.enable")),1)]),_:1},8,["modelValue","disabled"]),m(ae,{modelValue:r.value,"onUpdate:modelValue":p[1]||(p[1]=v=>r.value=v),disabled:d.value},{default:c(()=>[z(_(e(g)("preferences.breadcrumb.hideOnlyOne")),1)]),_:1},8,["modelValue","disabled"]),m(ae,{modelValue:o.value,"onUpdate:modelValue":p[2]||(p[2]=v=>o.value=v),disabled:d.value},{default:c(()=>[z(_(e(g)("preferences.breadcrumb.icon")),1)]),_:1},8,["modelValue","disabled"]),m(ae,{modelValue:n.value,"onUpdate:modelValue":p[3]||(p[3]=v=>n.value=v),disabled:d.value||!o.value},{default:c(()=>[z(_(e(g)("preferences.breadcrumb.home")),1)]),_:1},8,["modelValue","disabled"]),m(la,{modelValue:s.value,"onUpdate:modelValue":p[4]||(p[4]=v=>s.value=v),disabled:d.value,items:i},{default:c(()=>[z(_(e(g)("preferences.breadcrumb.style")),1)]),_:1},8,["modelValue","disabled"])],64))}}),Zi={},Qi={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function ed(l,a){return u(),k("svg",Qi,a[0]||(a[0]=[it('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="15.58168" y="3.20832"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="53.60438" x="43.484" y="13.66705"></rect><path id="svg_14" d="m3.43932,15.53192c0,-1.08676 1.03344,-2 2.26323,-2l30.33036,0c1.22979,0 2.26323,0.91324 2.26323,2l0,17.24865c0,1.08676 -1.03344,2 -2.26323,2l-30.33036,0c-1.22979,0 -2.26323,-0.91324 -2.26323,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="95.02528" x="3.30419" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="7.13843" rx="2" stroke="null" width="7.78397" x="1.5327" y="0.881"></rect></g>',1)]))}const Tl=$e(Zi,[["render",ed]]),td={},ad={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function ld(l,a){return u(),k("svg",ad,a[0]||(a[0]=[it('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="15.58168" y="3.20832"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="41.98275" x="45.37589" y="13.53192"></rect><path id="svg_14" d="m16.4123,15.53192c0,-1.08676 0.74096,-2 1.62271,-2l21.74653,0c0.88175,0 1.62271,0.91324 1.62271,2l0,17.24865c0,1.08676 -0.74096,2 -1.62271,2l-21.74653,0c-0.88175,0 -1.62271,-0.91324 -1.62271,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="71.10636" x="16.54743" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="7.13843" rx="2" stroke="null" width="7.78397" x="1.5327" y="0.881"></rect></g>',1)]))}const od=$e(td,[["render",ld]]),nd={},sd={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function rd(l,a){return u(),k("svg",sd,a[0]||(a[0]=[$("g",null,[$("path",{id:"svg_1",d:"m0.13514,4.13514c0,-2.17352 1.82648,-4 4,-4l96,0c2.17352,0 4,1.82648 4,4l0,58c0,2.17352 -1.82648,4 -4,4l-96,0c-2.17352,0 -4,-1.82648 -4,-4l0,-58z",fill:"currentColor","fill-opacity":"0.02",opacity:"undefined",stroke:"null"}),$("rect",{id:"svg_13",fill:"currentColor","fill-opacity":"0.08",height:"26.57155",rx:"2",stroke:"null",width:"53.18333",x:"45.79979",y:"3.77232"}),$("path",{id:"svg_14",d:"m4.28142,5.96169c0,-1.37748 1.06465,-2.53502 2.33158,-2.53502l31.2463,0c1.26693,0 2.33158,1.15754 2.33158,2.53502l0,21.86282c0,1.37748 -1.06465,2.53502 -2.33158,2.53502l-31.2463,0c-1.26693,0 -2.33158,-1.15754 -2.33158,-2.53502l0,-21.86282z",fill:"currentColor","fill-opacity":"0.08",opacity:"undefined",stroke:"null"}),$("rect",{id:"svg_15",fill:"currentColor","fill-opacity":"0.08",height:"25.02247",rx:"2",stroke:"null",width:"94.39371",x:"4.56735",y:"34.92584"})],-1)]))}const id=$e(nd,[["render",rd]]),dd={},ud={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function cd(l,a){return u(),k("svg",ud,a[0]||(a[0]=[it('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><path id="svg_2" d="m-3.37838,3.7543a1.93401,4.02457 0 0 1 1.93401,-4.02457l11.3488,0l0,66.40541l-11.3488,0a1.93401,4.02457 0 0 1 -1.93401,-4.02457l0,-58.35627z" fill="hsl(var(--primary))" stroke="null"></path><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="15.46086"></rect><rect id="svg_4" fill="#ffffff" height="7.67897" rx="2" stroke="null" width="8.18938" x="0.58676" y="1.42154"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" rx="2" stroke="null" width="75.91967" x="25.38277" y="1.42876"></rect><rect id="svg_9" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="27.91529" y="3.69284"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="3.62876"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.78868" y="3.69981"></rect><rect id="svg_12" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="94.6847" y="3.62876"></rect><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="42.9287" x="58.75427" y="14.613"></rect><rect id="svg_14" fill="currentColor" fill-opacity="0.08" height="20.97838" rx="2" stroke="null" width="28.36894" x="26.14342" y="14.613"></rect><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="75.09493" x="26.34264" y="39.68822"></rect><rect id="svg_5" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.79832" y="28.39462"></rect><rect id="svg_6" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="41.80156"></rect><rect id="svg_7" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="55.36623"></rect><rect id="svg_16" fill="currentColor" fill-opacity="0.08" height="65.72065" stroke="null" width="12.49265" x="9.85477" y="-0.02618"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="35.14924" y="4.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="47.25735" y="4.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="59.23033" y="4.07319"></rect></g>',1)]))}const pd=$e(dd,[["render",cd]]),fd={},md={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function hd(l,a){return u(),k("svg",md,a[0]||(a[0]=[it('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="currentColor" fill-opacity="0.08" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#b2b2b2" height="1.689" rx="1.395" stroke="null" width="6.52486" x="10.08168" y="3.50832"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="2.89362"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.58249" y="2.89362"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="44.13071" x="53.37873" y="13.45652"></rect><path id="svg_14" d="m19.4393,15.74245c0,-1.08676 0.79001,-2 1.73013,-2l23.18605,0c0.94011,0 1.73013,0.91324 1.73013,2l0,17.24865c0,1.08676 -0.79001,2 -1.73013,2l-23.18605,0c-0.94011,0 -1.73013,-0.91324 -1.73013,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="78.39372" x="19.93575" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="5.13843" rx="2" stroke="null" width="5.78397" x="1.5327" y="1.081"></rect><rect id="svg_5" fill="hsl(var(--primary))" height="56.81191" stroke="null" width="15.44642" x="-0.06423" y="9.03113"></rect><path id="svg_2" d="m2.38669,15.38074c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="#fff" opacity="undefined" stroke="null"></path><path id="svg_6" d="m2.38669,28.43336c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="#fff" opacity="undefined" stroke="null"></path><path id="svg_7" d="m2.17616,41.27545c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="#fff" opacity="undefined" stroke="null"></path><path id="svg_9" d="m2.17616,54.32806c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="#fff" opacity="undefined" stroke="null"></path></g>',1)]))}const bd=$e(fd,[["render",hd]]),vd={},gd={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function yd(l,a){return u(),k("svg",gd,a[0]||(a[0]=[it('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="15.58168" y="3.20832"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="44.13071" x="53.37873" y="13.45652"></rect><path id="svg_14" d="m19.4393,15.74245c0,-1.08676 0.79001,-2 1.73013,-2l23.18605,0c0.94011,0 1.73013,0.91324 1.73013,2l0,17.24865c0,1.08676 -0.79001,2 -1.73013,2l-23.18605,0c-0.94011,0 -1.73013,-0.91324 -1.73013,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="78.39372" x="19.93575" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="7.13843" rx="2" stroke="null" width="7.78397" x="1.5327" y="0.881"></rect><rect id="svg_5" fill="currentColor" fill-opacity="0.08" height="56.81191" stroke="null" width="15.44642" x="-0.06423" y="9.03113"></rect><path id="svg_2" d="m2.38669,15.38074c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><path id="svg_6" d="m2.38669,28.43336c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><path id="svg_7" d="m2.17616,41.27545c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><path id="svg_9" d="m2.17616,54.32806c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path></g>',1)]))}const wd=$e(vd,[["render",yd]]),xd={},kd={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Cd(l,a){return u(),k("svg",kd,a[0]||(a[0]=[it('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><path id="svg_2" d="m-3.37838,3.7543a1.93401,4.02457 0 0 1 1.93401,-4.02457l11.3488,0l0,66.40541l-11.3488,0a1.93401,4.02457 0 0 1 -1.93401,-4.02457l0,-58.35627z" fill="hsl(var(--primary))" stroke="null"></path><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="15.46086"></rect><rect id="svg_4" fill="#ffffff" height="7.67897" rx="2" stroke="null" width="8.18938" x="0.58676" y="1.42154"></rect><rect id="svg_8" fill="currentColor" fill-opacity="0.08" height="9.07027" rx="2" stroke="null" width="75.91967" x="25.38277" y="1.42876"></rect><rect id="svg_9" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="27.91529" y="3.69284"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="3.62876"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.78868" y="3.69981"></rect><rect id="svg_12" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="94.6847" y="3.62876"></rect><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="42.9287" x="58.75427" y="14.613"></rect><rect id="svg_14" fill="currentColor" fill-opacity="0.08" height="20.97838" rx="2" stroke="null" width="28.36894" x="26.14342" y="14.613"></rect><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="75.09493" x="26.34264" y="39.68822"></rect><rect id="svg_5" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.79832" y="28.39462"></rect><rect id="svg_6" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="41.80156"></rect><rect id="svg_7" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="55.36623"></rect><rect id="svg_16" fill="currentColor" fill-opacity="0.08" height="65.72065" stroke="null" width="12.49265" x="9.85477" y="-0.02618"></rect></g>',1)]))}const Sd=$e(xd,[["render",Cd]]),_d={},Td={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Md(l,a){return u(),k("svg",Td,a[0]||(a[0]=[it('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104"></rect><path id="svg_2" d="m-3.37838,3.61916a4.4919,4.02457 0 0 1 4.4919,-4.02457l26.35848,0l0,66.40541l-26.35848,0a4.4919,4.02457 0 0 1 -4.4919,-4.02457l0,-58.35627z" fill="hsl(var(--primary))" stroke="null"></path><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" width="17.66" x="4.906" y="23.884"></rect><rect id="svg_4" fill="#ffffff" height="9.706" rx="2" width="9.811" x="8.83" y="5.881"></rect><path id="svg_5" d="m4.906,35.833c0,-0.75801 0.63699,-1.395 1.395,-1.395l14.87,0c0.75801,0 1.395,0.63699 1.395,1.395l0,-0.001c0,0.75801 -0.63699,1.395 -1.395,1.395l-14.87,0c-0.75801,0 -1.395,-0.63699 -1.395,-1.395l0,0.001z" fill="#ffffff" opacity="undefined"></path><rect id="svg_6" fill="#ffffff" height="2.789" rx="1.395" width="17.66" x="4.906" y="44.992"></rect><rect id="svg_7" fill="#ffffff" height="2.789" rx="1.395" width="17.66" x="4.906" y="55.546"></rect><rect id="svg_8" fill="currentColor" fill-opacity="0.08" height="9.07027" rx="2" stroke="null" width="73.53879" x="28.97986" y="1.42876"></rect><rect id="svg_9" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="32.039" y="3.89903"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="3.62876"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.58249" y="3.49362"></rect><rect id="svg_12" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="94.6847" y="3.62876"></rect><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="45.63141" x="56.05157" y="14.613"></rect><rect id="svg_14" fill="currentColor" fill-opacity="0.08" height="20.97838" rx="2" stroke="null" width="22.82978" x="29.38527" y="14.613"></rect><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="72.45771" x="28.97986" y="39.48203"></rect></g>',1)]))}const $d=$e(_d,[["render",Md]]),Bd=Tl,Vd={class:"flex w-full gap-5"},Ed=["onClick"],Ld={class:"text-muted-foreground mt-2 text-center text-xs"},zd=V({name:"PreferenceLayoutContent",__name:"content",props:{modelValue:{default:"wide"},modelModifiers:{}},emits:["update:modelValue"],setup(l){const a=x(l,"modelValue"),t={compact:od,wide:Bd},o=w(()=>[{name:g("preferences.wide"),type:"wide"},{name:g("preferences.compact"),type:"compact"}]);function s(n){return n===a.value?["outline-box-active"]:[]}return(n,r)=>(u(),k("div",Vd,[(u(!0),k(Z,null,ue(o.value,i=>(u(),k("div",{key:i.name,class:"flex w-[100px] cursor-pointer flex-col",onClick:d=>a.value=i.type},[$("div",{class:R([s(i.type),"outline-box flex-center"])},[(u(),y(Le(t[i.type])))],2),$("div",Ld,_(i.name),1)],8,Ed))),128))]))}}),Pd={class:"flex items-center text-sm"},bt=V({name:"PreferenceSelectItem",__name:"input-item",props:ve({disabled:{type:Boolean,default:!1},items:{default:()=>[]},placeholder:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const a=x(l,"modelValue"),t=Ne();return(o,s)=>(u(),k("div",{class:R([{"hover:bg-accent":!e(t).tip,"pointer-events-none opacity-50":o.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[$("span",Pd,[E(o.$slots,"default"),e(t).tip?(u(),y(e(rt),{key:0,side:"bottom"},{trigger:c(()=>[m(e(ft),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[E(o.$slots,"tip")]),_:3})):A("",!0)]),m(e(Yo),{modelValue:a.value,"onUpdate:modelValue":s[0]||(s[0]=n=>a.value=n),class:"h-8 w-[165px]"},null,8,["modelValue"])],2))}}),Ad=V({__name:"copyright",props:ve({disabled:{type:Boolean}},{copyrightEnable:{type:Boolean},copyrightEnableModifiers:{},copyrightDate:{},copyrightDateModifiers:{},copyrightIcp:{},copyrightIcpModifiers:{},copyrightIcpLink:{},copyrightIcpLinkModifiers:{},copyrightCompanyName:{},copyrightCompanyNameModifiers:{},copyrightCompanySiteLink:{},copyrightCompanySiteLinkModifiers:{}}),emits:["update:copyrightEnable","update:copyrightDate","update:copyrightIcp","update:copyrightIcpLink","update:copyrightCompanyName","update:copyrightCompanySiteLink"],setup(l){const a=l,t=x(l,"copyrightEnable"),o=x(l,"copyrightDate"),s=x(l,"copyrightIcp"),n=x(l,"copyrightIcpLink"),r=x(l,"copyrightCompanyName"),i=x(l,"copyrightCompanySiteLink"),d=w(()=>a.disabled||!t.value);return(f,p)=>(u(),k(Z,null,[m(ae,{modelValue:t.value,"onUpdate:modelValue":p[0]||(p[0]=v=>t.value=v),disabled:f.disabled},{default:c(()=>[z(_(e(g)("preferences.copyright.enable")),1)]),_:1},8,["modelValue","disabled"]),m(bt,{modelValue:r.value,"onUpdate:modelValue":p[1]||(p[1]=v=>r.value=v),disabled:d.value},{default:c(()=>[z(_(e(g)("preferences.copyright.companyName")),1)]),_:1},8,["modelValue","disabled"]),m(bt,{modelValue:i.value,"onUpdate:modelValue":p[2]||(p[2]=v=>i.value=v),disabled:d.value},{default:c(()=>[z(_(e(g)("preferences.copyright.companySiteLink")),1)]),_:1},8,["modelValue","disabled"]),m(bt,{modelValue:o.value,"onUpdate:modelValue":p[3]||(p[3]=v=>o.value=v),disabled:d.value},{default:c(()=>[z(_(e(g)("preferences.copyright.date")),1)]),_:1},8,["modelValue","disabled"]),m(bt,{modelValue:s.value,"onUpdate:modelValue":p[4]||(p[4]=v=>s.value=v),disabled:d.value},{default:c(()=>[z(_(e(g)("preferences.copyright.icp")),1)]),_:1},8,["modelValue","disabled"]),m(bt,{modelValue:n.value,"onUpdate:modelValue":p[5]||(p[5]=v=>n.value=v),disabled:d.value},{default:c(()=>[z(_(e(g)("preferences.copyright.icpLink")),1)]),_:1},8,["modelValue","disabled"])],64))}}),Id=V({__name:"footer",props:{footerEnable:{type:Boolean},footerEnableModifiers:{},footerFixed:{type:Boolean},footerFixedModifiers:{}},emits:["update:footerEnable","update:footerFixed"],setup(l){const a=x(l,"footerEnable"),t=x(l,"footerFixed");return(o,s)=>(u(),k(Z,null,[m(ae,{modelValue:a.value,"onUpdate:modelValue":s[0]||(s[0]=n=>a.value=n)},{default:c(()=>[z(_(e(g)("preferences.footer.visible")),1)]),_:1},8,["modelValue"]),m(ae,{modelValue:t.value,"onUpdate:modelValue":s[1]||(s[1]=n=>t.value=n),disabled:!a.value},{default:c(()=>[z(_(e(g)("preferences.footer.fixed")),1)]),_:1},8,["modelValue","disabled"])],64))}}),Ud=V({__name:"header",props:ve({disabled:{type:Boolean}},{headerEnable:{type:Boolean},headerEnableModifiers:{},headerMode:{},headerModeModifiers:{},headerMenuAlign:{},headerMenuAlignModifiers:{}}),emits:["update:headerEnable","update:headerMode","update:headerMenuAlign"],setup(l){const a=x(l,"headerEnable"),t=x(l,"headerMode"),o=x(l,"headerMenuAlign"),s=[{label:g("preferences.header.modeStatic"),value:"static"},{label:g("preferences.header.modeFixed"),value:"fixed"},{label:g("preferences.header.modeAuto"),value:"auto"},{label:g("preferences.header.modeAutoScroll"),value:"auto-scroll"}],n=[{label:g("preferences.header.menuAlignStart"),value:"start"},{label:g("preferences.header.menuAlignCenter"),value:"center"},{label:g("preferences.header.menuAlignEnd"),value:"end"}];return(r,i)=>(u(),k(Z,null,[m(ae,{modelValue:a.value,"onUpdate:modelValue":i[0]||(i[0]=d=>a.value=d),disabled:r.disabled},{default:c(()=>[z(_(e(g)("preferences.header.visible")),1)]),_:1},8,["modelValue","disabled"]),m(Ht,{modelValue:t.value,"onUpdate:modelValue":i[1]||(i[1]=d=>t.value=d),disabled:!a.value,items:s},{default:c(()=>[z(_(e(g)("preferences.mode")),1)]),_:1},8,["modelValue","disabled"]),m(la,{modelValue:o.value,"onUpdate:modelValue":i[2]||(i[2]=d=>o.value=d),disabled:!a.value,items:n},{default:c(()=>[z(_(e(g)("preferences.header.menuAlign")),1)]),_:1},8,["modelValue","disabled"])],64))}}),Hd={class:"flex w-full flex-wrap gap-5"},Od=["onClick"],Dd={class:"text-muted-foreground flex-center hover:text-foreground mt-2 text-center text-xs"},Wd=V({name:"PreferenceLayout",__name:"layout",props:{modelValue:{default:"sidebar-nav"},modelModifiers:{}},emits:["update:modelValue"],setup(l){const a=x(l,"modelValue"),t={"full-content":id,"header-nav":Tl,"mixed-nav":wd,"sidebar-mixed-nav":Sd,"sidebar-nav":$d,"header-mixed-nav":pd,"header-sidebar-nav":bd},o=w(()=>[{name:g("preferences.vertical"),tip:g("preferences.verticalTip"),type:"sidebar-nav"},{name:g("preferences.twoColumn"),tip:g("preferences.twoColumnTip"),type:"sidebar-mixed-nav"},{name:g("preferences.horizontal"),tip:g("preferences.horizontalTip"),type:"header-nav"},{name:g("preferences.headerSidebarNav"),tip:g("preferences.headerSidebarNavTip"),type:"header-sidebar-nav"},{name:g("preferences.mixedMenu"),tip:g("preferences.mixedMenuTip"),type:"mixed-nav"},{name:g("preferences.headerTwoColumn"),tip:g("preferences.headerTwoColumnTip"),type:"header-mixed-nav"},{name:g("preferences.fullContent"),tip:g("preferences.fullContentTip"),type:"full-content"}]);function s(n){return n===a.value?["outline-box-active"]:[]}return(n,r)=>(u(),k("div",Hd,[(u(!0),k(Z,null,ue(o.value,i=>(u(),k("div",{key:i.name,class:"flex w-[100px] cursor-pointer flex-col",onClick:d=>a.value=i.type},[$("div",{class:R([s(i.type),"outline-box flex-center"])},[(u(),y(Le(t[i.type])))],2),$("div",Dd,[z(_(i.name)+" ",1),i.tip?(u(),y(e(rt),{key:0,side:"bottom"},{trigger:c(()=>[m(e(ft),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[z(" "+_(i.tip),1)]),_:2},1024)):A("",!0)])],8,Od))),128))]))}}),Rd=V({name:"PreferenceNavigationConfig",__name:"navigation",props:ve({disabled:{type:Boolean},disabledNavigationSplit:{type:Boolean}},{navigationStyleType:{},navigationStyleTypeModifiers:{},navigationSplit:{type:Boolean},navigationSplitModifiers:{},navigationAccordion:{type:Boolean},navigationAccordionModifiers:{}}),emits:["update:navigationStyleType","update:navigationSplit","update:navigationAccordion"],setup(l){const a=x(l,"navigationStyleType"),t=x(l,"navigationSplit"),o=x(l,"navigationAccordion"),s=[{label:g("preferences.rounded"),value:"rounded"},{label:g("preferences.plain"),value:"plain"}];return(n,r)=>(u(),k(Z,null,[m(la,{modelValue:a.value,"onUpdate:modelValue":r[0]||(r[0]=i=>a.value=i),disabled:n.disabled,items:s},{default:c(()=>[z(_(e(g)("preferences.navigationMenu.style")),1)]),_:1},8,["modelValue","disabled"]),m(ae,{modelValue:t.value,"onUpdate:modelValue":r[1]||(r[1]=i=>t.value=i),disabled:n.disabledNavigationSplit||n.disabled},{tip:c(()=>[z(_(e(g)("preferences.navigationMenu.splitTip")),1)]),default:c(()=>[z(_(e(g)("preferences.navigationMenu.split"))+" ",1)]),_:1},8,["modelValue","disabled"]),m(ae,{modelValue:o.value,"onUpdate:modelValue":r[2]||(r[2]=i=>o.value=i),disabled:n.disabled},{default:c(()=>[z(_(e(g)("preferences.navigationMenu.accordion")),1)]),_:1},8,["modelValue","disabled"])],64))}}),Nd={class:"flex items-center text-sm"},Fd=V({name:"PreferenceCheckboxItem",__name:"checkbox-item",props:ve({disabled:{type:Boolean,default:!1},items:{default:()=>[]},multiple:{type:Boolean,default:!1},onBtnClick:{type:Function,default:()=>{}},placeholder:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const a=x(l,"modelValue"),t=Ne();return(o,s)=>(u(),k("div",{class:R([{"hover:bg-accent":!e(t).tip,"pointer-events-none opacity-50":o.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[$("span",Nd,[E(o.$slots,"default"),e(t).tip?(u(),y(e(rt),{key:0,side:"bottom"},{trigger:c(()=>[m(e(ft),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[E(o.$slots,"tip")]),_:3})):A("",!0)]),m(e(Qs),{modelValue:a.value,"onUpdate:modelValue":s[0]||(s[0]=n=>a.value=n),class:"h-8 w-[165px]",options:o.items,disabled:o.disabled,multiple:o.multiple,onBtnClick:o.onBtnClick},null,8,["modelValue","options","disabled","multiple","onBtnClick"])],2))}}),Kd={class:"flex items-center text-sm"},Ml=V({name:"PreferenceSelectItem",__name:"number-field-item",props:ve({disabled:{type:Boolean,default:!1},items:{default:()=>[]},placeholder:{default:""},tip:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const a=x(l,"modelValue"),t=Ne();return(o,s)=>(u(),k("div",{class:R([{"hover:bg-accent":!e(t).tip,"pointer-events-none opacity-50":o.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[$("span",Kd,[E(o.$slots,"default"),e(t).tip||o.tip?(u(),y(e(rt),{key:0,side:"bottom"},{trigger:c(()=>[m(e(ft),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[E(o.$slots,"tip",{},()=>[o.tip?(u(!0),k(Z,{key:0},ue(o.tip.split(`
`),(n,r)=>(u(),k("p",{key:r},_(n),1))),128)):A("",!0)])]),_:3})):A("",!0)]),m(e(Ps),fe({modelValue:a.value,"onUpdate:modelValue":s[0]||(s[0]=n=>a.value=n)},o.$attrs,{class:"w-[165px]"}),{default:c(()=>[m(e(As),null,{default:c(()=>[m(e(Is)),m(e(Hs)),m(e(Us))]),_:1})]),_:1},16,["modelValue"])],2))}}),jd=V({__name:"sidebar",props:ve({currentLayout:{},disabled:{type:Boolean}},{sidebarEnable:{type:Boolean},sidebarEnableModifiers:{},sidebarWidth:{},sidebarWidthModifiers:{},sidebarCollapsedShowTitle:{type:Boolean},sidebarCollapsedShowTitleModifiers:{},sidebarAutoActivateChild:{type:Boolean},sidebarAutoActivateChildModifiers:{},sidebarCollapsed:{type:Boolean},sidebarCollapsedModifiers:{},sidebarExpandOnHover:{type:Boolean},sidebarExpandOnHoverModifiers:{},sidebarButtons:{default:[]},sidebarButtonsModifiers:{},sidebarCollapsedButton:{type:Boolean},sidebarCollapsedButtonModifiers:{},sidebarFixedButton:{type:Boolean},sidebarFixedButtonModifiers:{}}),emits:["update:sidebarEnable","update:sidebarWidth","update:sidebarCollapsedShowTitle","update:sidebarAutoActivateChild","update:sidebarCollapsed","update:sidebarExpandOnHover","update:sidebarButtons","update:sidebarCollapsedButton","update:sidebarFixedButton"],setup(l){const a=x(l,"sidebarEnable"),t=x(l,"sidebarWidth"),o=x(l,"sidebarCollapsedShowTitle"),s=x(l,"sidebarAutoActivateChild"),n=x(l,"sidebarCollapsed"),r=x(l,"sidebarExpandOnHover"),i=x(l,"sidebarButtons"),d=x(l,"sidebarCollapsedButton"),f=x(l,"sidebarFixedButton");qe(()=>{d.value&&!i.value.includes("collapsed")&&i.value.push("collapsed"),f.value&&!i.value.includes("fixed")&&i.value.push("fixed")});const p=()=>{d.value=!!i.value.includes("collapsed"),f.value=!!i.value.includes("fixed")};return(v,h)=>(u(),k(Z,null,[m(ae,{modelValue:a.value,"onUpdate:modelValue":h[0]||(h[0]=b=>a.value=b),disabled:v.disabled},{default:c(()=>[z(_(e(g)("preferences.sidebar.visible")),1)]),_:1},8,["modelValue","disabled"]),m(ae,{modelValue:n.value,"onUpdate:modelValue":h[1]||(h[1]=b=>n.value=b),disabled:!a.value||v.disabled},{default:c(()=>[z(_(e(g)("preferences.sidebar.collapsed")),1)]),_:1},8,["modelValue","disabled"]),m(ae,{modelValue:r.value,"onUpdate:modelValue":h[2]||(h[2]=b=>r.value=b),disabled:!a.value||v.disabled||!n.value,tip:e(g)("preferences.sidebar.expandOnHoverTip")},{default:c(()=>[z(_(e(g)("preferences.sidebar.expandOnHover")),1)]),_:1},8,["modelValue","disabled","tip"]),m(ae,{modelValue:o.value,"onUpdate:modelValue":h[3]||(h[3]=b=>o.value=b),disabled:!a.value||v.disabled||!n.value},{default:c(()=>[z(_(e(g)("preferences.sidebar.collapsedShowTitle")),1)]),_:1},8,["modelValue","disabled"]),m(ae,{modelValue:s.value,"onUpdate:modelValue":h[4]||(h[4]=b=>s.value=b),disabled:!a.value||!["sidebar-mixed-nav","mixed-nav","header-mixed-nav"].includes(v.currentLayout)||v.disabled,tip:e(g)("preferences.sidebar.autoActivateChildTip")},{default:c(()=>[z(_(e(g)("preferences.sidebar.autoActivateChild")),1)]),_:1},8,["modelValue","disabled","tip"]),m(Fd,{items:[{label:e(g)("preferences.sidebar.buttonCollapsed"),value:"collapsed"},{label:e(g)("preferences.sidebar.buttonFixed"),value:"fixed"}],multiple:"",modelValue:i.value,"onUpdate:modelValue":h[5]||(h[5]=b=>i.value=b),"on-btn-click":p},{default:c(()=>[z(_(e(g)("preferences.sidebar.buttons")),1)]),_:1},8,["items","modelValue"]),m(Ml,{modelValue:t.value,"onUpdate:modelValue":h[6]||(h[6]=b=>t.value=b),disabled:!a.value||v.disabled,max:320,min:160,step:10},{default:c(()=>[z(_(e(g)("preferences.sidebar.width")),1)]),_:1},8,["modelValue","disabled"])],64))}}),Gd=V({name:"PreferenceTabsConfig",__name:"tabbar",props:ve({disabled:{type:Boolean}},{tabbarEnable:{type:Boolean},tabbarEnableModifiers:{},tabbarShowIcon:{type:Boolean},tabbarShowIconModifiers:{},tabbarPersist:{type:Boolean},tabbarPersistModifiers:{},tabbarDraggable:{type:Boolean},tabbarDraggableModifiers:{},tabbarWheelable:{type:Boolean},tabbarWheelableModifiers:{},tabbarStyleType:{},tabbarStyleTypeModifiers:{},tabbarShowMore:{type:Boolean},tabbarShowMoreModifiers:{},tabbarShowMaximize:{type:Boolean},tabbarShowMaximizeModifiers:{},tabbarMaxCount:{},tabbarMaxCountModifiers:{},tabbarMiddleClickToClose:{type:Boolean},tabbarMiddleClickToCloseModifiers:{}}),emits:["update:tabbarEnable","update:tabbarShowIcon","update:tabbarPersist","update:tabbarDraggable","update:tabbarWheelable","update:tabbarStyleType","update:tabbarShowMore","update:tabbarShowMaximize","update:tabbarMaxCount","update:tabbarMiddleClickToClose"],setup(l){const a=x(l,"tabbarEnable"),t=x(l,"tabbarShowIcon"),o=x(l,"tabbarPersist"),s=x(l,"tabbarDraggable"),n=x(l,"tabbarWheelable"),r=x(l,"tabbarStyleType"),i=x(l,"tabbarShowMore"),d=x(l,"tabbarShowMaximize"),f=x(l,"tabbarMaxCount"),p=x(l,"tabbarMiddleClickToClose"),v=w(()=>[{label:g("preferences.tabbar.styleType.chrome"),value:"chrome"},{label:g("preferences.tabbar.styleType.plain"),value:"plain"},{label:g("preferences.tabbar.styleType.card"),value:"card"},{label:g("preferences.tabbar.styleType.brisk"),value:"brisk"}]);return(h,b)=>(u(),k(Z,null,[m(ae,{modelValue:a.value,"onUpdate:modelValue":b[0]||(b[0]=S=>a.value=S),disabled:h.disabled},{default:c(()=>[z(_(e(g)("preferences.tabbar.enable")),1)]),_:1},8,["modelValue","disabled"]),m(ae,{modelValue:o.value,"onUpdate:modelValue":b[1]||(b[1]=S=>o.value=S),disabled:!a.value},{default:c(()=>[z(_(e(g)("preferences.tabbar.persist")),1)]),_:1},8,["modelValue","disabled"]),m(Ml,{modelValue:f.value,"onUpdate:modelValue":b[2]||(b[2]=S=>f.value=S),disabled:!a.value,max:30,min:0,step:5,tip:e(g)("preferences.tabbar.maxCountTip")},{default:c(()=>[z(_(e(g)("preferences.tabbar.maxCount")),1)]),_:1},8,["modelValue","disabled","tip"]),m(ae,{modelValue:s.value,"onUpdate:modelValue":b[3]||(b[3]=S=>s.value=S),disabled:!a.value},{default:c(()=>[z(_(e(g)("preferences.tabbar.draggable")),1)]),_:1},8,["modelValue","disabled"]),m(ae,{modelValue:n.value,"onUpdate:modelValue":b[4]||(b[4]=S=>n.value=S),disabled:!a.value,tip:e(g)("preferences.tabbar.wheelableTip")},{default:c(()=>[z(_(e(g)("preferences.tabbar.wheelable")),1)]),_:1},8,["modelValue","disabled","tip"]),m(ae,{modelValue:p.value,"onUpdate:modelValue":b[5]||(b[5]=S=>p.value=S),disabled:!a.value},{default:c(()=>[z(_(e(g)("preferences.tabbar.middleClickClose")),1)]),_:1},8,["modelValue","disabled"]),m(ae,{modelValue:t.value,"onUpdate:modelValue":b[6]||(b[6]=S=>t.value=S),disabled:!a.value},{default:c(()=>[z(_(e(g)("preferences.tabbar.icon")),1)]),_:1},8,["modelValue","disabled"]),m(ae,{modelValue:i.value,"onUpdate:modelValue":b[7]||(b[7]=S=>i.value=S),disabled:!a.value},{default:c(()=>[z(_(e(g)("preferences.tabbar.showMore")),1)]),_:1},8,["modelValue","disabled"]),m(ae,{modelValue:d.value,"onUpdate:modelValue":b[8]||(b[8]=S=>d.value=S),disabled:!a.value},{default:c(()=>[z(_(e(g)("preferences.tabbar.showMaximize")),1)]),_:1},8,["modelValue","disabled"]),m(Ht,{modelValue:r.value,"onUpdate:modelValue":b[9]||(b[9]=S=>r.value=S),items:v.value},{default:c(()=>[z(_(e(g)("preferences.tabbar.styleType.title")),1)]),_:1},8,["modelValue","items"])],64))}}),qd=V({name:"PreferenceInterfaceControl",__name:"widget",props:{widgetGlobalSearch:{type:Boolean},widgetGlobalSearchModifiers:{},widgetFullscreen:{type:Boolean},widgetFullscreenModifiers:{},widgetLanguageToggle:{type:Boolean},widgetLanguageToggleModifiers:{},widgetNotification:{type:Boolean},widgetNotificationModifiers:{},widgetThemeToggle:{type:Boolean},widgetThemeToggleModifiers:{},widgetSidebarToggle:{type:Boolean},widgetSidebarToggleModifiers:{},widgetLockScreen:{type:Boolean},widgetLockScreenModifiers:{},appPreferencesButtonPosition:{},appPreferencesButtonPositionModifiers:{},widgetRefresh:{type:Boolean},widgetRefreshModifiers:{}},emits:["update:widgetGlobalSearch","update:widgetFullscreen","update:widgetLanguageToggle","update:widgetNotification","update:widgetThemeToggle","update:widgetSidebarToggle","update:widgetLockScreen","update:appPreferencesButtonPosition","update:widgetRefresh"],setup(l){const a=x(l,"widgetGlobalSearch"),t=x(l,"widgetFullscreen"),o=x(l,"widgetLanguageToggle"),s=x(l,"widgetNotification"),n=x(l,"widgetThemeToggle"),r=x(l,"widgetSidebarToggle"),i=x(l,"widgetLockScreen"),d=x(l,"appPreferencesButtonPosition"),f=x(l,"widgetRefresh"),p=w(()=>[{label:g("preferences.position.auto"),value:"auto"},{label:g("preferences.position.header"),value:"header"},{label:g("preferences.position.fixed"),value:"fixed"}]);return(v,h)=>(u(),k(Z,null,[m(ae,{modelValue:a.value,"onUpdate:modelValue":h[0]||(h[0]=b=>a.value=b)},{default:c(()=>[z(_(e(g)("preferences.widget.globalSearch")),1)]),_:1},8,["modelValue"]),m(ae,{modelValue:n.value,"onUpdate:modelValue":h[1]||(h[1]=b=>n.value=b)},{default:c(()=>[z(_(e(g)("preferences.widget.themeToggle")),1)]),_:1},8,["modelValue"]),m(ae,{modelValue:o.value,"onUpdate:modelValue":h[2]||(h[2]=b=>o.value=b)},{default:c(()=>[z(_(e(g)("preferences.widget.languageToggle")),1)]),_:1},8,["modelValue"]),m(ae,{modelValue:t.value,"onUpdate:modelValue":h[3]||(h[3]=b=>t.value=b)},{default:c(()=>[z(_(e(g)("preferences.widget.fullscreen")),1)]),_:1},8,["modelValue"]),m(ae,{modelValue:s.value,"onUpdate:modelValue":h[4]||(h[4]=b=>s.value=b)},{default:c(()=>[z(_(e(g)("preferences.widget.notification")),1)]),_:1},8,["modelValue"]),m(ae,{modelValue:i.value,"onUpdate:modelValue":h[5]||(h[5]=b=>i.value=b)},{default:c(()=>[z(_(e(g)("preferences.widget.lockScreen")),1)]),_:1},8,["modelValue"]),m(ae,{modelValue:r.value,"onUpdate:modelValue":h[6]||(h[6]=b=>r.value=b)},{default:c(()=>[z(_(e(g)("preferences.widget.sidebarToggle")),1)]),_:1},8,["modelValue"]),m(ae,{modelValue:f.value,"onUpdate:modelValue":h[7]||(h[7]=b=>f.value=b)},{default:c(()=>[z(_(e(g)("preferences.widget.refresh")),1)]),_:1},8,["modelValue"]),m(Ht,{modelValue:d.value,"onUpdate:modelValue":h[8]||(h[8]=b=>d.value=b),items:p.value},{default:c(()=>[z(_(e(g)("preferences.position.title")),1)]),_:1},8,["modelValue","items"])],64))}}),Yd=V({name:"PreferenceGeneralConfig",__name:"global",props:{shortcutKeysEnable:{type:Boolean},shortcutKeysEnableModifiers:{},shortcutKeysGlobalSearch:{type:Boolean},shortcutKeysGlobalSearchModifiers:{},shortcutKeysLogout:{type:Boolean},shortcutKeysLogoutModifiers:{},shortcutKeysLockScreen:{type:Boolean},shortcutKeysLockScreenModifiers:{}},emits:["update:shortcutKeysEnable","update:shortcutKeysGlobalSearch","update:shortcutKeysLogout","update:shortcutKeysLockScreen"],setup(l){const a=x(l,"shortcutKeysEnable"),t=x(l,"shortcutKeysGlobalSearch"),o=x(l,"shortcutKeysLogout"),s=x(l,"shortcutKeysLockScreen"),n=w(()=>wt()?"Alt":"⌥");return(r,i)=>(u(),k(Z,null,[m(ae,{modelValue:a.value,"onUpdate:modelValue":i[0]||(i[0]=d=>a.value=d)},{default:c(()=>[z(_(e(g)("preferences.shortcutKeys.title")),1)]),_:1},8,["modelValue"]),m(ae,{modelValue:t.value,"onUpdate:modelValue":i[1]||(i[1]=d=>t.value=d),disabled:!a.value},{shortcut:c(()=>[z(_(e(wt)()?"Ctrl":"⌘")+" ",1),i[4]||(i[4]=$("kbd",null," K ",-1))]),default:c(()=>[z(_(e(g)("preferences.shortcutKeys.search"))+" ",1)]),_:1},8,["modelValue","disabled"]),m(ae,{modelValue:o.value,"onUpdate:modelValue":i[2]||(i[2]=d=>o.value=d),disabled:!a.value},{shortcut:c(()=>[z(_(n.value)+" Q ",1)]),default:c(()=>[z(_(e(g)("preferences.shortcutKeys.logout"))+" ",1)]),_:1},8,["modelValue","disabled"]),m(ae,{modelValue:s.value,"onUpdate:modelValue":i[3]||(i[3]=d=>s.value=d),disabled:!a.value},{shortcut:c(()=>[z(_(n.value)+" L ",1)]),default:c(()=>[z(_(e(g)("ui.widgets.lockScreen.title"))+" ",1)]),_:1},8,["modelValue","disabled"])],64))}}),Jd={class:"flex w-full flex-wrap justify-between"},Xd=["onClick"],Zd={class:"flex-center relative size-5 rounded-sm"},Qd=["value"],eu={class:"text-muted-foreground my-2 text-center text-xs"},tu=V({name:"PreferenceBuiltinTheme",__name:"builtin",props:ve({isDark:{type:Boolean}},{modelValue:{default:"default"},modelModifiers:{},themeColorPrimary:{},themeColorPrimaryModifiers:{}}),emits:["update:modelValue","update:themeColorPrimary"],setup(l){const a=l,t=X(),o=x(l,"modelValue"),s=x(l,"themeColorPrimary"),n=Pt(h=>{s.value=h},300,!0,!0),r=w(()=>new Tn(s.value||"").toHexString()),i=w(()=>[...Mn]);function d(h){switch(h){case"custom":return g("preferences.theme.builtin.custom");case"deep-blue":return g("preferences.theme.builtin.deepBlue");case"deep-green":return g("preferences.theme.builtin.deepGreen");case"default":return g("preferences.theme.builtin.default");case"gray":return g("preferences.theme.builtin.gray");case"green":return g("preferences.theme.builtin.green");case"neutral":return g("preferences.theme.builtin.neutral");case"orange":return g("preferences.theme.builtin.orange");case"pink":return g("preferences.theme.builtin.pink");case"rose":return g("preferences.theme.builtin.rose");case"sky-blue":return g("preferences.theme.builtin.skyBlue");case"slate":return g("preferences.theme.builtin.slate");case"violet":return g("preferences.theme.builtin.violet");case"yellow":return g("preferences.theme.builtin.yellow");case"zinc":return g("preferences.theme.builtin.zinc")}}function f(h){o.value=h.type}function p(h){const b=h.target;n($n(b.value))}function v(){var h,b,S;(S=(b=(h=t.value)==null?void 0:h[0])==null?void 0:b.click)==null||S.call(b)}return pe(()=>[o.value,a.isDark],([h,b],[S,H])=>{const O=i.value.find(C=>C.type===h);if(O){const C=b&&O.darkPrimaryColor||O.primaryColor;O.type==="custom"&&b!==H||(s.value=C||O.color)}}),(h,b)=>(u(),k("div",Jd,[(u(!0),k(Z,null,ue(i.value,S=>(u(),k("div",{key:S.type,class:"flex cursor-pointer flex-col",onClick:H=>f(S)},[$("div",{class:R([{"outline-box-active":S.type===o.value},"outline-box flex-center group cursor-pointer"])},[S.type!=="custom"?(u(),k("div",{key:0,style:ce({backgroundColor:S.color}),class:"mx-10 my-2 size-5 rounded-md"},null,4)):(u(),k("div",{key:1,class:"size-full px-10 py-2",onClick:Me(v,["stop"])},[$("div",Zd,[m(e(xs),{class:"absolute z-10 size-5 opacity-60 group-hover:opacity-100"}),$("input",{ref_for:!0,ref_key:"colorInput",ref:t,value:r.value,class:"absolute inset-0 opacity-0",type:"color",onInput:p},null,40,Qd)])]))],2),$("div",eu,_(d(S.type)),1)],8,Xd))),128))]))}}),au=V({name:"PreferenceColorMode",__name:"color-mode",props:{appColorWeakMode:{type:Boolean,default:!1},appColorWeakModeModifiers:{},appColorGrayMode:{type:Boolean,default:!1},appColorGrayModeModifiers:{}},emits:["update:appColorWeakMode","update:appColorGrayMode"],setup(l){const a=x(l,"appColorWeakMode"),t=x(l,"appColorGrayMode");return(o,s)=>(u(),k(Z,null,[m(ae,{modelValue:a.value,"onUpdate:modelValue":s[0]||(s[0]=n=>a.value=n)},{default:c(()=>[z(_(e(g)("preferences.theme.weakMode")),1)]),_:1},8,["modelValue"]),m(ae,{modelValue:t.value,"onUpdate:modelValue":s[1]||(s[1]=n=>t.value=n)},{default:c(()=>[z(_(e(g)("preferences.theme.grayMode")),1)]),_:1},8,["modelValue"])],64))}}),lu=V({name:"PreferenceColorMode",__name:"radius",props:{themeRadius:{default:"0.5"},themeRadiusModifiers:{}},emits:["update:themeRadius"],setup(l){const a=x(l,"themeRadius"),t=[{label:"0",value:"0"},{label:"0.25",value:"0.25"},{label:"0.5",value:"0.5"},{label:"0.75",value:"0.75"},{label:"1",value:"1"}];return(o,s)=>(u(),y(e(fl),{modelValue:a.value,"onUpdate:modelValue":s[0]||(s[0]=n=>a.value=n),class:"gap-2",size:"sm",type:"single",variant:"outline"},{default:c(()=>[(u(),k(Z,null,ue(t,n=>m(e(ml),{key:n.value,value:n.value,class:"data-[state=on]:bg-primary data-[state=on]:text-primary-foreground h-7 w-16 rounded-sm"},{default:c(()=>[z(_(n.label),1)]),_:2},1032,["value"])),64))]),_:1},8,["modelValue"]))}}),ou={class:"flex w-full flex-wrap justify-between"},nu=["onClick"],su={class:"text-muted-foreground mt-2 text-center text-xs"},ru=V({name:"PreferenceTheme",__name:"theme",props:{modelValue:{default:"auto"},modelModifiers:{},themeSemiDarkSidebar:{type:Boolean},themeSemiDarkSidebarModifiers:{},themeSemiDarkHeader:{type:Boolean},themeSemiDarkHeaderModifiers:{}},emits:["update:modelValue","update:themeSemiDarkSidebar","update:themeSemiDarkHeader"],setup(l){const a=x(l,"modelValue"),t=x(l,"themeSemiDarkSidebar"),o=x(l,"themeSemiDarkHeader"),s=[{icon:Rn,name:"light"},{icon:Nn,name:"dark"},{icon:Fn,name:"auto"}];function n(i){return i===a.value?["outline-box-active"]:[]}function r(i){switch(i){case"auto":return g("preferences.followSystem");case"dark":return g("preferences.theme.dark");case"light":return g("preferences.theme.light")}}return(i,d)=>(u(),k("div",ou,[(u(),k(Z,null,ue(s,f=>$("div",{key:f.name,class:"flex cursor-pointer flex-col",onClick:p=>a.value=f.name},[$("div",{class:R([n(f.name),"outline-box flex-center py-4"])},[(u(),y(Le(f.icon),{class:"mx-9 size-5"}))],2),$("div",su,_(r(f.name)),1)],8,nu)),64)),m(ae,{modelValue:t.value,"onUpdate:modelValue":d[0]||(d[0]=f=>t.value=f),disabled:a.value==="dark",class:"mt-6"},{default:c(()=>[z(_(e(g)("preferences.theme.darkSidebar")),1)]),_:1},8,["modelValue","disabled"]),m(ae,{modelValue:o.value,"onUpdate:modelValue":d[1]||(d[1]=f=>o.value=f),disabled:a.value==="dark"},{default:c(()=>[z(_(e(g)("preferences.theme.darkHeader")),1)]),_:1},8,["modelValue","disabled"])]))}}),iu={class:"flex items-center"},du={key:0,class:"bg-primary absolute right-0.5 top-0.5 h-2 w-2 rounded"},uu={class:"p-1"},cu=V({__name:"preferences-drawer",props:{appLocale:{},appLocaleModifiers:{},appDynamicTitle:{type:Boolean},appDynamicTitleModifiers:{},appLayout:{},appLayoutModifiers:{},appColorGrayMode:{type:Boolean},appColorGrayModeModifiers:{},appColorWeakMode:{type:Boolean},appColorWeakModeModifiers:{},appContentCompact:{},appContentCompactModifiers:{},appWatermark:{type:Boolean},appWatermarkModifiers:{},appEnableCheckUpdates:{type:Boolean},appEnableCheckUpdatesModifiers:{},appPreferencesButtonPosition:{},appPreferencesButtonPositionModifiers:{},transitionProgress:{type:Boolean},transitionProgressModifiers:{},transitionName:{},transitionNameModifiers:{},transitionLoading:{type:Boolean},transitionLoadingModifiers:{},transitionEnable:{type:Boolean},transitionEnableModifiers:{},themeColorPrimary:{},themeColorPrimaryModifiers:{},themeBuiltinType:{},themeBuiltinTypeModifiers:{},themeMode:{},themeModeModifiers:{},themeRadius:{},themeRadiusModifiers:{},themeSemiDarkSidebar:{type:Boolean},themeSemiDarkSidebarModifiers:{},themeSemiDarkHeader:{type:Boolean},themeSemiDarkHeaderModifiers:{},sidebarEnable:{type:Boolean},sidebarEnableModifiers:{},sidebarWidth:{},sidebarWidthModifiers:{},sidebarCollapsed:{type:Boolean},sidebarCollapsedModifiers:{},sidebarCollapsedShowTitle:{type:Boolean},sidebarCollapsedShowTitleModifiers:{},sidebarAutoActivateChild:{type:Boolean},sidebarAutoActivateChildModifiers:{},sidebarExpandOnHover:{type:Boolean},sidebarExpandOnHoverModifiers:{},sidebarCollapsedButton:{type:Boolean},sidebarCollapsedButtonModifiers:{},sidebarFixedButton:{type:Boolean},sidebarFixedButtonModifiers:{},headerEnable:{type:Boolean},headerEnableModifiers:{},headerMode:{},headerModeModifiers:{},headerMenuAlign:{},headerMenuAlignModifiers:{},breadcrumbEnable:{type:Boolean},breadcrumbEnableModifiers:{},breadcrumbShowIcon:{type:Boolean},breadcrumbShowIconModifiers:{},breadcrumbShowHome:{type:Boolean},breadcrumbShowHomeModifiers:{},breadcrumbStyleType:{},breadcrumbStyleTypeModifiers:{},breadcrumbHideOnlyOne:{type:Boolean},breadcrumbHideOnlyOneModifiers:{},tabbarEnable:{type:Boolean},tabbarEnableModifiers:{},tabbarShowIcon:{type:Boolean},tabbarShowIconModifiers:{},tabbarShowMore:{type:Boolean},tabbarShowMoreModifiers:{},tabbarShowMaximize:{type:Boolean},tabbarShowMaximizeModifiers:{},tabbarPersist:{type:Boolean},tabbarPersistModifiers:{},tabbarDraggable:{type:Boolean},tabbarDraggableModifiers:{},tabbarWheelable:{type:Boolean},tabbarWheelableModifiers:{},tabbarStyleType:{},tabbarStyleTypeModifiers:{},tabbarMaxCount:{},tabbarMaxCountModifiers:{},tabbarMiddleClickToClose:{type:Boolean},tabbarMiddleClickToCloseModifiers:{},navigationStyleType:{},navigationStyleTypeModifiers:{},navigationSplit:{type:Boolean},navigationSplitModifiers:{},navigationAccordion:{type:Boolean},navigationAccordionModifiers:{},footerEnable:{type:Boolean},footerEnableModifiers:{},footerFixed:{type:Boolean},footerFixedModifiers:{},copyrightSettingShow:{type:Boolean},copyrightSettingShowModifiers:{},copyrightEnable:{type:Boolean},copyrightEnableModifiers:{},copyrightCompanyName:{},copyrightCompanyNameModifiers:{},copyrightCompanySiteLink:{},copyrightCompanySiteLinkModifiers:{},copyrightDate:{},copyrightDateModifiers:{},copyrightIcp:{},copyrightIcpModifiers:{},copyrightIcpLink:{},copyrightIcpLinkModifiers:{},shortcutKeysEnable:{type:Boolean},shortcutKeysEnableModifiers:{},shortcutKeysGlobalSearch:{type:Boolean},shortcutKeysGlobalSearchModifiers:{},shortcutKeysGlobalLogout:{type:Boolean},shortcutKeysGlobalLogoutModifiers:{},shortcutKeysGlobalLockScreen:{type:Boolean},shortcutKeysGlobalLockScreenModifiers:{},widgetGlobalSearch:{type:Boolean},widgetGlobalSearchModifiers:{},widgetFullscreen:{type:Boolean},widgetFullscreenModifiers:{},widgetLanguageToggle:{type:Boolean},widgetLanguageToggleModifiers:{},widgetNotification:{type:Boolean},widgetNotificationModifiers:{},widgetThemeToggle:{type:Boolean},widgetThemeToggleModifiers:{},widgetSidebarToggle:{type:Boolean},widgetSidebarToggleModifiers:{},widgetLockScreen:{type:Boolean},widgetLockScreenModifiers:{},widgetRefresh:{type:Boolean},widgetRefreshModifiers:{}},emits:ve(["clearPreferencesAndLogout"],["update:appLocale","update:appDynamicTitle","update:appLayout","update:appColorGrayMode","update:appColorWeakMode","update:appContentCompact","update:appWatermark","update:appEnableCheckUpdates","update:appPreferencesButtonPosition","update:transitionProgress","update:transitionName","update:transitionLoading","update:transitionEnable","update:themeColorPrimary","update:themeBuiltinType","update:themeMode","update:themeRadius","update:themeSemiDarkSidebar","update:themeSemiDarkHeader","update:sidebarEnable","update:sidebarWidth","update:sidebarCollapsed","update:sidebarCollapsedShowTitle","update:sidebarAutoActivateChild","update:sidebarExpandOnHover","update:sidebarCollapsedButton","update:sidebarFixedButton","update:headerEnable","update:headerMode","update:headerMenuAlign","update:breadcrumbEnable","update:breadcrumbShowIcon","update:breadcrumbShowHome","update:breadcrumbStyleType","update:breadcrumbHideOnlyOne","update:tabbarEnable","update:tabbarShowIcon","update:tabbarShowMore","update:tabbarShowMaximize","update:tabbarPersist","update:tabbarDraggable","update:tabbarWheelable","update:tabbarStyleType","update:tabbarMaxCount","update:tabbarMiddleClickToClose","update:navigationStyleType","update:navigationSplit","update:navigationAccordion","update:footerEnable","update:footerFixed","update:copyrightSettingShow","update:copyrightEnable","update:copyrightCompanyName","update:copyrightCompanySiteLink","update:copyrightDate","update:copyrightIcp","update:copyrightIcpLink","update:shortcutKeysEnable","update:shortcutKeysGlobalSearch","update:shortcutKeysGlobalLogout","update:shortcutKeysGlobalLockScreen","update:widgetGlobalSearch","update:widgetFullscreen","update:widgetLanguageToggle","update:widgetNotification","update:widgetThemeToggle","update:widgetSidebarToggle","update:widgetLockScreen","update:widgetRefresh"]),setup(l,{emit:a}){const t=a,o=Ja.getMessage(),s=x(l,"appLocale"),n=x(l,"appDynamicTitle"),r=x(l,"appLayout"),i=x(l,"appColorGrayMode"),d=x(l,"appColorWeakMode"),f=x(l,"appContentCompact"),p=x(l,"appWatermark"),v=x(l,"appEnableCheckUpdates"),h=x(l,"appPreferencesButtonPosition"),b=x(l,"transitionProgress"),S=x(l,"transitionName"),H=x(l,"transitionLoading"),O=x(l,"transitionEnable"),C=x(l,"themeColorPrimary"),W=x(l,"themeBuiltinType"),F=x(l,"themeMode"),U=x(l,"themeRadius"),L=x(l,"themeSemiDarkSidebar"),K=x(l,"themeSemiDarkHeader"),P=x(l,"sidebarEnable"),B=x(l,"sidebarWidth"),N=x(l,"sidebarCollapsed"),J=x(l,"sidebarCollapsedShowTitle"),oe=x(l,"sidebarAutoActivateChild"),de=x(l,"sidebarExpandOnHover"),ge=x(l,"sidebarCollapsedButton"),G=x(l,"sidebarFixedButton"),ee=x(l,"headerEnable"),he=x(l,"headerMode"),Ce=x(l,"headerMenuAlign"),Be=x(l,"breadcrumbEnable"),Ae=x(l,"breadcrumbShowIcon"),_e=x(l,"breadcrumbShowHome"),j=x(l,"breadcrumbStyleType"),ne=x(l,"breadcrumbHideOnlyOne"),le=x(l,"tabbarEnable"),ye=x(l,"tabbarShowIcon"),Se=x(l,"tabbarShowMore"),Ve=x(l,"tabbarShowMaximize"),De=x(l,"tabbarPersist"),Ie=x(l,"tabbarDraggable"),re=x(l,"tabbarWheelable"),me=x(l,"tabbarStyleType"),q=x(l,"tabbarMaxCount"),we=x(l,"tabbarMiddleClickToClose"),D=x(l,"navigationStyleType"),te=x(l,"navigationSplit"),ie=x(l,"navigationAccordion"),We=x(l,"footerEnable"),Re=x(l,"footerFixed"),Ol=x(l,"copyrightSettingShow"),sa=x(l,"copyrightEnable"),ra=x(l,"copyrightCompanyName"),ia=x(l,"copyrightCompanySiteLink"),da=x(l,"copyrightDate"),ua=x(l,"copyrightIcp"),ca=x(l,"copyrightIcpLink"),pa=x(l,"shortcutKeysEnable"),fa=x(l,"shortcutKeysGlobalSearch"),ma=x(l,"shortcutKeysGlobalLogout"),ha=x(l,"shortcutKeysGlobalLockScreen"),ba=x(l,"widgetGlobalSearch"),va=x(l,"widgetFullscreen"),ga=x(l,"widgetLanguageToggle"),ya=x(l,"widgetNotification"),wa=x(l,"widgetThemeToggle"),xa=x(l,"widgetSidebarToggle"),ka=x(l,"widgetLockScreen"),Ca=x(l,"widgetRefresh"),{diffPreference:dt,isDark:Dl,isFullContent:Ot,isHeaderNav:Wl,isHeaderSidebarNav:Rl,isMixedNav:Sa,isSideMixedNav:Nl,isSideMode:Fl,isSideNav:Kl}=ot(),{copy:jl}=Bn({legacy:!0}),[Gl]=_l(),_a=X("appearance"),ql=w(()=>[{label:g("preferences.appearance"),value:"appearance"},{label:g("preferences.layout"),value:"layout"},{label:g("preferences.shortcutKeys.title"),value:"shortcutKey"},{label:g("preferences.general"),value:"general"}]),Yl=w(()=>!Ot.value&&!Sa.value&&!Wl.value&&I.header.enable);function Jl(){return Y(this,null,function*(){var Dt;yield jl(JSON.stringify(dt.value,null,2)),(Dt=o.copyPreferencesSuccess)==null||Dt.call(o,g("preferences.copyPreferencesSuccessTitle"),g("preferences.copyPreferencesSuccess"))})}function Xl(){return Y(this,null,function*(){Ea(),Vn(),t("clearPreferencesAndLogout")})}function Zl(){return Y(this,null,function*(){dt.value&&(Ea(),yield al(I.app.locale))})}return(Dt,T)=>(u(),k("div",null,[m(e(Gl),{description:e(g)("preferences.subtitle"),title:e(g)("preferences.title"),class:"sm:max-w-sm"},{extra:c(()=>[$("div",iu,[m(e(Ze),{disabled:!e(dt),tooltip:e(g)("preferences.resetTip"),class:"relative"},{default:c(()=>[e(dt)?(u(),k("span",du)):A("",!0),m(e(ta),{class:"size-4",onClick:Zl})]),_:1},8,["disabled","tooltip"])])]),footer:c(()=>[m(e(Pe),{disabled:!e(dt),class:"mx-4 w-full",size:"sm",variant:"default",onClick:Jl},{default:c(()=>[m(e(cs),{class:"mr-2 size-3"}),z(" "+_(e(g)("preferences.copyPreferences")),1)]),_:1},8,["disabled"]),m(e(Pe),{disabled:!e(dt),class:"mr-4 w-full",size:"sm",variant:"ghost",onClick:Xl},{default:c(()=>[z(_(e(g)("preferences.clearAndLogout")),1)]),_:1},8,["disabled"])]),default:c(()=>[$("div",uu,[m(e($r),{modelValue:_a.value,"onUpdate:modelValue":T[68]||(T[68]=M=>_a.value=M),tabs:ql.value},{general:c(()=>[m(e(Te),{title:e(g)("preferences.general")},{default:c(()=>[m(e(Yi),{"app-dynamic-title":n.value,"onUpdate:appDynamicTitle":T[0]||(T[0]=M=>n.value=M),"app-enable-check-updates":v.value,"onUpdate:appEnableCheckUpdates":T[1]||(T[1]=M=>v.value=M),"app-locale":s.value,"onUpdate:appLocale":T[2]||(T[2]=M=>s.value=M),"app-watermark":p.value,"onUpdate:appWatermark":T[3]||(T[3]=M=>p.value=M)},null,8,["app-dynamic-title","app-enable-check-updates","app-locale","app-watermark"])]),_:1},8,["title"]),m(e(Te),{title:e(g)("preferences.animation.title")},{default:c(()=>[m(e(Gi),{"transition-enable":O.value,"onUpdate:transitionEnable":T[4]||(T[4]=M=>O.value=M),"transition-loading":H.value,"onUpdate:transitionLoading":T[5]||(T[5]=M=>H.value=M),"transition-name":S.value,"onUpdate:transitionName":T[6]||(T[6]=M=>S.value=M),"transition-progress":b.value,"onUpdate:transitionProgress":T[7]||(T[7]=M=>b.value=M)},null,8,["transition-enable","transition-loading","transition-name","transition-progress"])]),_:1},8,["title"])]),appearance:c(()=>[m(e(Te),{title:e(g)("preferences.theme.title")},{default:c(()=>[m(e(ru),{modelValue:F.value,"onUpdate:modelValue":T[8]||(T[8]=M=>F.value=M),"theme-semi-dark-header":K.value,"onUpdate:themeSemiDarkHeader":T[9]||(T[9]=M=>K.value=M),"theme-semi-dark-sidebar":L.value,"onUpdate:themeSemiDarkSidebar":T[10]||(T[10]=M=>L.value=M)},null,8,["modelValue","theme-semi-dark-header","theme-semi-dark-sidebar"])]),_:1},8,["title"]),m(e(Te),{title:e(g)("preferences.theme.builtin.title")},{default:c(()=>[m(e(tu),{modelValue:W.value,"onUpdate:modelValue":T[11]||(T[11]=M=>W.value=M),"theme-color-primary":C.value,"onUpdate:themeColorPrimary":T[12]||(T[12]=M=>C.value=M),"is-dark":e(Dl)},null,8,["modelValue","theme-color-primary","is-dark"])]),_:1},8,["title"]),m(e(Te),{title:e(g)("preferences.theme.radius")},{default:c(()=>[m(e(lu),{modelValue:U.value,"onUpdate:modelValue":T[13]||(T[13]=M=>U.value=M)},null,8,["modelValue"])]),_:1},8,["title"]),m(e(Te),{title:e(g)("preferences.other")},{default:c(()=>[m(e(au),{"app-color-gray-mode":i.value,"onUpdate:appColorGrayMode":T[14]||(T[14]=M=>i.value=M),"app-color-weak-mode":d.value,"onUpdate:appColorWeakMode":T[15]||(T[15]=M=>d.value=M)},null,8,["app-color-gray-mode","app-color-weak-mode"])]),_:1},8,["title"])]),layout:c(()=>[m(e(Te),{title:e(g)("preferences.layout")},{default:c(()=>[m(e(Wd),{modelValue:r.value,"onUpdate:modelValue":T[16]||(T[16]=M=>r.value=M)},null,8,["modelValue"])]),_:1},8,["title"]),m(e(Te),{title:e(g)("preferences.content")},{default:c(()=>[m(e(zd),{modelValue:f.value,"onUpdate:modelValue":T[17]||(T[17]=M=>f.value=M)},null,8,["modelValue"])]),_:1},8,["title"]),m(e(Te),{title:e(g)("preferences.sidebar.title")},{default:c(()=>[m(e(jd),{"sidebar-auto-activate-child":oe.value,"onUpdate:sidebarAutoActivateChild":T[18]||(T[18]=M=>oe.value=M),"sidebar-collapsed":N.value,"onUpdate:sidebarCollapsed":T[19]||(T[19]=M=>N.value=M),"sidebar-collapsed-show-title":J.value,"onUpdate:sidebarCollapsedShowTitle":T[20]||(T[20]=M=>J.value=M),"sidebar-enable":P.value,"onUpdate:sidebarEnable":T[21]||(T[21]=M=>P.value=M),"sidebar-expand-on-hover":de.value,"onUpdate:sidebarExpandOnHover":T[22]||(T[22]=M=>de.value=M),"sidebar-width":B.value,"onUpdate:sidebarWidth":T[23]||(T[23]=M=>B.value=M),"sidebar-collapsed-button":ge.value,"onUpdate:sidebarCollapsedButton":T[24]||(T[24]=M=>ge.value=M),"sidebar-fixed-button":G.value,"onUpdate:sidebarFixedButton":T[25]||(T[25]=M=>G.value=M),"current-layout":r.value,disabled:!e(Fl)},null,8,["sidebar-auto-activate-child","sidebar-collapsed","sidebar-collapsed-show-title","sidebar-enable","sidebar-expand-on-hover","sidebar-width","sidebar-collapsed-button","sidebar-fixed-button","current-layout","disabled"])]),_:1},8,["title"]),m(e(Te),{title:e(g)("preferences.header.title")},{default:c(()=>[m(e(Ud),{"header-enable":ee.value,"onUpdate:headerEnable":T[26]||(T[26]=M=>ee.value=M),"header-menu-align":Ce.value,"onUpdate:headerMenuAlign":T[27]||(T[27]=M=>Ce.value=M),"header-mode":he.value,"onUpdate:headerMode":T[28]||(T[28]=M=>he.value=M),disabled:e(Ot)},null,8,["header-enable","header-menu-align","header-mode","disabled"])]),_:1},8,["title"]),m(e(Te),{title:e(g)("preferences.navigationMenu.title")},{default:c(()=>[m(e(Rd),{"navigation-accordion":ie.value,"onUpdate:navigationAccordion":T[29]||(T[29]=M=>ie.value=M),"navigation-split":te.value,"onUpdate:navigationSplit":T[30]||(T[30]=M=>te.value=M),"navigation-style-type":D.value,"onUpdate:navigationStyleType":T[31]||(T[31]=M=>D.value=M),disabled:e(Ot),"disabled-navigation-split":!e(Sa)},null,8,["navigation-accordion","navigation-split","navigation-style-type","disabled","disabled-navigation-split"])]),_:1},8,["title"]),m(e(Te),{title:e(g)("preferences.breadcrumb.title")},{default:c(()=>[m(e(Xi),{"breadcrumb-enable":Be.value,"onUpdate:breadcrumbEnable":T[32]||(T[32]=M=>Be.value=M),"breadcrumb-hide-only-one":ne.value,"onUpdate:breadcrumbHideOnlyOne":T[33]||(T[33]=M=>ne.value=M),"breadcrumb-show-home":_e.value,"onUpdate:breadcrumbShowHome":T[34]||(T[34]=M=>_e.value=M),"breadcrumb-show-icon":Ae.value,"onUpdate:breadcrumbShowIcon":T[35]||(T[35]=M=>Ae.value=M),"breadcrumb-style-type":j.value,"onUpdate:breadcrumbStyleType":T[36]||(T[36]=M=>j.value=M),disabled:!Yl.value||!(e(Kl)||e(Nl)||e(Rl))},null,8,["breadcrumb-enable","breadcrumb-hide-only-one","breadcrumb-show-home","breadcrumb-show-icon","breadcrumb-style-type","disabled"])]),_:1},8,["title"]),m(e(Te),{title:e(g)("preferences.tabbar.title")},{default:c(()=>[m(e(Gd),{"tabbar-draggable":Ie.value,"onUpdate:tabbarDraggable":T[37]||(T[37]=M=>Ie.value=M),"tabbar-enable":le.value,"onUpdate:tabbarEnable":T[38]||(T[38]=M=>le.value=M),"tabbar-persist":De.value,"onUpdate:tabbarPersist":T[39]||(T[39]=M=>De.value=M),"tabbar-show-icon":ye.value,"onUpdate:tabbarShowIcon":T[40]||(T[40]=M=>ye.value=M),"tabbar-show-maximize":Ve.value,"onUpdate:tabbarShowMaximize":T[41]||(T[41]=M=>Ve.value=M),"tabbar-show-more":Se.value,"onUpdate:tabbarShowMore":T[42]||(T[42]=M=>Se.value=M),"tabbar-style-type":me.value,"onUpdate:tabbarStyleType":T[43]||(T[43]=M=>me.value=M),"tabbar-wheelable":re.value,"onUpdate:tabbarWheelable":T[44]||(T[44]=M=>re.value=M),"tabbar-max-count":q.value,"onUpdate:tabbarMaxCount":T[45]||(T[45]=M=>q.value=M),"tabbar-middle-click-to-close":we.value,"onUpdate:tabbarMiddleClickToClose":T[46]||(T[46]=M=>we.value=M)},null,8,["tabbar-draggable","tabbar-enable","tabbar-persist","tabbar-show-icon","tabbar-show-maximize","tabbar-show-more","tabbar-style-type","tabbar-wheelable","tabbar-max-count","tabbar-middle-click-to-close"])]),_:1},8,["title"]),m(e(Te),{title:e(g)("preferences.widget.title")},{default:c(()=>[m(e(qd),{"app-preferences-button-position":h.value,"onUpdate:appPreferencesButtonPosition":T[47]||(T[47]=M=>h.value=M),"widget-fullscreen":va.value,"onUpdate:widgetFullscreen":T[48]||(T[48]=M=>va.value=M),"widget-global-search":ba.value,"onUpdate:widgetGlobalSearch":T[49]||(T[49]=M=>ba.value=M),"widget-language-toggle":ga.value,"onUpdate:widgetLanguageToggle":T[50]||(T[50]=M=>ga.value=M),"widget-lock-screen":ka.value,"onUpdate:widgetLockScreen":T[51]||(T[51]=M=>ka.value=M),"widget-notification":ya.value,"onUpdate:widgetNotification":T[52]||(T[52]=M=>ya.value=M),"widget-refresh":Ca.value,"onUpdate:widgetRefresh":T[53]||(T[53]=M=>Ca.value=M),"widget-sidebar-toggle":xa.value,"onUpdate:widgetSidebarToggle":T[54]||(T[54]=M=>xa.value=M),"widget-theme-toggle":wa.value,"onUpdate:widgetThemeToggle":T[55]||(T[55]=M=>wa.value=M)},null,8,["app-preferences-button-position","widget-fullscreen","widget-global-search","widget-language-toggle","widget-lock-screen","widget-notification","widget-refresh","widget-sidebar-toggle","widget-theme-toggle"])]),_:1},8,["title"]),m(e(Te),{title:e(g)("preferences.footer.title")},{default:c(()=>[m(e(Id),{"footer-enable":We.value,"onUpdate:footerEnable":T[56]||(T[56]=M=>We.value=M),"footer-fixed":Re.value,"onUpdate:footerFixed":T[57]||(T[57]=M=>Re.value=M)},null,8,["footer-enable","footer-fixed"])]),_:1},8,["title"]),Ol.value?(u(),y(e(Te),{key:0,title:e(g)("preferences.copyright.title")},{default:c(()=>[m(e(Ad),{"copyright-company-name":ra.value,"onUpdate:copyrightCompanyName":T[58]||(T[58]=M=>ra.value=M),"copyright-company-site-link":ia.value,"onUpdate:copyrightCompanySiteLink":T[59]||(T[59]=M=>ia.value=M),"copyright-date":da.value,"onUpdate:copyrightDate":T[60]||(T[60]=M=>da.value=M),"copyright-enable":sa.value,"onUpdate:copyrightEnable":T[61]||(T[61]=M=>sa.value=M),"copyright-icp":ua.value,"onUpdate:copyrightIcp":T[62]||(T[62]=M=>ua.value=M),"copyright-icp-link":ca.value,"onUpdate:copyrightIcpLink":T[63]||(T[63]=M=>ca.value=M),disabled:!We.value},null,8,["copyright-company-name","copyright-company-site-link","copyright-date","copyright-enable","copyright-icp","copyright-icp-link","disabled"])]),_:1},8,["title"])):A("",!0)]),shortcutKey:c(()=>[m(e(Te),{title:e(g)("preferences.shortcutKeys.global")},{default:c(()=>[m(e(Yd),{"shortcut-keys-enable":pa.value,"onUpdate:shortcutKeysEnable":T[64]||(T[64]=M=>pa.value=M),"shortcut-keys-global-search":fa.value,"onUpdate:shortcutKeysGlobalSearch":T[65]||(T[65]=M=>fa.value=M),"shortcut-keys-lock-screen":ha.value,"onUpdate:shortcutKeysLockScreen":T[66]||(T[66]=M=>ha.value=M),"shortcut-keys-logout":ma.value,"onUpdate:shortcutKeysLogout":T[67]||(T[67]=M=>ma.value=M)},null,8,["shortcut-keys-enable","shortcut-keys-global-search","shortcut-keys-lock-screen","shortcut-keys-logout"])]),_:1},8,["title"])]),_:1},8,["modelValue","tabs"])])]),_:1},8,["description","title"])]))}}),$l=V({__name:"preferences",setup(l){const[a,t]=_l({connectedComponent:cu}),o=w(()=>{const n={};for(const[r,i]of Object.entries(I))for(const[d,f]of Object.entries(i))n[`${r}${La(d)}`]=f;return n}),s=w(()=>{const n={};for(const[r,i]of Object.entries(I))if(typeof i=="object")for(const d of Object.keys(i))n[`update:${r}${La(d)}`]=f=>{et({[r]:{[d]:f}}),r==="app"&&d==="locale"&&al(f)};else n[r]=i;return n});return(n,r)=>(u(),k("div",null,[m(e(a),fe(Q(Q({},n.$attrs),o.value),rl(s.value)),null,16),$("div",{onClick:r[0]||(r[0]=()=>e(t).open())},[E(n.$slots,"default",{},()=>[m(e(Pe),{title:e(g)("preferences.title"),class:"bg-primary flex-col-center size-10 cursor-pointer rounded-l-lg rounded-r-none border-none"},{default:c(()=>[m(e(yl),{class:"size-5"})]),_:1},8,["title"])])])]))}}),pu=V({__name:"preferences-button",emits:["clearPreferencesAndLogout"],setup(l,{emit:a}){const t=a;function o(){t("clearPreferencesAndLogout")}return(s,n)=>(u(),y($l,{onClearPreferencesAndLogout:o},{default:c(()=>[m(e(Ze),null,{default:c(()=>[m(e(yl),{class:"text-foreground size-4"})]),_:1})]),_:1}))}}),fu={class:"hover:bg-accent ml-1 mr-2 cursor-pointer rounded-full p-1.5"},mu={class:"hover:text-accent-foreground flex-center"},hu={class:"ml-2 w-full"},bu={key:0,class:"text-foreground mb-1 flex items-center text-sm font-medium"},vu={class:"text-muted-foreground text-xs font-normal"},Ac=V({name:"UserDropdown",__name:"user-dropdown",props:{avatar:{default:""},description:{default:""},enableShortcutKey:{type:Boolean,default:!0},menus:{default:()=>[]},tagText:{default:""},text:{default:""},trigger:{default:"click"},hoverDelay:{default:500}},emits:["logout"],setup(l,{emit:a}){const t=l,o=a,{globalLockScreenShortcutKey:s,globalLogoutShortcutKey:n}=ot(),r=st(),[i,d]=kt({connectedComponent:ui}),[f,p]=kt({onConfirm(){K()}}),v=za("refTrigger"),h=za("refContent"),[b,S]=Br([v,h],()=>t.hoverDelay);pe(()=>t.trigger==="hover"||t.trigger==="both",P=>{P?S.enable():S.disable()},{immediate:!0});const H=w(()=>wt()?"Alt":"⌥"),O=w(()=>t.enableShortcutKey&&n.value),C=w(()=>t.enableShortcutKey&&s.value),W=w(()=>t.enableShortcutKey&&I.shortcutKeys.enable);function F(){d.open()}function U(P){d.close(),r.lockScreen(P)}function L(){p.open(),b.value=!1}function K(){o("logout"),p.close()}if(W.value){const P=sl();Et(P["Alt+KeyQ"],()=>{O.value&&L()}),Et(P["Alt+KeyL"],()=>{C.value&&F()})}return(P,B)=>(u(),k(Z,null,[e(I).widget.lockScreen?(u(),y(e(i),{key:0,avatar:P.avatar,text:P.text,onSubmit:U},null,8,["avatar","text"])):A("",!0),m(e(f),{"cancel-text":e(g)("common.cancel"),"confirm-text":e(g)("common.confirm"),"fullscreen-button":!1,title:e(g)("common.prompt"),centered:"","content-class":"px-8 min-h-10","footer-class":"border-none mb-3 mr-3","header-class":"border-none"},{default:c(()=>[z(_(e(g)("ui.widgets.logoutTip")),1)]),_:1},8,["cancel-text","confirm-text","title"]),m(e(Zt),{open:e(b),"onUpdate:open":B[0]||(B[0]=N=>Xt(b)?b.value=N:null)},{default:c(()=>[m(e(Qt),{ref_key:"refTrigger",ref:v,disabled:t.trigger==="hover"},{default:c(()=>[$("div",fu,[$("div",mu,[m(e(xt),{alt:P.text,src:P.avatar,class:"size-8",dot:""},null,8,["alt","src"])])])]),_:1},8,["disabled"]),m(e(ea),{class:"mr-2 min-w-[240px] p-0 pb-1"},{default:c(()=>{var N;return[$("div",{ref_key:"refContent",ref:h},[m(e(Vs),{class:"flex items-center p-3"},{default:c(()=>[m(e(xt),{alt:P.text,src:P.avatar,class:"size-12",dot:"","dot-class":"bottom-0 right-1 border-2 size-4 bg-green-500"},null,8,["alt","src"]),$("div",hu,[P.tagText||P.text||P.$slots.tagText?(u(),k("div",bu,[z(_(P.text)+" ",1),E(P.$slots,"tagText",{},()=>[P.tagText?(u(),y(e(Cs),{key:0,class:"ml-2 text-green-400"},{default:c(()=>[z(_(P.tagText),1)]),_:1})):A("",!0)])])):A("",!0),$("div",vu,_(P.description),1)])]),_:3}),(N=P.menus)!=null&&N.length?(u(),y(e($t),{key:0})):A("",!0),(u(!0),k(Z,null,ue(P.menus,J=>(u(),y(e(gt),{key:J.text,class:"mx-1 flex cursor-pointer items-center rounded-sm py-1 leading-8",onClick:J.handler},{default:c(()=>[m(e(Ue),{icon:J.icon,class:"mr-2 size-4"},null,8,["icon"]),z(" "+_(J.text),1)]),_:2},1032,["onClick"]))),128)),m(e($t)),e(I).widget.lockScreen?(u(),y(e(gt),{key:1,class:"mx-1 flex cursor-pointer items-center rounded-sm py-1 leading-8",onClick:F},{default:c(()=>[m(e(bl),{class:"mr-2 size-4"}),z(" "+_(e(g)("ui.widgets.lockScreen.title"))+" ",1),C.value?(u(),y(e(Oa),{key:0},{default:c(()=>[z(_(H.value)+" L ",1)]),_:1})):A("",!0)]),_:1})):A("",!0),e(I).widget.lockScreen?(u(),y(e($t),{key:2})):A("",!0),m(e(gt),{class:"mx-1 flex cursor-pointer items-center rounded-sm py-1 leading-8",onClick:L},{default:c(()=>[m(e(hs),{class:"mr-2 size-4"}),z(" "+_(e(g)("common.logout"))+" ",1),O.value?(u(),y(e(Oa),{key:0},{default:c(()=>[z(_(H.value)+" Q ",1)]),_:1})):A("",!0)]),_:1})],512)]}),_:3})]),_:3},8,["open"])],64))}}),gu=V({__name:"layout-content",props:{contentCompact:{},contentCompactWidth:{},padding:{},paddingBottom:{},paddingLeft:{},paddingRight:{},paddingTop:{}},setup(l){const a=l,{contentElement:t,overlayStyle:o}=Jo(),s=w(()=>{const{contentCompact:n,padding:r,paddingBottom:i,paddingLeft:d,paddingRight:f,paddingTop:p}=a,v=n==="compact"?{margin:"0 auto",width:`${a.contentCompactWidth}px`}:{};return xe(Q({},v),{flex:1,padding:`${r}px`,paddingBottom:`${i}px`,paddingLeft:`${d}px`,paddingRight:`${f}px`,paddingTop:`${p}px`})});return(n,r)=>(u(),k("main",{ref_key:"contentElement",ref:t,style:ce(s.value),class:"bg-background-deep relative"},[m(e(Xo),{style:ce(e(o))},{default:c(()=>[E(n.$slots,"overlay")]),_:3},8,["style"]),E(n.$slots,"default")],4))}}),yu=V({__name:"layout-footer",props:{fixed:{type:Boolean},height:{},show:{type:Boolean,default:!0},width:{},zIndex:{}},setup(l){const a=l,t=w(()=>{const{fixed:o,height:s,show:n,width:r,zIndex:i}=a;return{height:`${s}px`,marginBottom:n?"0":`-${s}px`,position:o?"fixed":"static",width:r,zIndex:i}});return(o,s)=>(u(),k("footer",{style:ce(t.value),class:"bg-background-deep bottom-0 w-full transition-all duration-200"},[E(o.$slots,"default")],4))}}),wu=V({__name:"layout-header",props:{fullWidth:{type:Boolean},height:{},isMobile:{type:Boolean},show:{type:Boolean},sidebarWidth:{},theme:{},width:{},zIndex:{}},setup(l){const a=l,t=Ne(),o=w(()=>{const{fullWidth:n,height:r,show:i}=a,d=!i||!n?void 0:0;return{height:`${r}px`,marginTop:i?0:`-${r}px`,right:d}}),s=w(()=>({minWidth:`${a.isMobile?40:a.sidebarWidth}px`}));return(n,r)=>(u(),k("header",{class:R([n.theme,"border-border bg-header top-0 flex w-full flex-[0_0_auto] items-center border-b pl-2 transition-[margin-top] duration-200"]),style:ce(o.value)},[e(t).logo?(u(),k("div",{key:0,style:ce(s.value)},[E(n.$slots,"logo")],4)):A("",!0),E(n.$slots,"toggle-button"),E(n.$slots,"default")],6))}}),Fa=V({__name:"sidebar-collapse-button",props:{collapsed:{type:Boolean},collapsedModifiers:{}},emits:["update:collapsed"],setup(l){const a=x(l,"collapsed");function t(){a.value=!a.value}return(o,s)=>(u(),k("div",{class:"flex-center hover:text-foreground text-foreground/60 hover:bg-accent-hover bg-accent absolute bottom-2 left-3 z-10 cursor-pointer rounded-sm p-1",onClick:Me(t,["stop"])},[a.value?(u(),y(e(Zo),{key:0,class:"size-4"})):(u(),y(e(Qo),{key:1,class:"size-4"}))]))}}),Ka=V({__name:"sidebar-fixed-button",props:{expandOnHover:{type:Boolean},expandOnHoverModifiers:{}},emits:["update:expandOnHover"],setup(l){const a=x(l,"expandOnHover");function t(){a.value=!a.value}return(o,s)=>(u(),k("div",{class:"flex-center hover:text-foreground text-foreground/60 hover:bg-accent-hover bg-accent absolute bottom-2 right-3 z-10 cursor-pointer rounded-sm p-[5px] transition-all duration-300",onClick:t},[a.value?(u(),y(e(Ut),{key:1,class:"size-3.5"})):(u(),y(e(gl),{key:0,class:"size-3.5"}))]))}}),xu=V({__name:"layout-sidebar",props:ve({collapseHeight:{default:42},collapseWidth:{default:48},domVisible:{type:Boolean,default:!0},extraWidth:{},fixedExtra:{type:Boolean,default:!1},headerHeight:{},isSidebarMixed:{type:Boolean,default:!1},marginTop:{default:0},mixedWidth:{default:70},paddingTop:{default:0},show:{type:Boolean,default:!0},showCollapseButton:{type:Boolean,default:!0},showFixedButton:{type:Boolean,default:!0},theme:{},width:{},zIndex:{default:0}},{collapse:{type:Boolean},collapseModifiers:{},extraCollapse:{type:Boolean},extraCollapseModifiers:{},expandOnHovering:{type:Boolean},expandOnHoveringModifiers:{},expandOnHover:{type:Boolean},expandOnHoverModifiers:{},extraVisible:{type:Boolean},extraVisibleModifiers:{}}),emits:ve(["leave"],["update:collapse","update:extraCollapse","update:expandOnHovering","update:expandOnHover","update:extraVisible"]),setup(l,{emit:a}){const t=l,o=a,s=x(l,"collapse"),n=x(l,"extraCollapse"),r=x(l,"expandOnHovering"),i=x(l,"expandOnHover"),d=x(l,"extraVisible"),f=En(document.body),p=Ne(),v=Vt(),h=w(()=>L(!0)),b=w(()=>{const{isSidebarMixed:B,marginTop:N,paddingTop:J,zIndex:oe}=t;return Q(xe(Q({"--scroll-shadow":"var(--sidebar)"},L(!1)),{height:`calc(100% - ${N}px)`,marginTop:`${N}px`,paddingTop:`${J}px`,zIndex:oe}),B&&d.value?{transition:"none"}:{})}),S=w(()=>{const{extraWidth:B,show:N,width:J,zIndex:oe}=t;return{left:`${J}px`,width:d.value&&N?`${B}px`:0,zIndex:oe}}),H=w(()=>{const{headerHeight:B}=t;return{height:`${B-1}px`}}),O=w(()=>{const{collapseWidth:B,fixedExtra:N,isSidebarMixed:J,mixedWidth:oe}=t;return J&&N?{width:`${s.value?B:oe}px`}:{}}),C=w(()=>{const{collapseHeight:B,headerHeight:N}=t;return Q({height:`calc(100% - ${N+B}px)`,paddingTop:"8px"},O.value)}),W=w(()=>{const{headerHeight:B,isSidebarMixed:N}=t;return Q(xe(Q({},N?{display:"flex",justifyContent:"center"}:{}),{height:`${B-1}px`}),O.value)}),F=w(()=>{const{collapseHeight:B,headerHeight:N}=t;return{height:`calc(100% - ${N+B}px)`}}),U=w(()=>({height:`${t.collapseHeight}px`}));il(()=>{d.value=t.fixedExtra?!0:d.value});function L(B){const{extraWidth:N,fixedExtra:J,isSidebarMixed:oe,show:de,width:ge}=t;let G=ge===0?"0px":`${ge+(oe&&J&&d.value?N:0)}px`;const{collapseWidth:ee}=t;return B&&r.value&&!i.value&&(G=`${ee}px`),xe(Q({},G==="0px"?{overflow:"hidden"}:{}),{flex:`0 0 ${G}`,marginLeft:de?0:`-${G}`,maxWidth:G,minWidth:G,width:G})}function K(B){(B==null?void 0:B.offsetX)<10||i.value||(r.value||(s.value=!1),t.isSidebarMixed&&(f.value=!0),r.value=!0)}function P(){o("leave"),t.isSidebarMixed&&(f.value=!1),!i.value&&(r.value=!1,s.value=!0,d.value=!1)}return(B,N)=>(u(),k(Z,null,[B.domVisible?(u(),k("div",{key:0,class:R([B.theme,"h-full transition-all duration-150"]),style:ce(h.value)},null,6)):A("",!0),$("aside",{class:R([[B.theme,{"bg-sidebar-deep":B.isSidebarMixed,"bg-sidebar border-border border-r":!B.isSidebarMixed}],"fixed left-0 top-0 h-full transition-all duration-150"]),style:ce(b.value),onMouseenter:K,onMouseleave:P},[!s.value&&!B.isSidebarMixed&&B.showFixedButton?(u(),y(e(Ka),{key:0,"expand-on-hover":i.value,"onUpdate:expandOnHover":N[0]||(N[0]=J=>i.value=J)},null,8,["expand-on-hover"])):A("",!0),e(p).logo?(u(),k("div",{key:1,style:ce(W.value)},[E(B.$slots,"logo")],4)):A("",!0),m(e(Ct),{style:ce(C.value),shadow:"","shadow-border":""},{default:c(()=>[E(B.$slots,"default")]),_:3},8,["style"]),$("div",{style:ce(U.value)},null,4),B.showCollapseButton&&!B.isSidebarMixed?(u(),y(e(Fa),{key:2,collapsed:s.value,"onUpdate:collapsed":N[1]||(N[1]=J=>s.value=J)},null,8,["collapsed"])):A("",!0),B.isSidebarMixed?(u(),k("div",{key:3,ref_key:"asideRef",ref:v,class:R([{"border-l":d.value},"border-border bg-sidebar fixed top-0 h-full overflow-hidden border-r transition-all duration-200"]),style:ce(S.value)},[B.isSidebarMixed&&i.value?(u(),y(e(Fa),{key:0,collapsed:n.value,"onUpdate:collapsed":N[2]||(N[2]=J=>n.value=J)},null,8,["collapsed"])):A("",!0),n.value?A("",!0):(u(),y(e(Ka),{key:1,"expand-on-hover":i.value,"onUpdate:expandOnHover":N[3]||(N[3]=J=>i.value=J)},null,8,["expand-on-hover"])),n.value?A("",!0):(u(),k("div",{key:2,style:ce(H.value),class:"pl-2"},[E(B.$slots,"extra-title")],4)),m(e(Ct),{style:ce(F.value),class:"border-border py-2",shadow:"","shadow-border":""},{default:c(()=>[E(B.$slots,"extra")]),_:3},8,["style"])],6)):A("",!0)],38)],64))}}),ku=V({__name:"layout-tabbar",props:{height:{}},setup(l){const a=l,t=w(()=>{const{height:o}=a;return{height:`${o}px`}});return(o,s)=>(u(),k("section",{style:ce(t.value),class:"border-border bg-background flex w-full border-b transition-all"},[E(o.$slots,"default")],4))}});function Cu(l){const a=w(()=>l.isMobile?"sidebar-nav":l.layout),t=w(()=>a.value==="full-content"),o=w(()=>a.value==="sidebar-mixed-nav"),s=w(()=>a.value==="header-nav"),n=w(()=>a.value==="mixed-nav"||a.value==="header-sidebar-nav"),r=w(()=>a.value==="header-mixed-nav");return{currentLayout:a,isFullContent:t,isHeaderMixedNav:r,isHeaderNav:s,isMixedNav:n,isSidebarMixedNav:o}}const Su={class:"relative flex min-h-full w-full"},_u=V({name:"VbenLayout",__name:"vben-layout",props:ve({contentCompact:{default:"wide"},contentCompactWidth:{default:1200},contentPadding:{default:0},contentPaddingBottom:{default:0},contentPaddingLeft:{default:0},contentPaddingRight:{default:0},contentPaddingTop:{default:0},footerEnable:{type:Boolean,default:!1},footerFixed:{type:Boolean,default:!0},footerHeight:{default:32},headerHeight:{default:50},headerHidden:{type:Boolean,default:!1},headerMode:{default:"fixed"},headerTheme:{},headerToggleSidebarButton:{type:Boolean,default:!0},headerVisible:{type:Boolean,default:!0},isMobile:{type:Boolean,default:!1},layout:{default:"sidebar-nav"},sidebarCollapse:{type:Boolean},sidebarCollapsedButton:{type:Boolean,default:!0},sidebarCollapseShowTitle:{type:Boolean,default:!1},sidebarEnable:{type:Boolean},sidebarExtraCollapsedWidth:{default:60},sidebarFixedButton:{type:Boolean,default:!0},sidebarHidden:{type:Boolean,default:!1},sidebarMixedWidth:{default:80},sidebarTheme:{default:"dark"},sidebarWidth:{default:180},sideCollapseWidth:{default:60},tabbarEnable:{type:Boolean,default:!0},tabbarHeight:{default:40},zIndex:{default:200}},{sidebarCollapse:{type:Boolean,default:!1},sidebarCollapseModifiers:{},sidebarExtraVisible:{type:Boolean},sidebarExtraVisibleModifiers:{},sidebarExtraCollapse:{type:Boolean,default:!1},sidebarExtraCollapseModifiers:{},sidebarExpandOnHover:{type:Boolean,default:!1},sidebarExpandOnHoverModifiers:{},sidebarEnable:{type:Boolean,default:!0},sidebarEnableModifiers:{}}),emits:ve(["sideMouseLeave","toggleSidebar"],["update:sidebarCollapse","update:sidebarExtraVisible","update:sidebarExtraCollapse","update:sidebarExpandOnHover","update:sidebarEnable"]),setup(l,{emit:a}){const t=l,o=a,s=x(l,"sidebarCollapse"),n=x(l,"sidebarExtraVisible"),r=x(l,"sidebarExtraCollapse"),i=x(l,"sidebarExpandOnHover"),d=x(l,"sidebarEnable"),f=X(!1),p=X(!1),v=X(),{arrivedState:h,directions:b,isScrolling:S,y:H}=Ln(document),{setLayoutHeaderHeight:O}=en(),{setLayoutFooterHeight:C}=tn(),{y:W}=zn({target:v,type:"client"}),{currentLayout:F,isFullContent:U,isHeaderMixedNav:L,isHeaderNav:K,isMixedNav:P,isSidebarMixedNav:B}=Cu(t),N=w(()=>t.headerMode==="auto"),J=w(()=>{let D=0;return t.headerVisible&&!t.headerHidden&&(D+=t.headerHeight),t.tabbarEnable&&(D+=t.tabbarHeight),D}),oe=w(()=>{const{sidebarCollapseShowTitle:D,sidebarMixedWidth:te,sideCollapseWidth:ie}=t;return D||B.value||L.value?te:ie}),de=w(()=>!K.value&&d.value),ge=w(()=>{const{headerHeight:D,isMobile:te}=t;return P.value&&!te?D:0}),G=w(()=>{const{isMobile:D,sidebarHidden:te,sidebarMixedWidth:ie,sidebarWidth:We}=t;let Re=0;return te||!de.value||te&&!B.value&&!P.value&&!L.value||((L.value||B.value)&&!D?Re=ie:s.value?Re=D?0:oe.value:Re=We),Re}),ee=w(()=>{const{sidebarExtraCollapsedWidth:D,sidebarWidth:te}=t;return r.value?D:te}),he=w(()=>F.value==="mixed-nav"||F.value==="sidebar-mixed-nav"||F.value==="sidebar-nav"||F.value==="header-mixed-nav"||F.value==="header-sidebar-nav"),Ce=w(()=>{const{headerMode:D}=t;return P.value||D==="fixed"||D==="auto-scroll"||D==="auto"}),Be=w(()=>he.value&&d.value&&!t.sidebarHidden),Ae=w(()=>!s.value&&t.isMobile),_e=w(()=>{let D="100%",te="unset";if(Ce.value&&F.value!=="header-nav"&&F.value!=="mixed-nav"&&F.value!=="header-sidebar-nav"&&Be.value&&!t.isMobile)if((B.value||L.value)&&i.value&&n.value){const We=s.value?oe.value:t.sidebarMixedWidth,Re=r.value?t.sidebarExtraCollapsedWidth:t.sidebarWidth;te=`${We+Re}px`,D=`calc(100% - ${te})`}else te=f.value&&!i.value?`${oe.value}px`:`${G.value}px`,D=`calc(100% - ${te})`;return{sidebarAndExtraWidth:te,width:D}}),j=w(()=>{let D="",te=0;if(!P.value||t.sidebarHidden)D="100%";else if(d.value){const ie=i.value?t.sidebarWidth:oe.value;te=s.value?oe.value:ie,D=`calc(100% - ${s.value?G.value:ie}px)`}else D="100%";return{marginLeft:`${te}px`,width:D}}),ne=w(()=>{const D=Ce.value,{footerEnable:te,footerFixed:ie,footerHeight:We}=t;return{marginTop:D&&!U.value&&!p.value&&(!N.value||H.value<J.value)?`${J.value}px`:0,paddingBottom:`${te&&ie?We:0}px`}}),le=w(()=>{const{zIndex:D}=t,te=P.value?1:0;return D+te}),ye=w(()=>{const D=Ce.value;return{height:U.value?"0":`${J.value}px`,left:P.value?0:_e.value.sidebarAndExtraWidth,position:D?"fixed":"static",top:p.value||U.value?`-${J.value}px`:0,width:_e.value.width,"z-index":le.value}}),Se=w(()=>{const{isMobile:D,zIndex:te}=t;let ie=D||he.value?1:-1;return P.value&&(ie+=1),te+ie}),Ve=w(()=>t.footerFixed?_e.value.width:"100%"),De=w(()=>({zIndex:t.zIndex})),Ie=w(()=>t.isMobile||t.headerToggleSidebarButton&&he.value&&!B.value&&!P.value&&!t.isMobile),re=w(()=>!he.value||P.value||t.isMobile);pe(()=>t.isMobile,D=>{D&&(s.value=!0)},{immediate:!0}),pe([()=>J.value,()=>U.value],([D])=>{O(U.value?0:D)},{immediate:!0}),pe(()=>t.footerHeight,D=>{C(D)},{immediate:!0});{const D=()=>{W.value>J.value?p.value=!0:p.value=!1};pe([()=>t.headerMode,()=>W.value],()=>{if(!N.value||P.value||U.value){t.headerMode!=="auto-scroll"&&(p.value=!1);return}p.value=!0,D()},{immediate:!0})}{const D=Pt((te,ie,We)=>{if(H.value<J.value){p.value=!1;return}if(We){p.value=!1;return}te?p.value=!1:ie&&(p.value=!0)},300);pe(()=>H.value,()=>{t.headerMode!=="auto-scroll"||P.value||U.value||S.value&&D(b.top,b.bottom,h.top)})}function me(){s.value=!0}function q(){t.isMobile?s.value=!1:o("toggleSidebar")}const we=Za;return(D,te)=>(u(),k("div",Su,[de.value?(u(),y(e(xu),{key:0,collapse:s.value,"onUpdate:collapse":te[0]||(te[0]=ie=>s.value=ie),"expand-on-hover":i.value,"onUpdate:expandOnHover":te[1]||(te[1]=ie=>i.value=ie),"expand-on-hovering":f.value,"onUpdate:expandOnHovering":te[2]||(te[2]=ie=>f.value=ie),"extra-collapse":r.value,"onUpdate:extraCollapse":te[3]||(te[3]=ie=>r.value=ie),"extra-visible":n.value,"onUpdate:extraVisible":te[4]||(te[4]=ie=>n.value=ie),"show-collapse-button":D.sidebarCollapsedButton,"show-fixed-button":D.sidebarFixedButton,"collapse-width":oe.value,"dom-visible":!D.isMobile,"extra-width":ee.value,"fixed-extra":i.value,"header-height":e(P)?0:D.headerHeight,"is-sidebar-mixed":e(B)||e(L),"margin-top":ge.value,"mixed-width":D.sidebarMixedWidth,show:Be.value,theme:D.sidebarTheme,width:G.value,"z-index":Se.value,onLeave:te[5]||(te[5]=()=>o("sideMouseLeave"))},ut({extra:c(()=>[E(D.$slots,"side-extra")]),"extra-title":c(()=>[E(D.$slots,"side-extra-title")]),default:c(()=>[e(B)||e(L)?E(D.$slots,"mixed-menu",{key:0}):E(D.$slots,"menu",{key:1})]),_:2},[he.value&&!e(P)?{name:"logo",fn:c(()=>[E(D.$slots,"logo")]),key:"0"}:void 0]),1032,["collapse","expand-on-hover","expand-on-hovering","extra-collapse","extra-visible","show-collapse-button","show-fixed-button","collapse-width","dom-visible","extra-width","fixed-extra","header-height","is-sidebar-mixed","margin-top","mixed-width","show","theme","width","z-index"])):A("",!0),$("div",{ref_key:"contentRef",ref:v,class:"flex flex-1 flex-col overflow-hidden transition-all duration-300 ease-in"},[$("div",{class:R([[{"shadow-[0_16px_24px_hsl(var(--background))]":e(H)>20},e(an)],"overflow-hidden transition-all duration-200"]),style:ce(ye.value)},[D.headerVisible?(u(),y(e(wu),{key:0,"full-width":!he.value,height:D.headerHeight,"is-mobile":D.isMobile,show:!e(U)&&!D.headerHidden,"sidebar-width":D.sidebarWidth,theme:D.headerTheme,width:_e.value.width,"z-index":le.value},ut({"toggle-button":c(()=>[Ie.value?(u(),y(e(Ze),{key:0,class:"my-0 mr-1 rounded-md",onClick:q},{default:c(()=>[m(e(ln),{class:"size-4"})]),_:1})):A("",!0)]),default:c(()=>[E(D.$slots,"header")]),_:2},[re.value?{name:"logo",fn:c(()=>[E(D.$slots,"logo")]),key:"0"}:void 0]),1032,["full-width","height","is-mobile","show","sidebar-width","theme","width","z-index"])):A("",!0),D.tabbarEnable?(u(),y(e(ku),{key:1,height:D.tabbarHeight,style:ce(j.value)},{default:c(()=>[E(D.$slots,"tabbar")]),_:3},8,["height","style"])):A("",!0)],6),m(e(gu),{id:e(we),"content-compact":D.contentCompact,"content-compact-width":D.contentCompactWidth,padding:D.contentPadding,"padding-bottom":D.contentPaddingBottom,"padding-left":D.contentPaddingLeft,"padding-right":D.contentPaddingRight,"padding-top":D.contentPaddingTop,style:ce(ne.value),class:"transition-[margin-top] duration-200"},{overlay:c(()=>[E(D.$slots,"content-overlay")]),default:c(()=>[E(D.$slots,"content")]),_:3},8,["id","content-compact","content-compact-width","padding","padding-bottom","padding-left","padding-right","padding-top","style"]),D.footerEnable?(u(),y(e(yu),{key:0,fixed:D.footerFixed,height:D.footerHeight,show:!e(U),width:Ve.value,"z-index":D.zIndex},{default:c(()=>[E(D.$slots,"footer")]),_:3},8,["fixed","height","show","width","z-index"])):A("",!0)],512),E(D.$slots,"extra"),Ae.value?(u(),k("div",{key:1,style:ce(De.value),class:"bg-overlay fixed left-0 top-0 h-full w-full transition-[background-color] duration-200",onClick:me},null,4)):A("",!0)]))}});function Tu(){const l=X(!1),a=X(0),t=nt(),o=500,s=w(()=>I.transition.loading),n=()=>{if(!s.value)return;const r=performance.now()-a.value;r<o?setTimeout(()=>{l.value=!1},o-r):l.value=!1};return t.beforeEach(r=>(r.meta.loaded||!s.value||r.meta.iframeSrc||(a.value=performance.now(),l.value=!0),!0)),t.afterEach(r=>(r.meta.loaded||!s.value||r.meta.iframeSrc||n(),!0)),{spinning:l}}const Mu=V({name:"LayoutContentSpinner",__name:"content-spinner",setup(l){const{spinning:a}=Tu();return(t,o)=>(u(),y(e(ll),{spinning:e(a)},null,8,["spinning"]))}}),$u={key:0,class:"relative size-full"},Bu=["src","onLoad"],Vu=V({name:"IFrameRouterView",__name:"iframe-router-view",setup(l){const a=X([]),t=mt(),o=Ge(),s=w(()=>I.tabbar.enable),n=w(()=>s.value?t.getTabs.filter(h=>{var b;return!!((b=h.meta)!=null&&b.iframeSrc)}):o.meta.iframeSrc?[o]:[]),r=w(()=>new Set(n.value.map(h=>h.name))),i=w(()=>n.value.length>0);function d(h){return h.name===o.name}function f(h){const{meta:b,name:S}=h;return!S||!t.renderRouteView?!1:s.value?!(b!=null&&b.keepAlive)&&r.value.has(S)&&S!==o.name?!1:t.getTabs.some(H=>H.name===S):d(h)}function p(h){a.value[h]=!1}function v(h){const b=a.value[h];return b===void 0?!0:b}return(h,b)=>i.value?(u(!0),k(Z,{key:0},ue(n.value,(S,H)=>(u(),k(Z,{key:S.fullPath},[f(S)?Ee((u(),k("div",$u,[m(e(ll),{spinning:v(H)},null,8,["spinning"]),$("iframe",{src:S.meta.iframeSrc,class:"size-full",onLoad:O=>p(H)},null,40,Bu)],512)),[[ze,d(S)]]):A("",!0)],64))),128)):A("",!0)}}),Eu={class:"relative h-full"},Lu=V({name:"LayoutContent",__name:"content",setup(l){const a=mt(),{keepAlive:t}=ot(),{getCachedTabs:o,getExcludeCachedTabs:s,renderRouteView:n}=tl(a),r=w(()=>{const{transition:f}=I;return f.name&&f.enable});function i(f){const{tabbar:p,transition:v}=I,h=v.name;if(!(!h||!v.enable))return!p.enable||!t,h}function d(f,p){var b;if(!f){console.error("Component view not found，please check the route configuration");return}const v=p.name;if(!v)return f;const h=(b=f==null?void 0:f.type)==null?void 0:b.name;return h||h===v||(f.type||(f.type={}),f.type.name=v),f}return(f,p)=>(u(),k("div",Eu,[m(e(Vu)),m(e(on),null,{default:c(({Component:v,route:h})=>[r.value?(u(),y(at,{key:0,name:i(h),appear:"",mode:"out-in"},{default:c(()=>[e(t)?(u(),y(Pa,{key:0,exclude:e(s),include:e(o)},[e(n)?Ee((u(),y(Le(d(v,h)),{key:e(Ke)(h)})),[[ze,!h.meta.iframeSrc]]):A("",!0)],1032,["exclude","include"])):e(n)?(u(),y(Le(v),{key:e(Ke)(h)})):A("",!0)]),_:2},1032,["name"])):(u(),k(Z,{key:1},[e(t)?(u(),y(Pa,{key:0,exclude:e(s),include:e(o)},[e(n)?Ee((u(),y(Le(d(v,h)),{key:e(Ke)(h)})),[[ze,!h.meta.iframeSrc]]):A("",!0)],1032,["exclude","include"])):e(n)?(u(),y(Le(v),{key:e(Ke)(h)})):A("",!0)],64))]),_:1})]))}}),zu={class:"flex-center text-muted-foreground relative h-full w-full text-xs"},Pu=V({name:"LayoutFooter",__name:"footer",setup(l){return(a,t)=>(u(),k("div",zu,[E(a.$slots,"default")]))}}),Au={class:"flex-center hidden lg:block"},Iu={class:"flex h-full min-w-0 flex-shrink-0 items-center"},Xe=50,Uu=V({name:"LayoutHeader",__name:"header",props:{theme:{default:"light"}},emits:["clearPreferencesAndLogout"],setup(l,{emit:a}){const t=a,o=st(),{globalSearchShortcutKey:s,preferencesButtonPosition:n}=ot(),r=Ne(),{refresh:i}=Cl(),d=w(()=>{const v=[{index:Xe+100,name:"user-dropdown"}];return I.widget.globalSearch&&v.push({index:Xe,name:"global-search"}),n.value.header&&v.push({index:Xe+10,name:"preferences"}),I.widget.themeToggle&&v.push({index:Xe+20,name:"theme-toggle"}),I.widget.languageToggle&&v.push({index:Xe+30,name:"language-toggle"}),I.widget.fullscreen&&v.push({index:Xe+40,name:"fullscreen"}),I.widget.notification&&v.push({index:Xe+50,name:"notification"}),Object.keys(r).forEach(h=>{const b=h.split("-");h.startsWith("header-right")&&v.push({index:Number(b[2]),name:h})}),v.sort((h,b)=>h.index-b.index)}),f=w(()=>{const v=[];return I.widget.refresh&&v.push({index:0,name:"refresh"}),Object.keys(r).forEach(h=>{const b=h.split("-");h.startsWith("header-left")&&v.push({index:Number(b[2]),name:h})}),v.sort((h,b)=>h.index-b.index)});function p(){t("clearPreferencesAndLogout")}return(v,h)=>(u(),k(Z,null,[(u(!0),k(Z,null,ue(f.value.filter(b=>b.index<Xe),b=>E(v.$slots,b.name,{key:b.name},()=>[b.name==="refresh"?(u(),y(e(Ze),{key:0,class:"my-0 mr-1 rounded-md",onClick:e(i)},{default:c(()=>[m(e(ta),{class:"size-4"})]),_:1},8,["onClick"])):A("",!0)],!0)),128)),$("div",Au,[E(v.$slots,"breadcrumb",{},void 0,!0)]),(u(!0),k(Z,null,ue(f.value.filter(b=>b.index>Xe),b=>E(v.$slots,b.name,{key:b.name},void 0,!0)),128)),$("div",{class:R([`menu-align-${e(I).header.menuAlign}`,"flex h-full min-w-0 flex-1 items-center"])},[E(v.$slots,"menu",{},void 0,!0)],2),$("div",Iu,[(u(!0),k(Z,null,ue(d.value,b=>E(v.$slots,b.name,{key:b.name},()=>[b.name==="global-search"?(u(),y(e(ni),{key:0,"enable-shortcut-key":e(s),menus:e(o).accessMenus,class:"mr-1 sm:mr-4"},null,8,["enable-shortcut-key","menus"])):b.name==="preferences"?(u(),y(e(pu),{key:1,class:"mr-1",onClearPreferencesAndLogout:p})):b.name==="theme-toggle"?(u(),y(e(Kn),{key:2,class:"mr-1 mt-[2px]"})):b.name==="language-toggle"?(u(),y(e(jn),{key:3,class:"mr-1"})):b.name==="fullscreen"?(u(),y(e(wr),{key:4,class:"mr-1"})):A("",!0)],!0)),128))])],64))}}),Hu=$e(Uu,[["__scopeId","data-v-7670467e"]]),Ou={class:"relative mr-1 flex size-1.5"},Du=V({__name:"menu-badge-dot",props:{dotClass:{default:""},dotStyle:{default:()=>({})}},setup(l){return(a,t)=>(u(),k("span",Ou,[$("span",{class:R([a.dotClass,"absolute inline-flex h-full w-full animate-ping rounded-full opacity-75"]),style:ce(a.dotStyle)},null,6),$("span",{class:R([a.dotClass,"relative inline-flex size-1.5 rounded-full"]),style:ce(a.dotStyle)},null,6)]))}}),Bl=V({__name:"menu-badge",props:{hasChildren:{type:Boolean},badge:{},badgeType:{},badgeVariants:{}},setup(l){const a=l,t={default:"bg-green-500",destructive:"bg-destructive",primary:"bg-primary",success:"bg-green-500",warning:"bg-yellow-500"},o=w(()=>a.badgeType==="dot"),s=w(()=>{const{badgeVariants:r}=a;return r?t[r]||r:t.default}),n=w(()=>s.value&&Pn(s.value)?{backgroundColor:s.value}:{});return(r,i)=>o.value||r.badge?(u(),k("span",{key:0,class:R([r.$attrs.class,"absolute"])},[o.value?(u(),y(Du,{key:0,"dot-class":s.value,"dot-style":n.value},null,8,["dot-class","dot-style"])):(u(),k("div",{key:1,class:R([s.value,"text-primary-foreground flex-center rounded-xl px-1.5 py-0.5 text-[10px]"]),style:ce(n.value)},_(r.badge),7))],2)):A("",!0)}}),Wu=["onClick","onMouseenter"],Ru=V({name:"NormalMenu",__name:"normal-menu",props:{activePath:{default:""},collapse:{type:Boolean,default:!1},menus:{default:()=>[]},rounded:{type:Boolean},theme:{default:"dark"}},emits:["enter","select"],setup(l,{emit:a}){const t=l,o=a,{b:s,e:n,is:r}=tt("normal-menu");function i(d){return t.activePath===d.path&&d.activeIcon||d.icon}return(d,f)=>(u(),k("ul",{class:R([[d.theme,e(s)(),e(r)("collapse",d.collapse),e(r)(d.theme,!0),e(r)("rounded",d.rounded)],"relative"])},[(u(!0),k(Z,null,ue(d.menus,p=>(u(),k("li",{key:p.path,class:R([e(n)("item"),e(r)("active",d.activePath===p.path)]),onClick:()=>o("select",p),onMouseenter:()=>o("enter",p)},[m(e(Ue),{class:R(e(n)("icon")),icon:i(p),fallback:""},null,8,["class","icon"]),$("span",{class:R([e(n)("name"),"truncate"])},_(p.name),3)],42,Wu))),128))],2))}}),Nu=$e(Ru,[["__scopeId","data-v-3ebda870"]]);function Vl(l,a){var o,s;let t=l.parent;for(;t&&!a.includes((s=(o=t==null?void 0:t.type)==null?void 0:o.name)!=null?s:"");)t=t.parent;return t}const Bt=l=>{const a=Array.isArray(l)?l:[l],t=[];return a.forEach(o=>{var s;Array.isArray(o)?t.push(...Bt(o)):Aa(o)&&Array.isArray(o.children)?t.push(...Bt(o.children)):(t.push(o),Aa(o)&&((s=o.component)!=null&&s.subTree)&&t.push(...Bt(o.component.subTree)))}),t};function El(){const l=It();if(!l)throw new Error("instance is required");const a=w(()=>{var n;let o=l.parent;const s=[l.props.path];for(;(o==null?void 0:o.type.name)!=="Menu";)o!=null&&o.props.path&&s.unshift(o.props.path),o=(n=o==null?void 0:o.parent)!=null?n:null;return s});return{parentMenu:w(()=>Vl(l,["Menu","SubMenu"])),parentPaths:a}}function Ll(l){return w(()=>{var t;return{"--menu-level":l?(t=l==null?void 0:l.level)!=null?t:1:0}})}const zl=Symbol("menuContext");function Fu(l){At(zl,l)}function Pl(l){const a=It();At(`subMenu:${a==null?void 0:a.uid}`,l)}function oa(){if(!It())throw new Error("instance is required");return zt(zl)}function Al(){const l=It();if(!l)throw new Error("instance is required");const a=Vl(l,["Menu","SubMenu"]);return zt(`subMenu:${a==null?void 0:a.uid}`)}const Ku=V({name:"MenuItem",__name:"menu-item",props:{activeIcon:{},disabled:{type:Boolean,default:!1},icon:{},path:{},badge:{},badgeType:{},badgeVariants:{}},emits:["click"],setup(l,{emit:a}){const t=l,o=a,s=Ne(),{b:n,e:r,is:i}=tt("menu-item"),d=tt("menu"),f=oa(),p=Al(),{parentMenu:v,parentPaths:h}=El(),b=w(()=>t.path===(f==null?void 0:f.activePath)),S=w(()=>b.value&&t.activeIcon||t.icon),H=w(()=>{var U;return((U=v.value)==null?void 0:U.type.name)==="Menu"}),O=w(()=>{var U;return((U=f.props)==null?void 0:U.collapseShowTitle)&&H.value&&f.props.collapse}),C=w(()=>{var U;return f.props.mode==="vertical"&&H.value&&((U=f.props)==null?void 0:U.collapse)&&s.title}),W=pt({active:b,parentPaths:h.value,path:t.path||""});function F(){var U;t.disabled||((U=f==null?void 0:f.handleMenuItemClick)==null||U.call(f,{parentPaths:h.value,path:t.path}),o("click",W))}return qe(()=>{var U,L;(U=p==null?void 0:p.addSubMenu)==null||U.call(p,W),(L=f==null?void 0:f.addMenuItem)==null||L.call(f,W)}),dl(()=>{var U,L;(U=p==null?void 0:p.removeSubMenu)==null||U.call(p,W),(L=f==null?void 0:f.removeMenuItem)==null||L.call(f,W)}),(U,L)=>(u(),k("li",{class:R([e(f).theme,e(n)(),e(i)("active",b.value),e(i)("disabled",U.disabled),e(i)("collapse-show-title",O.value)]),role:"menuitem",onClick:Me(F,["stop"])},[C.value?(u(),y(e(rt),{key:0,"content-class":[e(f).theme],side:"right"},{trigger:c(()=>[$("div",{class:R([e(d).be("tooltip","trigger")])},[m(e(Ue),{class:R(e(d).e("icon")),icon:S.value,fallback:""},null,8,["class","icon"]),E(U.$slots,"default"),O.value?(u(),k("span",{key:0,class:R(e(d).e("name"))},[E(U.$slots,"title")],2)):A("",!0)],2)]),default:c(()=>[E(U.$slots,"title")]),_:3},8,["content-class"])):A("",!0),Ee($("div",{class:R([e(r)("content")])},[e(f).props.mode!=="horizontal"?(u(),y(e(Bl),fe({key:0,class:"right-2"},t),null,16)):A("",!0),m(e(Ue),{class:R(e(d).e("icon")),icon:S.value},null,8,["class","icon"]),E(U.$slots,"default"),E(U.$slots,"title")],2),[[ze,!C.value]])],2))}});function ju(l,a={}){const{enable:t=!0,delay:o=320}=a;function s(){if(!(typeof t=="boolean"?t:t.value))return;const i=document.querySelector("aside li[role=menuitem].is-active");i&&i.scrollIntoView({behavior:"smooth",block:"center",inline:"center"})}const n=qt(s,o);return pe(l,()=>{(typeof t=="boolean"?t:t.value)&&n()}),{scrollToActiveItem:s}}const Gu=V({name:"CollapseTransition",__name:"collapse-transition",setup(l){const a=o=>{o.style.maxHeight="",o.style.overflow=o.dataset.oldOverflow,o.style.paddingTop=o.dataset.oldPaddingTop,o.style.paddingBottom=o.dataset.oldPaddingBottom},t={afterEnter(o){o.style.maxHeight="",o.style.overflow=o.dataset.oldOverflow},afterLeave(o){a(o)},beforeEnter(o){o.dataset||(o.dataset={}),o.dataset.oldPaddingTop=o.style.paddingTop,o.dataset.oldMarginTop=o.style.marginTop,o.dataset.oldPaddingBottom=o.style.paddingBottom,o.dataset.oldMarginBottom=o.style.marginBottom,o.style.height&&(o.dataset.elExistsHeight=o.style.height),o.style.maxHeight=0,o.style.paddingTop=0,o.style.marginTop=0,o.style.paddingBottom=0,o.style.marginBottom=0},beforeLeave(o){o.dataset||(o.dataset={}),o.dataset.oldPaddingTop=o.style.paddingTop,o.dataset.oldMarginTop=o.style.marginTop,o.dataset.oldPaddingBottom=o.style.paddingBottom,o.dataset.oldMarginBottom=o.style.marginBottom,o.dataset.oldOverflow=o.style.overflow,o.style.maxHeight=`${o.scrollHeight}px`,o.style.overflow="hidden"},enter(o){requestAnimationFrame(()=>{o.dataset.oldOverflow=o.style.overflow,o.dataset.elExistsHeight?o.style.maxHeight=o.dataset.elExistsHeight:o.scrollHeight===0?o.style.maxHeight=0:o.style.maxHeight=`${o.scrollHeight}px`,o.style.paddingTop=o.dataset.oldPaddingTop,o.style.paddingBottom=o.dataset.oldPaddingBottom,o.style.marginTop=o.dataset.oldMarginTop,o.style.marginBottom=o.dataset.oldMarginBottom,o.style.overflow="hidden"})},enterCancelled(o){a(o)},leave(o){o.scrollHeight!==0&&(o.style.maxHeight=0,o.style.paddingTop=0,o.style.paddingBottom=0,o.style.marginTop=0,o.style.marginBottom=0)},leaveCancelled(o){a(o)}};return(o,s)=>(u(),y(at,fe({name:"collapse-transition"},rl(t)),{default:c(()=>[E(o.$slots,"default")]),_:3},16))}}),ja=V({name:"SubMenuContent",__name:"sub-menu-content",props:{isMenuMore:{type:Boolean,default:!1},isTopLevelMenuSubmenu:{type:Boolean},level:{default:0},activeIcon:{},disabled:{type:Boolean},icon:{},path:{},badge:{},badgeType:{},badgeVariants:{}},setup(l){const a=l,t=oa(),{b:o,e:s,is:n}=tt("sub-menu-content"),r=tt("menu"),i=w(()=>t==null?void 0:t.openedMenus.includes(a.path)),d=w(()=>t.props.collapse),f=w(()=>a.level===1),p=w(()=>t.props.collapseShowTitle&&f.value&&d.value),v=w(()=>t==null?void 0:t.props.mode),h=w(()=>v.value==="horizontal"||!(f.value&&d.value)),b=w(()=>v.value==="vertical"&&f.value&&d.value&&!p.value),S=w(()=>v.value==="horizontal"&&!f.value||v.value==="vertical"&&d.value?Yt:Jt),H=w(()=>i.value?{transform:"rotate(180deg)"}:{});return(O,C)=>(u(),k("div",{class:R([e(o)(),e(n)("collapse-show-title",p.value),e(n)("more",O.isMenuMore)])},[E(O.$slots,"default"),O.isMenuMore?A("",!0):(u(),y(e(Ue),{key:0,class:R(e(r).e("icon")),icon:O.icon,fallback:""},null,8,["class","icon"])),b.value?A("",!0):(u(),k("div",{key:1,class:R([e(s)("title")])},[E(O.$slots,"title")],2)),O.isMenuMore?A("",!0):Ee((u(),y(Le(S.value),{key:2,class:R([[e(s)("icon-arrow")],"size-4"]),style:ce(H.value)},null,8,["class","style"])),[[ze,h.value]])],2))}}),Il=V({name:"SubMenu",__name:"sub-menu",props:{isSubMenuMore:{type:Boolean,default:!1},activeIcon:{},disabled:{type:Boolean,default:!1},icon:{},path:{},badge:{},badgeType:{},badgeVariants:{}},setup(l){var ge;const a=l,{parentMenu:t,parentPaths:o}=El(),{b:s,is:n}=tt("sub-menu"),r=tt("menu"),i=oa(),d=Al(),f=Ll(d),p=X(!1),v=X({}),h=X({}),b=X(null);Pl({addSubMenu:K,handleMouseleave:J,level:((ge=d==null?void 0:d.level)!=null?ge:0)+1,mouseInChild:p,removeSubMenu:P});const S=w(()=>i==null?void 0:i.openedMenus.includes(a.path)),H=w(()=>{var G;return((G=t.value)==null?void 0:G.type.name)==="Menu"}),O=w(()=>{var G;return(G=i==null?void 0:i.props.mode)!=null?G:"vertical"}),C=w(()=>i==null?void 0:i.props.rounded),W=w(()=>{var G;return(G=d==null?void 0:d.level)!=null?G:0}),F=w(()=>W.value===1),U=w(()=>{const G=O.value==="horizontal",ee=G&&F.value?"bottom":"right";return{collisionPadding:{top:20},side:ee,sideOffset:G?5:10}}),L=w(()=>{let G=!1;return Object.values(v.value).forEach(ee=>{ee.active&&(G=!0)}),Object.values(h.value).forEach(ee=>{ee.active&&(G=!0)}),G});function K(G){h.value[G.path]=G}function P(G){Reflect.deleteProperty(h.value,G.path)}function B(){const G=i==null?void 0:i.props.mode;a.disabled||i!=null&&i.props.collapse&&G==="vertical"||G==="horizontal"||i==null||i.handleSubMenuClick({active:L.value,parentPaths:o.value,path:a.path})}function N(G,ee=300){var he,Ce;if(G.type!=="focus"){if(!(i!=null&&i.props.collapse)&&(i==null?void 0:i.props.mode)==="vertical"||a.disabled){d&&(d.mouseInChild.value=!0);return}d&&(d.mouseInChild.value=!0),b.value&&window.clearTimeout(b.value),b.value=setTimeout(()=>{i==null||i.openMenu(a.path,o.value)},ee),(Ce=(he=t.value)==null?void 0:he.vnode.el)==null||Ce.dispatchEvent(new MouseEvent("mouseenter"))}}function J(G=!1){var ee;if(!(i!=null&&i.props.collapse)&&(i==null?void 0:i.props.mode)==="vertical"&&d){d.mouseInChild.value=!1;return}b.value&&window.clearTimeout(b.value),d&&(d.mouseInChild.value=!1),b.value=setTimeout(()=>{!p.value&&(i==null||i.closeMenu(a.path,o.value))},300),G&&((ee=d==null?void 0:d.handleMouseleave)==null||ee.call(d,!0))}const oe=w(()=>L.value&&a.activeIcon||a.icon),de=pt({active:L,parentPaths:o,path:a.path});return qe(()=>{var G,ee;(G=d==null?void 0:d.addSubMenu)==null||G.call(d,de),(ee=i==null?void 0:i.addSubMenu)==null||ee.call(i,de)}),dl(()=>{var G,ee;(G=d==null?void 0:d.removeSubMenu)==null||G.call(d,de),(ee=i==null?void 0:i.removeSubMenu)==null||ee.call(i,de)}),(G,ee)=>(u(),k("li",{class:R([e(s)(),e(n)("opened",S.value),e(n)("active",L.value),e(n)("disabled",G.disabled)]),onFocus:N,onMouseenter:N,onMouseleave:ee[3]||(ee[3]=()=>J())},[e(i).isMenuPopup?(u(),y(e(kr),{key:0,"content-class":[e(i).theme,e(r).e("popup-container"),e(n)(e(i).theme,!0),S.value?"":"hidden","overflow-auto","max-h-[calc(var(--radix-hover-card-content-available-height)-20px)]"],"content-props":U.value,open:!0,"open-delay":0},{trigger:c(()=>[m(ja,{class:R(e(n)("active",L.value)),icon:oe.value,"is-menu-more":G.isSubMenuMore,"is-top-level-menu-submenu":H.value,level:W.value,path:G.path,onClick:Me(B,["stop"])},{title:c(()=>[E(G.$slots,"title")]),_:3},8,["class","icon","is-menu-more","is-top-level-menu-submenu","level","path"])]),default:c(()=>[$("div",{class:R([e(r).is(O.value,!0),e(r).e("popup")]),onFocus:ee[0]||(ee[0]=he=>N(he,100)),onMouseenter:ee[1]||(ee[1]=he=>N(he,100)),onMouseleave:ee[2]||(ee[2]=()=>J(!0))},[$("ul",{class:R([e(r).b(),e(n)("rounded",C.value)]),style:ce(e(f))},[E(G.$slots,"default")],6)],34)]),_:3},8,["content-class","content-props"])):(u(),k(Z,{key:1},[m(ja,{class:R(e(n)("active",L.value)),icon:oe.value,"is-menu-more":G.isSubMenuMore,"is-top-level-menu-submenu":H.value,level:W.value,path:G.path,onClick:Me(B,["stop"])},{title:c(()=>[E(G.$slots,"title")]),default:c(()=>[E(G.$slots,"content")]),_:3},8,["class","icon","is-menu-more","is-top-level-menu-submenu","level","path"]),m(Gu,null,{default:c(()=>[Ee($("ul",{class:R([e(r).b(),e(n)("rounded",C.value)]),style:ce(e(f))},[E(G.$slots,"default")],6),[[ze,S.value]])]),_:3})],64))],34))}}),qu=V({name:"Menu",__name:"menu",props:{accordion:{type:Boolean,default:!0},collapse:{type:Boolean,default:!1},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{default:"vertical"},rounded:{type:Boolean,default:!0},scrollToActive:{type:Boolean,default:!1},theme:{default:"dark"}},emits:["close","open","select"],setup(l,{emit:a}){const t=l,o=a,{b:s,is:n}=tt("menu"),r=Ll(),i=Ne(),d=X(),f=X(-1),p=X(t.defaultOpeneds&&!t.collapse?[...t.defaultOpeneds]:[]),v=X(t.defaultActive),h=X({}),b=X({}),S=X(!1),H=w(()=>t.mode==="horizontal"||t.mode==="vertical"&&t.collapse),O=w(()=>{var Se,Ve;const j=(Ve=(Se=i.default)==null?void 0:Se.call(i))!=null?Ve:[],ne=Bt(j),le=f.value===-1?ne:ne.slice(0,f.value),ye=f.value===-1?[]:ne.slice(f.value);return{showSlotMore:ye.length>0,slotDefault:le,slotMore:ye}});pe(()=>t.collapse,j=>{j&&(p.value=[])}),pe(h.value,N),pe(()=>t.defaultActive,(j="")=>{h.value[j]||(v.value=""),J(j)});let C;il(()=>{t.mode==="horizontal"?C=In(d,K).stop:C==null||C()}),Fu(pt({activePath:v,addMenuItem:he,addSubMenu:Ce,closeMenu:G,handleMenuItemClick:oe,handleSubMenuClick:de,isMenuPopup:H,openedMenus:p,openMenu:ee,props:t,removeMenuItem:Ae,removeSubMenu:Be,subMenus:b,theme:An(t,"theme"),items:h})),Pl({addSubMenu:Ce,level:1,mouseInChild:S,removeSubMenu:Be});function W(j){const ne=getComputedStyle(j),le=Number.parseInt(ne.marginLeft,10),ye=Number.parseInt(ne.marginRight,10);return j.offsetWidth+le+ye||0}function F(){var re,me,q;if(!d.value)return-1;const j=[...(me=(re=d.value)==null?void 0:re.childNodes)!=null?me:[]].filter(we=>we.nodeName!=="#comment"&&(we.nodeName!=="#text"||we.nodeValue)),ne=46,le=getComputedStyle(d==null?void 0:d.value),ye=Number.parseInt(le.paddingLeft,10),Se=Number.parseInt(le.paddingRight,10),Ve=((q=d.value)==null?void 0:q.clientWidth)-ye-Se;let De=0,Ie=0;return j.forEach((we,D)=>{De+=W(we),De<=Ve-ne&&(Ie=D+1)}),Ie===j.length?-1:Ie}function U(j,ne=33.34){let le;return()=>{le&&clearTimeout(le),le=setTimeout(()=>{j()},ne)}}let L=!0;function K(){if(f.value===F())return;const j=()=>{f.value=-1,je(()=>{f.value=F()})};j(),L?j():U(j)(),L=!1}const P=w(()=>t.scrollToActive&&t.mode==="vertical"&&!t.collapse),{scrollToActiveItem:B}=ju(v,{enable:P,delay:320});pe(v,()=>{B()});function N(){_e().forEach(ne=>{const le=b.value[ne];le&&ee(ne,le.parentPaths)})}function J(j){const ne=h.value,le=ne[j]||v.value&&ne[v.value]||ne[t.defaultActive||""];v.value=le?le.path:j}function oe(j){const{collapse:ne,mode:le}=t;(le==="horizontal"||ne)&&(p.value=[]);const{parentPaths:ye,path:Se}=j;!Se||!ye||o("select",Se,ye)}function de({parentPaths:j,path:ne}){p.value.includes(ne)?G(ne,j):ee(ne,j)}function ge(j){const ne=p.value.indexOf(j);ne!==-1&&p.value.splice(ne,1)}function G(j,ne){var le,ye;t.accordion&&(p.value=(ye=(le=b.value[j])==null?void 0:le.parentPaths)!=null?ye:[]),ge(j),o("close",j,ne)}function ee(j,ne){if(!p.value.includes(j)){if(t.accordion){const le=_e();le.includes(j)&&(ne=le),p.value=p.value.filter(ye=>ne.includes(ye))}p.value.push(j),o("open",j,ne)}}function he(j){h.value[j.path]=j}function Ce(j){b.value[j.path]=j}function Be(j){Reflect.deleteProperty(b.value,j.path)}function Ae(j){Reflect.deleteProperty(h.value,j.path)}function _e(){const j=v.value&&h.value[v.value];return!j||t.mode==="horizontal"||t.collapse?[]:j.parentPaths}return(j,ne)=>(u(),k("ul",{ref_key:"menu",ref:d,class:R([j.theme,e(s)(),e(n)(j.mode,!0),e(n)(j.theme,!0),e(n)("rounded",j.rounded),e(n)("collapse",j.collapse),e(n)("menu-align",j.mode==="horizontal")]),style:ce(e(r)),role:"menu"},[j.mode==="horizontal"&&O.value.showSlotMore?(u(),k(Z,{key:0},[(u(!0),k(Z,null,ue(O.value.slotDefault,le=>(u(),y(Le(le),{key:le.key}))),128)),m(Il,{"is-sub-menu-more":"",path:"sub-menu-more"},{title:c(()=>[m(e(nn),{class:"size-4"})]),default:c(()=>[(u(!0),k(Z,null,ue(O.value.slotMore,le=>(u(),y(Le(le),{key:le.key}))),128))]),_:1})],64)):E(j.$slots,"default",{key:1})],6))}}),Ul=V({name:"SubMenuUi",__name:"sub-menu",props:{menu:{}},setup(l){const a=l,t=w(()=>{const{menu:o}=a;return Reflect.has(o,"children")&&!!o.children&&o.children.length>0});return(o,s)=>t.value?(u(),y(e(Il),{key:`${o.menu.path}_sub`,"active-icon":o.menu.activeIcon,icon:o.menu.icon,path:o.menu.path},{content:c(()=>[m(e(Bl),{badge:o.menu.badge,"badge-type":o.menu.badgeType,"badge-variants":o.menu.badgeVariants,class:"right-6"},null,8,["badge","badge-type","badge-variants"])]),title:c(()=>[$("span",null,_(o.menu.name),1)]),default:c(()=>[(u(!0),k(Z,null,ue(o.menu.children||[],n=>(u(),y(Ul,{key:n.path,menu:n},null,8,["menu"]))),128))]),_:1},8,["active-icon","icon","path"])):(u(),y(e(Ku),{key:o.menu.path,"active-icon":o.menu.activeIcon,badge:o.menu.badge,"badge-type":o.menu.badgeType,"badge-variants":o.menu.badgeVariants,icon:o.menu.icon,path:o.menu.path},{title:c(()=>[$("span",null,_(o.menu.name),1)]),_:1},8,["active-icon","badge","badge-type","badge-variants","icon","path"]))}}),Hl=V({name:"MenuView",__name:"menu",props:{menus:{},accordion:{type:Boolean},collapse:{type:Boolean,default:!1},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{},rounded:{type:Boolean},scrollToActive:{type:Boolean},theme:{}},setup(l){const t=lt(l);return(o,s)=>(u(),y(e(qu),He(Qe(e(t))),{default:c(()=>[(u(!0),k(Z,null,ue(o.menus,n=>(u(),y(Ul,{key:n.path,menu:n},null,8,["menu"]))),128))]),_:1},16))}});function na(){const l=nt(),a=new Map,t=()=>{l.getRoutes().forEach(d=>{a.set(d.path,d)})};t(),l.afterEach(()=>{t()});const o=i=>{var f,p;if(Gt(i))return!0;const d=a.get(i);return!!((f=d==null?void 0:d.meta)!=null&&f.link||(p=d==null?void 0:d.meta)!=null&&p.openInNewWindow)},s=i=>l.resolve(i).href;return{navigation:i=>Y(null,null,function*(){var d;try{const f=a.get(i),{openInNewWindow:p=!1,query:v={},link:h}=(d=f==null?void 0:f.meta)!=null?d:{};if(h&&typeof h=="string"){Ia(h,{target:"_blank"});return}Gt(i)?Ia(i,{target:"_blank"}):p?ol(s(i)):yield l.push({path:i,query:v})}catch(f){throw console.error("Navigation failed:",f),f}}),willOpenedByWindow:i=>o(i)}}const Yu=V({__name:"extra-menu",props:{collapse:{type:Boolean},menus:{default:()=>[]},accordion:{type:Boolean,default:!0},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{},rounded:{type:Boolean},scrollToActive:{type:Boolean},theme:{}},setup(l){const a=Ge(),{navigation:t}=na();function o(s){return Y(this,null,function*(){yield t(s)})}return(s,n)=>{var r;return u(),y(e(Hl),{accordion:s.accordion,collapse:s.collapse,"default-active":((r=e(a).meta)==null?void 0:r.activePath)||e(a).path,menus:s.menus,rounded:s.rounded,theme:s.theme,mode:"vertical",onSelect:o},null,8,["accordion","collapse","default-active","menus","rounded","theme"])}}}),Ga=V({__name:"menu",props:{menus:{default:()=>[]},accordion:{type:Boolean,default:!0},collapse:{type:Boolean},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{},rounded:{type:Boolean},scrollToActive:{type:Boolean},theme:{}},emits:["open","select"],setup(l,{emit:a}){const t=l,o=a;function s(r){o("select",r,t.mode)}function n(r,i){o("open",r,i)}return(r,i)=>(u(),y(e(Hl),{accordion:r.accordion,collapse:r.collapse,"collapse-show-title":r.collapseShowTitle,"default-active":r.defaultActive,menus:r.menus,mode:r.mode,rounded:r.rounded,"scroll-to-active":"",theme:r.theme,onOpen:n,onSelect:s},null,8,["accordion","collapse","collapse-show-title","default-active","menus","mode","rounded","theme"]))}}),Ju=V({__name:"mixed-menu",props:{activePath:{},collapse:{type:Boolean},menus:{},rounded:{type:Boolean},theme:{}},emits:["defaultSelect","enter","select"],setup(l,{emit:a}){const t=l,o=a,s=Ge();return ul(()=>{const n=aa(t.menus||[],s.path);if(n){const r=(t.menus||[]).find(i=>{var d;return i.path===((d=n.parents)==null?void 0:d[0])});o("defaultSelect",n,r)}}),(n,r)=>(u(),y(e(Nu),{"active-path":n.activePath,collapse:n.collapse,menus:n.menus,rounded:n.rounded,theme:n.theme,onEnter:r[0]||(r[0]=i=>o("enter",i)),onSelect:r[1]||(r[1]=i=>o("select",i))},null,8,["active-path","collapse","menus","rounded","theme"]))}});function Xu(l){const a=st(),{navigation:t,willOpenedByWindow:o}=na(),s=w(()=>{var C;return(C=l==null?void 0:l.value)!=null?C:a.accessMenus}),n=new Map,r=X([]),i=Ge(),d=X([]),f=X(!1),p=X(""),v=w(()=>I.app.layout==="header-mixed-nav"?1:0),h=C=>Y(null,null,function*(){var U,L,K;const W=(U=C==null?void 0:C.children)!=null?U:[],F=W.length>0;o(C.path)||(d.value=W!=null?W:[],p.value=(K=(L=C.parents)==null?void 0:L[v.value])!=null?K:C.path,f.value=F),F?I.sidebar.autoActivateChild&&(yield t(n.has(C.path)?n.get(C.path):C.path)):yield t(C.path)}),b=(C,W)=>Y(null,null,function*(){var F,U,L,K;d.value=(U=(F=W==null?void 0:W.children)!=null?F:r.value)!=null?U:[],p.value=(K=(L=C.parents)==null?void 0:L[v.value])!=null?K:C.path,I.sidebar.expandOnHover&&(f.value=d.value.length>0)}),S=()=>{var U,L;if(I.sidebar.expandOnHover)return;const{findMenu:C,rootMenu:W,rootMenuPath:F}=yt(s.value,i.path);p.value=(U=F!=null?F:C==null?void 0:C.path)!=null?U:"",d.value=(L=W==null?void 0:W.children)!=null?L:[]},H=C=>{var W,F,U;if(!I.sidebar.expandOnHover){const{findMenu:L}=yt(s.value,C.path);d.value=(W=L==null?void 0:L.children)!=null?W:[],p.value=(U=(F=C.parents)==null?void 0:F[v.value])!=null?U:C.path,f.value=d.value.length>0}};function O(C){var K,P,B,N;const W=((K=i.meta)==null?void 0:K.activePath)||C,{findMenu:F,rootMenu:U,rootMenuPath:L}=yt(s.value,W,v.value);r.value=(P=U==null?void 0:U.children)!=null?P:[],L&&n.set(L,W),p.value=(B=L!=null?L:F==null?void 0:F.path)!=null?B:"",d.value=(N=U==null?void 0:U.children)!=null?N:[],I.sidebar.expandOnHover&&(f.value=d.value.length>0)}return pe(()=>[i.path,I.app.layout],([C])=>{O(C||"")},{immediate:!0}),{extraActiveMenu:p,extraMenus:d,handleDefaultSelect:b,handleMenuMouseEnter:H,handleMixedMenuSelect:h,handleSideMouseLeave:S,sidebarExtraVisible:f}}function Zu(){const{navigation:l,willOpenedByWindow:a}=na(),t=st(),o=Ge(),s=X([]),n=X(""),r=X(""),i=X([]),d=new Map,{isMixedNav:f,isHeaderMixedNav:p}=ot(),v=w(()=>I.navigation.split&&f.value||p.value),h=w(()=>{const K=I.sidebar.enable;return v.value?K&&s.value.length>0:K}),b=w(()=>t.accessMenus),S=w(()=>v.value?b.value.map(K=>xe(Q({},K),{children:[]})):b.value),H=w(()=>v.value?s.value:b.value),O=w(()=>p.value?H.value:S.value),C=w(()=>{var K,P;return(P=(K=o==null?void 0:o.meta)==null?void 0:K.activePath)!=null?P:o.path}),W=w(()=>{var K,P;return v.value?n.value:(P=(K=o.meta)==null?void 0:K.activePath)!=null?P:o.path}),F=(K,P)=>{var J,oe;if(!v.value||P==="vertical"){l(K);return}const B=b.value.find(de=>de.path===K),N=(J=B==null?void 0:B.children)!=null?J:[];a(K)||(n.value=(oe=B==null?void 0:B.path)!=null?oe:"",s.value=N),N.length===0?l(K):B&&I.sidebar.autoActivateChild&&l(d.has(B.path)?d.get(B.path):B.path)},U=(K,P)=>{P.length<=1&&I.sidebar.autoActivateChild&&l(d.has(K)?d.get(K):K)};function L(K=o.path){var N,J,oe,de,ge;let{rootMenu:P}=yt(b.value,K);P||(P=b.value.find(G=>G.path===K));const B=yt((P==null?void 0:P.children)||[],K,1);r.value=(N=B.rootMenuPath)!=null?N:"",i.value=(oe=(J=B.rootMenu)==null?void 0:J.children)!=null?oe:[],n.value=(de=P==null?void 0:P.path)!=null?de:"",s.value=(ge=P==null?void 0:P.children)!=null?ge:[]}return pe(()=>o.path,K=>{var B,N,J,oe;const P=(oe=(J=(B=o==null?void 0:o.meta)==null?void 0:B.activePath)!=null?J:(N=o==null?void 0:o.meta)==null?void 0:N.link)!=null?oe:K;a(P)||(L(P),n.value&&d.set(n.value,P))},{immediate:!0}),ul(()=>{var K;L(((K=o.meta)==null?void 0:K.activePath)||o.path)}),{handleMenuSelect:F,handleMenuOpen:U,headerActive:W,headerMenus:S,sidebarActive:C,sidebarMenus:H,mixHeaderMenus:O,mixExtraMenus:i,sidebarVisible:h}}const Qu={class:"flex-center hover:bg-muted hover:text-foreground text-muted-foreground border-border h-full cursor-pointer border-l px-2 text-lg font-semibold"},ec=V({__name:"tool-more",props:{menus:{}},setup(l){return(a,t)=>(u(),y(e(yr),{menus:a.menus,modal:!1},{default:c(()=>[$("div",Qu,[m(e(Jt),{class:"size-4"})])]),_:1},8,["menus"]))}}),tc=V({__name:"tool-screen",props:{screen:{type:Boolean},screenModifiers:{}},emits:["update:screen"],setup(l){const a=x(l,"screen");function t(){a.value=!a.value}return(o,s)=>(u(),k("div",{class:"flex-center hover:bg-muted hover:text-foreground text-muted-foreground border-border h-full cursor-pointer border-l px-2 text-lg font-semibold",onClick:t},[a.value?(u(),y(e(vl),{key:0,class:"size-4"})):(u(),y(e(hl),{key:1,class:"size-4"}))]))}}),ac=["data-active-tab","data-index","onClick","onMousedown"],lc={class:"relative size-full px-1"},oc={key:0,class:"tabs-chrome__divider bg-border absolute left-[var(--gap)] top-1/2 z-0 h-4 w-[1px] translate-y-[-50%] transition-all"},nc={class:"tabs-chrome__extra absolute right-[var(--gap)] top-1/2 z-[3] size-4 translate-y-[-50%]"},sc={class:"tabs-chrome__item-main group-[.is-active]:text-primary dark:group-[.is-active]:text-accent-foreground text-accent-foreground z-[2] mx-[calc(var(--gap)*2)] my-0 flex h-full items-center overflow-hidden rounded-tl-[5px] rounded-tr-[5px] pl-2 pr-4 duration-150"},rc={class:"flex-1 overflow-hidden whitespace-nowrap text-sm"},ic=V({name:"VbenTabsChrome",inheritAttrs:!1,__name:"tabs",props:ve({active:{},contentClass:{default:"vben-tabs-content"},contextMenus:{type:Function,default:()=>[]},draggable:{type:Boolean},gap:{default:7},maxWidth:{},middleClickToClose:{type:Boolean},minWidth:{},showIcon:{type:Boolean},styleType:{},tabs:{default:()=>[]},wheelable:{type:Boolean}},{active:{},activeModifiers:{}}),emits:ve(["close","unpin"],["update:active"]),setup(l,{emit:a}){const t=l,o=a,s=x(l,"active"),n=X(),r=X(),i=w(()=>{const{gap:p}=t;return{"--gap":`${p}px`}}),d=w(()=>t.tabs.map(p=>{const{fullPath:v,meta:h,name:b,path:S,key:H}=p||{},{affixTab:O,icon:C,newTabTitle:W,tabClosable:F,title:U}=h||{};return{affixTab:!!O,closable:Reflect.has(h,"tabClosable")?!!F:!0,fullPath:v,icon:C,key:H,meta:h,name:b,path:S,title:W||U||b}}));function f(p,v){p.button===1&&v.closable&&!v.affixTab&&d.value.length>1&&t.middleClickToClose&&(p.preventDefault(),p.stopPropagation(),o("close",v.key))}return(p,v)=>(u(),k("div",{ref_key:"contentRef",ref:n,class:R([p.contentClass,"tabs-chrome !flex h-full w-max overflow-y-hidden pr-6"]),style:ce(i.value)},[m(Lt,{name:"slide-left"},{default:c(()=>[(u(!0),k(Z,null,ue(d.value,(h,b)=>(u(),k("div",{key:h.key,ref_for:!0,ref_key:"tabRef",ref:r,class:R([[{"is-active":h.key===s.value,draggable:!h.affixTab,"affix-tab":h.affixTab}],"tabs-chrome__item draggable translate-all group relative -mr-3 flex h-full select-none items-center"]),"data-active-tab":s.value,"data-index":b,"data-tab-item":"true",onClick:S=>s.value=h.key,onMousedown:S=>f(S,h)},[m(e(xl),{"handler-data":h,menus:p.contextMenus,modal:!1,"item-class":"pr-6"},{default:c(()=>[$("div",lc,[b!==0&&h.key!==s.value?(u(),k("div",oc)):A("",!0),v[0]||(v[0]=$("div",{class:"tabs-chrome__background absolute z-[-1] size-full px-[calc(var(--gap)-1px)] py-0 transition-opacity duration-150"},[$("div",{class:"tabs-chrome__background-content group-[.is-active]:bg-primary/15 dark:group-[.is-active]:bg-accent h-full rounded-tl-[var(--gap)] rounded-tr-[var(--gap)] duration-150"}),$("svg",{class:"tabs-chrome__background-before group-[.is-active]:fill-primary/15 dark:group-[.is-active]:fill-accent absolute bottom-0 left-[-1px] fill-transparent transition-all duration-150",height:"7",width:"7"},[$("path",{d:"M 0 7 A 7 7 0 0 0 7 0 L 7 7 Z"})]),$("svg",{class:"tabs-chrome__background-after group-[.is-active]:fill-primary/15 dark:group-[.is-active]:fill-accent absolute bottom-0 right-[-1px] fill-transparent transition-all duration-150",height:"7",width:"7"},[$("path",{d:"M 0 0 A 7 7 0 0 0 7 7 L 0 7 Z"})])],-1)),$("div",nc,[Ee(m(e(ct),{class:"hover:bg-accent stroke-accent-foreground/80 hover:stroke-accent-foreground text-accent-foreground/80 group-[.is-active]:text-accent-foreground mt-[2px] size-3 cursor-pointer rounded-full transition-all",onClick:Me(()=>o("close",h.key),["stop"])},null,8,["onClick"]),[[ze,!h.affixTab&&d.value.length>1&&h.closable]]),Ee(m(e(Ut),{class:"hover:text-accent-foreground text-accent-foreground/80 group-[.is-active]:text-accent-foreground mt-[1px] size-3.5 cursor-pointer rounded-full transition-all",onClick:Me(()=>o("unpin",h),["stop"])},null,8,["onClick"]),[[ze,h.affixTab&&d.value.length>1&&h.closable]])]),$("div",sc,[p.showIcon?(u(),y(e(Ue),{key:0,icon:h.icon,class:"mr-1 flex size-4 items-center overflow-hidden"},null,8,["icon"])):A("",!0),$("span",rc,_(h.title),1)])])]),_:2},1032,["handler-data","menus"])],42,ac))),128))]),_:1})],6))}}),dc=$e(ic,[["__scopeId","data-v-08fc8c7f"]]),uc=["data-index","onClick","onMousedown"],cc={class:"relative flex size-full items-center"},pc={class:"absolute right-1.5 top-1/2 z-[3] translate-y-[-50%] overflow-hidden"},fc={class:"text-accent-foreground group-[.is-active]:text-primary dark:group-[.is-active]:text-accent-foreground mx-3 mr-4 flex h-full items-center overflow-hidden rounded-tl-[5px] rounded-tr-[5px] pr-3 transition-all duration-300"},mc={class:"flex-1 overflow-hidden whitespace-nowrap text-sm"},hc=V({name:"VbenTabs",inheritAttrs:!1,__name:"tabs",props:ve({active:{},contentClass:{default:"vben-tabs-content"},contextMenus:{type:Function,default:()=>[]},draggable:{type:Boolean},gap:{},maxWidth:{},middleClickToClose:{type:Boolean},minWidth:{},showIcon:{type:Boolean},styleType:{},tabs:{default:()=>[]},wheelable:{type:Boolean}},{active:{},activeModifiers:{}}),emits:ve(["close","unpin"],["update:active"]),setup(l,{emit:a}){const t=l,o=a,s=x(l,"active"),n=w(()=>({brisk:{content:"h-full after:content-['']  after:absolute after:bottom-0 after:left-0 after:w-full after:h-[1.5px] after:bg-primary after:scale-x-0 after:transition-[transform] after:ease-out after:duration-300 hover:after:scale-x-100 after:origin-left [&.is-active]:after:scale-x-100 [&:not(:first-child)]:border-l last:border-r last:border-r border-border"},card:{content:"h-[calc(100%-6px)] rounded-md ml-2 border border-border  transition-all"},plain:{content:"h-full [&:not(:first-child)]:border-l last:border-r border-border"}})[t.styleType||"plain"]||{content:""}),r=w(()=>t.tabs.map(d=>{const{fullPath:f,meta:p,name:v,path:h,key:b}=d||{},{affixTab:S,icon:H,newTabTitle:O,tabClosable:C,title:W}=p||{};return{affixTab:!!S,closable:Reflect.has(p,"tabClosable")?!!C:!0,fullPath:f,icon:H,key:b,meta:p,name:v,path:h,title:O||W||v}}));function i(d,f){d.button===1&&f.closable&&!f.affixTab&&r.value.length>1&&t.middleClickToClose&&(d.preventDefault(),d.stopPropagation(),o("close",f.key))}return(d,f)=>(u(),k("div",{class:R([d.contentClass,"relative !flex h-full w-max items-center overflow-hidden pr-6"])},[m(Lt,{name:"slide-left"},{default:c(()=>[(u(!0),k(Z,null,ue(r.value,(p,v)=>(u(),k("div",{key:p.key,class:R([[{"is-active dark:bg-accent bg-primary/15":p.key===s.value,draggable:!p.affixTab,"affix-tab":p.affixTab},n.value.content],"tab-item [&:not(.is-active)]:hover:bg-accent translate-all group relative flex cursor-pointer select-none"]),"data-index":v,"data-tab-item":"true",onClick:h=>s.value=p.key,onMousedown:h=>i(h,p)},[m(e(xl),{"handler-data":p,menus:d.contextMenus,modal:!1,"item-class":"pr-6"},{default:c(()=>[$("div",cc,[$("div",pc,[Ee(m(e(ct),{class:"hover:bg-accent stroke-accent-foreground/80 hover:stroke-accent-foreground dark:group-[.is-active]:text-accent-foreground group-[.is-active]:text-primary size-3 cursor-pointer rounded-full transition-all",onClick:Me(()=>o("close",p.key),["stop"])},null,8,["onClick"]),[[ze,!p.affixTab&&r.value.length>1&&p.closable]]),Ee(m(e(Ut),{class:"hover:bg-accent hover:stroke-accent-foreground group-[.is-active]:text-primary dark:group-[.is-active]:text-accent-foreground mt-[1px] size-3.5 cursor-pointer rounded-full transition-all",onClick:Me(()=>o("unpin",p),["stop"])},null,8,["onClick"]),[[ze,p.affixTab&&r.value.length>1&&p.closable]])]),$("div",fc,[d.showIcon?(u(),y(e(Ue),{key:0,icon:p.icon,class:"mr-2 flex size-4 items-center overflow-hidden",fallback:""},null,8,["icon"])):A("",!0),$("span",mc,_(p.title),1)])])]),_:2},1032,["handler-data","menus"])],42,uc))),128))]),_:1})],2))}});function jt(l){const a="group";return l.classList.contains(a)?l:l.closest(`.${a}`)}function bc(l,a){const t=X(null);function o(){return Y(this,null,function*(){var d;yield je();const n=(d=document.querySelectorAll(`.${l.contentClass}`))==null?void 0:d[0];if(!n){console.warn("Element not found for sortable initialization");return}const r=()=>Y(null,null,function*(){var f;n.style.cursor="default",(f=n.querySelector(".draggable"))==null||f.classList.remove("dragging")}),{initializeSortable:i}=sn(n,{filter:(f,p)=>{const v=jt(p);return!(v==null?void 0:v.classList.contains("draggable"))||!l.draggable},onEnd(f){const{newIndex:p,oldIndex:v}=f,{srcElement:h}=f.originalEvent;if(!h){r();return}const b=jt(h);if(!b){r();return}if(!b.classList.contains("draggable")){r();return}v!==void 0&&p!==void 0&&!Number.isNaN(v)&&!Number.isNaN(p)&&v!==p&&a("sortTabs",v,p),r()},onMove(f){const p=jt(f.related);if(p!=null&&p.classList.contains("draggable")&&l.draggable){const v=f.dragged.classList.contains("affix-tab"),h=f.related.classList.contains("affix-tab");return v===h}else return!1},onStart:()=>{var f;n.style.cursor="grabbing",(f=n.querySelector(".draggable"))==null||f.classList.add("dragging")}});t.value=yield i()})}function s(){return Y(this,null,function*(){const{isMobile:n}=Xa();n.value||(yield je(),o())})}qe(s),pe(()=>l.styleType,()=>{var n;(n=t.value)==null||n.destroy(),s()}),St(()=>{var n;(n=t.value)==null||n.destroy()})}function vc(l){let a=null,t=null,o=0;const s=X(null),n=X(null),r=X(!1),i=X(!0),d=X(!1);function f(){var F;const O=(F=s.value)==null?void 0:F.$el;if(!O||!n.value)return{};const C=O.clientWidth,W=n.value.clientWidth;return{scrollbarWidth:C,scrollViewWidth:W}}function p(O,C=150){var U;const{scrollbarWidth:W,scrollViewWidth:F}=f();!W||!F||W>F||(U=n.value)==null||U.scrollBy({behavior:"smooth",left:O==="left"?-(W-C):+(W-C)})}function v(){return Y(this,null,function*(){var W,F;yield je();const O=(W=s.value)==null?void 0:W.$el;if(!O)return;const C=O==null?void 0:O.querySelector("div[data-radix-scroll-area-viewport]");n.value=C,b(),yield je(),h(),a==null||a.disconnect(),a=new ResizeObserver(qt(U=>{b(),h()},100)),a.observe(C),o=((F=l.tabs)==null?void 0:F.length)||0,t==null||t.disconnect(),t=new MutationObserver(()=>{const U=C.querySelectorAll('div[data-tab-item="true"]').length;U>o&&h(),U!==o&&(b(),o=U)}),t.observe(C,{attributes:!1,childList:!0,subtree:!0})})}function h(){return Y(this,null,function*(){if(!n.value)return;yield je();const O=n.value,{scrollbarWidth:C}=f(),{scrollWidth:W}=O;C>=W||requestAnimationFrame(()=>{const F=O==null?void 0:O.querySelector(".is-active");F==null||F.scrollIntoView({behavior:"smooth",inline:"start"})})})}function b(){return Y(this,null,function*(){if(!n.value)return;const{scrollbarWidth:O}=f();r.value=n.value.scrollWidth>O})}const S=qt(({left:O,right:C})=>{i.value=O,d.value=C},100);function H({deltaY:O}){var C;(C=n.value)==null||C.scrollBy({left:O*3})}return pe(()=>l.active,()=>Y(null,null,function*(){h()}),{flush:"post"}),pe(()=>l.styleType,()=>{v()}),qe(v),St(()=>{a==null||a.disconnect(),t==null||t.disconnect(),a=null,t=null}),{handleScrollAt:S,handleWheel:H,initScrollbar:v,scrollbarRef:s,scrollDirection:p,scrollIsAtLeft:i,scrollIsAtRight:d,showScrollButton:r}}const gc={class:"flex h-full flex-1 overflow-hidden"},yc=V({name:"TabsView",__name:"tabs-view",props:{active:{},contentClass:{default:"vben-tabs-content"},contextMenus:{},draggable:{type:Boolean,default:!0},gap:{},maxWidth:{},middleClickToClose:{type:Boolean},minWidth:{},showIcon:{type:Boolean},styleType:{default:"chrome"},tabs:{},wheelable:{type:Boolean,default:!0}},emits:["close","sortTabs","unpin"],setup(l,{emit:a}){const t=l,o=a,s=Oe(t,o),{handleScrollAt:n,handleWheel:r,scrollbarRef:i,scrollDirection:d,scrollIsAtLeft:f,scrollIsAtRight:p,showScrollButton:v}=vc(t);function h(b){t.wheelable&&(r(b),b.stopPropagation(),b.preventDefault())}return bc(t,o),(b,S)=>(u(),k("div",gc,[Ee($("span",{class:R([{"hover:bg-muted text-muted-foreground cursor-pointer":!e(f),"pointer-events-none opacity-30":e(f)},"border-r px-2"]),onClick:S[0]||(S[0]=H=>e(d)("left"))},[m(e(rn),{class:"size-4 h-full"})],2),[[ze,e(v)]]),$("div",{class:R([{"pt-[3px]":b.styleType==="chrome"},"size-full flex-1 overflow-hidden"])},[m(e(Ct),{ref_key:"scrollbarRef",ref:i,"shadow-bottom":!1,"shadow-top":!1,class:"h-full",horizontal:"","scroll-bar-class":"z-10 hidden ",shadow:"","shadow-left":"","shadow-right":"",onScrollAt:e(n),onWheel:h},{default:c(()=>[b.styleType==="chrome"?(u(),y(e(dc),He(fe({key:0},Q(Q(Q({},e(s)),b.$attrs),b.$props))),null,16)):(u(),y(e(hc),He(fe({key:1},Q(Q(Q({},e(s)),b.$attrs),b.$props))),null,16))]),_:1},8,["onScrollAt"])],2),Ee($("span",{class:R([{"hover:bg-muted text-muted-foreground cursor-pointer":!e(p),"pointer-events-none opacity-30":e(p)},"hover:bg-muted text-muted-foreground cursor-pointer border-l px-2"]),onClick:S[1]||(S[1]=H=>e(d)("right"))},[m(e(Yt),{class:"size-4 h-full"})],2),[[ze,e(v)]])]))}});function wc(){const l=nt(),a=Ge(),t=st(),o=mt(),{contentIsMaximize:s,toggleMaximize:n}=kl(),{closeAllTabs:r,closeCurrentTab:i,closeLeftTabs:d,closeOtherTabs:f,closeRightTabs:p,closeTabByKey:v,getTabDisableState:h,openTabInNewWindow:b,refreshTab:S,toggleTabPin:H}=Sl(),O=w(()=>Ke(a)),{locale:C}=el(),W=X();pe([()=>o.getTabs,()=>o.updateTime,()=>C.value],([B])=>{W.value=B.map(N=>K(N))});const F=()=>{const B=Un(l.getRoutes(),N=>{var J;return!!((J=N.meta)!=null&&J.affixTab)});o.setAffixTabs(B)},U=B=>{const{fullPath:N,path:J}=o.getTabByKey(B);l.push(N||J)},L=B=>Y(null,null,function*(){yield v(B)});function K(B){var N;return xe(Q({},B),{meta:xe(Q({},B==null?void 0:B.meta),{title:g((N=B==null?void 0:B.meta)==null?void 0:N.title)})})}return pe(()=>t.accessMenus,()=>{F()},{immediate:!0}),pe(()=>a.fullPath,()=>{var N,J;const B=(J=(N=a.matched)==null?void 0:N[a.matched.length-1])==null?void 0:J.meta;o.addTab(xe(Q({},a),{meta:B||a.meta}))},{immediate:!0}),{createContextMenus:B=>{var Ce,Be;const{disabledCloseAll:N,disabledCloseCurrent:J,disabledCloseLeft:oe,disabledCloseOther:de,disabledCloseRight:ge,disabledRefresh:G}=h(B),ee=(Be=(Ce=B==null?void 0:B.meta)==null?void 0:Ce.affixTab)!=null?Be:!1;return[{disabled:J,handler:()=>Y(null,null,function*(){yield i(B)}),icon:ct,key:"close",text:g("preferences.tabbar.contextMenu.close")},{handler:()=>Y(null,null,function*(){yield H(B)}),icon:ee?gl:Ut,key:"affix",text:ee?g("preferences.tabbar.contextMenu.unpin"):g("preferences.tabbar.contextMenu.pin")},{handler:()=>Y(null,null,function*(){s.value||(yield l.push(B.fullPath)),n()}),icon:s.value?vl:hl,key:s.value?"restore-maximize":"maximize",text:s.value?g("preferences.tabbar.contextMenu.restoreMaximize"):g("preferences.tabbar.contextMenu.maximize")},{disabled:G,handler:()=>S(),icon:ta,key:"reload",text:g("preferences.tabbar.contextMenu.reload")},{handler:()=>Y(null,null,function*(){yield b(B)}),icon:fs,key:"open-in-new-window",separator:!0,text:g("preferences.tabbar.contextMenu.openInNewWindow")},{disabled:oe,handler:()=>Y(null,null,function*(){yield d(B)}),icon:os,key:"close-left",text:g("preferences.tabbar.contextMenu.closeLeft")},{disabled:ge,handler:()=>Y(null,null,function*(){yield p(B)}),icon:ss,key:"close-right",separator:!0,text:g("preferences.tabbar.contextMenu.closeRight")},{disabled:de,handler:()=>Y(null,null,function*(){yield f(B)}),icon:ms,key:"close-other",text:g("preferences.tabbar.contextMenu.closeOther")},{disabled:N,handler:r,icon:ns,key:"close-all",text:g("preferences.tabbar.contextMenu.closeAll")}].filter(Ae=>o.getMenuList.includes(Ae.key))},currentActive:O,currentTabs:W,handleClick:U,handleClose:L}}const xc={class:"flex-center h-full"},kc=V({name:"LayoutTabbar",__name:"tabbar",props:{showIcon:{type:Boolean},theme:{}},setup(l){const a=Ge(),t=mt(),{contentIsMaximize:o,toggleMaximize:s}=kl(),{unpinTab:n}=Sl(),{createContextMenus:r,currentActive:i,currentTabs:d,handleClick:f,handleClose:p}=wc(),v=w(()=>{const h=t.getTabByKey(i.value);return r(h).map(S=>xe(Q({},S),{label:S.text,value:S.key}))});return I.tabbar.persist||t.closeOtherTabs(a),(h,b)=>(u(),k(Z,null,[m(e(yc),{active:e(i),class:R(h.theme),"context-menus":e(r),draggable:e(I).tabbar.draggable,"show-icon":h.showIcon,"style-type":e(I).tabbar.styleType,tabs:e(d),wheelable:e(I).tabbar.wheelable,"middle-click-to-close":e(I).tabbar.middleClickToClose,onClose:e(p),onSortTabs:e(t).sortTabs,onUnpin:e(n),"onUpdate:active":e(f)},null,8,["active","class","context-menus","draggable","show-icon","style-type","tabs","wheelable","middle-click-to-close","onClose","onSortTabs","onUnpin","onUpdate:active"]),$("div",xc,[e(I).tabbar.showMore?(u(),y(e(ec),{key:0,menus:v.value},null,8,["menus"])):A("",!0),e(I).tabbar.showMaximize?(u(),y(e(tc),{key:1,screen:e(o),onChange:e(s),"onUpdate:screen":e(s)},null,8,["screen","onChange","onUpdate:screen"])):A("",!0)])],64))}}),Ic=V({name:"BasicLayout",__name:"layout",emits:["clearPreferencesAndLogout","clickLogo"],setup(l,{emit:a}){const t=a,{isDark:o,isHeaderNav:s,isMixedNav:n,isMobile:r,isSideMixedNav:i,isHeaderMixedNav:d,isHeaderSidebarNav:f,layout:p,preferencesButtonPosition:v,sidebarCollapsed:h,theme:b}=ot(),S=st(),{refresh:H}=Cl(),O=w(()=>o.value||I.theme.semiDarkSidebar?"dark":"light"),C=w(()=>o.value||I.theme.semiDarkHeader?"dark":"light"),W=w(()=>{const{collapsedShowTitle:re}=I.sidebar,me=[];return re&&h.value&&!n.value&&me.push("mx-auto"),i.value&&me.push("flex-center"),me.join(" ")}),F=w(()=>I.navigation.styleType==="rounded"),U=w(()=>r.value&&h.value?!0:s.value||n.value||f.value?!1:h.value||i.value||d.value),L=w(()=>!r.value&&(s.value||n.value||d.value)),{handleMenuSelect:K,handleMenuOpen:P,headerActive:B,headerMenus:N,sidebarActive:J,sidebarMenus:oe,mixHeaderMenus:de,sidebarVisible:ge}=Zu(),{extraActiveMenu:G,extraMenus:ee,handleDefaultSelect:he,handleMenuMouseEnter:Ce,handleMixedMenuSelect:Be,handleSideMouseLeave:Ae,sidebarExtraVisible:_e}=Xu(de);function j(re,me=!0){return me?nl(re,q=>xe(Q({},Ua(q)),{name:g(q.name)})):re.map(q=>xe(Q({},Ua(q)),{name:g(q.name)}))}function ne(){et({sidebar:{hidden:!I.sidebar.hidden}})}function le(){t("clearPreferencesAndLogout")}function ye(){t("clickLogo")}function Se(re){I.app.layout==="sidebar-mixed-nav"&&re.meta&&re.meta.hideInMenu&&(_e.value=!1)}const Ve=Ge();qe(()=>{Se(Ve)}),pe(()=>I.app.layout,re=>Y(null,null,function*(){re==="sidebar-mixed-nav"&&I.sidebar.hidden&&et({sidebar:{hidden:!1}})})),pe(dn.global.locale,H,{flush:"post"});const De=Ne(),Ie=w(()=>Object.keys(De).filter(re=>re.startsWith("header-")));return(re,me)=>(u(),y(e(_u),{"sidebar-extra-visible":e(_e),"onUpdate:sidebarExtraVisible":me[0]||(me[0]=q=>Xt(_e)?_e.value=q:null),"content-compact":e(I).app.contentCompact,"content-compact-width":e(I).app.contentCompactWidth,"content-padding":e(I).app.contentPadding,"content-padding-bottom":e(I).app.contentPaddingBottom,"content-padding-left":e(I).app.contentPaddingLeft,"content-padding-right":e(I).app.contentPaddingRight,"content-padding-top":e(I).app.contentPaddingTop,"footer-enable":e(I).footer.enable,"footer-fixed":e(I).footer.fixed,"footer-height":e(I).footer.height,"header-height":e(I).header.height,"header-hidden":e(I).header.hidden,"header-mode":e(I).header.mode,"header-theme":C.value,"header-toggle-sidebar-button":e(I).widget.sidebarToggle,"header-visible":e(I).header.enable,"is-mobile":e(I).app.isMobile,layout:e(p),"sidebar-collapse":e(I).sidebar.collapsed,"sidebar-collapse-show-title":e(I).sidebar.collapsedShowTitle,"sidebar-enable":e(ge),"sidebar-collapsed-button":e(I).sidebar.collapsedButton,"sidebar-fixed-button":e(I).sidebar.fixedButton,"sidebar-expand-on-hover":e(I).sidebar.expandOnHover,"sidebar-extra-collapse":e(I).sidebar.extraCollapse,"sidebar-extra-collapsed-width":e(I).sidebar.extraCollapsedWidth,"sidebar-hidden":e(I).sidebar.hidden,"sidebar-mixed-width":e(I).sidebar.mixedWidth,"sidebar-theme":O.value,"sidebar-width":e(I).sidebar.width,"side-collapse-width":e(I).sidebar.collapseWidth,"tabbar-enable":e(I).tabbar.enable,"tabbar-height":e(I).tabbar.height,"z-index":e(I).app.zIndex,onSideMouseLeave:e(Ae),onToggleSidebar:ne,"onUpdate:sidebarCollapse":me[1]||(me[1]=q=>e(et)({sidebar:{collapsed:q}})),"onUpdate:sidebarEnable":me[2]||(me[2]=q=>e(et)({sidebar:{enable:q}})),"onUpdate:sidebarExpandOnHover":me[3]||(me[3]=q=>e(et)({sidebar:{expandOnHover:q}})),"onUpdate:sidebarExtraCollapse":me[4]||(me[4]=q=>e(et)({sidebar:{extraCollapse:q}}))},ut({logo:c(()=>[e(I).logo.enable?(u(),y(e(Wa),{key:0,fit:e(I).logo.fit,class:R(W.value),collapsed:U.value,src:e(I).logo.source,text:e(I).app.name,theme:L.value?C.value:e(b),onClick:ye},ut({_:2},[re.$slots["logo-text"]?{name:"text",fn:c(()=>[E(re.$slots,"logo-text")]),key:"0"}:void 0]),1032,["fit","class","collapsed","src","text","theme"])):A("",!0)]),header:c(()=>[m(e(Hu),{theme:e(b),onClearPreferencesAndLogout:le},ut({"user-dropdown":c(()=>[E(re.$slots,"user-dropdown")]),notification:c(()=>[E(re.$slots,"notification")]),_:2},[!L.value&&e(I).breadcrumb.enable?{name:"breadcrumb",fn:c(()=>[m(e(Ir),{"hide-when-only-one":e(I).breadcrumb.hideOnlyOne,"show-home":e(I).breadcrumb.showHome,"show-icon":e(I).breadcrumb.showIcon,type:e(I).breadcrumb.styleType},null,8,["hide-when-only-one","show-home","show-icon","type"])]),key:"0"}:void 0,L.value?{name:"menu",fn:c(()=>[m(e(Ga),{"default-active":e(B),menus:j(e(N)),rounded:F.value,theme:C.value,class:"w-full",mode:"horizontal",onSelect:e(K)},null,8,["default-active","menus","rounded","theme","onSelect"])]),key:"1"}:void 0,ue(Ie.value,q=>({name:q,fn:c(()=>[E(re.$slots,q)])}))]),1032,["theme"])]),menu:c(()=>[m(e(Ga),{accordion:e(I).navigation.accordion,collapse:e(I).sidebar.collapsed,"collapse-show-title":e(I).sidebar.collapsedShowTitle,"default-active":e(J),menus:j(e(oe)),rounded:F.value,theme:O.value,mode:"vertical",onOpen:e(P),onSelect:e(K)},null,8,["accordion","collapse","collapse-show-title","default-active","menus","rounded","theme","onOpen","onSelect"])]),"mixed-menu":c(()=>[m(e(Ju),{"active-path":e(G),menus:j(e(de),!1),rounded:F.value,theme:O.value,onDefaultSelect:e(he),onEnter:e(Ce),onSelect:e(Be)},null,8,["active-path","menus","rounded","theme","onDefaultSelect","onEnter","onSelect"])]),"side-extra":c(()=>[m(e(Yu),{accordion:e(I).navigation.accordion,collapse:e(I).sidebar.extraCollapse,menus:j(e(ee)),rounded:F.value,theme:O.value},null,8,["accordion","collapse","menus","rounded","theme"])]),"side-extra-title":c(()=>[e(I).logo.enable?(u(),y(e(Wa),{key:0,fit:e(I).logo.fit,text:e(I).app.name,theme:e(b)},ut({_:2},[re.$slots["logo-text"]?{name:"text",fn:c(()=>[E(re.$slots,"logo-text")]),key:"0"}:void 0]),1032,["fit","text","theme"])):A("",!0)]),tabbar:c(()=>[e(I).tabbar.enable?(u(),y(e(kc),{key:0,"show-icon":e(I).tabbar.showIcon,theme:e(b)},null,8,["show-icon","theme"])):A("",!0)]),content:c(()=>[m(e(Lu))]),extra:c(()=>[E(re.$slots,"extra"),e(I).app.enableCheckUpdates?(u(),y(e(Ur),{key:0,"check-updates-interval":e(I).app.checkUpdatesInterval},null,8,["check-updates-interval"])):A("",!0),e(I).widget.lockScreen?(u(),y(at,{key:1,name:"slide-up"},{default:c(()=>[e(S).isLockScreen?E(re.$slots,"lock-screen",{key:0}):A("",!0)]),_:3})):A("",!0),e(v).fixed?(u(),y(e($l),{key:2,class:"z-100 fixed bottom-20 right-0",onClearPreferencesAndLogout:le})):A("",!0),m(e(tr))]),_:2},[e(I).transition.loading?{name:"content-overlay",fn:c(()=>[m(e(Mu))]),key:"0"}:void 0,e(I).footer.enable?{name:"footer",fn:c(()=>[m(e(Pu),null,{default:c(()=>[e(I).copyright.enable?(u(),y(e(Gn),He(fe({key:0},e(I).copyright)),null,16)):A("",!0)]),_:1})]),key:"1"}:void 0]),1032,["sidebar-extra-visible","content-compact","content-compact-width","content-padding","content-padding-bottom","content-padding-left","content-padding-right","content-padding-top","footer-enable","footer-fixed","footer-height","header-height","header-hidden","header-mode","header-theme","header-toggle-sidebar-button","header-visible","is-mobile","layout","sidebar-collapse","sidebar-collapse-show-title","sidebar-enable","sidebar-collapsed-button","sidebar-fixed-button","sidebar-expand-on-hover","sidebar-extra-collapse","sidebar-extra-collapsed-width","sidebar-hidden","sidebar-mixed-width","sidebar-theme","sidebar-width","side-collapse-width","tabbar-enable","tabbar-height","z-index","onSideMouseLeave"]))}});export{Pc as N,zc as _,Ac as a,Ic as b,Vu as c,Ir as d,Ur as e,ni as f,ui as g,pu as h,$l as i};
