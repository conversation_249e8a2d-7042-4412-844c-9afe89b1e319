var b=(m,u,n)=>new Promise((l,t)=>{var c=o=>{try{s(n.next(o))}catch(r){t(r)}},g=o=>{try{s(n.throw(o))}catch(r){t(r)}},s=o=>o.done?l(o.value):Promise.resolve(o.value).then(c,g);s((n=n.apply(m,u)).next())});import{aT as N,aS as e,aU as _}from"./bootstrap-DlHXJWd_.js";import{T as $}from"./auth-title-CuTIjiqG.js";import{u as y,s as v}from"./use-vben-form-D_KgkLrU.js";import{a4 as w,T as L,J as S,av as x,ab as C,x as T,aa as V,aq as P,ac as f,a8 as B,ai as p,aj as h,a7 as i,aB as A,aV as E,P as D}from"../jse/index-index-DYNcUVMZ.js";import"./render-content.vue_vue_type_script_lang-D2VaNJ52.js";const F={class:"text-muted-foreground"},q=w({name:"AuthenticationCodeLogin",__name:"code-login",props:{formSchema:{},loading:{type:Boolean,default:!1},loginPath:{default:"/auth/login"},title:{default:""},subTitle:{default:""},submitButtonText:{default:""},showBack:{type:Boolean,default:!0}},emits:["submit"],setup(m,{expose:u,emit:n}){const l=m,t=n,c=N(),[g,s]=y(L({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:S(()=>l.formSchema),showDefaultActions:!1}));function o(){return b(this,null,function*(){const{valid:a}=yield s.validate(),d=yield s.getValues();a&&t("submit",d)})}function r(){c.push(l.loginPath)}return u({getFormApi:()=>s}),(a,d)=>(C(),x("div",null,[T($,null,{desc:f(()=>[A("span",F,[B(a.$slots,"subTitle",{},()=>[p(h(a.subTitle||i(e)("authentication.codeSubtitle")),1)])])]),default:f(()=>[B(a.$slots,"title",{},()=>[p(h(a.title||i(e)("authentication.welcomeBack"))+" 📲 ",1)])]),_:3}),T(i(g)),T(i(_),{class:E([{"cursor-wait":a.loading},"w-full"]),loading:a.loading,onClick:o},{default:f(()=>[B(a.$slots,"submitButtonText",{},()=>[p(h(a.submitButtonText||i(e)("common.login")),1)])]),_:3},8,["class","loading"]),a.showBack?(C(),V(i(_),{key:0,class:"mt-4 w-full",variant:"outline",onClick:d[0]||(d[0]=I=>r())},{default:f(()=>[p(h(i(e)("common.back")),1)]),_:1})):P("",!0)]))}}),k=6,M=w({name:"CodeLogin",__name:"code-login",setup(m){const u=D(!1),n=S(()=>[{component:"VbenInput",componentProps:{placeholder:e("authentication.mobile")},fieldName:"phoneNumber",label:e("authentication.mobile"),rules:v().min(1,{message:e("authentication.mobileTip")}).refine(t=>/^\d{11}$/.test(t),{message:e("authentication.mobileErrortip")})},{component:"VbenPinInput",componentProps:{codeLength:k,createText:t=>t>0?e("authentication.sendText",[t]):e("authentication.sendCode"),placeholder:e("authentication.code")},fieldName:"code",label:e("authentication.code"),rules:v().length(k,{message:e("authentication.codeTip",[k])})}]);function l(t){return b(this,null,function*(){console.log(t)})}return(t,c)=>(C(),V(i(q),{"form-schema":n.value,loading:u.value,onSubmit:l},null,8,["form-schema","loading"]))}});export{M as default};
