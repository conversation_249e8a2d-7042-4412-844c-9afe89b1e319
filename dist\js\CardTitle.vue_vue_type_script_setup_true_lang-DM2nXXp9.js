import{a4 as r,av as t,ab as n,aV as c,a7 as o,aW as l,a8 as p}from"../jse/index-index-B2UBupFX.js";const u=r({__name:"Card",props:{class:{}},setup(s){const a=s;return(e,d)=>(n(),t("div",{class:c(o(l)("bg-card text-card-foreground border-border rounded-xl border",a.class))},[p(e.$slots,"default")],2))}}),f=r({__name:"CardContent",props:{class:{}},setup(s){const a=s;return(e,d)=>(n(),t("div",{class:c(o(l)("p-6 pt-0",a.class))},[p(e.$slots,"default")],2))}}),i=r({__name:"CardHeader",props:{class:{}},setup(s){const a=s;return(e,d)=>(n(),t("div",{class:c(o(l)("flex flex-col gap-y-1.5 p-5",a.class))},[p(e.$slots,"default")],2))}}),m=r({__name:"CardTitle",props:{class:{}},setup(s){const a=s;return(e,d)=>(n(),t("h3",{class:c(o(l)("font-semibold leading-none tracking-tight",a.class))},[p(e.$slots,"default")],2))}});export{i as _,m as a,f as b,u as c};
