import {
  defineVxeComponent,
  dynamicApp
} from "./chunk-KAYKK4JO.js";
import {
  require_xe_utils
} from "./chunk-RWHEUJNV.js";
import {
  VxeUI,
  createEvent,
  getConfig,
  useSize
} from "./chunk-KJAC55GV.js";
import "./chunk-G6CDOZZI.js";
import {
  h
} from "./chunk-ZCM5A7SR.js";
import "./chunk-WDDBQLJB.js";
import {
  __toESM
} from "./chunk-V4OQ3NZ2.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.9.29_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/icon/src/icon.js
var import_xe_utils = __toESM(require_xe_utils());
var icon_default = defineVxeComponent({
  name: "VxeIcon",
  props: {
    name: String,
    className: String,
    roll: Boolean,
    status: String,
    size: {
      type: String,
      default: () => getConfig().icon.size || getConfig().size
    }
  },
  emits: [
    "click"
  ],
  setup(props, context) {
    const { emit } = context;
    const xID = import_xe_utils.default.uniqueId();
    const { computeSize } = useSize(props);
    const $xeIcon = {
      xID,
      props,
      context
    };
    const clickEvent = (evnt) => {
      emit("click", createEvent(evnt, {}));
    };
    const dispatchEvent = (type, params, evnt) => {
      emit(type, createEvent(evnt, { $icon: $xeIcon }, params));
    };
    const iconMethods = {
      dispatchEvent
    };
    const iconPrivateMethods = {};
    Object.assign($xeIcon, iconMethods, iconPrivateMethods);
    const renderVN = () => {
      const { name, roll, status, className } = props;
      const vSize = computeSize.value;
      return h("i", {
        class: ["vxe-icon", `vxe-icon-${name}`, `${className || ""}`, {
          [`size--${vSize}`]: vSize,
          [`theme--${status}`]: status,
          roll
        }],
        onClick: clickEvent
      });
    };
    $xeIcon.renderVN = renderVN;
    return $xeIcon;
  },
  render() {
    return this.renderVN();
  }
});

// ../../node_modules/.pnpm/vxe-pc-ui@4.9.29_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/icon/index.js
var VxeIcon = Object.assign({}, icon_default, {
  install(app) {
    app.component(icon_default.name, icon_default);
  }
});
dynamicApp.use(VxeIcon);
VxeUI.component(icon_default);
var Icon = VxeIcon;
var icon_default2 = VxeIcon;

// ../../node_modules/.pnpm/vxe-pc-ui@4.9.29_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-icon/index.js
var vxe_icon_default = icon_default2;
export {
  Icon,
  VxeIcon,
  vxe_icon_default as default
};
//# sourceMappingURL=vxe-pc-ui_es_vxe-icon_index__js.js.map
