var v=(d,u,o)=>new Promise((r,t)=>{var l=n=>{try{c(o.next(n))}catch(m){t(m)}},i=n=>{try{c(o.throw(n))}catch(m){t(m)}},c=n=>n.done?r(n.value):Promise.resolve(n.value).then(l,i);c((o=o.apply(d,u)).next())});import{aT as V,aS as e,aU as y}from"./bootstrap-DlHXJWd_.js";import{T as B}from"./auth-title-CuTIjiqG.js";import{a4 as C,T as $,J as _,av as x,ab as k,x as g,aB as P,ac as b,a8 as T,ai as h,aj as p,a7 as s,aV as N,P as A,k as S,aa as F}from"../jse/index-index-DYNcUVMZ.js";import{u as R,s as w,b as I}from"./use-vben-form-D_KgkLrU.js";import"./render-content.vue_vue_type_script_lang-D2VaNJ52.js";const L={class:"mt-4 text-center text-sm"},U=C({name:"RegisterForm",__name:"register",props:{formSchema:{default:()=>[]},loading:{type:Boolean,default:!1},loginPath:{default:"/auth/login"},title:{default:""},subTitle:{default:""},submitButtonText:{default:""}},emits:["submit"],setup(d,{expose:u,emit:o}){const r=d,t=o,[l,i]=R($({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:_(()=>r.formSchema),showDefaultActions:!1})),c=V();function n(){return v(this,null,function*(){const{valid:a}=yield i.validate(),f=yield i.getValues();a&&t("submit",f)})}function m(){c.push(r.loginPath)}return u({getFormApi:()=>i}),(a,f)=>(k(),x("div",null,[g(B,null,{desc:b(()=>[T(a.$slots,"subTitle",{},()=>[h(p(a.subTitle||s(e)("authentication.signUpSubtitle")),1)])]),default:b(()=>[T(a.$slots,"title",{},()=>[h(p(a.title||s(e)("authentication.createAnAccount"))+" 🚀 ",1)])]),_:3}),g(s(l)),g(s(y),{class:N([{"cursor-wait":a.loading},"mt-2 w-full"]),loading:a.loading,"aria-label":"register",onClick:n},{default:b(()=>[T(a.$slots,"submitButtonText",{},()=>[h(p(a.submitButtonText||s(e)("authentication.signUp")),1)])]),_:3},8,["class","loading"]),P("div",L,[h(p(s(e)("authentication.alreadyHaveAccount"))+" ",1),P("span",{class:"vben-link text-sm font-normal",onClick:f[0]||(f[0]=q=>m())},p(s(e)("authentication.goToLogin")),1)])]))}}),M=C({name:"Register",__name:"register",setup(d){const u=A(!1),o=_(()=>[{component:"VbenInput",componentProps:{placeholder:e("authentication.usernameTip")},fieldName:"username",label:e("authentication.username"),rules:w().min(1,{message:e("authentication.usernameTip")})},{component:"VbenInputPassword",componentProps:{passwordStrength:!0,placeholder:e("authentication.password")},fieldName:"password",label:e("authentication.password"),renderComponentContent(){return{strengthText:()=>e("authentication.passwordStrength")}},rules:w().min(1,{message:e("authentication.passwordTip")})},{component:"VbenInputPassword",componentProps:{placeholder:e("authentication.confirmPassword")},dependencies:{rules(t){const{password:l}=t;return w({required_error:e("authentication.passwordTip")}).min(1,{message:e("authentication.passwordTip")}).refine(i=>i===l,{message:e("authentication.confirmPasswordTip")})},triggerFields:["password"]},fieldName:"confirmPassword",label:e("authentication.confirmPassword")},{component:"VbenCheckbox",fieldName:"agreePolicy",renderComponentContent:()=>({default:()=>S("span",[e("authentication.agree"),S("a",{class:"vben-link ml-1 ",href:""},`${e("authentication.privacyPolicy")} & ${e("authentication.terms")}`)])}),rules:I().refine(t=>!!t,{message:e("authentication.agreeTip")})}]);function r(t){console.log("register submit:",t)}return(t,l)=>(k(),F(s(U),{"form-schema":o.value,loading:u.value,onSubmit:r},null,8,["form-schema","loading"]))}});export{M as default};
