{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/space/index.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { Fragment as _Fragment, createVNode as _createVNode } from \"vue\";\nimport { defineComponent, computed, ref, watch, Fragment } from 'vue';\nimport PropTypes from '../_util/vue-types';\nimport { filterEmpty } from '../_util/props-util';\nimport { booleanType, tuple } from '../_util/type';\nimport useConfigInject from '../config-provider/hooks/useConfigInject';\nimport useFlexGapSupport from '../_util/hooks/useFlexGapSupport';\nimport classNames from '../_util/classNames';\nimport Compact from './Compact';\nimport useStyle from './style';\nconst spaceSize = {\n  small: 8,\n  middle: 16,\n  large: 24\n};\nexport const spaceProps = () => ({\n  prefixCls: String,\n  size: {\n    type: [String, Number, Array]\n  },\n  direction: PropTypes.oneOf(tuple('horizontal', 'vertical')).def('horizontal'),\n  align: PropTypes.oneOf(tuple('start', 'end', 'center', 'baseline')),\n  wrap: booleanType()\n});\nfunction getNumberSize(size) {\n  return typeof size === 'string' ? spaceSize[size] : size || 0;\n}\nconst Space = defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'ASpace',\n  inheritAttrs: false,\n  props: spaceProps(),\n  slots: Object,\n  setup(props, _ref) {\n    let {\n      slots,\n      attrs\n    } = _ref;\n    const {\n      prefixCls,\n      space,\n      direction: directionConfig\n    } = useConfigInject('space', props);\n    const [wrapSSR, hashId] = useStyle(prefixCls);\n    const supportFlexGap = useFlexGapSupport();\n    const size = computed(() => {\n      var _a, _b, _c;\n      return (_c = (_a = props.size) !== null && _a !== void 0 ? _a : (_b = space === null || space === void 0 ? void 0 : space.value) === null || _b === void 0 ? void 0 : _b.size) !== null && _c !== void 0 ? _c : 'small';\n    });\n    const horizontalSize = ref();\n    const verticalSize = ref();\n    watch(size, () => {\n      [horizontalSize.value, verticalSize.value] = (Array.isArray(size.value) ? size.value : [size.value, size.value]).map(item => getNumberSize(item));\n    }, {\n      immediate: true\n    });\n    const mergedAlign = computed(() => props.align === undefined && props.direction === 'horizontal' ? 'center' : props.align);\n    const cn = computed(() => {\n      return classNames(prefixCls.value, hashId.value, `${prefixCls.value}-${props.direction}`, {\n        [`${prefixCls.value}-rtl`]: directionConfig.value === 'rtl',\n        [`${prefixCls.value}-align-${mergedAlign.value}`]: mergedAlign.value\n      });\n    });\n    const marginDirection = computed(() => directionConfig.value === 'rtl' ? 'marginLeft' : 'marginRight');\n    const style = computed(() => {\n      const gapStyle = {};\n      if (supportFlexGap.value) {\n        gapStyle.columnGap = `${horizontalSize.value}px`;\n        gapStyle.rowGap = `${verticalSize.value}px`;\n      }\n      return _extends(_extends({}, gapStyle), props.wrap && {\n        flexWrap: 'wrap',\n        marginBottom: `${-verticalSize.value}px`\n      });\n    });\n    return () => {\n      var _a, _b;\n      const {\n        wrap,\n        direction = 'horizontal'\n      } = props;\n      const children = (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots);\n      const items = filterEmpty(children);\n      const len = items.length;\n      if (len === 0) {\n        return null;\n      }\n      const split = (_b = slots.split) === null || _b === void 0 ? void 0 : _b.call(slots);\n      const itemClassName = `${prefixCls.value}-item`;\n      const horizontalSizeVal = horizontalSize.value;\n      const latestIndex = len - 1;\n      return _createVNode(\"div\", _objectSpread(_objectSpread({}, attrs), {}, {\n        \"class\": [cn.value, attrs.class],\n        \"style\": [style.value, attrs.style]\n      }), [items.map((child, index) => {\n        let originIndex = children.indexOf(child);\n        if (originIndex === -1) {\n          originIndex = `$$space-${index}`;\n        }\n        let itemStyle = {};\n        if (!supportFlexGap.value) {\n          if (direction === 'vertical') {\n            if (index < latestIndex) {\n              itemStyle = {\n                marginBottom: `${horizontalSizeVal / (split ? 2 : 1)}px`\n              };\n            }\n          } else {\n            itemStyle = _extends(_extends({}, index < latestIndex && {\n              [marginDirection.value]: `${horizontalSizeVal / (split ? 2 : 1)}px`\n            }), wrap && {\n              paddingBottom: `${verticalSize.value}px`\n            });\n          }\n        }\n        return wrapSSR(_createVNode(_Fragment, {\n          \"key\": originIndex\n        }, [_createVNode(\"div\", {\n          \"class\": itemClassName,\n          \"style\": itemStyle\n        }, [child]), index < latestIndex && split && _createVNode(\"span\", {\n          \"class\": `${itemClassName}-split`,\n          \"style\": itemStyle\n        }, [split])]));\n      })]);\n    };\n  }\n});\nSpace.Compact = Compact;\nSpace.install = function (app) {\n  app.component(Space.name, Space);\n  app.component(Compact.name, Compact);\n  return app;\n};\nexport { Compact };\nexport default Space;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,IAAM,YAAY;AAAA,EAChB,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AACT;AACO,IAAM,aAAa,OAAO;AAAA,EAC/B,WAAW;AAAA,EACX,MAAM;AAAA,IACJ,MAAM,CAAC,QAAQ,QAAQ,KAAK;AAAA,EAC9B;AAAA,EACA,WAAW,kBAAU,MAAM,MAAM,cAAc,UAAU,CAAC,EAAE,IAAI,YAAY;AAAA,EAC5E,OAAO,kBAAU,MAAM,MAAM,SAAS,OAAO,UAAU,UAAU,CAAC;AAAA,EAClE,MAAM,YAAY;AACpB;AACA,SAAS,cAAc,MAAM;AAC3B,SAAO,OAAO,SAAS,WAAW,UAAU,IAAI,IAAI,QAAQ;AAC9D;AACA,IAAM,QAAQ,gBAAgB;AAAA,EAC5B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO,WAAW;AAAA,EAClB,OAAO;AAAA,EACP,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,WAAW;AAAA,IACb,IAAI,wBAAgB,SAAS,KAAK;AAClC,UAAM,CAAC,SAAS,MAAM,IAAI,cAAS,SAAS;AAC5C,UAAM,iBAAiB,0BAAkB;AACzC,UAAM,OAAO,SAAS,MAAM;AAC1B,UAAI,IAAI,IAAI;AACZ,cAAQ,MAAM,KAAK,MAAM,UAAU,QAAQ,OAAO,SAAS,MAAM,KAAK,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,QAAQ,OAAO,SAAS,KAAK;AAAA,IAClN,CAAC;AACD,UAAM,iBAAiB,IAAI;AAC3B,UAAM,eAAe,IAAI;AACzB,UAAM,MAAM,MAAM;AAChB,OAAC,eAAe,OAAO,aAAa,KAAK,KAAK,MAAM,QAAQ,KAAK,KAAK,IAAI,KAAK,QAAQ,CAAC,KAAK,OAAO,KAAK,KAAK,GAAG,IAAI,UAAQ,cAAc,IAAI,CAAC;AAAA,IAClJ,GAAG;AAAA,MACD,WAAW;AAAA,IACb,CAAC;AACD,UAAM,cAAc,SAAS,MAAM,MAAM,UAAU,UAAa,MAAM,cAAc,eAAe,WAAW,MAAM,KAAK;AACzH,UAAM,KAAK,SAAS,MAAM;AACxB,aAAO,mBAAW,UAAU,OAAO,OAAO,OAAO,GAAG,UAAU,KAAK,IAAI,MAAM,SAAS,IAAI;AAAA,QACxF,CAAC,GAAG,UAAU,KAAK,MAAM,GAAG,gBAAgB,UAAU;AAAA,QACtD,CAAC,GAAG,UAAU,KAAK,UAAU,YAAY,KAAK,EAAE,GAAG,YAAY;AAAA,MACjE,CAAC;AAAA,IACH,CAAC;AACD,UAAM,kBAAkB,SAAS,MAAM,gBAAgB,UAAU,QAAQ,eAAe,aAAa;AACrG,UAAM,QAAQ,SAAS,MAAM;AAC3B,YAAM,WAAW,CAAC;AAClB,UAAI,eAAe,OAAO;AACxB,iBAAS,YAAY,GAAG,eAAe,KAAK;AAC5C,iBAAS,SAAS,GAAG,aAAa,KAAK;AAAA,MACzC;AACA,aAAO,SAAS,SAAS,CAAC,GAAG,QAAQ,GAAG,MAAM,QAAQ;AAAA,QACpD,UAAU;AAAA,QACV,cAAc,GAAG,CAAC,aAAa,KAAK;AAAA,MACtC,CAAC;AAAA,IACH,CAAC;AACD,WAAO,MAAM;AACX,UAAI,IAAI;AACR,YAAM;AAAA,QACJ;AAAA,QACA,YAAY;AAAA,MACd,IAAI;AACJ,YAAM,YAAY,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK;AACxF,YAAM,QAAQ,YAAY,QAAQ;AAClC,YAAM,MAAM,MAAM;AAClB,UAAI,QAAQ,GAAG;AACb,eAAO;AAAA,MACT;AACA,YAAM,SAAS,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK;AACnF,YAAM,gBAAgB,GAAG,UAAU,KAAK;AACxC,YAAM,oBAAoB,eAAe;AACzC,YAAM,cAAc,MAAM;AAC1B,aAAO,YAAa,OAAO,eAAc,eAAc,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,QACrE,SAAS,CAAC,GAAG,OAAO,MAAM,KAAK;AAAA,QAC/B,SAAS,CAAC,MAAM,OAAO,MAAM,KAAK;AAAA,MACpC,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,OAAO,UAAU;AAC/B,YAAI,cAAc,SAAS,QAAQ,KAAK;AACxC,YAAI,gBAAgB,IAAI;AACtB,wBAAc,WAAW,KAAK;AAAA,QAChC;AACA,YAAI,YAAY,CAAC;AACjB,YAAI,CAAC,eAAe,OAAO;AACzB,cAAI,cAAc,YAAY;AAC5B,gBAAI,QAAQ,aAAa;AACvB,0BAAY;AAAA,gBACV,cAAc,GAAG,qBAAqB,QAAQ,IAAI,EAAE;AAAA,cACtD;AAAA,YACF;AAAA,UACF,OAAO;AACL,wBAAY,SAAS,SAAS,CAAC,GAAG,QAAQ,eAAe;AAAA,cACvD,CAAC,gBAAgB,KAAK,GAAG,GAAG,qBAAqB,QAAQ,IAAI,EAAE;AAAA,YACjE,CAAC,GAAG,QAAQ;AAAA,cACV,eAAe,GAAG,aAAa,KAAK;AAAA,YACtC,CAAC;AAAA,UACH;AAAA,QACF;AACA,eAAO,QAAQ,YAAa,UAAW;AAAA,UACrC,OAAO;AAAA,QACT,GAAG,CAAC,YAAa,OAAO;AAAA,UACtB,SAAS;AAAA,UACT,SAAS;AAAA,QACX,GAAG,CAAC,KAAK,CAAC,GAAG,QAAQ,eAAe,SAAS,YAAa,QAAQ;AAAA,UAChE,SAAS,GAAG,aAAa;AAAA,UACzB,SAAS;AAAA,QACX,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAAA,MACf,CAAC,CAAC,CAAC;AAAA,IACL;AAAA,EACF;AACF,CAAC;AACD,MAAM,UAAU;AAChB,MAAM,UAAU,SAAU,KAAK;AAC7B,MAAI,UAAU,MAAM,MAAM,KAAK;AAC/B,MAAI,UAAU,gBAAQ,MAAM,eAAO;AACnC,SAAO;AACT;AAEA,IAAO,gBAAQ;", "names": []}