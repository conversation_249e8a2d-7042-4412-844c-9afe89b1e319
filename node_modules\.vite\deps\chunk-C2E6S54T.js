import {
  collapseMotion_default
} from "./chunk-4BUGUT7V.js";
import {
  useRefs_default
} from "./chunk-WH7ZTZK5.js";
import {
  tooltip_default
} from "./chunk-7JPFEL2Z.js";
import {
  useFlexGapSupport_default
} from "./chunk-TVPNQ7YP.js";
import {
  CheckOutlined_default,
  pickAttrs
} from "./chunk-EHSREHUV.js";
import {
  CloseOutlined_default,
  useMergedState
} from "./chunk-U3O42KGD.js";
import {
  collapse_default,
  getTransitionGroupProps,
  getTransitionProps,
  zoomIn
} from "./chunk-QTSHFG3L.js";
import {
  EyeOutlined_default
} from "./chunk-JJ34MIXD.js";
import {
  CloseCircleFilled_default
} from "./chunk-YW5ZHBYG.js";
import {
  button_default
} from "./chunk-ONW6DHTT.js";
import {
  LoadingOutlined_default
} from "./chunk-2QTATBSH.js";
import {
  cloneDeep_default,
  debounce_default,
  find_default,
  intersection_default,
  omit_default,
  partition_default
} from "./chunk-UJKZ5JTW.js";
import {
  isEqual_default
} from "./chunk-5EKQCNNW.js";
import {
  AntdIcon_default
} from "./chunk-T7UIWOTR.js";
import {
  FormItemContext_default,
  FormItemInputContext,
  useInjectFormItemContext,
  useProvideFormItemContext
} from "./chunk-NBQQ65A6.js";
import {
  devWarning_default
} from "./chunk-B322KET6.js";
import {
  vue_types_default
} from "./chunk-CKTQP5WV.js";
import {
  Keyframes_default,
  _objectSpread2,
  anyType,
  arrayType,
  booleanType,
  classNames_default,
  clearFix,
  filterEmpty,
  flattenChildren,
  functionType,
  genComponentStyleHook,
  initDefaultProps_default,
  isValidElement,
  merge,
  objectType,
  presetPrimaryColors,
  resetComponent,
  someType,
  stringType,
  textEllipsis,
  tuple,
  useConfigInject_default,
  useInjectDisabled,
  useInjectGlobalForm,
  useLocaleReceiver,
  useProviderDisabled,
  useProviderSize,
  useToken,
  warning,
  warning_default2 as warning_default,
  withInstall
} from "./chunk-MFV6O37K.js";
import {
  en_US_default4 as en_US_default
} from "./chunk-UT5NYIUT.js";
import {
  _extends
} from "./chunk-LHAI6UAP.js";
import {
  TinyColor
} from "./chunk-DOLVIAJN.js";
import {
  Fragment,
  Transition,
  TransitionGroup,
  cloneVNode,
  computed,
  createVNode,
  defineComponent,
  inject,
  nextTick,
  onBeforeUnmount,
  onMounted,
  onUpdated,
  provide,
  reactive,
  ref,
  shallowRef,
  toRaw,
  toRef,
  triggerRef,
  unref,
  vShow,
  watch,
  watchEffect,
  withDirectives
} from "./chunk-ZCM5A7SR.js";

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-upload/request.js
function getError(option, xhr) {
  const msg = `cannot ${option.method} ${option.action} ${xhr.status}'`;
  const err = new Error(msg);
  err.status = xhr.status;
  err.method = option.method;
  err.url = option.action;
  return err;
}
function getBody(xhr) {
  const text = xhr.responseText || xhr.response;
  if (!text) {
    return text;
  }
  try {
    return JSON.parse(text);
  } catch (e2) {
    return text;
  }
}
function upload(option) {
  const xhr = new XMLHttpRequest();
  if (option.onProgress && xhr.upload) {
    xhr.upload.onprogress = function progress(e2) {
      if (e2.total > 0) {
        e2.percent = e2.loaded / e2.total * 100;
      }
      option.onProgress(e2);
    };
  }
  const formData = new FormData();
  if (option.data) {
    Object.keys(option.data).forEach((key) => {
      const value = option.data[key];
      if (Array.isArray(value)) {
        value.forEach((item) => {
          formData.append(`${key}[]`, item);
        });
        return;
      }
      formData.append(key, value);
    });
  }
  if (option.file instanceof Blob) {
    formData.append(option.filename, option.file, option.file.name);
  } else {
    formData.append(option.filename, option.file);
  }
  xhr.onerror = function error(e2) {
    option.onError(e2);
  };
  xhr.onload = function onload() {
    if (xhr.status < 200 || xhr.status >= 300) {
      return option.onError(getError(option, xhr), getBody(xhr));
    }
    return option.onSuccess(getBody(xhr), xhr);
  };
  xhr.open(option.method, option.action, true);
  if (option.withCredentials && "withCredentials" in xhr) {
    xhr.withCredentials = true;
  }
  const headers = option.headers || {};
  if (headers["X-Requested-With"] !== null) {
    xhr.setRequestHeader("X-Requested-With", "XMLHttpRequest");
  }
  Object.keys(headers).forEach((h) => {
    if (headers[h] !== null) {
      xhr.setRequestHeader(h, headers[h]);
    }
  });
  xhr.send(formData);
  return {
    abort() {
      xhr.abort();
    }
  };
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-upload/uid.js
var now = +/* @__PURE__ */ new Date();
var index = 0;
function uid() {
  return `vc-upload-${now}-${++index}`;
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-upload/attr-accept.js
var attr_accept_default = (file, acceptedFiles) => {
  if (file && acceptedFiles) {
    const acceptedFilesArray = Array.isArray(acceptedFiles) ? acceptedFiles : acceptedFiles.split(",");
    const fileName = file.name || "";
    const mimeType = file.type || "";
    const baseMimeType = mimeType.replace(/\/.*$/, "");
    return acceptedFilesArray.some((type4) => {
      const validType = type4.trim();
      if (/^\*(\/\*)?$/.test(type4)) {
        return true;
      }
      if (validType.charAt(0) === ".") {
        const lowerFileName = fileName.toLowerCase();
        const lowerType = validType.toLowerCase();
        let affixList = [lowerType];
        if (lowerType === ".jpg" || lowerType === ".jpeg") {
          affixList = [".jpg", ".jpeg"];
        }
        return affixList.some((affix) => lowerFileName.endsWith(affix));
      }
      if (/\/\*$/.test(validType)) {
        return baseMimeType === validType.replace(/\/.*$/, "");
      }
      if (mimeType === validType) {
        return true;
      }
      if (/^\w+$/.test(validType)) {
        warning(false, `Upload takes an invalidate 'accept' type '${validType}'.Skip for check.`);
        return true;
      }
      return false;
    });
  }
  return true;
};

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-upload/traverseFileTree.js
function loopFiles(item, callback) {
  const dirReader = item.createReader();
  let fileList = [];
  function sequence() {
    dirReader.readEntries((entries) => {
      const entryList = Array.prototype.slice.apply(entries);
      fileList = fileList.concat(entryList);
      const isFinished = !entryList.length;
      if (isFinished) {
        callback(fileList);
      } else {
        sequence();
      }
    });
  }
  sequence();
}
var traverseFileTree = (files, callback, isAccepted) => {
  const _traverseFileTree = (item, path) => {
    item.path = path || "";
    if (item.isFile) {
      item.file((file) => {
        if (isAccepted(file)) {
          if (item.fullPath && !file.webkitRelativePath) {
            Object.defineProperties(file, {
              webkitRelativePath: {
                writable: true
              }
            });
            file.webkitRelativePath = item.fullPath.replace(/^\//, "");
            Object.defineProperties(file, {
              webkitRelativePath: {
                writable: false
              }
            });
          }
          callback([file]);
        }
      });
    } else if (item.isDirectory) {
      loopFiles(item, (entries) => {
        entries.forEach((entryItem) => {
          _traverseFileTree(entryItem, `${path}${item.name}/`);
        });
      });
    }
  };
  files.forEach((file) => {
    _traverseFileTree(file.webkitGetAsEntry());
  });
};
var traverseFileTree_default = traverseFileTree;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-upload/interface.js
var uploadProps = () => {
  return {
    capture: [Boolean, String],
    multipart: {
      type: Boolean,
      default: void 0
    },
    name: String,
    disabled: {
      type: Boolean,
      default: void 0
    },
    componentTag: String,
    action: [String, Function],
    method: String,
    directory: {
      type: Boolean,
      default: void 0
    },
    data: [Object, Function],
    headers: Object,
    accept: String,
    multiple: {
      type: Boolean,
      default: void 0
    },
    onBatchStart: Function,
    onReject: Function,
    onStart: Function,
    onError: Function,
    onSuccess: Function,
    onProgress: Function,
    beforeUpload: Function,
    customRequest: Function,
    withCredentials: {
      type: Boolean,
      default: void 0
    },
    openFileDialogOnClick: {
      type: Boolean,
      default: void 0
    },
    prefixCls: String,
    id: String,
    onMouseenter: Function,
    onMouseleave: Function,
    onClick: Function
  };
};

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-upload/AjaxUploader.js
var __awaiter = function(thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function(resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function(resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e2) {
        reject(e2);
      }
    }
    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e2) {
        reject(e2);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
};
var __rest = function(s, e2) {
  var t2 = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e2.indexOf(p) < 0) t2[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i2 = 0, p = Object.getOwnPropertySymbols(s); i2 < p.length; i2++) {
    if (e2.indexOf(p[i2]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i2])) t2[p[i2]] = s[p[i2]];
  }
  return t2;
};
var AjaxUploader_default = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "AjaxUploader",
  inheritAttrs: false,
  props: uploadProps(),
  setup(props, _ref) {
    let {
      slots,
      attrs,
      expose
    } = _ref;
    const uid2 = ref(uid());
    const reqs = {};
    const fileInput = ref();
    let isMounted = false;
    const processFile = (file, fileList) => __awaiter(this, void 0, void 0, function* () {
      const {
        beforeUpload
      } = props;
      let transformedFile = file;
      if (beforeUpload) {
        try {
          transformedFile = yield beforeUpload(file, fileList);
        } catch (e2) {
          transformedFile = false;
        }
        if (transformedFile === false) {
          return {
            origin: file,
            parsedFile: null,
            action: null,
            data: null
          };
        }
      }
      const {
        action
      } = props;
      let mergedAction;
      if (typeof action === "function") {
        mergedAction = yield action(file);
      } else {
        mergedAction = action;
      }
      const {
        data
      } = props;
      let mergedData;
      if (typeof data === "function") {
        mergedData = yield data(file);
      } else {
        mergedData = data;
      }
      const parsedData = (
        // string type is from legacy `transformFile`.
        // Not sure if this will work since no related test case works with it
        (typeof transformedFile === "object" || typeof transformedFile === "string") && transformedFile ? transformedFile : file
      );
      let parsedFile;
      if (parsedData instanceof File) {
        parsedFile = parsedData;
      } else {
        parsedFile = new File([parsedData], file.name, {
          type: file.type
        });
      }
      const mergedParsedFile = parsedFile;
      mergedParsedFile.uid = file.uid;
      return {
        origin: file,
        data: mergedData,
        parsedFile: mergedParsedFile,
        action: mergedAction
      };
    });
    const post = (_ref2) => {
      let {
        data,
        origin,
        action,
        parsedFile
      } = _ref2;
      if (!isMounted) {
        return;
      }
      const {
        onStart,
        customRequest,
        name,
        headers,
        withCredentials,
        method: method4
      } = props;
      const {
        uid: uid3
      } = origin;
      const request = customRequest || upload;
      const requestOption = {
        action,
        filename: name,
        data,
        file: parsedFile,
        headers,
        withCredentials,
        method: method4 || "post",
        onProgress: (e2) => {
          const {
            onProgress
          } = props;
          onProgress === null || onProgress === void 0 ? void 0 : onProgress(e2, parsedFile);
        },
        onSuccess: (ret, xhr) => {
          const {
            onSuccess
          } = props;
          onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(ret, parsedFile, xhr);
          delete reqs[uid3];
        },
        onError: (err, ret) => {
          const {
            onError
          } = props;
          onError === null || onError === void 0 ? void 0 : onError(err, ret, parsedFile);
          delete reqs[uid3];
        }
      };
      onStart(origin);
      reqs[uid3] = request(requestOption);
    };
    const reset = () => {
      uid2.value = uid();
    };
    const abort = (file) => {
      if (file) {
        const uid3 = file.uid ? file.uid : file;
        if (reqs[uid3] && reqs[uid3].abort) {
          reqs[uid3].abort();
        }
        delete reqs[uid3];
      } else {
        Object.keys(reqs).forEach((uid3) => {
          if (reqs[uid3] && reqs[uid3].abort) {
            reqs[uid3].abort();
          }
          delete reqs[uid3];
        });
      }
    };
    onMounted(() => {
      isMounted = true;
    });
    onBeforeUnmount(() => {
      isMounted = false;
      abort();
    });
    const uploadFiles = (files) => {
      const originFiles = [...files];
      const postFiles = originFiles.map((file) => {
        file.uid = uid();
        return processFile(file, originFiles);
      });
      Promise.all(postFiles).then((fileList) => {
        const {
          onBatchStart
        } = props;
        onBatchStart === null || onBatchStart === void 0 ? void 0 : onBatchStart(fileList.map((_ref3) => {
          let {
            origin,
            parsedFile
          } = _ref3;
          return {
            file: origin,
            parsedFile
          };
        }));
        fileList.filter((file) => file.parsedFile !== null).forEach((file) => {
          post(file);
        });
      });
    };
    const onChange = (e2) => {
      const {
        accept,
        directory
      } = props;
      const {
        files
      } = e2.target;
      const acceptedFiles = [...files].filter((file) => !directory || attr_accept_default(file, accept));
      uploadFiles(acceptedFiles);
      reset();
    };
    const onClick = (e2) => {
      const el = fileInput.value;
      if (!el) {
        return;
      }
      const {
        onClick: onClick2
      } = props;
      el.click();
      if (onClick2) {
        onClick2(e2);
      }
    };
    const onKeyDown = (e2) => {
      if (e2.key === "Enter") {
        onClick(e2);
      }
    };
    const onFileDrop = (e2) => {
      const {
        multiple
      } = props;
      e2.preventDefault();
      if (e2.type === "dragover") {
        return;
      }
      if (props.directory) {
        traverseFileTree_default(Array.prototype.slice.call(e2.dataTransfer.items), uploadFiles, (_file) => attr_accept_default(_file, props.accept));
      } else {
        const files = partition_default(Array.prototype.slice.call(e2.dataTransfer.files), (file) => attr_accept_default(file, props.accept));
        let successFiles = files[0];
        const errorFiles = files[1];
        if (multiple === false) {
          successFiles = successFiles.slice(0, 1);
        }
        uploadFiles(successFiles);
        if (errorFiles.length && props.onReject) props.onReject(errorFiles);
      }
    };
    expose({
      abort
    });
    return () => {
      var _a;
      const {
        componentTag: Tag,
        prefixCls,
        disabled,
        id,
        multiple,
        accept,
        capture,
        directory,
        openFileDialogOnClick,
        onMouseenter,
        onMouseleave
      } = props, otherProps = __rest(props, ["componentTag", "prefixCls", "disabled", "id", "multiple", "accept", "capture", "directory", "openFileDialogOnClick", "onMouseenter", "onMouseleave"]);
      const cls = {
        [prefixCls]: true,
        [`${prefixCls}-disabled`]: disabled,
        [attrs.class]: !!attrs.class
      };
      const dirProps = directory ? {
        directory: "directory",
        webkitdirectory: "webkitdirectory"
      } : {};
      const events = disabled ? {} : {
        onClick: openFileDialogOnClick ? onClick : () => {
        },
        onKeydown: openFileDialogOnClick ? onKeyDown : () => {
        },
        onMouseenter,
        onMouseleave,
        onDrop: onFileDrop,
        onDragover: onFileDrop,
        tabindex: "0"
      };
      return createVNode(Tag, _objectSpread2(_objectSpread2({}, events), {}, {
        "class": cls,
        "role": "button",
        "style": attrs.style
      }), {
        default: () => [createVNode("input", _objectSpread2(_objectSpread2(_objectSpread2({}, pickAttrs(otherProps, {
          aria: true,
          data: true
        })), {}, {
          "id": id,
          "type": "file",
          "ref": fileInput,
          "onClick": (e2) => e2.stopPropagation(),
          "onCancel": (e2) => e2.stopPropagation(),
          "key": uid2.value,
          "style": {
            display: "none"
          },
          "accept": accept
        }, dirProps), {}, {
          "multiple": multiple,
          "onChange": onChange
        }, capture != null ? {
          capture
        } : {}), null), (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots)]
      });
    };
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-upload/Upload.js
function empty() {
}
var Upload_default = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "Upload",
  inheritAttrs: false,
  props: initDefaultProps_default(uploadProps(), {
    componentTag: "span",
    prefixCls: "rc-upload",
    data: {},
    headers: {},
    name: "file",
    multipart: false,
    onStart: empty,
    onError: empty,
    onSuccess: empty,
    multiple: false,
    beforeUpload: null,
    customRequest: null,
    withCredentials: false,
    openFileDialogOnClick: true
  }),
  setup(props, _ref) {
    let {
      slots,
      attrs,
      expose
    } = _ref;
    const uploader = ref();
    const abort = (file) => {
      var _a;
      (_a = uploader.value) === null || _a === void 0 ? void 0 : _a.abort(file);
    };
    expose({
      abort
    });
    return () => {
      return createVNode(AjaxUploader_default, _objectSpread2(_objectSpread2(_objectSpread2({}, props), attrs), {}, {
        "ref": uploader
      }), slots);
    };
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-upload/index.js
var vc_upload_default = Upload_default;

// ../../node_modules/.pnpm/@ant-design+icons-svg@4.4.2/node_modules/@ant-design/icons-svg/es/asn/PaperClipOutlined.js
var PaperClipOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M779.3 196.6c-94.2-94.2-247.6-94.2-341.7 0l-261 260.8c-1.7 1.7-2.6 4-2.6 6.4s.9 4.7 2.6 6.4l36.9 36.9a9 9 0 0012.7 0l261-260.8c32.4-32.4 75.5-50.2 121.3-50.2s88.9 17.8 121.2 50.2c32.4 32.4 50.2 75.5 50.2 121.2 0 45.8-17.8 88.8-50.2 121.2l-266 265.9-43.1 43.1c-40.3 40.3-105.8 40.3-146.1 0-19.5-19.5-30.2-45.4-30.2-73s10.7-53.5 30.2-73l263.9-263.8c6.7-6.6 15.5-10.3 24.9-10.3h.1c9.4 0 18.1 3.7 24.7 10.3 6.7 6.7 10.3 15.5 10.3 24.9 0 9.3-3.7 18.1-10.3 24.7L372.4 653c-1.7 1.7-2.6 4-2.6 6.4s.9 4.7 2.6 6.4l36.9 36.9a9 9 0 0012.7 0l215.6-215.6c19.9-19.9 30.8-46.3 30.8-74.4s-11-54.6-30.8-74.4c-41.1-41.1-107.9-41-149 0L463 364 224.8 602.1A172.22 172.22 0 00174 724.8c0 46.3 18.1 89.8 50.8 122.5 33.9 33.8 78.3 50.7 122.7 50.7 44.4 0 88.8-16.9 122.6-50.7l309.2-309C824.8 492.7 850 432 850 367.5c.1-64.6-25.1-125.3-70.7-170.9z" } }] }, "name": "paper-clip", "theme": "outlined" };
var PaperClipOutlined_default = PaperClipOutlined;

// ../../node_modules/.pnpm/@ant-design+icons-vue@7.0.1_vue@3.5.17_typescript@5.8.3_/node_modules/@ant-design/icons-vue/es/icons/PaperClipOutlined.js
function _objectSpread(target) {
  for (var i2 = 1; i2 < arguments.length; i2++) {
    var source = arguments[i2] != null ? Object(arguments[i2]) : {};
    var ownKeys = Object.keys(source);
    if (typeof Object.getOwnPropertySymbols === "function") {
      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
        return Object.getOwnPropertyDescriptor(source, sym).enumerable;
      }));
    }
    ownKeys.forEach(function(key) {
      _defineProperty(target, key, source[key]);
    });
  }
  return target;
}
function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, { value, enumerable: true, configurable: true, writable: true });
  } else {
    obj[key] = value;
  }
  return obj;
}
var PaperClipOutlined2 = function PaperClipOutlined3(props, context) {
  var p = _objectSpread({}, props, context.attrs);
  return createVNode(AntdIcon_default, _objectSpread({}, p, {
    "icon": PaperClipOutlined_default
  }), null);
};
PaperClipOutlined2.displayName = "PaperClipOutlined";
PaperClipOutlined2.inheritAttrs = false;
var PaperClipOutlined_default2 = PaperClipOutlined2;

// ../../node_modules/.pnpm/@ant-design+icons-svg@4.4.2/node_modules/@ant-design/icons-svg/es/asn/PictureTwoTone.js
var PictureTwoTone = { "icon": function render(primaryColor, secondaryColor) {
  return { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 632H136v-39.9l138.5-164.3 150.1 178L658.1 489 888 761.6V792zm0-129.8L664.2 396.8c-3.2-3.8-9-3.8-12.2 0L424.6 666.4l-144-170.7c-3.2-3.8-9-3.8-12.2 0L136 652.7V232h752v430.2z", "fill": primaryColor } }, { "tag": "path", "attrs": { "d": "M424.6 765.8l-150.1-178L136 752.1V792h752v-30.4L658.1 489z", "fill": secondaryColor } }, { "tag": "path", "attrs": { "d": "M136 652.7l132.4-157c3.2-3.8 9-3.8 12.2 0l144 170.7L652 396.8c3.2-3.8 9-3.8 12.2 0L888 662.2V232H136v420.7zM304 280a88 88 0 110 176 88 88 0 010-176z", "fill": secondaryColor } }, { "tag": "path", "attrs": { "d": "M276 368a28 28 0 1056 0 28 28 0 10-56 0z", "fill": secondaryColor } }, { "tag": "path", "attrs": { "d": "M304 456a88 88 0 100-176 88 88 0 000 176zm0-116c15.5 0 28 12.5 28 28s-12.5 28-28 28-28-12.5-28-28 12.5-28 28-28z", "fill": primaryColor } }] };
}, "name": "picture", "theme": "twotone" };
var PictureTwoTone_default = PictureTwoTone;

// ../../node_modules/.pnpm/@ant-design+icons-vue@7.0.1_vue@3.5.17_typescript@5.8.3_/node_modules/@ant-design/icons-vue/es/icons/PictureTwoTone.js
function _objectSpread3(target) {
  for (var i2 = 1; i2 < arguments.length; i2++) {
    var source = arguments[i2] != null ? Object(arguments[i2]) : {};
    var ownKeys = Object.keys(source);
    if (typeof Object.getOwnPropertySymbols === "function") {
      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
        return Object.getOwnPropertyDescriptor(source, sym).enumerable;
      }));
    }
    ownKeys.forEach(function(key) {
      _defineProperty2(target, key, source[key]);
    });
  }
  return target;
}
function _defineProperty2(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, { value, enumerable: true, configurable: true, writable: true });
  } else {
    obj[key] = value;
  }
  return obj;
}
var PictureTwoTone2 = function PictureTwoTone3(props, context) {
  var p = _objectSpread3({}, props, context.attrs);
  return createVNode(AntdIcon_default, _objectSpread3({}, p, {
    "icon": PictureTwoTone_default
  }), null);
};
PictureTwoTone2.displayName = "PictureTwoTone";
PictureTwoTone2.inheritAttrs = false;
var PictureTwoTone_default2 = PictureTwoTone2;

// ../../node_modules/.pnpm/@ant-design+icons-svg@4.4.2/node_modules/@ant-design/icons-svg/es/asn/FileTwoTone.js
var FileTwoTone = { "icon": function render2(primaryColor, secondaryColor) {
  return { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M534 352V136H232v752h560V394H576a42 42 0 01-42-42z", "fill": secondaryColor } }, { "tag": "path", "attrs": { "d": "M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM602 137.8L790.2 326H602V137.8zM792 888H232V136h302v216a42 42 0 0042 42h216v494z", "fill": primaryColor } }] };
}, "name": "file", "theme": "twotone" };
var FileTwoTone_default = FileTwoTone;

// ../../node_modules/.pnpm/@ant-design+icons-vue@7.0.1_vue@3.5.17_typescript@5.8.3_/node_modules/@ant-design/icons-vue/es/icons/FileTwoTone.js
function _objectSpread4(target) {
  for (var i2 = 1; i2 < arguments.length; i2++) {
    var source = arguments[i2] != null ? Object(arguments[i2]) : {};
    var ownKeys = Object.keys(source);
    if (typeof Object.getOwnPropertySymbols === "function") {
      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
        return Object.getOwnPropertyDescriptor(source, sym).enumerable;
      }));
    }
    ownKeys.forEach(function(key) {
      _defineProperty3(target, key, source[key]);
    });
  }
  return target;
}
function _defineProperty3(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, { value, enumerable: true, configurable: true, writable: true });
  } else {
    obj[key] = value;
  }
  return obj;
}
var FileTwoTone2 = function FileTwoTone3(props, context) {
  var p = _objectSpread4({}, props, context.attrs);
  return createVNode(AntdIcon_default, _objectSpread4({}, p, {
    "icon": FileTwoTone_default
  }), null);
};
FileTwoTone2.displayName = "FileTwoTone";
FileTwoTone2.inheritAttrs = false;
var FileTwoTone_default2 = FileTwoTone2;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/upload/interface.js
function uploadProps2() {
  return {
    capture: someType([Boolean, String]),
    type: stringType(),
    name: String,
    defaultFileList: arrayType(),
    fileList: arrayType(),
    action: someType([String, Function]),
    directory: booleanType(),
    data: someType([Object, Function]),
    method: stringType(),
    headers: objectType(),
    showUploadList: someType([Boolean, Object]),
    multiple: booleanType(),
    accept: String,
    beforeUpload: functionType(),
    onChange: functionType(),
    "onUpdate:fileList": functionType(),
    onDrop: functionType(),
    listType: stringType(),
    onPreview: functionType(),
    onDownload: functionType(),
    onReject: functionType(),
    onRemove: functionType(),
    /** @deprecated Please use `onRemove` directly */
    remove: functionType(),
    supportServerRender: booleanType(),
    disabled: booleanType(),
    prefixCls: String,
    customRequest: functionType(),
    withCredentials: booleanType(),
    openFileDialogOnClick: booleanType(),
    locale: objectType(),
    id: String,
    previewFile: functionType(),
    /** @deprecated Please use `beforeUpload` directly */
    transformFile: functionType(),
    iconRender: functionType(),
    isImageUrl: functionType(),
    progress: objectType(),
    itemRender: functionType(),
    /** Config max count of `fileList`. Will replace current one when `maxCount` is 1 */
    maxCount: Number,
    height: someType([Number, String]),
    removeIcon: functionType(),
    downloadIcon: functionType(),
    previewIcon: functionType()
  };
}
function uploadListProps() {
  return {
    listType: stringType(),
    onPreview: functionType(),
    onDownload: functionType(),
    onRemove: functionType(),
    items: arrayType(),
    progress: objectType(),
    prefixCls: stringType(),
    showRemoveIcon: booleanType(),
    showDownloadIcon: booleanType(),
    showPreviewIcon: booleanType(),
    removeIcon: functionType(),
    downloadIcon: functionType(),
    previewIcon: functionType(),
    locale: objectType(void 0),
    previewFile: functionType(),
    iconRender: functionType(),
    isImageUrl: functionType(),
    appendAction: functionType(),
    appendActionVisible: booleanType(),
    itemRender: functionType()
  };
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/upload/utils.js
function file2Obj(file) {
  return _extends(_extends({}, file), {
    lastModified: file.lastModified,
    lastModifiedDate: file.lastModifiedDate,
    name: file.name,
    size: file.size,
    type: file.type,
    uid: file.uid,
    percent: 0,
    originFileObj: file
  });
}
function updateFileList(file, fileList) {
  const nextFileList = [...fileList];
  const fileIndex = nextFileList.findIndex((_ref) => {
    let {
      uid: uid2
    } = _ref;
    return uid2 === file.uid;
  });
  if (fileIndex === -1) {
    nextFileList.push(file);
  } else {
    nextFileList[fileIndex] = file;
  }
  return nextFileList;
}
function getFileItem(file, fileList) {
  const matchKey = file.uid !== void 0 ? "uid" : "name";
  return fileList.filter((item) => item[matchKey] === file[matchKey])[0];
}
function removeFileItem(file, fileList) {
  const matchKey = file.uid !== void 0 ? "uid" : "name";
  const removed = fileList.filter((item) => item[matchKey] !== file[matchKey]);
  if (removed.length === fileList.length) {
    return null;
  }
  return removed;
}
var extname = function() {
  let url2 = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : "";
  const temp = url2.split("/");
  const filename = temp[temp.length - 1];
  const filenameWithoutSuffix = filename.split(/#|\?/)[0];
  return (/\.[^./\\]*$/.exec(filenameWithoutSuffix) || [""])[0];
};
var isImageFileType = (type4) => type4.indexOf("image/") === 0;
var isImageUrl = (file) => {
  if (file.type && !file.thumbUrl) {
    return isImageFileType(file.type);
  }
  const url2 = file.thumbUrl || file.url || "";
  const extension = extname(url2);
  if (/^data:image\//.test(url2) || /(webp|svg|png|gif|jpg|jpeg|jfif|bmp|dpg|ico)$/i.test(extension)) {
    return true;
  }
  if (/^data:/.test(url2)) {
    return false;
  }
  if (extension) {
    return false;
  }
  return true;
};
var MEASURE_SIZE = 200;
function previewImage(file) {
  return new Promise((resolve) => {
    if (!file.type || !isImageFileType(file.type)) {
      resolve("");
      return;
    }
    const canvas = document.createElement("canvas");
    canvas.width = MEASURE_SIZE;
    canvas.height = MEASURE_SIZE;
    canvas.style.cssText = `position: fixed; left: 0; top: 0; width: ${MEASURE_SIZE}px; height: ${MEASURE_SIZE}px; z-index: 9999; display: none;`;
    document.body.appendChild(canvas);
    const ctx = canvas.getContext("2d");
    const img = new Image();
    img.onload = () => {
      const {
        width,
        height
      } = img;
      let drawWidth = MEASURE_SIZE;
      let drawHeight = MEASURE_SIZE;
      let offsetX = 0;
      let offsetY = 0;
      if (width > height) {
        drawHeight = height * (MEASURE_SIZE / width);
        offsetY = -(drawHeight - drawWidth) / 2;
      } else {
        drawWidth = width * (MEASURE_SIZE / height);
        offsetX = -(drawWidth - drawHeight) / 2;
      }
      ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);
      const dataURL = canvas.toDataURL();
      document.body.removeChild(canvas);
      resolve(dataURL);
    };
    img.crossOrigin = "anonymous";
    if (file.type.startsWith("image/svg+xml")) {
      const reader = new FileReader();
      reader.addEventListener("load", () => {
        if (reader.result) img.src = reader.result;
      });
      reader.readAsDataURL(file);
    } else {
      img.src = window.URL.createObjectURL(file);
    }
  });
}

// ../../node_modules/.pnpm/@ant-design+icons-svg@4.4.2/node_modules/@ant-design/icons-svg/es/asn/DeleteOutlined.js
var DeleteOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z" } }] }, "name": "delete", "theme": "outlined" };
var DeleteOutlined_default = DeleteOutlined;

// ../../node_modules/.pnpm/@ant-design+icons-vue@7.0.1_vue@3.5.17_typescript@5.8.3_/node_modules/@ant-design/icons-vue/es/icons/DeleteOutlined.js
function _objectSpread5(target) {
  for (var i2 = 1; i2 < arguments.length; i2++) {
    var source = arguments[i2] != null ? Object(arguments[i2]) : {};
    var ownKeys = Object.keys(source);
    if (typeof Object.getOwnPropertySymbols === "function") {
      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
        return Object.getOwnPropertyDescriptor(source, sym).enumerable;
      }));
    }
    ownKeys.forEach(function(key) {
      _defineProperty4(target, key, source[key]);
    });
  }
  return target;
}
function _defineProperty4(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, { value, enumerable: true, configurable: true, writable: true });
  } else {
    obj[key] = value;
  }
  return obj;
}
var DeleteOutlined2 = function DeleteOutlined3(props, context) {
  var p = _objectSpread5({}, props, context.attrs);
  return createVNode(AntdIcon_default, _objectSpread5({}, p, {
    "icon": DeleteOutlined_default
  }), null);
};
DeleteOutlined2.displayName = "DeleteOutlined";
DeleteOutlined2.inheritAttrs = false;
var DeleteOutlined_default2 = DeleteOutlined2;

// ../../node_modules/.pnpm/@ant-design+icons-svg@4.4.2/node_modules/@ant-design/icons-svg/es/asn/DownloadOutlined.js
var DownloadOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z" } }] }, "name": "download", "theme": "outlined" };
var DownloadOutlined_default = DownloadOutlined;

// ../../node_modules/.pnpm/@ant-design+icons-vue@7.0.1_vue@3.5.17_typescript@5.8.3_/node_modules/@ant-design/icons-vue/es/icons/DownloadOutlined.js
function _objectSpread6(target) {
  for (var i2 = 1; i2 < arguments.length; i2++) {
    var source = arguments[i2] != null ? Object(arguments[i2]) : {};
    var ownKeys = Object.keys(source);
    if (typeof Object.getOwnPropertySymbols === "function") {
      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
        return Object.getOwnPropertyDescriptor(source, sym).enumerable;
      }));
    }
    ownKeys.forEach(function(key) {
      _defineProperty5(target, key, source[key]);
    });
  }
  return target;
}
function _defineProperty5(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, { value, enumerable: true, configurable: true, writable: true });
  } else {
    obj[key] = value;
  }
  return obj;
}
var DownloadOutlined2 = function DownloadOutlined3(props, context) {
  var p = _objectSpread6({}, props, context.attrs);
  return createVNode(AntdIcon_default, _objectSpread6({}, p, {
    "icon": DownloadOutlined_default
  }), null);
};
DownloadOutlined2.displayName = "DownloadOutlined";
DownloadOutlined2.inheritAttrs = false;
var DownloadOutlined_default2 = DownloadOutlined2;

// ../../node_modules/.pnpm/@ant-design+icons-svg@4.4.2/node_modules/@ant-design/icons-svg/es/asn/CheckCircleFilled.js
var CheckCircleFilled = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z" } }] }, "name": "check-circle", "theme": "filled" };
var CheckCircleFilled_default = CheckCircleFilled;

// ../../node_modules/.pnpm/@ant-design+icons-vue@7.0.1_vue@3.5.17_typescript@5.8.3_/node_modules/@ant-design/icons-vue/es/icons/CheckCircleFilled.js
function _objectSpread7(target) {
  for (var i2 = 1; i2 < arguments.length; i2++) {
    var source = arguments[i2] != null ? Object(arguments[i2]) : {};
    var ownKeys = Object.keys(source);
    if (typeof Object.getOwnPropertySymbols === "function") {
      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
        return Object.getOwnPropertyDescriptor(source, sym).enumerable;
      }));
    }
    ownKeys.forEach(function(key) {
      _defineProperty6(target, key, source[key]);
    });
  }
  return target;
}
function _defineProperty6(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, { value, enumerable: true, configurable: true, writable: true });
  } else {
    obj[key] = value;
  }
  return obj;
}
var CheckCircleFilled2 = function CheckCircleFilled3(props, context) {
  var p = _objectSpread7({}, props, context.attrs);
  return createVNode(AntdIcon_default, _objectSpread7({}, p, {
    "icon": CheckCircleFilled_default
  }), null);
};
CheckCircleFilled2.displayName = "CheckCircleFilled";
CheckCircleFilled2.inheritAttrs = false;
var CheckCircleFilled_default2 = CheckCircleFilled2;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/progress/props.js
var progressStatuses = ["normal", "exception", "active", "success"];
var progressProps = () => ({
  prefixCls: String,
  type: stringType(),
  percent: Number,
  format: functionType(),
  status: stringType(),
  showInfo: booleanType(),
  strokeWidth: Number,
  strokeLinecap: stringType(),
  strokeColor: anyType(),
  trailColor: String,
  /** @deprecated Use `size` instead */
  width: Number,
  success: objectType(),
  gapDegree: Number,
  gapPosition: stringType(),
  size: someType([String, Number, Array]),
  steps: Number,
  /** @deprecated Use `success` instead */
  successPercent: Number,
  title: String,
  progressStatus: stringType()
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/progress/utils.js
function validProgress(progress) {
  if (!progress || progress < 0) {
    return 0;
  }
  if (progress > 100) {
    return 100;
  }
  return progress;
}
function getSuccessPercent(_ref) {
  let {
    success,
    successPercent
  } = _ref;
  let percent = successPercent;
  if (success && "progress" in success) {
    devWarning_default(false, "Progress", "`success.progress` is deprecated. Please use `success.percent` instead.");
    percent = success.progress;
  }
  if (success && "percent" in success) {
    percent = success.percent;
  }
  return percent;
}
function getPercentage(_ref2) {
  let {
    percent,
    success,
    successPercent
  } = _ref2;
  const realSuccessPercent = validProgress(getSuccessPercent({
    success,
    successPercent
  }));
  return [realSuccessPercent, validProgress(validProgress(percent) - realSuccessPercent)];
}
function getStrokeColor(_ref3) {
  let {
    success = {},
    strokeColor
  } = _ref3;
  const {
    strokeColor: successColor
  } = success;
  return [successColor || presetPrimaryColors.green, strokeColor || null];
}
var getSize = (size, type4, extra) => {
  var _a, _b, _c, _d;
  let width = -1;
  let height = -1;
  if (type4 === "step") {
    const steps = extra.steps;
    const strokeWidth = extra.strokeWidth;
    if (typeof size === "string" || typeof size === "undefined") {
      width = size === "small" ? 2 : 14;
      height = strokeWidth !== null && strokeWidth !== void 0 ? strokeWidth : 8;
    } else if (typeof size === "number") {
      [width, height] = [size, size];
    } else {
      [width = 14, height = 8] = size;
    }
    width *= steps;
  } else if (type4 === "line") {
    const strokeWidth = extra === null || extra === void 0 ? void 0 : extra.strokeWidth;
    if (typeof size === "string" || typeof size === "undefined") {
      height = strokeWidth || (size === "small" ? 6 : 8);
    } else if (typeof size === "number") {
      [width, height] = [size, size];
    } else {
      [width = -1, height = 8] = size;
    }
  } else if (type4 === "circle" || type4 === "dashboard") {
    if (typeof size === "string" || typeof size === "undefined") {
      [width, height] = size === "small" ? [60, 60] : [120, 120];
    } else if (typeof size === "number") {
      [width, height] = [size, size];
    } else {
      if (true) {
        devWarning_default(false, "Progress", 'Type "circle" and "dashboard" do not accept array as `size`, please use number or preset size instead.');
      }
      width = (_b = (_a = size[0]) !== null && _a !== void 0 ? _a : size[1]) !== null && _b !== void 0 ? _b : 120;
      height = (_d = (_c = size[0]) !== null && _c !== void 0 ? _c : size[1]) !== null && _d !== void 0 ? _d : 120;
    }
  }
  return {
    width,
    height
  };
};

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/progress/Line.js
var __rest2 = function(s, e2) {
  var t2 = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e2.indexOf(p) < 0) t2[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i2 = 0, p = Object.getOwnPropertySymbols(s); i2 < p.length; i2++) {
    if (e2.indexOf(p[i2]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i2])) t2[p[i2]] = s[p[i2]];
  }
  return t2;
};
var lineProps = () => _extends(_extends({}, progressProps()), {
  strokeColor: anyType(),
  direction: stringType()
});
var sortGradient = (gradients) => {
  let tempArr = [];
  Object.keys(gradients).forEach((key) => {
    const formattedKey = parseFloat(key.replace(/%/g, ""));
    if (!isNaN(formattedKey)) {
      tempArr.push({
        key: formattedKey,
        value: gradients[key]
      });
    }
  });
  tempArr = tempArr.sort((a, b) => a.key - b.key);
  return tempArr.map((_ref) => {
    let {
      key,
      value
    } = _ref;
    return `${value} ${key}%`;
  }).join(", ");
};
var handleGradient = (strokeColor, directionConfig) => {
  const {
    from = presetPrimaryColors.blue,
    to = presetPrimaryColors.blue,
    direction = directionConfig === "rtl" ? "to left" : "to right"
  } = strokeColor, rest = __rest2(strokeColor, ["from", "to", "direction"]);
  if (Object.keys(rest).length !== 0) {
    const sortedGradients = sortGradient(rest);
    return {
      backgroundImage: `linear-gradient(${direction}, ${sortedGradients})`
    };
  }
  return {
    backgroundImage: `linear-gradient(${direction}, ${from}, ${to})`
  };
};
var Line_default = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "ProgressLine",
  inheritAttrs: false,
  props: lineProps(),
  setup(props, _ref2) {
    let {
      slots,
      attrs
    } = _ref2;
    const backgroundProps = computed(() => {
      const {
        strokeColor,
        direction
      } = props;
      return strokeColor && typeof strokeColor !== "string" ? handleGradient(strokeColor, direction) : {
        backgroundColor: strokeColor
      };
    });
    const borderRadius = computed(() => props.strokeLinecap === "square" || props.strokeLinecap === "butt" ? 0 : void 0);
    const trailStyle = computed(() => props.trailColor ? {
      backgroundColor: props.trailColor
    } : void 0);
    const mergedSize = computed(() => {
      var _a;
      return (_a = props.size) !== null && _a !== void 0 ? _a : [-1, props.strokeWidth || (props.size === "small" ? 6 : 8)];
    });
    const sizeRef = computed(() => getSize(mergedSize.value, "line", {
      strokeWidth: props.strokeWidth
    }));
    if (true) {
      devWarning_default("strokeWidth" in props, "Progress", "`strokeWidth` is deprecated. Please use `size` instead.");
    }
    const percentStyle = computed(() => {
      const {
        percent
      } = props;
      return _extends({
        width: `${validProgress(percent)}%`,
        height: `${sizeRef.value.height}px`,
        borderRadius: borderRadius.value
      }, backgroundProps.value);
    });
    const successPercent = computed(() => {
      return getSuccessPercent(props);
    });
    const successPercentStyle = computed(() => {
      const {
        success
      } = props;
      return {
        width: `${validProgress(successPercent.value)}%`,
        height: `${sizeRef.value.height}px`,
        borderRadius: borderRadius.value,
        backgroundColor: success === null || success === void 0 ? void 0 : success.strokeColor
      };
    });
    const outerStyle = {
      width: sizeRef.value.width < 0 ? "100%" : sizeRef.value.width,
      height: `${sizeRef.value.height}px`
    };
    return () => {
      var _a;
      return createVNode(Fragment, null, [createVNode("div", _objectSpread2(_objectSpread2({}, attrs), {}, {
        "class": [`${props.prefixCls}-outer`, attrs.class],
        "style": [attrs.style, outerStyle]
      }), [createVNode("div", {
        "class": `${props.prefixCls}-inner`,
        "style": trailStyle.value
      }, [createVNode("div", {
        "class": `${props.prefixCls}-bg`,
        "style": percentStyle.value
      }, null), successPercent.value !== void 0 ? createVNode("div", {
        "class": `${props.prefixCls}-success-bg`,
        "style": successPercentStyle.value
      }, null) : null])]), (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots)]);
    };
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-progress/src/common.js
var defaultProps = {
  percent: 0,
  prefixCls: "vc-progress",
  strokeColor: "#2db7f5",
  strokeLinecap: "round",
  strokeWidth: 1,
  trailColor: "#D9D9D9",
  trailWidth: 1
};
var useTransitionDuration = (paths) => {
  const prevTimeStamp = ref(null);
  onUpdated(() => {
    const now2 = Date.now();
    let updated = false;
    paths.value.forEach((val) => {
      const path = (val === null || val === void 0 ? void 0 : val.$el) || val;
      if (!path) {
        return;
      }
      updated = true;
      const pathStyle = path.style;
      pathStyle.transitionDuration = ".3s, .3s, .3s, .06s";
      if (prevTimeStamp.value && now2 - prevTimeStamp.value < 100) {
        pathStyle.transitionDuration = "0s, 0s";
      }
    });
    if (updated) {
      prevTimeStamp.value = Date.now();
    }
  });
  return paths;
};

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-progress/src/types.js
var propTypes = {
  gapDegree: Number,
  gapPosition: {
    type: String
  },
  percent: {
    type: [Array, Number]
  },
  prefixCls: String,
  strokeColor: {
    type: [Object, String, Array]
  },
  strokeLinecap: {
    type: String
  },
  strokeWidth: Number,
  trailColor: String,
  trailWidth: Number,
  transition: String
};

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-progress/src/Line.js
var __rest3 = function(s, e2) {
  var t2 = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e2.indexOf(p) < 0) t2[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i2 = 0, p = Object.getOwnPropertySymbols(s); i2 < p.length; i2++) {
    if (e2.indexOf(p[i2]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i2])) t2[p[i2]] = s[p[i2]];
  }
  return t2;
};
var Line_default2 = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "ProgressLine",
  props: initDefaultProps_default(propTypes, defaultProps),
  setup(props) {
    const percentList = computed(() => {
      const {
        percent
      } = props;
      return Array.isArray(percent) ? percent : [percent];
    });
    const percentListProps = computed(() => {
      const {
        prefixCls,
        strokeLinecap,
        strokeWidth,
        transition
      } = props;
      let stackPtg = 0;
      return percentList.value.map((ptg, index2) => {
        let dashPercent = 1;
        switch (strokeLinecap) {
          case "round":
            dashPercent = 1 - strokeWidth / 100;
            break;
          case "square":
            dashPercent = 1 - strokeWidth / 2 / 100;
            break;
          default:
            dashPercent = 1;
            break;
        }
        const pathStyle = {
          strokeDasharray: `${ptg * dashPercent}px, 100px`,
          strokeDashoffset: `-${stackPtg}px`,
          transition: transition || "stroke-dashoffset 0.3s ease 0s, stroke-dasharray .3s ease 0s, stroke 0.3s linear"
        };
        const color = strokeColorList.value[index2] || strokeColorList.value[strokeColorList.value.length - 1];
        stackPtg += ptg;
        const pathProps = {
          key: index2,
          d: pathString.value,
          "stroke-linecap": strokeLinecap,
          stroke: color,
          "stroke-width": strokeWidth,
          "fill-opacity": "0",
          class: `${prefixCls}-line-path`,
          style: pathStyle
        };
        return pathProps;
      });
    });
    const strokeColorList = computed(() => {
      const {
        strokeColor
      } = props;
      return Array.isArray(strokeColor) ? strokeColor : [strokeColor];
    });
    const [setRef, paths] = useRefs_default();
    useTransitionDuration(paths);
    const center = computed(() => props.strokeWidth / 2);
    const right = computed(() => 100 - props.strokeWidth / 2);
    const pathString = computed(() => `M ${props.strokeLinecap === "round" ? center.value : 0},${center.value}
    L ${props.strokeLinecap === "round" ? right.value : 100},${center.value}`);
    const viewBoxString = computed(() => `0 0 100 ${props.strokeWidth}`);
    const pathFirst = computed(() => ({
      d: pathString.value,
      "stroke-linecap": props.strokeLinecap,
      stroke: props.trailColor,
      "stroke-width": props.trailWidth || props.strokeWidth,
      "fill-opacity": "0",
      class: `${props.prefixCls}-line-trail`
    }));
    return () => {
      const {
        percent,
        prefixCls,
        strokeColor,
        strokeLinecap,
        strokeWidth,
        trailColor,
        trailWidth,
        transition
      } = props, restProps = __rest3(props, ["percent", "prefixCls", "strokeColor", "strokeLinecap", "strokeWidth", "trailColor", "trailWidth", "transition"]);
      delete restProps.gapPosition;
      return createVNode("svg", _objectSpread2({
        "class": `${prefixCls}-line`,
        "viewBox": viewBoxString.value,
        "preserveAspectRatio": "none"
      }, restProps), [createVNode("path", pathFirst.value, null), percentListProps.value.map((pathProps, index2) => {
        return createVNode("path", _objectSpread2({
          "ref": setRef(index2)
        }, pathProps), null);
      })]);
    };
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-progress/src/Circle.js
var __rest4 = function(s, e2) {
  var t2 = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e2.indexOf(p) < 0) t2[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i2 = 0, p = Object.getOwnPropertySymbols(s); i2 < p.length; i2++) {
    if (e2.indexOf(p[i2]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i2])) t2[p[i2]] = s[p[i2]];
  }
  return t2;
};
var gradientSeed = 0;
function stripPercentToNumber(percent) {
  return +percent.replace("%", "");
}
function toArray(value) {
  return Array.isArray(value) ? value : [value];
}
function getPathStyles(offset, percent, strokeColor, strokeWidth) {
  let gapDegree = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : 0;
  let gapPosition = arguments.length > 5 ? arguments[5] : void 0;
  const radius = 50 - strokeWidth / 2;
  let beginPositionX = 0;
  let beginPositionY = -radius;
  let endPositionX = 0;
  let endPositionY = -2 * radius;
  switch (gapPosition) {
    case "left":
      beginPositionX = -radius;
      beginPositionY = 0;
      endPositionX = 2 * radius;
      endPositionY = 0;
      break;
    case "right":
      beginPositionX = radius;
      beginPositionY = 0;
      endPositionX = -2 * radius;
      endPositionY = 0;
      break;
    case "bottom":
      beginPositionY = radius;
      endPositionY = 2 * radius;
      break;
    default:
  }
  const pathString = `M 50,50 m ${beginPositionX},${beginPositionY}
   a ${radius},${radius} 0 1 1 ${endPositionX},${-endPositionY}
   a ${radius},${radius} 0 1 1 ${-endPositionX},${endPositionY}`;
  const len = Math.PI * 2 * radius;
  const pathStyle = {
    stroke: strokeColor,
    strokeDasharray: `${percent / 100 * (len - gapDegree)}px ${len}px`,
    strokeDashoffset: `-${gapDegree / 2 + offset / 100 * (len - gapDegree)}px`,
    transition: "stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s, stroke-width .06s ease .3s, opacity .3s ease 0s"
    // eslint-disable-line
  };
  return {
    pathString,
    pathStyle
  };
}
var Circle_default = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "VCCircle",
  props: initDefaultProps_default(propTypes, defaultProps),
  setup(props) {
    gradientSeed += 1;
    const gradientId = ref(gradientSeed);
    const percentList = computed(() => toArray(props.percent));
    const strokeColorList = computed(() => toArray(props.strokeColor));
    const [setRef, paths] = useRefs_default();
    useTransitionDuration(paths);
    const getStokeList = () => {
      const {
        prefixCls,
        strokeWidth,
        strokeLinecap,
        gapDegree,
        gapPosition
      } = props;
      let stackPtg = 0;
      return percentList.value.map((ptg, index2) => {
        const color = strokeColorList.value[index2] || strokeColorList.value[strokeColorList.value.length - 1];
        const stroke = Object.prototype.toString.call(color) === "[object Object]" ? `url(#${prefixCls}-gradient-${gradientId.value})` : "";
        const {
          pathString,
          pathStyle
        } = getPathStyles(stackPtg, ptg, color, strokeWidth, gapDegree, gapPosition);
        stackPtg += ptg;
        const pathProps = {
          key: index2,
          d: pathString,
          stroke,
          "stroke-linecap": strokeLinecap,
          "stroke-width": strokeWidth,
          opacity: ptg === 0 ? 0 : 1,
          "fill-opacity": "0",
          class: `${prefixCls}-circle-path`,
          style: pathStyle
        };
        return createVNode("path", _objectSpread2({
          "ref": setRef(index2)
        }, pathProps), null);
      });
    };
    return () => {
      const {
        prefixCls,
        strokeWidth,
        trailWidth,
        gapDegree,
        gapPosition,
        trailColor,
        strokeLinecap,
        strokeColor
      } = props, restProps = __rest4(props, ["prefixCls", "strokeWidth", "trailWidth", "gapDegree", "gapPosition", "trailColor", "strokeLinecap", "strokeColor"]);
      const {
        pathString,
        pathStyle
      } = getPathStyles(0, 100, trailColor, strokeWidth, gapDegree, gapPosition);
      delete restProps.percent;
      const gradient = strokeColorList.value.find((color) => Object.prototype.toString.call(color) === "[object Object]");
      const pathFirst = {
        d: pathString,
        stroke: trailColor,
        "stroke-linecap": strokeLinecap,
        "stroke-width": trailWidth || strokeWidth,
        "fill-opacity": "0",
        class: `${prefixCls}-circle-trail`,
        style: pathStyle
      };
      return createVNode("svg", _objectSpread2({
        "class": `${prefixCls}-circle`,
        "viewBox": "0 0 100 100"
      }, restProps), [gradient && createVNode("defs", null, [createVNode("linearGradient", {
        "id": `${prefixCls}-gradient-${gradientId.value}`,
        "x1": "100%",
        "y1": "0%",
        "x2": "0%",
        "y2": "0%"
      }, [Object.keys(gradient).sort((a, b) => stripPercentToNumber(a) - stripPercentToNumber(b)).map((key, index2) => createVNode("stop", {
        "key": index2,
        "offset": key,
        "stop-color": gradient[key]
      }, null))])]), createVNode("path", pathFirst, null), getStokeList().reverse()]);
    };
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/progress/Circle.js
var circleProps = () => _extends(_extends({}, progressProps()), {
  strokeColor: anyType()
});
var CIRCLE_MIN_STROKE_WIDTH = 3;
var getMinPercent = (width) => CIRCLE_MIN_STROKE_WIDTH / width * 100;
var Circle_default2 = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "ProgressCircle",
  inheritAttrs: false,
  props: initDefaultProps_default(circleProps(), {
    trailColor: null
  }),
  setup(props, _ref) {
    let {
      slots,
      attrs
    } = _ref;
    const originWidth = computed(() => {
      var _a;
      return (_a = props.width) !== null && _a !== void 0 ? _a : 120;
    });
    const mergedSize = computed(() => {
      var _a;
      return (_a = props.size) !== null && _a !== void 0 ? _a : [originWidth.value, originWidth.value];
    });
    const sizeRef = computed(() => getSize(mergedSize.value, "circle"));
    const gapDeg = computed(() => {
      if (props.gapDegree || props.gapDegree === 0) {
        return props.gapDegree;
      }
      if (props.type === "dashboard") {
        return 75;
      }
      return void 0;
    });
    const circleStyle = computed(() => {
      return {
        width: `${sizeRef.value.width}px`,
        height: `${sizeRef.value.height}px`,
        fontSize: `${sizeRef.value.width * 0.15 + 6}px`
      };
    });
    const circleWidth = computed(() => {
      var _a;
      return (_a = props.strokeWidth) !== null && _a !== void 0 ? _a : Math.max(getMinPercent(sizeRef.value.width), 6);
    });
    const gapPos = computed(() => props.gapPosition || props.type === "dashboard" && "bottom" || void 0);
    const percent = computed(() => getPercentage(props));
    const isGradient = computed(() => Object.prototype.toString.call(props.strokeColor) === "[object Object]");
    const strokeColor = computed(() => getStrokeColor({
      success: props.success,
      strokeColor: props.strokeColor
    }));
    const wrapperClassName = computed(() => ({
      [`${props.prefixCls}-inner`]: true,
      [`${props.prefixCls}-circle-gradient`]: isGradient.value
    }));
    return () => {
      var _a;
      const circleContent = createVNode(Circle_default, {
        "percent": percent.value,
        "strokeWidth": circleWidth.value,
        "trailWidth": circleWidth.value,
        "strokeColor": strokeColor.value,
        "strokeLinecap": props.strokeLinecap,
        "trailColor": props.trailColor,
        "prefixCls": props.prefixCls,
        "gapDegree": gapDeg.value,
        "gapPosition": gapPos.value
      }, null);
      return createVNode("div", _objectSpread2(_objectSpread2({}, attrs), {}, {
        "class": [wrapperClassName.value, attrs.class],
        "style": [attrs.style, circleStyle.value]
      }), [sizeRef.value.width <= 20 ? createVNode(tooltip_default, null, {
        default: () => [createVNode("span", null, [circleContent])],
        title: slots.default
      }) : createVNode(Fragment, null, [circleContent, (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots)])]);
    };
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/progress/Steps.js
var stepsProps = () => _extends(_extends({}, progressProps()), {
  steps: Number,
  strokeColor: someType(),
  trailColor: String
});
var Steps_default = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "Steps",
  props: stepsProps(),
  setup(props, _ref) {
    let {
      slots
    } = _ref;
    const current = computed(() => Math.round(props.steps * ((props.percent || 0) / 100)));
    const mergedSize = computed(() => {
      var _a;
      return (_a = props.size) !== null && _a !== void 0 ? _a : [props.size === "small" ? 2 : 14, props.strokeWidth || 8];
    });
    const sizeRef = computed(() => getSize(mergedSize.value, "step", {
      steps: props.steps,
      strokeWidth: props.strokeWidth || 8
    }));
    const styledSteps = computed(() => {
      const {
        steps,
        strokeColor,
        trailColor,
        prefixCls
      } = props;
      const temp = [];
      for (let i2 = 0; i2 < steps; i2 += 1) {
        const color = Array.isArray(strokeColor) ? strokeColor[i2] : strokeColor;
        const cls = {
          [`${prefixCls}-steps-item`]: true,
          [`${prefixCls}-steps-item-active`]: i2 <= current.value - 1
        };
        temp.push(createVNode("div", {
          "key": i2,
          "class": cls,
          "style": {
            backgroundColor: i2 <= current.value - 1 ? color : trailColor,
            width: `${sizeRef.value.width / steps}px`,
            height: `${sizeRef.value.height}px`
          }
        }, null));
      }
      return temp;
    });
    return () => {
      var _a;
      return createVNode("div", {
        "class": `${props.prefixCls}-steps-outer`
      }, [styledSteps.value, (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots)]);
    };
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/progress/style/index.js
var antProgressActive = new Keyframes_default("antProgressActive", {
  "0%": {
    transform: "translateX(-100%) scaleX(0)",
    opacity: 0.1
  },
  "20%": {
    transform: "translateX(-100%) scaleX(0)",
    opacity: 0.5
  },
  to: {
    transform: "translateX(0) scaleX(1)",
    opacity: 0
  }
});
var genBaseStyle = (token) => {
  const {
    componentCls: progressCls,
    iconCls: iconPrefixCls
  } = token;
  return {
    [progressCls]: _extends(_extends({}, resetComponent(token)), {
      display: "inline-block",
      "&-rtl": {
        direction: "rtl"
      },
      "&-line": {
        position: "relative",
        width: "100%",
        fontSize: token.fontSize,
        marginInlineEnd: token.marginXS,
        marginBottom: token.marginXS
      },
      [`${progressCls}-outer`]: {
        display: "inline-block",
        width: "100%"
      },
      [`&${progressCls}-show-info`]: {
        [`${progressCls}-outer`]: {
          marginInlineEnd: `calc(-2em - ${token.marginXS}px)`,
          paddingInlineEnd: `calc(2em + ${token.paddingXS}px)`
        }
      },
      [`${progressCls}-inner`]: {
        position: "relative",
        display: "inline-block",
        width: "100%",
        overflow: "hidden",
        verticalAlign: "middle",
        backgroundColor: token.progressRemainingColor,
        borderRadius: token.progressLineRadius
      },
      [`${progressCls}-inner:not(${progressCls}-circle-gradient)`]: {
        [`${progressCls}-circle-path`]: {
          stroke: token.colorInfo
        }
      },
      [`${progressCls}-success-bg, ${progressCls}-bg`]: {
        position: "relative",
        backgroundColor: token.colorInfo,
        borderRadius: token.progressLineRadius,
        transition: `all ${token.motionDurationSlow} ${token.motionEaseInOutCirc}`
      },
      [`${progressCls}-success-bg`]: {
        position: "absolute",
        insetBlockStart: 0,
        insetInlineStart: 0,
        backgroundColor: token.colorSuccess
      },
      [`${progressCls}-text`]: {
        display: "inline-block",
        width: "2em",
        marginInlineStart: token.marginXS,
        color: token.progressInfoTextColor,
        lineHeight: 1,
        whiteSpace: "nowrap",
        textAlign: "start",
        verticalAlign: "middle",
        wordBreak: "normal",
        [iconPrefixCls]: {
          fontSize: token.fontSize
        }
      },
      [`&${progressCls}-status-active`]: {
        [`${progressCls}-bg::before`]: {
          position: "absolute",
          inset: 0,
          backgroundColor: token.colorBgContainer,
          borderRadius: token.progressLineRadius,
          opacity: 0,
          animationName: antProgressActive,
          animationDuration: token.progressActiveMotionDuration,
          animationTimingFunction: token.motionEaseOutQuint,
          animationIterationCount: "infinite",
          content: '""'
        }
      },
      [`&${progressCls}-status-exception`]: {
        [`${progressCls}-bg`]: {
          backgroundColor: token.colorError
        },
        [`${progressCls}-text`]: {
          color: token.colorError
        }
      },
      [`&${progressCls}-status-exception ${progressCls}-inner:not(${progressCls}-circle-gradient)`]: {
        [`${progressCls}-circle-path`]: {
          stroke: token.colorError
        }
      },
      [`&${progressCls}-status-success`]: {
        [`${progressCls}-bg`]: {
          backgroundColor: token.colorSuccess
        },
        [`${progressCls}-text`]: {
          color: token.colorSuccess
        }
      },
      [`&${progressCls}-status-success ${progressCls}-inner:not(${progressCls}-circle-gradient)`]: {
        [`${progressCls}-circle-path`]: {
          stroke: token.colorSuccess
        }
      }
    })
  };
};
var genCircleStyle = (token) => {
  const {
    componentCls: progressCls,
    iconCls: iconPrefixCls
  } = token;
  return {
    [progressCls]: {
      [`${progressCls}-circle-trail`]: {
        stroke: token.progressRemainingColor
      },
      [`&${progressCls}-circle ${progressCls}-inner`]: {
        position: "relative",
        lineHeight: 1,
        backgroundColor: "transparent"
      },
      [`&${progressCls}-circle ${progressCls}-text`]: {
        position: "absolute",
        insetBlockStart: "50%",
        insetInlineStart: 0,
        width: "100%",
        margin: 0,
        padding: 0,
        color: token.colorText,
        lineHeight: 1,
        whiteSpace: "normal",
        textAlign: "center",
        transform: "translateY(-50%)",
        [iconPrefixCls]: {
          fontSize: `${token.fontSize / token.fontSizeSM}em`
        }
      },
      [`${progressCls}-circle&-status-exception`]: {
        [`${progressCls}-text`]: {
          color: token.colorError
        }
      },
      [`${progressCls}-circle&-status-success`]: {
        [`${progressCls}-text`]: {
          color: token.colorSuccess
        }
      }
    },
    [`${progressCls}-inline-circle`]: {
      lineHeight: 1,
      [`${progressCls}-inner`]: {
        verticalAlign: "bottom"
      }
    }
  };
};
var genStepStyle = (token) => {
  const {
    componentCls: progressCls
  } = token;
  return {
    [progressCls]: {
      [`${progressCls}-steps`]: {
        display: "inline-block",
        "&-outer": {
          display: "flex",
          flexDirection: "row",
          alignItems: "center"
        },
        "&-item": {
          flexShrink: 0,
          minWidth: token.progressStepMinWidth,
          marginInlineEnd: token.progressStepMarginInlineEnd,
          backgroundColor: token.progressRemainingColor,
          transition: `all ${token.motionDurationSlow}`,
          "&-active": {
            backgroundColor: token.colorInfo
          }
        }
      }
    }
  };
};
var genSmallLine = (token) => {
  const {
    componentCls: progressCls,
    iconCls: iconPrefixCls
  } = token;
  return {
    [progressCls]: {
      [`${progressCls}-small&-line, ${progressCls}-small&-line ${progressCls}-text ${iconPrefixCls}`]: {
        fontSize: token.fontSizeSM
      }
    }
  };
};
var style_default = genComponentStyleHook("Progress", (token) => {
  const progressStepMarginInlineEnd = token.marginXXS / 2;
  const progressToken = merge(token, {
    progressLineRadius: 100,
    progressInfoTextColor: token.colorText,
    progressDefaultColor: token.colorInfo,
    progressRemainingColor: token.colorFillSecondary,
    progressStepMarginInlineEnd,
    progressStepMinWidth: progressStepMarginInlineEnd,
    progressActiveMotionDuration: "2.4s"
  });
  return [genBaseStyle(progressToken), genCircleStyle(progressToken), genStepStyle(progressToken), genSmallLine(progressToken)];
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/progress/progress.js
var __rest5 = function(s, e2) {
  var t2 = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e2.indexOf(p) < 0) t2[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i2 = 0, p = Object.getOwnPropertySymbols(s); i2 < p.length; i2++) {
    if (e2.indexOf(p[i2]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i2])) t2[p[i2]] = s[p[i2]];
  }
  return t2;
};
var progress_default = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "AProgress",
  inheritAttrs: false,
  props: initDefaultProps_default(progressProps(), {
    type: "line",
    percent: 0,
    showInfo: true,
    // null for different theme definition
    trailColor: null,
    size: "default",
    strokeLinecap: "round"
  }),
  slots: Object,
  setup(props, _ref) {
    let {
      slots,
      attrs
    } = _ref;
    const {
      prefixCls,
      direction
    } = useConfigInject_default("progress", props);
    const [wrapSSR, hashId] = style_default(prefixCls);
    if (true) {
      devWarning_default("successPercent" in props, "Progress", "`successPercent` is deprecated. Please use `success.percent` instead.");
      devWarning_default("width" in props, "Progress", "`width` is deprecated. Please use `size` instead.");
    }
    const strokeColorNotArray = computed(() => Array.isArray(props.strokeColor) ? props.strokeColor[0] : props.strokeColor);
    const percentNumber = computed(() => {
      const {
        percent = 0
      } = props;
      const successPercent = getSuccessPercent(props);
      return parseInt(successPercent !== void 0 ? successPercent.toString() : percent.toString(), 10);
    });
    const progressStatus = computed(() => {
      const {
        status
      } = props;
      if (!progressStatuses.includes(status) && percentNumber.value >= 100) {
        return "success";
      }
      return status || "normal";
    });
    const classString = computed(() => {
      const {
        type: type4,
        showInfo,
        size
      } = props;
      const pre = prefixCls.value;
      return {
        [pre]: true,
        [`${pre}-inline-circle`]: type4 === "circle" && getSize(size, "circle").width <= 20,
        [`${pre}-${type4 === "dashboard" && "circle" || type4}`]: true,
        [`${pre}-status-${progressStatus.value}`]: true,
        [`${pre}-show-info`]: showInfo,
        [`${pre}-${size}`]: size,
        [`${pre}-rtl`]: direction.value === "rtl",
        [hashId.value]: true
      };
    });
    const strokeColorNotGradient = computed(() => typeof props.strokeColor === "string" || Array.isArray(props.strokeColor) ? props.strokeColor : void 0);
    const renderProcessInfo = () => {
      const {
        showInfo,
        format: format2,
        type: type4,
        percent,
        title
      } = props;
      const successPercent = getSuccessPercent(props);
      if (!showInfo) return null;
      let text;
      const textFormatter = format2 || (slots === null || slots === void 0 ? void 0 : slots.format) || ((val) => `${val}%`);
      const isLineType = type4 === "line";
      if (format2 || (slots === null || slots === void 0 ? void 0 : slots.format) || progressStatus.value !== "exception" && progressStatus.value !== "success") {
        text = textFormatter(validProgress(percent), validProgress(successPercent));
      } else if (progressStatus.value === "exception") {
        text = isLineType ? createVNode(CloseCircleFilled_default, null, null) : createVNode(CloseOutlined_default, null, null);
      } else if (progressStatus.value === "success") {
        text = isLineType ? createVNode(CheckCircleFilled_default2, null, null) : createVNode(CheckOutlined_default, null, null);
      }
      return createVNode("span", {
        "class": `${prefixCls.value}-text`,
        "title": title === void 0 && typeof text === "string" ? text : void 0
      }, [text]);
    };
    return () => {
      const {
        type: type4,
        steps,
        title
      } = props;
      const {
        class: cls
      } = attrs, restAttrs = __rest5(attrs, ["class"]);
      const progressInfo = renderProcessInfo();
      let progress;
      if (type4 === "line") {
        progress = steps ? createVNode(Steps_default, _objectSpread2(_objectSpread2({}, props), {}, {
          "strokeColor": strokeColorNotGradient.value,
          "prefixCls": prefixCls.value,
          "steps": steps
        }), {
          default: () => [progressInfo]
        }) : createVNode(Line_default, _objectSpread2(_objectSpread2({}, props), {}, {
          "strokeColor": strokeColorNotArray.value,
          "prefixCls": prefixCls.value,
          "direction": direction.value
        }), {
          default: () => [progressInfo]
        });
      } else if (type4 === "circle" || type4 === "dashboard") {
        progress = createVNode(Circle_default2, _objectSpread2(_objectSpread2({}, props), {}, {
          "prefixCls": prefixCls.value,
          "strokeColor": strokeColorNotArray.value,
          "progressStatus": progressStatus.value
        }), {
          default: () => [progressInfo]
        });
      }
      return wrapSSR(createVNode("div", _objectSpread2(_objectSpread2({
        "role": "progressbar"
      }, restAttrs), {}, {
        "class": [classString.value, cls],
        "title": title
      }), [progress]));
    };
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/progress/index.js
var progress_default2 = withInstall(progress_default);

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/upload/UploadList/ListItem.js
var listItemProps = () => {
  return {
    prefixCls: String,
    locale: objectType(void 0),
    file: objectType(),
    items: arrayType(),
    listType: stringType(),
    isImgUrl: functionType(),
    showRemoveIcon: booleanType(),
    showDownloadIcon: booleanType(),
    showPreviewIcon: booleanType(),
    removeIcon: functionType(),
    downloadIcon: functionType(),
    previewIcon: functionType(),
    iconRender: functionType(),
    actionIconRender: functionType(),
    itemRender: functionType(),
    onPreview: functionType(),
    onClose: functionType(),
    onDownload: functionType(),
    progress: objectType()
  };
};
var ListItem_default = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "ListItem",
  inheritAttrs: false,
  props: listItemProps(),
  setup(props, _ref) {
    let {
      slots,
      attrs
    } = _ref;
    var _a;
    const showProgress = shallowRef(false);
    const progressRafRef = shallowRef();
    onMounted(() => {
      progressRafRef.value = setTimeout(() => {
        showProgress.value = true;
      }, 300);
    });
    onBeforeUnmount(() => {
      clearTimeout(progressRafRef.value);
    });
    const mergedStatus = shallowRef((_a = props.file) === null || _a === void 0 ? void 0 : _a.status);
    watch(() => {
      var _a2;
      return (_a2 = props.file) === null || _a2 === void 0 ? void 0 : _a2.status;
    }, (status) => {
      if (status !== "removed") {
        mergedStatus.value = status;
      }
    });
    const {
      rootPrefixCls
    } = useConfigInject_default("upload", props);
    const transitionProps = computed(() => getTransitionProps(`${rootPrefixCls.value}-fade`));
    return () => {
      var _a2, _b;
      const {
        prefixCls,
        locale,
        listType,
        file,
        items,
        progress: progressProps2,
        iconRender = slots.iconRender,
        actionIconRender = slots.actionIconRender,
        itemRender = slots.itemRender,
        isImgUrl,
        showPreviewIcon,
        showRemoveIcon,
        showDownloadIcon,
        previewIcon: customPreviewIcon = slots.previewIcon,
        removeIcon: customRemoveIcon = slots.removeIcon,
        downloadIcon: customDownloadIcon = slots.downloadIcon,
        onPreview,
        onDownload,
        onClose
      } = props;
      const {
        class: className,
        style
      } = attrs;
      const iconNode = iconRender({
        file
      });
      let icon = createVNode("div", {
        "class": `${prefixCls}-text-icon`
      }, [iconNode]);
      if (listType === "picture" || listType === "picture-card") {
        if (mergedStatus.value === "uploading" || !file.thumbUrl && !file.url) {
          const uploadingClassName = {
            [`${prefixCls}-list-item-thumbnail`]: true,
            [`${prefixCls}-list-item-file`]: mergedStatus.value !== "uploading"
          };
          icon = createVNode("div", {
            "class": uploadingClassName
          }, [iconNode]);
        } else {
          const thumbnail = (isImgUrl === null || isImgUrl === void 0 ? void 0 : isImgUrl(file)) ? createVNode("img", {
            "src": file.thumbUrl || file.url,
            "alt": file.name,
            "class": `${prefixCls}-list-item-image`,
            "crossorigin": file.crossOrigin
          }, null) : iconNode;
          const aClassName = {
            [`${prefixCls}-list-item-thumbnail`]: true,
            [`${prefixCls}-list-item-file`]: isImgUrl && !isImgUrl(file)
          };
          icon = createVNode("a", {
            "class": aClassName,
            "onClick": (e2) => onPreview(file, e2),
            "href": file.url || file.thumbUrl,
            "target": "_blank",
            "rel": "noopener noreferrer"
          }, [thumbnail]);
        }
      }
      const infoUploadingClass = {
        [`${prefixCls}-list-item`]: true,
        [`${prefixCls}-list-item-${mergedStatus.value}`]: true
      };
      const linkProps = typeof file.linkProps === "string" ? JSON.parse(file.linkProps) : file.linkProps;
      const removeIcon = showRemoveIcon ? actionIconRender({
        customIcon: customRemoveIcon ? customRemoveIcon({
          file
        }) : createVNode(DeleteOutlined_default2, null, null),
        callback: () => onClose(file),
        prefixCls,
        title: locale.removeFile
      }) : null;
      const downloadIcon = showDownloadIcon && mergedStatus.value === "done" ? actionIconRender({
        customIcon: customDownloadIcon ? customDownloadIcon({
          file
        }) : createVNode(DownloadOutlined_default2, null, null),
        callback: () => onDownload(file),
        prefixCls,
        title: locale.downloadFile
      }) : null;
      const downloadOrDelete = listType !== "picture-card" && createVNode("span", {
        "key": "download-delete",
        "class": [`${prefixCls}-list-item-actions`, {
          picture: listType === "picture"
        }]
      }, [downloadIcon, removeIcon]);
      const listItemNameClass = `${prefixCls}-list-item-name`;
      const fileName = file.url ? [createVNode("a", _objectSpread2(_objectSpread2({
        "key": "view",
        "target": "_blank",
        "rel": "noopener noreferrer",
        "class": listItemNameClass,
        "title": file.name
      }, linkProps), {}, {
        "href": file.url,
        "onClick": (e2) => onPreview(file, e2)
      }), [file.name]), downloadOrDelete] : [createVNode("span", {
        "key": "view",
        "class": listItemNameClass,
        "onClick": (e2) => onPreview(file, e2),
        "title": file.name
      }, [file.name]), downloadOrDelete];
      const previewStyle = {
        pointerEvents: "none",
        opacity: 0.5
      };
      const previewIcon = showPreviewIcon ? createVNode("a", {
        "href": file.url || file.thumbUrl,
        "target": "_blank",
        "rel": "noopener noreferrer",
        "style": file.url || file.thumbUrl ? void 0 : previewStyle,
        "onClick": (e2) => onPreview(file, e2),
        "title": locale.previewFile
      }, [customPreviewIcon ? customPreviewIcon({
        file
      }) : createVNode(EyeOutlined_default, null, null)]) : null;
      const pictureCardActions = listType === "picture-card" && mergedStatus.value !== "uploading" && createVNode("span", {
        "class": `${prefixCls}-list-item-actions`
      }, [previewIcon, mergedStatus.value === "done" && downloadIcon, removeIcon]);
      const dom = createVNode("div", {
        "class": infoUploadingClass
      }, [icon, fileName, pictureCardActions, showProgress.value && createVNode(Transition, transitionProps.value, {
        default: () => [withDirectives(createVNode("div", {
          "class": `${prefixCls}-list-item-progress`
        }, ["percent" in file ? createVNode(progress_default2, _objectSpread2(_objectSpread2({}, progressProps2), {}, {
          "type": "line",
          "percent": file.percent
        }), null) : null]), [[vShow, mergedStatus.value === "uploading"]])]
      })]);
      const listContainerNameClass = {
        [`${prefixCls}-list-item-container`]: true,
        [`${className}`]: !!className
      };
      const message = file.response && typeof file.response === "string" ? file.response : ((_a2 = file.error) === null || _a2 === void 0 ? void 0 : _a2.statusText) || ((_b = file.error) === null || _b === void 0 ? void 0 : _b.message) || locale.uploadError;
      const item = mergedStatus.value === "error" ? createVNode(tooltip_default, {
        "title": message,
        "getPopupContainer": (node) => node.parentNode
      }, {
        default: () => [dom]
      }) : dom;
      return createVNode("div", {
        "class": listContainerNameClass,
        "style": style
      }, [itemRender ? itemRender({
        originNode: item,
        file,
        fileList: items,
        actions: {
          download: onDownload.bind(null, file),
          preview: onPreview.bind(null, file),
          remove: onClose.bind(null, file)
        }
      }) : item]);
    };
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/upload/UploadList/index.js
var HackSlot = (_, _ref) => {
  let {
    slots
  } = _ref;
  var _a;
  return filterEmpty((_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots))[0];
};
var UploadList_default = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "AUploadList",
  props: initDefaultProps_default(uploadListProps(), {
    listType: "text",
    progress: {
      strokeWidth: 2,
      showInfo: false
    },
    showRemoveIcon: true,
    showDownloadIcon: false,
    showPreviewIcon: true,
    previewFile: previewImage,
    isImageUrl,
    items: [],
    appendActionVisible: true
  }),
  setup(props, _ref2) {
    let {
      slots,
      expose
    } = _ref2;
    const motionAppear = shallowRef(false);
    onMounted(() => {
      motionAppear.value == true;
    });
    const mergedItems = shallowRef([]);
    watch(() => props.items, function() {
      let val = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];
      mergedItems.value = val.slice();
    }, {
      immediate: true,
      deep: true
    });
    watchEffect(() => {
      if (props.listType !== "picture" && props.listType !== "picture-card") {
        return;
      }
      let hasUpdate = false;
      (props.items || []).forEach((file, index2) => {
        if (typeof document === "undefined" || typeof window === "undefined" || !window.FileReader || !window.File || !(file.originFileObj instanceof File || file.originFileObj instanceof Blob) || file.thumbUrl !== void 0) {
          return;
        }
        file.thumbUrl = "";
        if (props.previewFile) {
          props.previewFile(file.originFileObj).then((previewDataUrl) => {
            const thumbUrl = previewDataUrl || "";
            if (thumbUrl !== file.thumbUrl) {
              mergedItems.value[index2].thumbUrl = thumbUrl;
              hasUpdate = true;
            }
          });
        }
      });
      if (hasUpdate) {
        triggerRef(mergedItems);
      }
    });
    const onInternalPreview = (file, e2) => {
      if (!props.onPreview) {
        return;
      }
      e2 === null || e2 === void 0 ? void 0 : e2.preventDefault();
      return props.onPreview(file);
    };
    const onInternalDownload = (file) => {
      if (typeof props.onDownload === "function") {
        props.onDownload(file);
      } else if (file.url) {
        window.open(file.url);
      }
    };
    const onInternalClose = (file) => {
      var _a;
      (_a = props.onRemove) === null || _a === void 0 ? void 0 : _a.call(props, file);
    };
    const internalIconRender = (_ref3) => {
      let {
        file
      } = _ref3;
      const iconRender = props.iconRender || slots.iconRender;
      if (iconRender) {
        return iconRender({
          file,
          listType: props.listType
        });
      }
      const isLoading = file.status === "uploading";
      const fileIcon = props.isImageUrl && props.isImageUrl(file) ? createVNode(PictureTwoTone_default2, null, null) : createVNode(FileTwoTone_default2, null, null);
      let icon = isLoading ? createVNode(LoadingOutlined_default, null, null) : createVNode(PaperClipOutlined_default2, null, null);
      if (props.listType === "picture") {
        icon = isLoading ? createVNode(LoadingOutlined_default, null, null) : fileIcon;
      } else if (props.listType === "picture-card") {
        icon = isLoading ? props.locale.uploading : fileIcon;
      }
      return icon;
    };
    const actionIconRender = (opt) => {
      const {
        customIcon,
        callback,
        prefixCls: prefixCls2,
        title
      } = opt;
      const btnProps = {
        type: "text",
        size: "small",
        title,
        onClick: () => {
          callback();
        },
        class: `${prefixCls2}-list-item-action`
      };
      if (isValidElement(customIcon)) {
        return createVNode(button_default, btnProps, {
          icon: () => customIcon
        });
      }
      return createVNode(button_default, btnProps, {
        default: () => [createVNode("span", null, [customIcon])]
      });
    };
    expose({
      handlePreview: onInternalPreview,
      handleDownload: onInternalDownload
    });
    const {
      prefixCls,
      rootPrefixCls
    } = useConfigInject_default("upload", props);
    const listClassNames = computed(() => ({
      [`${prefixCls.value}-list`]: true,
      [`${prefixCls.value}-list-${props.listType}`]: true
    }));
    const transitionGroupProps = computed(() => {
      const motion = _extends({}, collapseMotion_default(`${rootPrefixCls.value}-motion-collapse`));
      delete motion.onAfterAppear;
      delete motion.onAfterEnter;
      delete motion.onAfterLeave;
      const motionConfig = _extends(_extends({}, getTransitionGroupProps(`${prefixCls.value}-${props.listType === "picture-card" ? "animate-inline" : "animate"}`)), {
        class: listClassNames.value,
        appear: motionAppear.value
      });
      return props.listType !== "picture-card" ? _extends(_extends({}, motion), motionConfig) : motionConfig;
    });
    return () => {
      const {
        listType,
        locale,
        isImageUrl: isImgUrl,
        showPreviewIcon,
        showRemoveIcon,
        showDownloadIcon,
        removeIcon,
        previewIcon,
        downloadIcon,
        progress,
        appendAction,
        itemRender,
        appendActionVisible
      } = props;
      const appendActionDom = appendAction === null || appendAction === void 0 ? void 0 : appendAction();
      const items = mergedItems.value;
      return createVNode(TransitionGroup, _objectSpread2(_objectSpread2({}, transitionGroupProps.value), {}, {
        "tag": "div"
      }), {
        default: () => [items.map((file) => {
          const {
            uid: key
          } = file;
          return createVNode(ListItem_default, {
            "key": key,
            "locale": locale,
            "prefixCls": prefixCls.value,
            "file": file,
            "items": items,
            "progress": progress,
            "listType": listType,
            "isImgUrl": isImgUrl,
            "showPreviewIcon": showPreviewIcon,
            "showRemoveIcon": showRemoveIcon,
            "showDownloadIcon": showDownloadIcon,
            "onPreview": onInternalPreview,
            "onDownload": onInternalDownload,
            "onClose": onInternalClose,
            "removeIcon": removeIcon,
            "previewIcon": previewIcon,
            "downloadIcon": downloadIcon,
            "itemRender": itemRender
          }, _extends(_extends({}, slots), {
            iconRender: internalIconRender,
            actionIconRender
          }));
        }), appendAction ? withDirectives(createVNode(HackSlot, {
          "key": "__ant_upload_appendAction"
        }, {
          default: () => appendActionDom
        }), [[vShow, !!appendActionVisible]]) : null]
      });
    };
  }
});

// ../../node_modules/.pnpm/@ant-design+icons-svg@4.4.2/node_modules/@ant-design/icons-svg/es/asn/ExclamationCircleFilled.js
var ExclamationCircleFilled = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z" } }] }, "name": "exclamation-circle", "theme": "filled" };
var ExclamationCircleFilled_default = ExclamationCircleFilled;

// ../../node_modules/.pnpm/@ant-design+icons-vue@7.0.1_vue@3.5.17_typescript@5.8.3_/node_modules/@ant-design/icons-vue/es/icons/ExclamationCircleFilled.js
function _objectSpread8(target) {
  for (var i2 = 1; i2 < arguments.length; i2++) {
    var source = arguments[i2] != null ? Object(arguments[i2]) : {};
    var ownKeys = Object.keys(source);
    if (typeof Object.getOwnPropertySymbols === "function") {
      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
        return Object.getOwnPropertyDescriptor(source, sym).enumerable;
      }));
    }
    ownKeys.forEach(function(key) {
      _defineProperty7(target, key, source[key]);
    });
  }
  return target;
}
function _defineProperty7(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, { value, enumerable: true, configurable: true, writable: true });
  } else {
    obj[key] = value;
  }
  return obj;
}
var ExclamationCircleFilled2 = function ExclamationCircleFilled3(props, context) {
  var p = _objectSpread8({}, props, context.attrs);
  return createVNode(AntdIcon_default, _objectSpread8({}, p, {
    "icon": ExclamationCircleFilled_default
  }), null);
};
ExclamationCircleFilled2.displayName = "ExclamationCircleFilled";
ExclamationCircleFilled2.inheritAttrs = false;
var ExclamationCircleFilled_default2 = ExclamationCircleFilled2;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/responsiveObserve.js
var responsiveArray = ["xxxl", "xxl", "xl", "lg", "md", "sm", "xs"];
var getResponsiveMap = (token) => ({
  xs: `(max-width: ${token.screenXSMax}px)`,
  sm: `(min-width: ${token.screenSM}px)`,
  md: `(min-width: ${token.screenMD}px)`,
  lg: `(min-width: ${token.screenLG}px)`,
  xl: `(min-width: ${token.screenXL}px)`,
  xxl: `(min-width: ${token.screenXXL}px)`,
  xxxl: `{min-width: ${token.screenXXXL}px}`
});
function useResponsiveObserver() {
  const [, token] = useToken();
  return computed(() => {
    const responsiveMap = getResponsiveMap(token.value);
    const subscribers = /* @__PURE__ */ new Map();
    let subUid = -1;
    let screens = {};
    return {
      matchHandlers: {},
      dispatch(pointMap) {
        screens = pointMap;
        subscribers.forEach((func) => func(screens));
        return subscribers.size >= 1;
      },
      subscribe(func) {
        if (!subscribers.size) this.register();
        subUid += 1;
        subscribers.set(subUid, func);
        func(screens);
        return subUid;
      },
      unsubscribe(paramToken) {
        subscribers.delete(paramToken);
        if (!subscribers.size) this.unregister();
      },
      unregister() {
        Object.keys(responsiveMap).forEach((screen) => {
          const matchMediaQuery = responsiveMap[screen];
          const handler = this.matchHandlers[matchMediaQuery];
          handler === null || handler === void 0 ? void 0 : handler.mql.removeListener(handler === null || handler === void 0 ? void 0 : handler.listener);
        });
        subscribers.clear();
      },
      register() {
        Object.keys(responsiveMap).forEach((screen) => {
          const matchMediaQuery = responsiveMap[screen];
          const listener = (_ref) => {
            let {
              matches
            } = _ref;
            this.dispatch(_extends(_extends({}, screens), {
              [screen]: matches
            }));
          };
          const mql = window.matchMedia(matchMediaQuery);
          mql.addListener(listener);
          this.matchHandlers[matchMediaQuery] = {
            mql,
            listener
          };
          listener(mql);
        });
      },
      responsiveMap
    };
  });
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/grid/context.js
var RowContextKey = Symbol("rowContextKey");
var useProvideRow = (state) => {
  provide(RowContextKey, state);
};
var useInjectRow = () => {
  return inject(RowContextKey, {
    gutter: computed(() => void 0),
    wrap: computed(() => void 0),
    supportFlexGap: computed(() => void 0)
  });
};
var context_default = useProvideRow;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/grid/style/index.js
var genGridRowStyle = (token) => {
  const {
    componentCls
  } = token;
  return {
    // Grid system
    [componentCls]: {
      display: "flex",
      flexFlow: "row wrap",
      minWidth: 0,
      "&::before, &::after": {
        display: "flex"
      },
      "&-no-wrap": {
        flexWrap: "nowrap"
      },
      // The origin of the X-axis
      "&-start": {
        justifyContent: "flex-start"
      },
      // The center of the X-axis
      "&-center": {
        justifyContent: "center"
      },
      // The opposite of the X-axis
      "&-end": {
        justifyContent: "flex-end"
      },
      "&-space-between": {
        justifyContent: "space-between"
      },
      "&-space-around ": {
        justifyContent: "space-around"
      },
      "&-space-evenly ": {
        justifyContent: "space-evenly"
      },
      // Align at the top
      "&-top": {
        alignItems: "flex-start"
      },
      // Align at the center
      "&-middle": {
        alignItems: "center"
      },
      "&-bottom": {
        alignItems: "flex-end"
      }
    }
  };
};
var genGridColStyle = (token) => {
  const {
    componentCls
  } = token;
  return {
    // Grid system
    [componentCls]: {
      position: "relative",
      maxWidth: "100%",
      // Prevent columns from collapsing when empty
      minHeight: 1
    }
  };
};
var genLoopGridColumnsStyle = (token, sizeCls) => {
  const {
    componentCls,
    gridColumns
  } = token;
  const gridColumnsStyle = {};
  for (let i2 = gridColumns; i2 >= 0; i2--) {
    if (i2 === 0) {
      gridColumnsStyle[`${componentCls}${sizeCls}-${i2}`] = {
        display: "none"
      };
      gridColumnsStyle[`${componentCls}-push-${i2}`] = {
        insetInlineStart: "auto"
      };
      gridColumnsStyle[`${componentCls}-pull-${i2}`] = {
        insetInlineEnd: "auto"
      };
      gridColumnsStyle[`${componentCls}${sizeCls}-push-${i2}`] = {
        insetInlineStart: "auto"
      };
      gridColumnsStyle[`${componentCls}${sizeCls}-pull-${i2}`] = {
        insetInlineEnd: "auto"
      };
      gridColumnsStyle[`${componentCls}${sizeCls}-offset-${i2}`] = {
        marginInlineEnd: 0
      };
      gridColumnsStyle[`${componentCls}${sizeCls}-order-${i2}`] = {
        order: 0
      };
    } else {
      gridColumnsStyle[`${componentCls}${sizeCls}-${i2}`] = {
        display: "block",
        flex: `0 0 ${i2 / gridColumns * 100}%`,
        maxWidth: `${i2 / gridColumns * 100}%`
      };
      gridColumnsStyle[`${componentCls}${sizeCls}-push-${i2}`] = {
        insetInlineStart: `${i2 / gridColumns * 100}%`
      };
      gridColumnsStyle[`${componentCls}${sizeCls}-pull-${i2}`] = {
        insetInlineEnd: `${i2 / gridColumns * 100}%`
      };
      gridColumnsStyle[`${componentCls}${sizeCls}-offset-${i2}`] = {
        marginInlineStart: `${i2 / gridColumns * 100}%`
      };
      gridColumnsStyle[`${componentCls}${sizeCls}-order-${i2}`] = {
        order: i2
      };
    }
  }
  return gridColumnsStyle;
};
var genGridStyle = (token, sizeCls) => genLoopGridColumnsStyle(token, sizeCls);
var genGridMediaStyle = (token, screenSize, sizeCls) => ({
  [`@media (min-width: ${screenSize}px)`]: _extends({}, genGridStyle(token, sizeCls))
});
var useRowStyle = genComponentStyleHook("Grid", (token) => [genGridRowStyle(token)]);
var useColStyle = genComponentStyleHook("Grid", (token) => {
  const gridToken = merge(token, {
    gridColumns: 24
    // Row is divided into 24 parts in Grid
  });
  const gridMediaSizesMap = {
    "-sm": gridToken.screenSMMin,
    "-md": gridToken.screenMDMin,
    "-lg": gridToken.screenLGMin,
    "-xl": gridToken.screenXLMin,
    "-xxl": gridToken.screenXXLMin
  };
  return [genGridColStyle(gridToken), genGridStyle(gridToken, ""), genGridStyle(gridToken, "-xs"), Object.keys(gridMediaSizesMap).map((key) => genGridMediaStyle(gridToken, gridMediaSizesMap[key], key)).reduce((pre, cur) => _extends(_extends({}, pre), cur), {})];
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/grid/Row.js
var rowProps = () => ({
  align: someType([String, Object]),
  justify: someType([String, Object]),
  prefixCls: String,
  gutter: someType([Number, Array, Object], 0),
  wrap: {
    type: Boolean,
    default: void 0
  }
});
var ARow = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "ARow",
  inheritAttrs: false,
  props: rowProps(),
  setup(props, _ref) {
    let {
      slots,
      attrs
    } = _ref;
    const {
      prefixCls,
      direction
    } = useConfigInject_default("row", props);
    const [wrapSSR, hashId] = useRowStyle(prefixCls);
    let token;
    const responsiveObserve = useResponsiveObserver();
    const screens = ref({
      xs: true,
      sm: true,
      md: true,
      lg: true,
      xl: true,
      xxl: true
    });
    const curScreens = ref({
      xs: false,
      sm: false,
      md: false,
      lg: false,
      xl: false,
      xxl: false
    });
    const mergePropsByScreen = (oriProp) => {
      return computed(() => {
        if (typeof props[oriProp] === "string") {
          return props[oriProp];
        }
        if (typeof props[oriProp] !== "object") {
          return "";
        }
        for (let i2 = 0; i2 < responsiveArray.length; i2++) {
          const breakpoint = responsiveArray[i2];
          if (!curScreens.value[breakpoint]) continue;
          const curVal = props[oriProp][breakpoint];
          if (curVal !== void 0) {
            return curVal;
          }
        }
        return "";
      });
    };
    const mergeAlign = mergePropsByScreen("align");
    const mergeJustify = mergePropsByScreen("justify");
    const supportFlexGap = useFlexGapSupport_default();
    onMounted(() => {
      token = responsiveObserve.value.subscribe((screen) => {
        curScreens.value = screen;
        const currentGutter = props.gutter || 0;
        if (!Array.isArray(currentGutter) && typeof currentGutter === "object" || Array.isArray(currentGutter) && (typeof currentGutter[0] === "object" || typeof currentGutter[1] === "object")) {
          screens.value = screen;
        }
      });
    });
    onBeforeUnmount(() => {
      responsiveObserve.value.unsubscribe(token);
    });
    const gutter = computed(() => {
      const results = [void 0, void 0];
      const {
        gutter: gutter2 = 0
      } = props;
      const normalizedGutter = Array.isArray(gutter2) ? gutter2 : [gutter2, void 0];
      normalizedGutter.forEach((g, index2) => {
        if (typeof g === "object") {
          for (let i2 = 0; i2 < responsiveArray.length; i2++) {
            const breakpoint = responsiveArray[i2];
            if (screens.value[breakpoint] && g[breakpoint] !== void 0) {
              results[index2] = g[breakpoint];
              break;
            }
          }
        } else {
          results[index2] = g;
        }
      });
      return results;
    });
    context_default({
      gutter,
      supportFlexGap,
      wrap: computed(() => props.wrap)
    });
    const classes = computed(() => classNames_default(prefixCls.value, {
      [`${prefixCls.value}-no-wrap`]: props.wrap === false,
      [`${prefixCls.value}-${mergeJustify.value}`]: mergeJustify.value,
      [`${prefixCls.value}-${mergeAlign.value}`]: mergeAlign.value,
      [`${prefixCls.value}-rtl`]: direction.value === "rtl"
    }, attrs.class, hashId.value));
    const rowStyle = computed(() => {
      const gt = gutter.value;
      const style = {};
      const horizontalGutter = gt[0] != null && gt[0] > 0 ? `${gt[0] / -2}px` : void 0;
      const verticalGutter = gt[1] != null && gt[1] > 0 ? `${gt[1] / -2}px` : void 0;
      if (horizontalGutter) {
        style.marginLeft = horizontalGutter;
        style.marginRight = horizontalGutter;
      }
      if (supportFlexGap.value) {
        style.rowGap = `${gt[1]}px`;
      } else if (verticalGutter) {
        style.marginTop = verticalGutter;
        style.marginBottom = verticalGutter;
      }
      return style;
    });
    return () => {
      var _a;
      return wrapSSR(createVNode("div", _objectSpread2(_objectSpread2({}, attrs), {}, {
        "class": classes.value,
        "style": _extends(_extends({}, rowStyle.value), attrs.style)
      }), [(_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots)]));
    };
  }
});
var Row_default = ARow;

// ../../node_modules/.pnpm/async-validator@4.2.5/node_modules/async-validator/dist-web/index.js
function _extends2() {
  _extends2 = Object.assign ? Object.assign.bind() : function(target) {
    for (var i2 = 1; i2 < arguments.length; i2++) {
      var source = arguments[i2];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends2.apply(this, arguments);
}
function _inheritsLoose(subClass, superClass) {
  subClass.prototype = Object.create(superClass.prototype);
  subClass.prototype.constructor = subClass;
  _setPrototypeOf(subClass, superClass);
}
function _getPrototypeOf(o) {
  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf2(o2) {
    return o2.__proto__ || Object.getPrototypeOf(o2);
  };
  return _getPrototypeOf(o);
}
function _setPrototypeOf(o, p) {
  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf2(o2, p2) {
    o2.__proto__ = p2;
    return o2;
  };
  return _setPrototypeOf(o, p);
}
function _isNativeReflectConstruct() {
  if (typeof Reflect === "undefined" || !Reflect.construct) return false;
  if (Reflect.construct.sham) return false;
  if (typeof Proxy === "function") return true;
  try {
    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
    }));
    return true;
  } catch (e2) {
    return false;
  }
}
function _construct(Parent, args, Class) {
  if (_isNativeReflectConstruct()) {
    _construct = Reflect.construct.bind();
  } else {
    _construct = function _construct2(Parent2, args2, Class2) {
      var a = [null];
      a.push.apply(a, args2);
      var Constructor = Function.bind.apply(Parent2, a);
      var instance = new Constructor();
      if (Class2) _setPrototypeOf(instance, Class2.prototype);
      return instance;
    };
  }
  return _construct.apply(null, arguments);
}
function _isNativeFunction(fn) {
  return Function.toString.call(fn).indexOf("[native code]") !== -1;
}
function _wrapNativeSuper(Class) {
  var _cache = typeof Map === "function" ? /* @__PURE__ */ new Map() : void 0;
  _wrapNativeSuper = function _wrapNativeSuper2(Class2) {
    if (Class2 === null || !_isNativeFunction(Class2)) return Class2;
    if (typeof Class2 !== "function") {
      throw new TypeError("Super expression must either be null or a function");
    }
    if (typeof _cache !== "undefined") {
      if (_cache.has(Class2)) return _cache.get(Class2);
      _cache.set(Class2, Wrapper);
    }
    function Wrapper() {
      return _construct(Class2, arguments, _getPrototypeOf(this).constructor);
    }
    Wrapper.prototype = Object.create(Class2.prototype, {
      constructor: {
        value: Wrapper,
        enumerable: false,
        writable: true,
        configurable: true
      }
    });
    return _setPrototypeOf(Wrapper, Class2);
  };
  return _wrapNativeSuper(Class);
}
var formatRegExp = /%[sdj%]/g;
var warning2 = function warning3() {
};
if (typeof process !== "undefined" && process.env && true && typeof window !== "undefined" && typeof document !== "undefined") {
  warning2 = function warning4(type4, errors) {
    if (typeof console !== "undefined" && console.warn && typeof ASYNC_VALIDATOR_NO_WARNING === "undefined") {
      if (errors.every(function(e2) {
        return typeof e2 === "string";
      })) {
        console.warn(type4, errors);
      }
    }
  };
}
function convertFieldsError(errors) {
  if (!errors || !errors.length) return null;
  var fields = {};
  errors.forEach(function(error) {
    var field = error.field;
    fields[field] = fields[field] || [];
    fields[field].push(error);
  });
  return fields;
}
function format(template) {
  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
    args[_key - 1] = arguments[_key];
  }
  var i2 = 0;
  var len = args.length;
  if (typeof template === "function") {
    return template.apply(null, args);
  }
  if (typeof template === "string") {
    var str = template.replace(formatRegExp, function(x) {
      if (x === "%%") {
        return "%";
      }
      if (i2 >= len) {
        return x;
      }
      switch (x) {
        case "%s":
          return String(args[i2++]);
        case "%d":
          return Number(args[i2++]);
        case "%j":
          try {
            return JSON.stringify(args[i2++]);
          } catch (_) {
            return "[Circular]";
          }
          break;
        default:
          return x;
      }
    });
    return str;
  }
  return template;
}
function isNativeStringType(type4) {
  return type4 === "string" || type4 === "url" || type4 === "hex" || type4 === "email" || type4 === "date" || type4 === "pattern";
}
function isEmptyValue(value, type4) {
  if (value === void 0 || value === null) {
    return true;
  }
  if (type4 === "array" && Array.isArray(value) && !value.length) {
    return true;
  }
  if (isNativeStringType(type4) && typeof value === "string" && !value) {
    return true;
  }
  return false;
}
function asyncParallelArray(arr, func, callback) {
  var results = [];
  var total = 0;
  var arrLength = arr.length;
  function count(errors) {
    results.push.apply(results, errors || []);
    total++;
    if (total === arrLength) {
      callback(results);
    }
  }
  arr.forEach(function(a) {
    func(a, count);
  });
}
function asyncSerialArray(arr, func, callback) {
  var index2 = 0;
  var arrLength = arr.length;
  function next(errors) {
    if (errors && errors.length) {
      callback(errors);
      return;
    }
    var original = index2;
    index2 = index2 + 1;
    if (original < arrLength) {
      func(arr[original], next);
    } else {
      callback([]);
    }
  }
  next([]);
}
function flattenObjArr(objArr) {
  var ret = [];
  Object.keys(objArr).forEach(function(k) {
    ret.push.apply(ret, objArr[k] || []);
  });
  return ret;
}
var AsyncValidationError = function(_Error) {
  _inheritsLoose(AsyncValidationError2, _Error);
  function AsyncValidationError2(errors, fields) {
    var _this;
    _this = _Error.call(this, "Async Validation Error") || this;
    _this.errors = errors;
    _this.fields = fields;
    return _this;
  }
  return AsyncValidationError2;
}(_wrapNativeSuper(Error));
function asyncMap(objArr, option, func, callback, source) {
  if (option.first) {
    var _pending = new Promise(function(resolve, reject) {
      var next = function next2(errors) {
        callback(errors);
        return errors.length ? reject(new AsyncValidationError(errors, convertFieldsError(errors))) : resolve(source);
      };
      var flattenArr = flattenObjArr(objArr);
      asyncSerialArray(flattenArr, func, next);
    });
    _pending["catch"](function(e2) {
      return e2;
    });
    return _pending;
  }
  var firstFields = option.firstFields === true ? Object.keys(objArr) : option.firstFields || [];
  var objArrKeys = Object.keys(objArr);
  var objArrLength = objArrKeys.length;
  var total = 0;
  var results = [];
  var pending = new Promise(function(resolve, reject) {
    var next = function next2(errors) {
      results.push.apply(results, errors);
      total++;
      if (total === objArrLength) {
        callback(results);
        return results.length ? reject(new AsyncValidationError(results, convertFieldsError(results))) : resolve(source);
      }
    };
    if (!objArrKeys.length) {
      callback(results);
      resolve(source);
    }
    objArrKeys.forEach(function(key) {
      var arr = objArr[key];
      if (firstFields.indexOf(key) !== -1) {
        asyncSerialArray(arr, func, next);
      } else {
        asyncParallelArray(arr, func, next);
      }
    });
  });
  pending["catch"](function(e2) {
    return e2;
  });
  return pending;
}
function isErrorObj(obj) {
  return !!(obj && obj.message !== void 0);
}
function getValue(value, path) {
  var v = value;
  for (var i2 = 0; i2 < path.length; i2++) {
    if (v == void 0) {
      return v;
    }
    v = v[path[i2]];
  }
  return v;
}
function complementError(rule, source) {
  return function(oe) {
    var fieldValue;
    if (rule.fullFields) {
      fieldValue = getValue(source, rule.fullFields);
    } else {
      fieldValue = source[oe.field || rule.fullField];
    }
    if (isErrorObj(oe)) {
      oe.field = oe.field || rule.fullField;
      oe.fieldValue = fieldValue;
      return oe;
    }
    return {
      message: typeof oe === "function" ? oe() : oe,
      fieldValue,
      field: oe.field || rule.fullField
    };
  };
}
function deepMerge(target, source) {
  if (source) {
    for (var s in source) {
      if (source.hasOwnProperty(s)) {
        var value = source[s];
        if (typeof value === "object" && typeof target[s] === "object") {
          target[s] = _extends2({}, target[s], value);
        } else {
          target[s] = value;
        }
      }
    }
  }
  return target;
}
var required$1 = function required(rule, value, source, errors, options, type4) {
  if (rule.required && (!source.hasOwnProperty(rule.field) || isEmptyValue(value, type4 || rule.type))) {
    errors.push(format(options.messages.required, rule.fullField));
  }
};
var whitespace = function whitespace2(rule, value, source, errors, options) {
  if (/^\s+$/.test(value) || value === "") {
    errors.push(format(options.messages.whitespace, rule.fullField));
  }
};
var urlReg;
var getUrlRegex = function() {
  if (urlReg) {
    return urlReg;
  }
  var word = "[a-fA-F\\d:]";
  var b = function b2(options) {
    return options && options.includeBoundaries ? "(?:(?<=\\s|^)(?=" + word + ")|(?<=" + word + ")(?=\\s|$))" : "";
  };
  var v4 = "(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}";
  var v6seg = "[a-fA-F\\d]{1,4}";
  var v6 = ("\n(?:\n(?:" + v6seg + ":){7}(?:" + v6seg + "|:)|                                    // 1:2:3:4:5:6:7::  1:2:3:4:5:6:7:8\n(?:" + v6seg + ":){6}(?:" + v4 + "|:" + v6seg + "|:)|                             // 1:2:3:4:5:6::    1:2:3:4:5:6::8   1:2:3:4:5:6::8  1:2:3:4:5:6::*******\n(?:" + v6seg + ":){5}(?::" + v4 + "|(?::" + v6seg + "){1,2}|:)|                   // 1:2:3:4:5::      1:2:3:4:5::7:8   1:2:3:4:5::8    1:2:3:4:5::7:*******\n(?:" + v6seg + ":){4}(?:(?::" + v6seg + "){0,1}:" + v4 + "|(?::" + v6seg + "){1,3}|:)| // 1:2:3:4::        1:2:3:4::6:7:8   1:2:3:4::8      1:2:3:4::6:7:*******\n(?:" + v6seg + ":){3}(?:(?::" + v6seg + "){0,2}:" + v4 + "|(?::" + v6seg + "){1,4}|:)| // 1:2:3::          1:2:3::5:6:7:8   1:2:3::8        1:2:3::5:6:7:*******\n(?:" + v6seg + ":){2}(?:(?::" + v6seg + "){0,3}:" + v4 + "|(?::" + v6seg + "){1,5}|:)| // 1:2::            1:2::4:5:6:7:8   1:2::8          1:2::4:5:6:7:*******\n(?:" + v6seg + ":){1}(?:(?::" + v6seg + "){0,4}:" + v4 + "|(?::" + v6seg + "){1,6}|:)| // 1::              1::3:4:5:6:7:8   1::8            1::3:4:5:6:7:*******\n(?::(?:(?::" + v6seg + "){0,5}:" + v4 + "|(?::" + v6seg + "){1,7}|:))             // ::2:3:4:5:6:7:8  ::2:3:4:5:6:7:8  ::8             ::*******\n)(?:%[0-9a-zA-Z]{1,})?                                             // %eth0            %1\n").replace(/\s*\/\/.*$/gm, "").replace(/\n/g, "").trim();
  var v46Exact = new RegExp("(?:^" + v4 + "$)|(?:^" + v6 + "$)");
  var v4exact = new RegExp("^" + v4 + "$");
  var v6exact = new RegExp("^" + v6 + "$");
  var ip = function ip2(options) {
    return options && options.exact ? v46Exact : new RegExp("(?:" + b(options) + v4 + b(options) + ")|(?:" + b(options) + v6 + b(options) + ")", "g");
  };
  ip.v4 = function(options) {
    return options && options.exact ? v4exact : new RegExp("" + b(options) + v4 + b(options), "g");
  };
  ip.v6 = function(options) {
    return options && options.exact ? v6exact : new RegExp("" + b(options) + v6 + b(options), "g");
  };
  var protocol = "(?:(?:[a-z]+:)?//)";
  var auth = "(?:\\S+(?::\\S*)?@)?";
  var ipv4 = ip.v4().source;
  var ipv6 = ip.v6().source;
  var host = "(?:(?:[a-z\\u00a1-\\uffff0-9][-_]*)*[a-z\\u00a1-\\uffff0-9]+)";
  var domain = "(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*";
  var tld = "(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))";
  var port = "(?::\\d{2,5})?";
  var path = '(?:[/?#][^\\s"]*)?';
  var regex = "(?:" + protocol + "|www\\.)" + auth + "(?:localhost|" + ipv4 + "|" + ipv6 + "|" + host + domain + tld + ")" + port + path;
  urlReg = new RegExp("(?:^" + regex + "$)", "i");
  return urlReg;
};
var pattern$2 = {
  // http://emailregex.com/
  email: /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+\.)+[a-zA-Z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}))$/,
  // url: new RegExp(
  //   '^(?!mailto:)(?:(?:http|https|ftp)://|//)(?:\\S+(?::\\S*)?@)?(?:(?:(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[0-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]+-*)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]+-*)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))|localhost)(?::\\d{2,5})?(?:(/|\\?|#)[^\\s]*)?$',
  //   'i',
  // ),
  hex: /^#?([a-f0-9]{6}|[a-f0-9]{3})$/i
};
var types = {
  integer: function integer(value) {
    return types.number(value) && parseInt(value, 10) === value;
  },
  "float": function float(value) {
    return types.number(value) && !types.integer(value);
  },
  array: function array(value) {
    return Array.isArray(value);
  },
  regexp: function regexp(value) {
    if (value instanceof RegExp) {
      return true;
    }
    try {
      return !!new RegExp(value);
    } catch (e2) {
      return false;
    }
  },
  date: function date(value) {
    return typeof value.getTime === "function" && typeof value.getMonth === "function" && typeof value.getYear === "function" && !isNaN(value.getTime());
  },
  number: function number(value) {
    if (isNaN(value)) {
      return false;
    }
    return typeof value === "number";
  },
  object: function object(value) {
    return typeof value === "object" && !types.array(value);
  },
  method: function method(value) {
    return typeof value === "function";
  },
  email: function email(value) {
    return typeof value === "string" && value.length <= 320 && !!value.match(pattern$2.email);
  },
  url: function url(value) {
    return typeof value === "string" && value.length <= 2048 && !!value.match(getUrlRegex());
  },
  hex: function hex(value) {
    return typeof value === "string" && !!value.match(pattern$2.hex);
  }
};
var type$1 = function type(rule, value, source, errors, options) {
  if (rule.required && value === void 0) {
    required$1(rule, value, source, errors, options);
    return;
  }
  var custom = ["integer", "float", "array", "regexp", "object", "method", "email", "number", "date", "url", "hex"];
  var ruleType = rule.type;
  if (custom.indexOf(ruleType) > -1) {
    if (!types[ruleType](value)) {
      errors.push(format(options.messages.types[ruleType], rule.fullField, rule.type));
    }
  } else if (ruleType && typeof value !== rule.type) {
    errors.push(format(options.messages.types[ruleType], rule.fullField, rule.type));
  }
};
var range = function range2(rule, value, source, errors, options) {
  var len = typeof rule.len === "number";
  var min = typeof rule.min === "number";
  var max = typeof rule.max === "number";
  var spRegexp = /[\uD800-\uDBFF][\uDC00-\uDFFF]/g;
  var val = value;
  var key = null;
  var num = typeof value === "number";
  var str = typeof value === "string";
  var arr = Array.isArray(value);
  if (num) {
    key = "number";
  } else if (str) {
    key = "string";
  } else if (arr) {
    key = "array";
  }
  if (!key) {
    return false;
  }
  if (arr) {
    val = value.length;
  }
  if (str) {
    val = value.replace(spRegexp, "_").length;
  }
  if (len) {
    if (val !== rule.len) {
      errors.push(format(options.messages[key].len, rule.fullField, rule.len));
    }
  } else if (min && !max && val < rule.min) {
    errors.push(format(options.messages[key].min, rule.fullField, rule.min));
  } else if (max && !min && val > rule.max) {
    errors.push(format(options.messages[key].max, rule.fullField, rule.max));
  } else if (min && max && (val < rule.min || val > rule.max)) {
    errors.push(format(options.messages[key].range, rule.fullField, rule.min, rule.max));
  }
};
var ENUM$1 = "enum";
var enumerable$1 = function enumerable(rule, value, source, errors, options) {
  rule[ENUM$1] = Array.isArray(rule[ENUM$1]) ? rule[ENUM$1] : [];
  if (rule[ENUM$1].indexOf(value) === -1) {
    errors.push(format(options.messages[ENUM$1], rule.fullField, rule[ENUM$1].join(", ")));
  }
};
var pattern$1 = function pattern(rule, value, source, errors, options) {
  if (rule.pattern) {
    if (rule.pattern instanceof RegExp) {
      rule.pattern.lastIndex = 0;
      if (!rule.pattern.test(value)) {
        errors.push(format(options.messages.pattern.mismatch, rule.fullField, value, rule.pattern));
      }
    } else if (typeof rule.pattern === "string") {
      var _pattern = new RegExp(rule.pattern);
      if (!_pattern.test(value)) {
        errors.push(format(options.messages.pattern.mismatch, rule.fullField, value, rule.pattern));
      }
    }
  }
};
var rules = {
  required: required$1,
  whitespace,
  type: type$1,
  range,
  "enum": enumerable$1,
  pattern: pattern$1
};
var string = function string2(rule, value, callback, source, options) {
  var errors = [];
  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);
  if (validate) {
    if (isEmptyValue(value, "string") && !rule.required) {
      return callback();
    }
    rules.required(rule, value, source, errors, options, "string");
    if (!isEmptyValue(value, "string")) {
      rules.type(rule, value, source, errors, options);
      rules.range(rule, value, source, errors, options);
      rules.pattern(rule, value, source, errors, options);
      if (rule.whitespace === true) {
        rules.whitespace(rule, value, source, errors, options);
      }
    }
  }
  callback(errors);
};
var method2 = function method3(rule, value, callback, source, options) {
  var errors = [];
  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);
  if (validate) {
    if (isEmptyValue(value) && !rule.required) {
      return callback();
    }
    rules.required(rule, value, source, errors, options);
    if (value !== void 0) {
      rules.type(rule, value, source, errors, options);
    }
  }
  callback(errors);
};
var number2 = function number3(rule, value, callback, source, options) {
  var errors = [];
  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);
  if (validate) {
    if (value === "") {
      value = void 0;
    }
    if (isEmptyValue(value) && !rule.required) {
      return callback();
    }
    rules.required(rule, value, source, errors, options);
    if (value !== void 0) {
      rules.type(rule, value, source, errors, options);
      rules.range(rule, value, source, errors, options);
    }
  }
  callback(errors);
};
var _boolean = function _boolean2(rule, value, callback, source, options) {
  var errors = [];
  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);
  if (validate) {
    if (isEmptyValue(value) && !rule.required) {
      return callback();
    }
    rules.required(rule, value, source, errors, options);
    if (value !== void 0) {
      rules.type(rule, value, source, errors, options);
    }
  }
  callback(errors);
};
var regexp2 = function regexp3(rule, value, callback, source, options) {
  var errors = [];
  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);
  if (validate) {
    if (isEmptyValue(value) && !rule.required) {
      return callback();
    }
    rules.required(rule, value, source, errors, options);
    if (!isEmptyValue(value)) {
      rules.type(rule, value, source, errors, options);
    }
  }
  callback(errors);
};
var integer2 = function integer3(rule, value, callback, source, options) {
  var errors = [];
  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);
  if (validate) {
    if (isEmptyValue(value) && !rule.required) {
      return callback();
    }
    rules.required(rule, value, source, errors, options);
    if (value !== void 0) {
      rules.type(rule, value, source, errors, options);
      rules.range(rule, value, source, errors, options);
    }
  }
  callback(errors);
};
var floatFn = function floatFn2(rule, value, callback, source, options) {
  var errors = [];
  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);
  if (validate) {
    if (isEmptyValue(value) && !rule.required) {
      return callback();
    }
    rules.required(rule, value, source, errors, options);
    if (value !== void 0) {
      rules.type(rule, value, source, errors, options);
      rules.range(rule, value, source, errors, options);
    }
  }
  callback(errors);
};
var array2 = function array3(rule, value, callback, source, options) {
  var errors = [];
  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);
  if (validate) {
    if ((value === void 0 || value === null) && !rule.required) {
      return callback();
    }
    rules.required(rule, value, source, errors, options, "array");
    if (value !== void 0 && value !== null) {
      rules.type(rule, value, source, errors, options);
      rules.range(rule, value, source, errors, options);
    }
  }
  callback(errors);
};
var object2 = function object3(rule, value, callback, source, options) {
  var errors = [];
  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);
  if (validate) {
    if (isEmptyValue(value) && !rule.required) {
      return callback();
    }
    rules.required(rule, value, source, errors, options);
    if (value !== void 0) {
      rules.type(rule, value, source, errors, options);
    }
  }
  callback(errors);
};
var ENUM = "enum";
var enumerable2 = function enumerable3(rule, value, callback, source, options) {
  var errors = [];
  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);
  if (validate) {
    if (isEmptyValue(value) && !rule.required) {
      return callback();
    }
    rules.required(rule, value, source, errors, options);
    if (value !== void 0) {
      rules[ENUM](rule, value, source, errors, options);
    }
  }
  callback(errors);
};
var pattern2 = function pattern3(rule, value, callback, source, options) {
  var errors = [];
  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);
  if (validate) {
    if (isEmptyValue(value, "string") && !rule.required) {
      return callback();
    }
    rules.required(rule, value, source, errors, options);
    if (!isEmptyValue(value, "string")) {
      rules.pattern(rule, value, source, errors, options);
    }
  }
  callback(errors);
};
var date2 = function date3(rule, value, callback, source, options) {
  var errors = [];
  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);
  if (validate) {
    if (isEmptyValue(value, "date") && !rule.required) {
      return callback();
    }
    rules.required(rule, value, source, errors, options);
    if (!isEmptyValue(value, "date")) {
      var dateObject;
      if (value instanceof Date) {
        dateObject = value;
      } else {
        dateObject = new Date(value);
      }
      rules.type(rule, dateObject, source, errors, options);
      if (dateObject) {
        rules.range(rule, dateObject.getTime(), source, errors, options);
      }
    }
  }
  callback(errors);
};
var required2 = function required3(rule, value, callback, source, options) {
  var errors = [];
  var type4 = Array.isArray(value) ? "array" : typeof value;
  rules.required(rule, value, source, errors, options, type4);
  callback(errors);
};
var type2 = function type3(rule, value, callback, source, options) {
  var ruleType = rule.type;
  var errors = [];
  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);
  if (validate) {
    if (isEmptyValue(value, ruleType) && !rule.required) {
      return callback();
    }
    rules.required(rule, value, source, errors, options, ruleType);
    if (!isEmptyValue(value, ruleType)) {
      rules.type(rule, value, source, errors, options);
    }
  }
  callback(errors);
};
var any = function any2(rule, value, callback, source, options) {
  var errors = [];
  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);
  if (validate) {
    if (isEmptyValue(value) && !rule.required) {
      return callback();
    }
    rules.required(rule, value, source, errors, options);
  }
  callback(errors);
};
var validators = {
  string,
  method: method2,
  number: number2,
  "boolean": _boolean,
  regexp: regexp2,
  integer: integer2,
  "float": floatFn,
  array: array2,
  object: object2,
  "enum": enumerable2,
  pattern: pattern2,
  date: date2,
  url: type2,
  hex: type2,
  email: type2,
  required: required2,
  any
};
function newMessages() {
  return {
    "default": "Validation error on field %s",
    required: "%s is required",
    "enum": "%s must be one of %s",
    whitespace: "%s cannot be empty",
    date: {
      format: "%s date %s is invalid for format %s",
      parse: "%s date could not be parsed, %s is invalid ",
      invalid: "%s date %s is invalid"
    },
    types: {
      string: "%s is not a %s",
      method: "%s is not a %s (function)",
      array: "%s is not an %s",
      object: "%s is not an %s",
      number: "%s is not a %s",
      date: "%s is not a %s",
      "boolean": "%s is not a %s",
      integer: "%s is not an %s",
      "float": "%s is not a %s",
      regexp: "%s is not a valid %s",
      email: "%s is not a valid %s",
      url: "%s is not a valid %s",
      hex: "%s is not a valid %s"
    },
    string: {
      len: "%s must be exactly %s characters",
      min: "%s must be at least %s characters",
      max: "%s cannot be longer than %s characters",
      range: "%s must be between %s and %s characters"
    },
    number: {
      len: "%s must equal %s",
      min: "%s cannot be less than %s",
      max: "%s cannot be greater than %s",
      range: "%s must be between %s and %s"
    },
    array: {
      len: "%s must be exactly %s in length",
      min: "%s cannot be less than %s in length",
      max: "%s cannot be greater than %s in length",
      range: "%s must be between %s and %s in length"
    },
    pattern: {
      mismatch: "%s value %s does not match pattern %s"
    },
    clone: function clone() {
      var cloned = JSON.parse(JSON.stringify(this));
      cloned.clone = this.clone;
      return cloned;
    }
  };
}
var messages = newMessages();
var Schema = function() {
  function Schema2(descriptor) {
    this.rules = null;
    this._messages = messages;
    this.define(descriptor);
  }
  var _proto = Schema2.prototype;
  _proto.define = function define(rules2) {
    var _this = this;
    if (!rules2) {
      throw new Error("Cannot configure a schema with no rules");
    }
    if (typeof rules2 !== "object" || Array.isArray(rules2)) {
      throw new Error("Rules must be an object");
    }
    this.rules = {};
    Object.keys(rules2).forEach(function(name) {
      var item = rules2[name];
      _this.rules[name] = Array.isArray(item) ? item : [item];
    });
  };
  _proto.messages = function messages2(_messages) {
    if (_messages) {
      this._messages = deepMerge(newMessages(), _messages);
    }
    return this._messages;
  };
  _proto.validate = function validate(source_, o, oc) {
    var _this2 = this;
    if (o === void 0) {
      o = {};
    }
    if (oc === void 0) {
      oc = function oc2() {
      };
    }
    var source = source_;
    var options = o;
    var callback = oc;
    if (typeof options === "function") {
      callback = options;
      options = {};
    }
    if (!this.rules || Object.keys(this.rules).length === 0) {
      if (callback) {
        callback(null, source);
      }
      return Promise.resolve(source);
    }
    function complete(results) {
      var errors = [];
      var fields = {};
      function add(e2) {
        if (Array.isArray(e2)) {
          var _errors;
          errors = (_errors = errors).concat.apply(_errors, e2);
        } else {
          errors.push(e2);
        }
      }
      for (var i2 = 0; i2 < results.length; i2++) {
        add(results[i2]);
      }
      if (!errors.length) {
        callback(null, source);
      } else {
        fields = convertFieldsError(errors);
        callback(errors, fields);
      }
    }
    if (options.messages) {
      var messages$1 = this.messages();
      if (messages$1 === messages) {
        messages$1 = newMessages();
      }
      deepMerge(messages$1, options.messages);
      options.messages = messages$1;
    } else {
      options.messages = this.messages();
    }
    var series = {};
    var keys = options.keys || Object.keys(this.rules);
    keys.forEach(function(z) {
      var arr = _this2.rules[z];
      var value = source[z];
      arr.forEach(function(r2) {
        var rule = r2;
        if (typeof rule.transform === "function") {
          if (source === source_) {
            source = _extends2({}, source);
          }
          value = source[z] = rule.transform(value);
        }
        if (typeof rule === "function") {
          rule = {
            validator: rule
          };
        } else {
          rule = _extends2({}, rule);
        }
        rule.validator = _this2.getValidationMethod(rule);
        if (!rule.validator) {
          return;
        }
        rule.field = z;
        rule.fullField = rule.fullField || z;
        rule.type = _this2.getType(rule);
        series[z] = series[z] || [];
        series[z].push({
          rule,
          value,
          source,
          field: z
        });
      });
    });
    var errorFields = {};
    return asyncMap(series, options, function(data, doIt) {
      var rule = data.rule;
      var deep = (rule.type === "object" || rule.type === "array") && (typeof rule.fields === "object" || typeof rule.defaultField === "object");
      deep = deep && (rule.required || !rule.required && data.value);
      rule.field = data.field;
      function addFullField(key, schema) {
        return _extends2({}, schema, {
          fullField: rule.fullField + "." + key,
          fullFields: rule.fullFields ? [].concat(rule.fullFields, [key]) : [key]
        });
      }
      function cb(e2) {
        if (e2 === void 0) {
          e2 = [];
        }
        var errorList = Array.isArray(e2) ? e2 : [e2];
        if (!options.suppressWarning && errorList.length) {
          Schema2.warning("async-validator:", errorList);
        }
        if (errorList.length && rule.message !== void 0) {
          errorList = [].concat(rule.message);
        }
        var filledErrors = errorList.map(complementError(rule, source));
        if (options.first && filledErrors.length) {
          errorFields[rule.field] = 1;
          return doIt(filledErrors);
        }
        if (!deep) {
          doIt(filledErrors);
        } else {
          if (rule.required && !data.value) {
            if (rule.message !== void 0) {
              filledErrors = [].concat(rule.message).map(complementError(rule, source));
            } else if (options.error) {
              filledErrors = [options.error(rule, format(options.messages.required, rule.field))];
            }
            return doIt(filledErrors);
          }
          var fieldsSchema = {};
          if (rule.defaultField) {
            Object.keys(data.value).map(function(key) {
              fieldsSchema[key] = rule.defaultField;
            });
          }
          fieldsSchema = _extends2({}, fieldsSchema, data.rule.fields);
          var paredFieldsSchema = {};
          Object.keys(fieldsSchema).forEach(function(field) {
            var fieldSchema = fieldsSchema[field];
            var fieldSchemaList = Array.isArray(fieldSchema) ? fieldSchema : [fieldSchema];
            paredFieldsSchema[field] = fieldSchemaList.map(addFullField.bind(null, field));
          });
          var schema = new Schema2(paredFieldsSchema);
          schema.messages(options.messages);
          if (data.rule.options) {
            data.rule.options.messages = options.messages;
            data.rule.options.error = options.error;
          }
          schema.validate(data.value, data.rule.options || options, function(errs) {
            var finalErrors = [];
            if (filledErrors && filledErrors.length) {
              finalErrors.push.apply(finalErrors, filledErrors);
            }
            if (errs && errs.length) {
              finalErrors.push.apply(finalErrors, errs);
            }
            doIt(finalErrors.length ? finalErrors : null);
          });
        }
      }
      var res;
      if (rule.asyncValidator) {
        res = rule.asyncValidator(rule, data.value, cb, data.source, options);
      } else if (rule.validator) {
        try {
          res = rule.validator(rule, data.value, cb, data.source, options);
        } catch (error) {
          console.error == null ? void 0 : console.error(error);
          if (!options.suppressValidatorError) {
            setTimeout(function() {
              throw error;
            }, 0);
          }
          cb(error.message);
        }
        if (res === true) {
          cb();
        } else if (res === false) {
          cb(typeof rule.message === "function" ? rule.message(rule.fullField || rule.field) : rule.message || (rule.fullField || rule.field) + " fails");
        } else if (res instanceof Array) {
          cb(res);
        } else if (res instanceof Error) {
          cb(res.message);
        }
      }
      if (res && res.then) {
        res.then(function() {
          return cb();
        }, function(e2) {
          return cb(e2);
        });
      }
    }, function(results) {
      complete(results);
    }, source);
  };
  _proto.getType = function getType(rule) {
    if (rule.type === void 0 && rule.pattern instanceof RegExp) {
      rule.type = "pattern";
    }
    if (typeof rule.validator !== "function" && rule.type && !validators.hasOwnProperty(rule.type)) {
      throw new Error(format("Unknown rule type %s", rule.type));
    }
    return rule.type || "string";
  };
  _proto.getValidationMethod = function getValidationMethod(rule) {
    if (typeof rule.validator === "function") {
      return rule.validator;
    }
    var keys = Object.keys(rule);
    var messageIndex = keys.indexOf("message");
    if (messageIndex !== -1) {
      keys.splice(messageIndex, 1);
    }
    if (keys.length === 1 && keys[0] === "required") {
      return validators.required;
    }
    return validators[this.getType(rule)] || void 0;
  };
  return Schema2;
}();
Schema.register = function register(type4, validator) {
  if (typeof validator !== "function") {
    throw new Error("Cannot register a validator by type, validator is not a function");
  }
  validators[type4] = validator;
};
Schema.warning = warning2;
Schema.messages = messages;
Schema.validators = validators;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/form/utils/typeUtil.js
function toArray2(value) {
  if (value === void 0 || value === null) {
    return [];
  }
  return Array.isArray(value) ? value : [value];
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-util/get.js
function get(entity, path) {
  let current = entity;
  for (let i2 = 0; i2 < path.length; i2 += 1) {
    if (current === null || current === void 0) {
      return void 0;
    }
    current = current[path[i2]];
  }
  return current;
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-util/set.js
function internalSet(entity, paths, value, removeIfUndefined) {
  if (!paths.length) {
    return value;
  }
  const [path, ...restPath] = paths;
  let clone;
  if (!entity && typeof path === "number") {
    clone = [];
  } else if (Array.isArray(entity)) {
    clone = [...entity];
  } else {
    clone = _extends({}, entity);
  }
  if (removeIfUndefined && value === void 0 && restPath.length === 1) {
    delete clone[path][restPath[0]];
  } else {
    clone[path] = internalSet(clone[path], restPath, value, removeIfUndefined);
  }
  return clone;
}
function set(entity, paths, value) {
  let removeIfUndefined = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false;
  if (paths.length && removeIfUndefined && value === void 0 && !get(entity, paths.slice(0, -1))) {
    return entity;
  }
  return internalSet(entity, paths, value, removeIfUndefined);
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/form/utils/valueUtil.js
function getNamePath(path) {
  return toArray2(path);
}
function getValue2(store, namePath) {
  const value = get(store, namePath);
  return value;
}
function setValue(store, namePath, value) {
  let removeIfUndefined = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false;
  const newStore = set(store, namePath, value, removeIfUndefined);
  return newStore;
}
function containsNamePath(namePathList, namePath) {
  return namePathList && namePathList.some((path) => matchNamePath(path, namePath));
}
function isObject(obj) {
  return typeof obj === "object" && obj !== null && Object.getPrototypeOf(obj) === Object.prototype;
}
function internalSetValues(store, values) {
  const newStore = Array.isArray(store) ? [...store] : _extends({}, store);
  if (!values) {
    return newStore;
  }
  Object.keys(values).forEach((key) => {
    const prevValue = newStore[key];
    const value = values[key];
    const recursive = isObject(prevValue) && isObject(value);
    newStore[key] = recursive ? internalSetValues(prevValue, value || {}) : value;
  });
  return newStore;
}
function setValues(store) {
  for (var _len = arguments.length, restValues = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
    restValues[_key - 1] = arguments[_key];
  }
  return restValues.reduce((current, newStore) => internalSetValues(current, newStore), store);
}
function cloneByNamePathList(store, namePathList) {
  let newStore = {};
  namePathList.forEach((namePath) => {
    const value = getValue2(store, namePath);
    newStore = setValue(newStore, namePath, value);
  });
  return newStore;
}
function matchNamePath(namePath, changedNamePath) {
  if (!namePath || !changedNamePath || namePath.length !== changedNamePath.length) {
    return false;
  }
  return namePath.every((nameUnit, i2) => changedNamePath[i2] === nameUnit);
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/form/utils/messages.js
var typeTemplate = "'${name}' is not a valid ${type}";
var defaultValidateMessages = {
  default: "Validation error on field '${name}'",
  required: "'${name}' is required",
  enum: "'${name}' must be one of [${enum}]",
  whitespace: "'${name}' cannot be empty",
  date: {
    format: "'${name}' is invalid for format date",
    parse: "'${name}' could not be parsed as date",
    invalid: "'${name}' is invalid date"
  },
  types: {
    string: typeTemplate,
    method: typeTemplate,
    array: typeTemplate,
    object: typeTemplate,
    number: typeTemplate,
    date: typeTemplate,
    boolean: typeTemplate,
    integer: typeTemplate,
    float: typeTemplate,
    regexp: typeTemplate,
    email: typeTemplate,
    url: typeTemplate,
    hex: typeTemplate
  },
  string: {
    len: "'${name}' must be exactly ${len} characters",
    min: "'${name}' must be at least ${min} characters",
    max: "'${name}' cannot be longer than ${max} characters",
    range: "'${name}' must be between ${min} and ${max} characters"
  },
  number: {
    len: "'${name}' must equal ${len}",
    min: "'${name}' cannot be less than ${min}",
    max: "'${name}' cannot be greater than ${max}",
    range: "'${name}' must be between ${min} and ${max}"
  },
  array: {
    len: "'${name}' must be exactly ${len} in length",
    min: "'${name}' cannot be less than ${min} in length",
    max: "'${name}' cannot be greater than ${max} in length",
    range: "'${name}' must be between ${min} and ${max} in length"
  },
  pattern: {
    mismatch: "'${name}' does not match pattern ${pattern}"
  }
};

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/form/utils/validateUtil.js
var __awaiter2 = function(thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function(resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function(resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e2) {
        reject(e2);
      }
    }
    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e2) {
        reject(e2);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
};
var AsyncValidator = Schema;
function replaceMessage(template, kv) {
  return template.replace(/\$\{\w+\}/g, (str) => {
    const key = str.slice(2, -1);
    return kv[key];
  });
}
function validateRule(name, value, rule, options, messageVariables) {
  return __awaiter2(this, void 0, void 0, function* () {
    const cloneRule = _extends({}, rule);
    delete cloneRule.ruleIndex;
    delete cloneRule.trigger;
    let subRuleField = null;
    if (cloneRule && cloneRule.type === "array" && cloneRule.defaultField) {
      subRuleField = cloneRule.defaultField;
      delete cloneRule.defaultField;
    }
    const validator = new AsyncValidator({
      [name]: [cloneRule]
    });
    const messages2 = setValues({}, defaultValidateMessages, options.validateMessages);
    validator.messages(messages2);
    let result = [];
    try {
      yield Promise.resolve(validator.validate({
        [name]: value
      }, _extends({}, options)));
    } catch (errObj) {
      if (errObj.errors) {
        result = errObj.errors.map((_ref, index2) => {
          let {
            message
          } = _ref;
          return (
            // Wrap VueNode with `key`
            isValidElement(message) ? cloneVNode(message, {
              key: `error_${index2}`
            }) : message
          );
        });
      } else {
        console.error(errObj);
        result = [messages2.default()];
      }
    }
    if (!result.length && subRuleField) {
      const subResults = yield Promise.all(value.map((subValue, i2) => validateRule(`${name}.${i2}`, subValue, subRuleField, options, messageVariables)));
      return subResults.reduce((prev, errors) => [...prev, ...errors], []);
    }
    const kv = _extends(_extends(_extends({}, rule), {
      name,
      enum: (rule.enum || []).join(", ")
    }), messageVariables);
    const fillVariableResult = result.map((error) => {
      if (typeof error === "string") {
        return replaceMessage(error, kv);
      }
      return error;
    });
    return fillVariableResult;
  });
}
function validateRules(namePath, value, rules2, options, validateFirst, messageVariables) {
  const name = namePath.join(".");
  const filledRules = rules2.map((currentRule, ruleIndex) => {
    const originValidatorFunc = currentRule.validator;
    const cloneRule = _extends(_extends({}, currentRule), {
      ruleIndex
    });
    if (originValidatorFunc) {
      cloneRule.validator = (rule, val, callback) => {
        let hasPromise = false;
        const wrappedCallback = function() {
          for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
            args[_key] = arguments[_key];
          }
          Promise.resolve().then(() => {
            warning(!hasPromise, "Your validator function has already return a promise. `callback` will be ignored.");
            if (!hasPromise) {
              callback(...args);
            }
          });
        };
        const promise = originValidatorFunc(rule, val, wrappedCallback);
        hasPromise = promise && typeof promise.then === "function" && typeof promise.catch === "function";
        warning(hasPromise, "`callback` is deprecated. Please return a promise instead.");
        if (hasPromise) {
          promise.then(() => {
            callback();
          }).catch((err) => {
            callback(err || " ");
          });
        }
      };
    }
    return cloneRule;
  }).sort((_ref2, _ref3) => {
    let {
      warningOnly: w1,
      ruleIndex: i1
    } = _ref2;
    let {
      warningOnly: w2,
      ruleIndex: i2
    } = _ref3;
    if (!!w1 === !!w2) {
      return i1 - i2;
    }
    if (w1) {
      return 1;
    }
    return -1;
  });
  let summaryPromise;
  if (validateFirst === true) {
    summaryPromise = new Promise((resolve, reject) => __awaiter2(this, void 0, void 0, function* () {
      for (let i2 = 0; i2 < filledRules.length; i2 += 1) {
        const rule = filledRules[i2];
        const errors = yield validateRule(name, value, rule, options, messageVariables);
        if (errors.length) {
          reject([{
            errors,
            rule
          }]);
          return;
        }
      }
      resolve([]);
    }));
  } else {
    const rulePromises = filledRules.map((rule) => validateRule(name, value, rule, options, messageVariables).then((errors) => ({
      errors,
      rule
    })));
    summaryPromise = (validateFirst ? finishOnFirstFailed(rulePromises) : finishOnAllFailed(rulePromises)).then((errors) => {
      return Promise.reject(errors);
    });
  }
  summaryPromise.catch((e2) => e2);
  return summaryPromise;
}
function finishOnAllFailed(rulePromises) {
  return __awaiter2(this, void 0, void 0, function* () {
    return Promise.all(rulePromises).then((errorsList) => {
      const errors = [].concat(...errorsList);
      return errors;
    });
  });
}
function finishOnFirstFailed(rulePromises) {
  return __awaiter2(this, void 0, void 0, function* () {
    let count = 0;
    return new Promise((resolve) => {
      rulePromises.forEach((promise) => {
        promise.then((ruleError) => {
          if (ruleError.errors.length) {
            resolve([ruleError]);
          }
          count += 1;
          if (count === rulePromises.length) {
            resolve([]);
          }
        });
      });
    });
  });
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/form/context.js
var FormContextKey = Symbol("formContextKey");
var useProvideForm = (state) => {
  provide(FormContextKey, state);
};
var useInjectForm = () => {
  return inject(FormContextKey, {
    name: computed(() => void 0),
    labelAlign: computed(() => "right"),
    vertical: computed(() => false),
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    addField: (_eventKey, _field) => {
    },
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    removeField: (_eventKey) => {
    },
    model: computed(() => void 0),
    rules: computed(() => void 0),
    colon: computed(() => void 0),
    labelWrap: computed(() => void 0),
    labelCol: computed(() => void 0),
    requiredMark: computed(() => false),
    validateTrigger: computed(() => void 0),
    onValidate: () => {
    },
    validateMessages: computed(() => defaultValidateMessages)
  });
};
var FormItemPrefixContextKey = Symbol("formItemPrefixContextKey");
var useProvideFormItemPrefix = (state) => {
  provide(FormItemPrefixContextKey, state);
};
var useInjectFormItemPrefix = () => {
  return inject(FormItemPrefixContextKey, {
    prefixCls: computed(() => "")
  });
};

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/grid/Col.js
function parseFlex(flex) {
  if (typeof flex === "number") {
    return `${flex} ${flex} auto`;
  }
  if (/^\d+(\.\d+)?(px|em|rem|%)$/.test(flex)) {
    return `0 0 ${flex}`;
  }
  return flex;
}
var colProps = () => ({
  span: [String, Number],
  order: [String, Number],
  offset: [String, Number],
  push: [String, Number],
  pull: [String, Number],
  xs: {
    type: [String, Number, Object],
    default: void 0
  },
  sm: {
    type: [String, Number, Object],
    default: void 0
  },
  md: {
    type: [String, Number, Object],
    default: void 0
  },
  lg: {
    type: [String, Number, Object],
    default: void 0
  },
  xl: {
    type: [String, Number, Object],
    default: void 0
  },
  xxl: {
    type: [String, Number, Object],
    default: void 0
  },
  prefixCls: String,
  flex: [String, Number]
});
var sizes = ["xs", "sm", "md", "lg", "xl", "xxl"];
var Col_default = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "ACol",
  inheritAttrs: false,
  props: colProps(),
  setup(props, _ref) {
    let {
      slots,
      attrs
    } = _ref;
    const {
      gutter,
      supportFlexGap,
      wrap
    } = useInjectRow();
    const {
      prefixCls,
      direction
    } = useConfigInject_default("col", props);
    const [wrapSSR, hashId] = useColStyle(prefixCls);
    const classes = computed(() => {
      const {
        span,
        order,
        offset,
        push,
        pull
      } = props;
      const pre = prefixCls.value;
      let sizeClassObj = {};
      sizes.forEach((size) => {
        let sizeProps = {};
        const propSize = props[size];
        if (typeof propSize === "number") {
          sizeProps.span = propSize;
        } else if (typeof propSize === "object") {
          sizeProps = propSize || {};
        }
        sizeClassObj = _extends(_extends({}, sizeClassObj), {
          [`${pre}-${size}-${sizeProps.span}`]: sizeProps.span !== void 0,
          [`${pre}-${size}-order-${sizeProps.order}`]: sizeProps.order || sizeProps.order === 0,
          [`${pre}-${size}-offset-${sizeProps.offset}`]: sizeProps.offset || sizeProps.offset === 0,
          [`${pre}-${size}-push-${sizeProps.push}`]: sizeProps.push || sizeProps.push === 0,
          [`${pre}-${size}-pull-${sizeProps.pull}`]: sizeProps.pull || sizeProps.pull === 0,
          [`${pre}-rtl`]: direction.value === "rtl"
        });
      });
      return classNames_default(pre, {
        [`${pre}-${span}`]: span !== void 0,
        [`${pre}-order-${order}`]: order,
        [`${pre}-offset-${offset}`]: offset,
        [`${pre}-push-${push}`]: push,
        [`${pre}-pull-${pull}`]: pull
      }, sizeClassObj, attrs.class, hashId.value);
    });
    const mergedStyle = computed(() => {
      const {
        flex
      } = props;
      const gutterVal = gutter.value;
      const style = {};
      if (gutterVal && gutterVal[0] > 0) {
        const horizontalGutter = `${gutterVal[0] / 2}px`;
        style.paddingLeft = horizontalGutter;
        style.paddingRight = horizontalGutter;
      }
      if (gutterVal && gutterVal[1] > 0 && !supportFlexGap.value) {
        const verticalGutter = `${gutterVal[1] / 2}px`;
        style.paddingTop = verticalGutter;
        style.paddingBottom = verticalGutter;
      }
      if (flex) {
        style.flex = parseFlex(flex);
        if (wrap.value === false && !style.minWidth) {
          style.minWidth = 0;
        }
      }
      return style;
    });
    return () => {
      var _a;
      return wrapSSR(createVNode("div", _objectSpread2(_objectSpread2({}, attrs), {}, {
        "class": classes.value,
        "style": [mergedStyle.value, attrs.style]
      }), [(_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots)]));
    };
  }
});

// ../../node_modules/.pnpm/@ant-design+icons-svg@4.4.2/node_modules/@ant-design/icons-svg/es/asn/QuestionCircleOutlined.js
var QuestionCircleOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z" } }, { "tag": "path", "attrs": { "d": "M623.6 316.7C593.6 290.4 554 276 512 276s-81.6 14.5-111.6 40.7C369.2 344 352 380.7 352 420v7.6c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V420c0-44.1 43.1-80 96-80s96 35.9 96 80c0 31.1-22 59.6-56.1 72.7-21.2 8.1-39.2 22.3-52.1 40.9-13.1 19-19.9 41.8-19.9 64.9V620c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-22.7a48.3 48.3 0 0130.9-44.8c59-22.7 97.1-74.7 97.1-132.5.1-39.3-17.1-76-48.3-103.3zM472 732a40 40 0 1080 0 40 40 0 10-80 0z" } }] }, "name": "question-circle", "theme": "outlined" };
var QuestionCircleOutlined_default = QuestionCircleOutlined;

// ../../node_modules/.pnpm/@ant-design+icons-vue@7.0.1_vue@3.5.17_typescript@5.8.3_/node_modules/@ant-design/icons-vue/es/icons/QuestionCircleOutlined.js
function _objectSpread9(target) {
  for (var i2 = 1; i2 < arguments.length; i2++) {
    var source = arguments[i2] != null ? Object(arguments[i2]) : {};
    var ownKeys = Object.keys(source);
    if (typeof Object.getOwnPropertySymbols === "function") {
      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
        return Object.getOwnPropertyDescriptor(source, sym).enumerable;
      }));
    }
    ownKeys.forEach(function(key) {
      _defineProperty8(target, key, source[key]);
    });
  }
  return target;
}
function _defineProperty8(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, { value, enumerable: true, configurable: true, writable: true });
  } else {
    obj[key] = value;
  }
  return obj;
}
var QuestionCircleOutlined2 = function QuestionCircleOutlined3(props, context) {
  var p = _objectSpread9({}, props, context.attrs);
  return createVNode(AntdIcon_default, _objectSpread9({}, p, {
    "icon": QuestionCircleOutlined_default
  }), null);
};
QuestionCircleOutlined2.displayName = "QuestionCircleOutlined";
QuestionCircleOutlined2.inheritAttrs = false;
var QuestionCircleOutlined_default2 = QuestionCircleOutlined2;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/form/FormItemLabel.js
var FormItemLabel = (props, _ref) => {
  let {
    slots,
    emit,
    attrs
  } = _ref;
  var _a, _b, _c, _d, _e;
  const {
    prefixCls,
    htmlFor,
    labelCol,
    labelAlign,
    colon,
    required: required4,
    requiredMark
  } = _extends(_extends({}, props), attrs);
  const [formLocale] = useLocaleReceiver("Form");
  const label = (_a = props.label) !== null && _a !== void 0 ? _a : (_b = slots.label) === null || _b === void 0 ? void 0 : _b.call(slots);
  if (!label) return null;
  const {
    vertical,
    labelAlign: contextLabelAlign,
    labelCol: contextLabelCol,
    labelWrap,
    colon: contextColon
  } = useInjectForm();
  const mergedLabelCol = labelCol || (contextLabelCol === null || contextLabelCol === void 0 ? void 0 : contextLabelCol.value) || {};
  const mergedLabelAlign = labelAlign || (contextLabelAlign === null || contextLabelAlign === void 0 ? void 0 : contextLabelAlign.value);
  const labelClsBasic = `${prefixCls}-item-label`;
  const labelColClassName = classNames_default(labelClsBasic, mergedLabelAlign === "left" && `${labelClsBasic}-left`, mergedLabelCol.class, {
    [`${labelClsBasic}-wrap`]: !!labelWrap.value
  });
  let labelChildren = label;
  const computedColon = colon === true || (contextColon === null || contextColon === void 0 ? void 0 : contextColon.value) !== false && colon !== false;
  const haveColon = computedColon && !vertical.value;
  if (haveColon && typeof label === "string" && label.trim() !== "") {
    labelChildren = label.replace(/[:|：]\s*$/, "");
  }
  if (props.tooltip || slots.tooltip) {
    const tooltipNode = createVNode("span", {
      "class": `${prefixCls}-item-tooltip`
    }, [createVNode(tooltip_default, {
      "title": props.tooltip
    }, {
      default: () => [createVNode(QuestionCircleOutlined_default2, null, null)]
    })]);
    labelChildren = createVNode(Fragment, null, [labelChildren, slots.tooltip ? (_c = slots.tooltip) === null || _c === void 0 ? void 0 : _c.call(slots, {
      class: `${prefixCls}-item-tooltip`
    }) : tooltipNode]);
  }
  if (requiredMark === "optional" && !required4) {
    labelChildren = createVNode(Fragment, null, [labelChildren, createVNode("span", {
      "class": `${prefixCls}-item-optional`
    }, [((_d = formLocale.value) === null || _d === void 0 ? void 0 : _d.optional) || ((_e = en_US_default.Form) === null || _e === void 0 ? void 0 : _e.optional)])]);
  }
  const labelClassName = classNames_default({
    [`${prefixCls}-item-required`]: required4,
    [`${prefixCls}-item-required-mark-optional`]: requiredMark === "optional",
    [`${prefixCls}-item-no-colon`]: !computedColon
  });
  return createVNode(Col_default, _objectSpread2(_objectSpread2({}, mergedLabelCol), {}, {
    "class": labelColClassName
  }), {
    default: () => [createVNode("label", {
      "for": htmlFor,
      "class": labelClassName,
      "title": typeof label === "string" ? label : "",
      "onClick": (e2) => emit("click", e2)
    }, [labelChildren])]
  });
};
FormItemLabel.displayName = "FormItemLabel";
FormItemLabel.inheritAttrs = false;
var FormItemLabel_default = FormItemLabel;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/form/style/explain.js
var genFormValidateMotionStyle = (token) => {
  const {
    componentCls
  } = token;
  const helpCls = `${componentCls}-show-help`;
  const helpItemCls = `${componentCls}-show-help-item`;
  return {
    [helpCls]: {
      // Explain holder
      transition: `opacity ${token.motionDurationSlow} ${token.motionEaseInOut}`,
      "&-appear, &-enter": {
        opacity: 0,
        "&-active": {
          opacity: 1
        }
      },
      "&-leave": {
        opacity: 1,
        "&-active": {
          opacity: 0
        }
      },
      // Explain
      [helpItemCls]: {
        overflow: "hidden",
        transition: `height ${token.motionDurationSlow} ${token.motionEaseInOut},
                     opacity ${token.motionDurationSlow} ${token.motionEaseInOut},
                     transform ${token.motionDurationSlow} ${token.motionEaseInOut} !important`,
        [`&${helpItemCls}-appear, &${helpItemCls}-enter`]: {
          transform: `translateY(-5px)`,
          opacity: 0,
          [`&-active`]: {
            transform: "translateY(0)",
            opacity: 1
          }
        },
        [`&${helpItemCls}-leave-active`]: {
          transform: `translateY(-5px)`
        }
      }
    }
  };
};
var explain_default = genFormValidateMotionStyle;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/form/style/index.js
var resetForm = (token) => ({
  legend: {
    display: "block",
    width: "100%",
    marginBottom: token.marginLG,
    padding: 0,
    color: token.colorTextDescription,
    fontSize: token.fontSizeLG,
    lineHeight: "inherit",
    border: 0,
    borderBottom: `${token.lineWidth}px ${token.lineType} ${token.colorBorder}`
  },
  label: {
    fontSize: token.fontSize
  },
  'input[type="search"]': {
    boxSizing: "border-box"
  },
  // Position radios and checkboxes better
  'input[type="radio"], input[type="checkbox"]': {
    lineHeight: "normal"
  },
  'input[type="file"]': {
    display: "block"
  },
  // Make range inputs behave like textual form controls
  'input[type="range"]': {
    display: "block",
    width: "100%"
  },
  // Make multiple select elements height not fixed
  "select[multiple], select[size]": {
    height: "auto"
  },
  // Focus for file, radio, and checkbox
  [`input[type='file']:focus,
  input[type='radio']:focus,
  input[type='checkbox']:focus`]: {
    outline: 0,
    boxShadow: `0 0 0 ${token.controlOutlineWidth}px ${token.controlOutline}`
  },
  // Adjust output element
  output: {
    display: "block",
    paddingTop: 15,
    color: token.colorText,
    fontSize: token.fontSize,
    lineHeight: token.lineHeight
  }
});
var genFormSize = (token, height) => {
  const {
    formItemCls
  } = token;
  return {
    [formItemCls]: {
      [`${formItemCls}-label > label`]: {
        height
      },
      [`${formItemCls}-control-input`]: {
        minHeight: height
      }
    }
  };
};
var genFormStyle = (token) => {
  const {
    componentCls
  } = token;
  return {
    [token.componentCls]: _extends(_extends(_extends({}, resetComponent(token)), resetForm(token)), {
      [`${componentCls}-text`]: {
        display: "inline-block",
        paddingInlineEnd: token.paddingSM
      },
      // ================================================================
      // =                             Size                             =
      // ================================================================
      "&-small": _extends({}, genFormSize(token, token.controlHeightSM)),
      "&-large": _extends({}, genFormSize(token, token.controlHeightLG))
    })
  };
};
var genFormItemStyle = (token) => {
  const {
    formItemCls,
    iconCls,
    componentCls,
    rootPrefixCls
  } = token;
  return {
    [formItemCls]: _extends(_extends({}, resetComponent(token)), {
      marginBottom: token.marginLG,
      verticalAlign: "top",
      "&-with-help": {
        transition: "none"
      },
      [`&-hidden,
        &-hidden.${rootPrefixCls}-row`]: {
        // https://github.com/ant-design/ant-design/issues/26141
        display: "none"
      },
      "&-has-warning": {
        [`${formItemCls}-split`]: {
          color: token.colorError
        }
      },
      "&-has-error": {
        [`${formItemCls}-split`]: {
          color: token.colorWarning
        }
      },
      // ==============================================================
      // =                            Label                           =
      // ==============================================================
      [`${formItemCls}-label`]: {
        display: "inline-block",
        flexGrow: 0,
        overflow: "hidden",
        whiteSpace: "nowrap",
        textAlign: "end",
        verticalAlign: "middle",
        "&-left": {
          textAlign: "start"
        },
        "&-wrap": {
          overflow: "unset",
          lineHeight: `${token.lineHeight} - 0.25em`,
          whiteSpace: "unset"
        },
        "> label": {
          position: "relative",
          display: "inline-flex",
          alignItems: "center",
          maxWidth: "100%",
          height: token.controlHeight,
          color: token.colorTextHeading,
          fontSize: token.fontSize,
          [`> ${iconCls}`]: {
            fontSize: token.fontSize,
            verticalAlign: "top"
          },
          // Required mark
          [`&${formItemCls}-required:not(${formItemCls}-required-mark-optional)::before`]: {
            display: "inline-block",
            marginInlineEnd: token.marginXXS,
            color: token.colorError,
            fontSize: token.fontSize,
            fontFamily: "SimSun, sans-serif",
            lineHeight: 1,
            content: '"*"',
            [`${componentCls}-hide-required-mark &`]: {
              display: "none"
            }
          },
          // Optional mark
          [`${formItemCls}-optional`]: {
            display: "inline-block",
            marginInlineStart: token.marginXXS,
            color: token.colorTextDescription,
            [`${componentCls}-hide-required-mark &`]: {
              display: "none"
            }
          },
          // Optional mark
          [`${formItemCls}-tooltip`]: {
            color: token.colorTextDescription,
            cursor: "help",
            writingMode: "horizontal-tb",
            marginInlineStart: token.marginXXS
          },
          "&::after": {
            content: '":"',
            position: "relative",
            marginBlock: 0,
            marginInlineStart: token.marginXXS / 2,
            marginInlineEnd: token.marginXS
          },
          [`&${formItemCls}-no-colon::after`]: {
            content: '" "'
          }
        }
      },
      // ==============================================================
      // =                            Input                           =
      // ==============================================================
      [`${formItemCls}-control`]: {
        display: "flex",
        flexDirection: "column",
        flexGrow: 1,
        [`&:first-child:not([class^="'${rootPrefixCls}-col-'"]):not([class*="' ${rootPrefixCls}-col-'"])`]: {
          width: "100%"
        },
        "&-input": {
          position: "relative",
          display: "flex",
          alignItems: "center",
          minHeight: token.controlHeight,
          "&-content": {
            flex: "auto",
            maxWidth: "100%"
          }
        }
      },
      // ==============================================================
      // =                           Explain                          =
      // ==============================================================
      [formItemCls]: {
        "&-explain, &-extra": {
          clear: "both",
          color: token.colorTextDescription,
          fontSize: token.fontSize,
          lineHeight: token.lineHeight
        },
        "&-explain-connected": {
          width: "100%"
        },
        "&-extra": {
          minHeight: token.controlHeightSM,
          transition: `color ${token.motionDurationMid} ${token.motionEaseOut}`
          // sync input color transition
        },
        "&-explain": {
          "&-error": {
            color: token.colorError
          },
          "&-warning": {
            color: token.colorWarning
          }
        }
      },
      [`&-with-help ${formItemCls}-explain`]: {
        height: "auto",
        opacity: 1
      },
      // ==============================================================
      // =                        Feedback Icon                       =
      // ==============================================================
      [`${formItemCls}-feedback-icon`]: {
        fontSize: token.fontSize,
        textAlign: "center",
        visibility: "visible",
        animationName: zoomIn,
        animationDuration: token.motionDurationMid,
        animationTimingFunction: token.motionEaseOutBack,
        pointerEvents: "none",
        "&-success": {
          color: token.colorSuccess
        },
        "&-error": {
          color: token.colorError
        },
        "&-warning": {
          color: token.colorWarning
        },
        "&-validating": {
          color: token.colorPrimary
        }
      }
    })
  };
};
var genHorizontalStyle = (token) => {
  const {
    componentCls,
    formItemCls,
    rootPrefixCls
  } = token;
  return {
    [`${componentCls}-horizontal`]: {
      [`${formItemCls}-label`]: {
        flexGrow: 0
      },
      [`${formItemCls}-control`]: {
        flex: "1 1 0",
        // https://github.com/ant-design/ant-design/issues/32777
        // https://github.com/ant-design/ant-design/issues/33773
        minWidth: 0
      },
      // https://github.com/ant-design/ant-design/issues/32980
      [`${formItemCls}-label.${rootPrefixCls}-col-24 + ${formItemCls}-control`]: {
        minWidth: "unset"
      }
    }
  };
};
var genInlineStyle = (token) => {
  const {
    componentCls,
    formItemCls
  } = token;
  return {
    [`${componentCls}-inline`]: {
      display: "flex",
      flexWrap: "wrap",
      [formItemCls]: {
        flex: "none",
        flexWrap: "nowrap",
        marginInlineEnd: token.margin,
        marginBottom: 0,
        "&-with-help": {
          marginBottom: token.marginLG
        },
        [`> ${formItemCls}-label,
        > ${formItemCls}-control`]: {
          display: "inline-block",
          verticalAlign: "top"
        },
        [`> ${formItemCls}-label`]: {
          flex: "none"
        },
        [`${componentCls}-text`]: {
          display: "inline-block"
        },
        [`${formItemCls}-has-feedback`]: {
          display: "inline-block"
        }
      }
    }
  };
};
var makeVerticalLayoutLabel = (token) => ({
  margin: 0,
  padding: `0 0 ${token.paddingXS}px`,
  whiteSpace: "initial",
  textAlign: "start",
  "> label": {
    margin: 0,
    "&::after": {
      display: "none"
    }
  }
});
var makeVerticalLayout = (token) => {
  const {
    componentCls,
    formItemCls
  } = token;
  return {
    [`${formItemCls} ${formItemCls}-label`]: makeVerticalLayoutLabel(token),
    [componentCls]: {
      [formItemCls]: {
        flexWrap: "wrap",
        [`${formItemCls}-label,
          ${formItemCls}-control`]: {
          flex: "0 0 100%",
          maxWidth: "100%"
        }
      }
    }
  };
};
var genVerticalStyle = (token) => {
  const {
    componentCls,
    formItemCls,
    rootPrefixCls
  } = token;
  return {
    [`${componentCls}-vertical`]: {
      [formItemCls]: {
        "&-row": {
          flexDirection: "column"
        },
        "&-label > label": {
          height: "auto"
        },
        [`${componentCls}-item-control`]: {
          width: "100%"
        }
      }
    },
    [`${componentCls}-vertical ${formItemCls}-label,
      .${rootPrefixCls}-col-24${formItemCls}-label,
      .${rootPrefixCls}-col-xl-24${formItemCls}-label`]: makeVerticalLayoutLabel(token),
    [`@media (max-width: ${token.screenXSMax}px)`]: [makeVerticalLayout(token), {
      [componentCls]: {
        [`.${rootPrefixCls}-col-xs-24${formItemCls}-label`]: makeVerticalLayoutLabel(token)
      }
    }],
    [`@media (max-width: ${token.screenSMMax}px)`]: {
      [componentCls]: {
        [`.${rootPrefixCls}-col-sm-24${formItemCls}-label`]: makeVerticalLayoutLabel(token)
      }
    },
    [`@media (max-width: ${token.screenMDMax}px)`]: {
      [componentCls]: {
        [`.${rootPrefixCls}-col-md-24${formItemCls}-label`]: makeVerticalLayoutLabel(token)
      }
    },
    [`@media (max-width: ${token.screenLGMax}px)`]: {
      [componentCls]: {
        [`.${rootPrefixCls}-col-lg-24${formItemCls}-label`]: makeVerticalLayoutLabel(token)
      }
    }
  };
};
var style_default2 = genComponentStyleHook("Form", (token, _ref) => {
  let {
    rootPrefixCls
  } = _ref;
  const formToken = merge(token, {
    formItemCls: `${token.componentCls}-item`,
    rootPrefixCls
  });
  return [genFormStyle(formToken), genFormItemStyle(formToken), explain_default(formToken), genHorizontalStyle(formToken), genInlineStyle(formToken), genVerticalStyle(formToken), collapse_default(formToken), zoomIn];
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/form/ErrorList.js
var ErrorList_default = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "ErrorList",
  inheritAttrs: false,
  props: ["errors", "help", "onErrorVisibleChanged", "helpStatus", "warnings"],
  setup(props, _ref) {
    let {
      attrs
    } = _ref;
    const {
      prefixCls,
      status
    } = useInjectFormItemPrefix();
    const baseClassName = computed(() => `${prefixCls.value}-item-explain`);
    const visible = computed(() => !!(props.errors && props.errors.length));
    const innerStatus = ref(status.value);
    const [, hashId] = style_default2(prefixCls);
    watch([visible, status], () => {
      if (visible.value) {
        innerStatus.value = status.value;
      }
    });
    return () => {
      var _a, _b;
      const colMItem = collapseMotion_default(`${prefixCls.value}-show-help-item`);
      const transitionGroupProps = getTransitionGroupProps(`${prefixCls.value}-show-help-item`, colMItem);
      transitionGroupProps.role = "alert";
      transitionGroupProps.class = [hashId.value, baseClassName.value, attrs.class, `${prefixCls.value}-show-help`];
      return createVNode(Transition, _objectSpread2(_objectSpread2({}, getTransitionProps(`${prefixCls.value}-show-help`)), {}, {
        "onAfterEnter": () => props.onErrorVisibleChanged(true),
        "onAfterLeave": () => props.onErrorVisibleChanged(false)
      }), {
        default: () => [withDirectives(createVNode(TransitionGroup, _objectSpread2(_objectSpread2({}, transitionGroupProps), {}, {
          "tag": "div"
        }), {
          default: () => [(_b = props.errors) === null || _b === void 0 ? void 0 : _b.map((error, index2) => createVNode("div", {
            "key": index2,
            "class": innerStatus.value ? `${baseClassName.value}-${innerStatus.value}` : ""
          }, [error]))]
        }), [[vShow, !!((_a = props.errors) === null || _a === void 0 ? void 0 : _a.length)]])]
      });
    };
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/form/FormItemInput.js
var FormItemInput = defineComponent({
  compatConfig: {
    MODE: 3
  },
  slots: Object,
  inheritAttrs: false,
  props: ["prefixCls", "errors", "hasFeedback", "onDomErrorVisibleChange", "wrapperCol", "help", "extra", "status", "marginBottom", "onErrorVisibleChanged"],
  setup(props, _ref) {
    let {
      slots
    } = _ref;
    const formContext = useInjectForm();
    const {
      wrapperCol: contextWrapperCol
    } = formContext;
    const subFormContext = _extends({}, formContext);
    delete subFormContext.labelCol;
    delete subFormContext.wrapperCol;
    useProvideForm(subFormContext);
    useProvideFormItemPrefix({
      prefixCls: computed(() => props.prefixCls),
      status: computed(() => props.status)
    });
    return () => {
      var _a, _b, _c;
      const {
        prefixCls,
        wrapperCol,
        marginBottom,
        onErrorVisibleChanged,
        help = (_a = slots.help) === null || _a === void 0 ? void 0 : _a.call(slots),
        errors = filterEmpty((_b = slots.errors) === null || _b === void 0 ? void 0 : _b.call(slots)),
        // hasFeedback,
        // status,
        extra = (_c = slots.extra) === null || _c === void 0 ? void 0 : _c.call(slots)
      } = props;
      const baseClassName = `${prefixCls}-item`;
      const mergedWrapperCol = wrapperCol || (contextWrapperCol === null || contextWrapperCol === void 0 ? void 0 : contextWrapperCol.value) || {};
      const className = classNames_default(`${baseClassName}-control`, mergedWrapperCol.class);
      return createVNode(Col_default, _objectSpread2(_objectSpread2({}, mergedWrapperCol), {}, {
        "class": className
      }), {
        default: () => {
          var _a2;
          return createVNode(Fragment, null, [createVNode("div", {
            "class": `${baseClassName}-control-input`
          }, [createVNode("div", {
            "class": `${baseClassName}-control-input-content`
          }, [(_a2 = slots.default) === null || _a2 === void 0 ? void 0 : _a2.call(slots)])]), marginBottom !== null || errors.length ? createVNode("div", {
            "style": {
              display: "flex",
              flexWrap: "nowrap"
            }
          }, [createVNode(ErrorList_default, {
            "errors": errors,
            "help": help,
            "class": `${baseClassName}-explain-connected`,
            "onErrorVisibleChanged": onErrorVisibleChanged
          }, null), !!marginBottom && createVNode("div", {
            "style": {
              width: 0,
              height: `${marginBottom}px`
            }
          }, null)]) : null, extra ? createVNode("div", {
            "class": `${baseClassName}-extra`
          }, [extra]) : null]);
        }
      });
    };
  }
});
var FormItemInput_default = FormItemInput;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/form/utils/useDebounce.js
function useDebounce(value) {
  const cacheValue = shallowRef(value.value.slice());
  let timeout = null;
  watchEffect(() => {
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      cacheValue.value = value.value;
    }, value.value.length ? 0 : 10);
  });
  return cacheValue;
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/form/FormItem.js
var ValidateStatuses = tuple("success", "warning", "error", "validating", "");
var iconMap = {
  success: CheckCircleFilled_default2,
  warning: ExclamationCircleFilled_default2,
  error: CloseCircleFilled_default,
  validating: LoadingOutlined_default
};
function getPropByPath(obj, namePathList, strict) {
  let tempObj = obj;
  const keyArr = namePathList;
  let i2 = 0;
  try {
    for (let len = keyArr.length; i2 < len - 1; ++i2) {
      if (!tempObj && !strict) break;
      const key = keyArr[i2];
      if (key in tempObj) {
        tempObj = tempObj[key];
      } else {
        if (strict) {
          throw Error("please transfer a valid name path to form item!");
        }
        break;
      }
    }
    if (strict && !tempObj) {
      throw Error("please transfer a valid name path to form item!");
    }
  } catch (error) {
    console.error("please transfer a valid name path to form item!");
  }
  return {
    o: tempObj,
    k: keyArr[i2],
    v: tempObj ? tempObj[keyArr[i2]] : void 0
  };
}
var formItemProps = () => ({
  htmlFor: String,
  prefixCls: String,
  label: vue_types_default.any,
  help: vue_types_default.any,
  extra: vue_types_default.any,
  labelCol: {
    type: Object
  },
  wrapperCol: {
    type: Object
  },
  hasFeedback: {
    type: Boolean,
    default: false
  },
  colon: {
    type: Boolean,
    default: void 0
  },
  labelAlign: String,
  prop: {
    type: [String, Number, Array]
  },
  name: {
    type: [String, Number, Array]
  },
  rules: [Array, Object],
  autoLink: {
    type: Boolean,
    default: true
  },
  required: {
    type: Boolean,
    default: void 0
  },
  validateFirst: {
    type: Boolean,
    default: void 0
  },
  validateStatus: vue_types_default.oneOf(tuple("", "success", "warning", "error", "validating")),
  validateTrigger: {
    type: [String, Array]
  },
  messageVariables: {
    type: Object
  },
  hidden: Boolean,
  noStyle: Boolean,
  tooltip: String
});
var indexGuid = 0;
var defaultItemNamePrefixCls = "form_item";
var FormItem_default = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "AFormItem",
  inheritAttrs: false,
  __ANT_NEW_FORM_ITEM: true,
  props: formItemProps(),
  slots: Object,
  setup(props, _ref) {
    let {
      slots,
      attrs,
      expose
    } = _ref;
    warning(props.prop === void 0, `\`prop\` is deprecated. Please use \`name\` instead.`);
    const eventKey = `form-item-${++indexGuid}`;
    const {
      prefixCls
    } = useConfigInject_default("form", props);
    const [wrapSSR, hashId] = style_default2(prefixCls);
    const itemRef = shallowRef();
    const formContext = useInjectForm();
    const fieldName = computed(() => props.name || props.prop);
    const errors = shallowRef([]);
    const validateDisabled = shallowRef(false);
    const inputRef = shallowRef();
    const namePath = computed(() => {
      const val = fieldName.value;
      return getNamePath(val);
    });
    const fieldId = computed(() => {
      if (!namePath.value.length) {
        return void 0;
      } else {
        const formName = formContext.name.value;
        const mergedId = namePath.value.join("_");
        return formName ? `${formName}_${mergedId}` : `${defaultItemNamePrefixCls}_${mergedId}`;
      }
    });
    const getNewFieldValue = () => {
      const model = formContext.model.value;
      if (!model || !fieldName.value) {
        return;
      } else {
        return getPropByPath(model, namePath.value, true).v;
      }
    };
    const fieldValue = computed(() => getNewFieldValue());
    const initialValue = shallowRef(cloneDeep_default(fieldValue.value));
    const mergedValidateTrigger = computed(() => {
      let validateTrigger = props.validateTrigger !== void 0 ? props.validateTrigger : formContext.validateTrigger.value;
      validateTrigger = validateTrigger === void 0 ? "change" : validateTrigger;
      return toArray2(validateTrigger);
    });
    const rulesRef = computed(() => {
      let formRules = formContext.rules.value;
      const selfRules = props.rules;
      const requiredRule = props.required !== void 0 ? {
        required: !!props.required,
        trigger: mergedValidateTrigger.value
      } : [];
      const prop = getPropByPath(formRules, namePath.value);
      formRules = formRules ? prop.o[prop.k] || prop.v : [];
      const rules2 = [].concat(selfRules || formRules || []);
      if (find_default(rules2, (rule) => rule.required)) {
        return rules2;
      } else {
        return rules2.concat(requiredRule);
      }
    });
    const isRequired2 = computed(() => {
      const rules2 = rulesRef.value;
      let isRequired3 = false;
      if (rules2 && rules2.length) {
        rules2.every((rule) => {
          if (rule.required) {
            isRequired3 = true;
            return false;
          }
          return true;
        });
      }
      return isRequired3 || props.required;
    });
    const validateState = shallowRef();
    watchEffect(() => {
      validateState.value = props.validateStatus;
    });
    const messageVariables = computed(() => {
      let variables = {};
      if (typeof props.label === "string") {
        variables.label = props.label;
      } else if (props.name) {
        variables.label = String(props.name);
      }
      if (props.messageVariables) {
        variables = _extends(_extends({}, variables), props.messageVariables);
      }
      return variables;
    });
    const validateRules2 = (options) => {
      if (namePath.value.length === 0) {
        return;
      }
      const {
        validateFirst = false
      } = props;
      const {
        triggerName
      } = options || {};
      let filteredRules = rulesRef.value;
      if (triggerName) {
        filteredRules = filteredRules.filter((rule) => {
          const {
            trigger
          } = rule;
          if (!trigger && !mergedValidateTrigger.value.length) {
            return true;
          }
          const triggerList = toArray2(trigger || mergedValidateTrigger.value);
          return triggerList.includes(triggerName);
        });
      }
      if (!filteredRules.length) {
        return Promise.resolve();
      }
      const promise = validateRules(namePath.value, fieldValue.value, filteredRules, _extends({
        validateMessages: formContext.validateMessages.value
      }, options), validateFirst, messageVariables.value);
      validateState.value = "validating";
      errors.value = [];
      promise.catch((e2) => e2).then(function() {
        let results = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];
        if (validateState.value === "validating") {
          const res = results.filter((result) => result && result.errors.length);
          validateState.value = res.length ? "error" : "success";
          errors.value = res.map((r2) => r2.errors);
          formContext.onValidate(fieldName.value, !errors.value.length, errors.value.length ? toRaw(errors.value[0]) : null);
        }
      });
      return promise;
    };
    const onFieldBlur = () => {
      validateRules2({
        triggerName: "blur"
      });
    };
    const onFieldChange = () => {
      if (validateDisabled.value) {
        validateDisabled.value = false;
        return;
      }
      validateRules2({
        triggerName: "change"
      });
    };
    const clearValidate = () => {
      validateState.value = props.validateStatus;
      validateDisabled.value = false;
      errors.value = [];
    };
    const resetField = () => {
      var _a;
      validateState.value = props.validateStatus;
      validateDisabled.value = true;
      errors.value = [];
      const model = formContext.model.value || {};
      const value = fieldValue.value;
      const prop = getPropByPath(model, namePath.value, true);
      if (Array.isArray(value)) {
        prop.o[prop.k] = [].concat((_a = initialValue.value) !== null && _a !== void 0 ? _a : []);
      } else {
        prop.o[prop.k] = initialValue.value;
      }
      nextTick(() => {
        validateDisabled.value = false;
      });
    };
    const htmlFor = computed(() => {
      return props.htmlFor === void 0 ? fieldId.value : props.htmlFor;
    });
    const onLabelClick = () => {
      const id = htmlFor.value;
      if (!id || !inputRef.value) {
        return;
      }
      const control = inputRef.value.$el.querySelector(`[id="${id}"]`);
      if (control && control.focus) {
        control.focus();
      }
    };
    expose({
      onFieldBlur,
      onFieldChange,
      clearValidate,
      resetField
    });
    useProvideFormItemContext({
      id: fieldId,
      onFieldBlur: () => {
        if (props.autoLink) {
          onFieldBlur();
        }
      },
      onFieldChange: () => {
        if (props.autoLink) {
          onFieldChange();
        }
      },
      clearValidate
    }, computed(() => {
      return !!(props.autoLink && formContext.model.value && fieldName.value);
    }));
    let registered = false;
    watch(fieldName, (val) => {
      if (val) {
        if (!registered) {
          registered = true;
          formContext.addField(eventKey, {
            fieldValue,
            fieldId,
            fieldName,
            resetField,
            clearValidate,
            namePath,
            validateRules: validateRules2,
            rules: rulesRef
          });
        }
      } else {
        registered = false;
        formContext.removeField(eventKey);
      }
    }, {
      immediate: true
    });
    onBeforeUnmount(() => {
      formContext.removeField(eventKey);
    });
    const debounceErrors = useDebounce(errors);
    const mergedValidateStatus = computed(() => {
      if (props.validateStatus !== void 0) {
        return props.validateStatus;
      } else if (debounceErrors.value.length) {
        return "error";
      }
      return validateState.value;
    });
    const itemClassName = computed(() => ({
      [`${prefixCls.value}-item`]: true,
      [hashId.value]: true,
      // Status
      [`${prefixCls.value}-item-has-feedback`]: mergedValidateStatus.value && props.hasFeedback,
      [`${prefixCls.value}-item-has-success`]: mergedValidateStatus.value === "success",
      [`${prefixCls.value}-item-has-warning`]: mergedValidateStatus.value === "warning",
      [`${prefixCls.value}-item-has-error`]: mergedValidateStatus.value === "error",
      [`${prefixCls.value}-item-is-validating`]: mergedValidateStatus.value === "validating",
      [`${prefixCls.value}-item-hidden`]: props.hidden
    }));
    const formItemInputContext = reactive({});
    FormItemInputContext.useProvide(formItemInputContext);
    watchEffect(() => {
      let feedbackIcon;
      if (props.hasFeedback) {
        const IconNode = mergedValidateStatus.value && iconMap[mergedValidateStatus.value];
        feedbackIcon = IconNode ? createVNode("span", {
          "class": classNames_default(`${prefixCls.value}-item-feedback-icon`, `${prefixCls.value}-item-feedback-icon-${mergedValidateStatus.value}`)
        }, [createVNode(IconNode, null, null)]) : null;
      }
      _extends(formItemInputContext, {
        status: mergedValidateStatus.value,
        hasFeedback: props.hasFeedback,
        feedbackIcon,
        isFormItemInput: true
      });
    });
    const marginBottom = shallowRef(null);
    const showMarginOffset = shallowRef(false);
    const updateMarginBottom = () => {
      if (itemRef.value) {
        const itemStyle = getComputedStyle(itemRef.value);
        marginBottom.value = parseInt(itemStyle.marginBottom, 10);
      }
    };
    onMounted(() => {
      watch(showMarginOffset, () => {
        if (showMarginOffset.value) {
          updateMarginBottom();
        }
      }, {
        flush: "post",
        immediate: true
      });
    });
    const onErrorVisibleChanged = (nextVisible) => {
      if (!nextVisible) {
        marginBottom.value = null;
      }
    };
    return () => {
      var _a, _b;
      if (props.noStyle) return (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots);
      const help = (_b = props.help) !== null && _b !== void 0 ? _b : slots.help ? filterEmpty(slots.help()) : null;
      const withHelp = !!(help !== void 0 && help !== null && Array.isArray(help) && help.length || debounceErrors.value.length);
      showMarginOffset.value = withHelp;
      return wrapSSR(createVNode("div", {
        "class": [itemClassName.value, withHelp ? `${prefixCls.value}-item-with-help` : "", attrs.class],
        "ref": itemRef
      }, [createVNode(Row_default, _objectSpread2(_objectSpread2({}, attrs), {}, {
        "class": `${prefixCls.value}-item-row`,
        "key": "row"
      }), {
        default: () => {
          var _a2, _b2;
          return createVNode(Fragment, null, [createVNode(FormItemLabel_default, _objectSpread2(_objectSpread2({}, props), {}, {
            "htmlFor": htmlFor.value,
            "required": isRequired2.value,
            "requiredMark": formContext.requiredMark.value,
            "prefixCls": prefixCls.value,
            "onClick": onLabelClick,
            "label": props.label
          }), {
            label: slots.label,
            tooltip: slots.tooltip
          }), createVNode(FormItemInput_default, _objectSpread2(_objectSpread2({}, props), {}, {
            "errors": help !== void 0 && help !== null ? toArray2(help) : debounceErrors.value,
            "marginBottom": marginBottom.value,
            "prefixCls": prefixCls.value,
            "status": mergedValidateStatus.value,
            "ref": inputRef,
            "help": help,
            "extra": (_a2 = props.extra) !== null && _a2 !== void 0 ? _a2 : (_b2 = slots.extra) === null || _b2 === void 0 ? void 0 : _b2.call(slots),
            "onErrorVisibleChanged": onErrorVisibleChanged
          }), {
            default: slots.default
          })]);
        }
      }), !!marginBottom.value && createVNode("div", {
        "class": `${prefixCls.value}-margin-offset`,
        "style": {
          marginBottom: `-${marginBottom.value}px`
        }
      }, null)]));
    };
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/form/utils/asyncUtil.js
function allPromiseFinish(promiseList) {
  let hasError = false;
  let count = promiseList.length;
  const results = [];
  if (!promiseList.length) {
    return Promise.resolve([]);
  }
  return new Promise((resolve, reject) => {
    promiseList.forEach((promise, index2) => {
      promise.catch((e2) => {
        hasError = true;
        return e2;
      }).then((result) => {
        count -= 1;
        results[index2] = result;
        if (count > 0) {
          return;
        }
        if (hasError) {
          reject(results);
        }
        resolve(results);
      });
    });
  });
}

// ../../node_modules/.pnpm/compute-scroll-into-view@1.0.20/node_modules/compute-scroll-into-view/dist/index.mjs
function t(t2) {
  return "object" == typeof t2 && null != t2 && 1 === t2.nodeType;
}
function e(t2, e2) {
  return (!e2 || "hidden" !== t2) && "visible" !== t2 && "clip" !== t2;
}
function n(t2, n2) {
  if (t2.clientHeight < t2.scrollHeight || t2.clientWidth < t2.scrollWidth) {
    var r2 = getComputedStyle(t2, null);
    return e(r2.overflowY, n2) || e(r2.overflowX, n2) || function(t3) {
      var e2 = function(t4) {
        if (!t4.ownerDocument || !t4.ownerDocument.defaultView) return null;
        try {
          return t4.ownerDocument.defaultView.frameElement;
        } catch (t5) {
          return null;
        }
      }(t3);
      return !!e2 && (e2.clientHeight < t3.scrollHeight || e2.clientWidth < t3.scrollWidth);
    }(t2);
  }
  return false;
}
function r(t2, e2, n2, r2, i2, o, l, d) {
  return o < t2 && l > e2 || o > t2 && l < e2 ? 0 : o <= t2 && d <= n2 || l >= e2 && d >= n2 ? o - t2 - r2 : l > e2 && d < n2 || o < t2 && d > n2 ? l - e2 + i2 : 0;
}
var i = function(e2, i2) {
  var o = window, l = i2.scrollMode, d = i2.block, f = i2.inline, h = i2.boundary, u = i2.skipOverflowHiddenElements, s = "function" == typeof h ? h : function(t2) {
    return t2 !== h;
  };
  if (!t(e2)) throw new TypeError("Invalid target");
  for (var a, c, g = document.scrollingElement || document.documentElement, p = [], m = e2; t(m) && s(m); ) {
    if ((m = null == (c = (a = m).parentElement) ? a.getRootNode().host || null : c) === g) {
      p.push(m);
      break;
    }
    null != m && m === document.body && n(m) && !n(document.documentElement) || null != m && n(m, u) && p.push(m);
  }
  for (var w = o.visualViewport ? o.visualViewport.width : innerWidth, v = o.visualViewport ? o.visualViewport.height : innerHeight, W = window.scrollX || pageXOffset, H = window.scrollY || pageYOffset, b = e2.getBoundingClientRect(), y = b.height, E = b.width, M = b.top, V = b.right, x = b.bottom, I = b.left, C = "start" === d || "nearest" === d ? M : "end" === d ? x : M + y / 2, R = "center" === f ? I + E / 2 : "end" === f ? V : I, T = [], k = 0; k < p.length; k++) {
    var B = p[k], D = B.getBoundingClientRect(), O = D.height, X = D.width, Y = D.top, L = D.right, S = D.bottom, j = D.left;
    if ("if-needed" === l && M >= 0 && I >= 0 && x <= v && V <= w && M >= Y && x <= S && I >= j && V <= L) return T;
    var N = getComputedStyle(B), q = parseInt(N.borderLeftWidth, 10), z = parseInt(N.borderTopWidth, 10), A = parseInt(N.borderRightWidth, 10), F = parseInt(N.borderBottomWidth, 10), G = 0, J = 0, K = "offsetWidth" in B ? B.offsetWidth - B.clientWidth - q - A : 0, P = "offsetHeight" in B ? B.offsetHeight - B.clientHeight - z - F : 0, Q = "offsetWidth" in B ? 0 === B.offsetWidth ? 0 : X / B.offsetWidth : 0, U = "offsetHeight" in B ? 0 === B.offsetHeight ? 0 : O / B.offsetHeight : 0;
    if (g === B) G = "start" === d ? C : "end" === d ? C - v : "nearest" === d ? r(H, H + v, v, z, F, H + C, H + C + y, y) : C - v / 2, J = "start" === f ? R : "center" === f ? R - w / 2 : "end" === f ? R - w : r(W, W + w, w, q, A, W + R, W + R + E, E), G = Math.max(0, G + H), J = Math.max(0, J + W);
    else {
      G = "start" === d ? C - Y - z : "end" === d ? C - S + F + P : "nearest" === d ? r(Y, S, O, z, F + P, C, C + y, y) : C - (Y + O / 2) + P / 2, J = "start" === f ? R - j - q : "center" === f ? R - (j + X / 2) + K / 2 : "end" === f ? R - L + A + K : r(j, L, X, q, A + K, R, R + E, E);
      var Z = B.scrollLeft, $ = B.scrollTop;
      C += $ - (G = Math.max(0, Math.min($ + G / U, B.scrollHeight - O / U + P))), R += Z - (J = Math.max(0, Math.min(Z + J / Q, B.scrollWidth - X / Q + K)));
    }
    T.push({ el: B, top: G, left: J });
  }
  return T;
};

// ../../node_modules/.pnpm/scroll-into-view-if-needed@2.2.31/node_modules/scroll-into-view-if-needed/es/index.js
function isOptionsObject(options) {
  return options === Object(options) && Object.keys(options).length !== 0;
}
function defaultBehavior(actions, behavior) {
  if (behavior === void 0) {
    behavior = "auto";
  }
  var canSmoothScroll = "scrollBehavior" in document.body.style;
  actions.forEach(function(_ref) {
    var el = _ref.el, top = _ref.top, left = _ref.left;
    if (el.scroll && canSmoothScroll) {
      el.scroll({
        top,
        left,
        behavior
      });
    } else {
      el.scrollTop = top;
      el.scrollLeft = left;
    }
  });
}
function getOptions(options) {
  if (options === false) {
    return {
      block: "end",
      inline: "nearest"
    };
  }
  if (isOptionsObject(options)) {
    return options;
  }
  return {
    block: "start",
    inline: "nearest"
  };
}
function scrollIntoView(target, options) {
  var isTargetAttached = target.isConnected || target.ownerDocument.documentElement.contains(target);
  if (isOptionsObject(options) && typeof options.behavior === "function") {
    return options.behavior(isTargetAttached ? i(target, options) : []);
  }
  if (!isTargetAttached) {
    return;
  }
  var computeOptions = getOptions(options);
  return defaultBehavior(i(target, computeOptions), computeOptions.behavior);
}
var es_default = scrollIntoView;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/form/useForm.js
function isRequired(rules2) {
  let isRequired2 = false;
  if (rules2 && rules2.length) {
    rules2.every((rule) => {
      if (rule.required) {
        isRequired2 = true;
        return false;
      }
      return true;
    });
  }
  return isRequired2;
}
function toArray3(value) {
  if (value === void 0 || value === null) {
    return [];
  }
  return Array.isArray(value) ? value : [value];
}
function getPropByPath2(obj, path, strict) {
  let tempObj = obj;
  path = path.replace(/\[(\w+)\]/g, ".$1");
  path = path.replace(/^\./, "");
  const keyArr = path.split(".");
  let i2 = 0;
  for (let len = keyArr.length; i2 < len - 1; ++i2) {
    if (!tempObj && !strict) break;
    const key = keyArr[i2];
    if (key in tempObj) {
      tempObj = tempObj[key];
    } else {
      if (strict) {
        throw new Error("please transfer a valid name path to validate!");
      }
      break;
    }
  }
  return {
    o: tempObj,
    k: keyArr[i2],
    v: tempObj ? tempObj[keyArr[i2]] : null,
    isValid: tempObj && keyArr[i2] in tempObj
  };
}
function useForm(modelRef) {
  let rulesRef = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : ref({});
  let options = arguments.length > 2 ? arguments[2] : void 0;
  const initialModel = cloneDeep_default(unref(modelRef));
  const validateInfos = reactive({});
  const rulesKeys = shallowRef([]);
  const resetFields = (newValues) => {
    _extends(unref(modelRef), _extends(_extends({}, cloneDeep_default(initialModel)), newValues));
    nextTick(() => {
      Object.keys(validateInfos).forEach((key) => {
        validateInfos[key] = {
          autoLink: false,
          required: isRequired(unref(rulesRef)[key])
        };
      });
    });
  };
  const filterRules = function() {
    let rules2 = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];
    let trigger = arguments.length > 1 ? arguments[1] : void 0;
    if (!trigger.length) {
      return rules2;
    } else {
      return rules2.filter((rule) => {
        const triggerList = toArray3(rule.trigger || "change");
        return intersection_default(triggerList, trigger).length;
      });
    }
  };
  let lastValidatePromise = null;
  const validateFields = function(names) {
    let option = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    let strict = arguments.length > 2 ? arguments[2] : void 0;
    const promiseList = [];
    const values = {};
    for (let i2 = 0; i2 < names.length; i2++) {
      const name = names[i2];
      const prop = getPropByPath2(unref(modelRef), name, strict);
      if (!prop.isValid) continue;
      values[name] = prop.v;
      const rules2 = filterRules(unref(rulesRef)[name], toArray3(option && option.trigger));
      if (rules2.length) {
        promiseList.push(validateField(name, prop.v, rules2, option || {}).then(() => ({
          name,
          errors: [],
          warnings: []
        })).catch((ruleErrors) => {
          const mergedErrors = [];
          const mergedWarnings = [];
          ruleErrors.forEach((_ref) => {
            let {
              rule: {
                warningOnly
              },
              errors
            } = _ref;
            if (warningOnly) {
              mergedWarnings.push(...errors);
            } else {
              mergedErrors.push(...errors);
            }
          });
          if (mergedErrors.length) {
            return Promise.reject({
              name,
              errors: mergedErrors,
              warnings: mergedWarnings
            });
          }
          return {
            name,
            errors: mergedErrors,
            warnings: mergedWarnings
          };
        }));
      }
    }
    const summaryPromise = allPromiseFinish(promiseList);
    lastValidatePromise = summaryPromise;
    const returnPromise = summaryPromise.then(() => {
      if (lastValidatePromise === summaryPromise) {
        return Promise.resolve(values);
      }
      return Promise.reject([]);
    }).catch((results) => {
      const errorList = results.filter((result) => result && result.errors.length);
      return errorList.length ? Promise.reject({
        values,
        errorFields: errorList,
        outOfDate: lastValidatePromise !== summaryPromise
      }) : Promise.resolve(values);
    });
    returnPromise.catch((e2) => e2);
    return returnPromise;
  };
  const validateField = function(name, value, rules2) {
    let option = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : {};
    const promise = validateRules([name], value, rules2, _extends({
      validateMessages: defaultValidateMessages
    }, option), !!option.validateFirst);
    if (!validateInfos[name]) {
      return promise.catch((e2) => e2);
    }
    validateInfos[name].validateStatus = "validating";
    promise.catch((e2) => e2).then(function() {
      let results = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];
      var _a;
      if (validateInfos[name].validateStatus === "validating") {
        const res = results.filter((result) => result && result.errors.length);
        validateInfos[name].validateStatus = res.length ? "error" : "success";
        validateInfos[name].help = res.length ? res.map((r2) => r2.errors) : null;
        (_a = options === null || options === void 0 ? void 0 : options.onValidate) === null || _a === void 0 ? void 0 : _a.call(options, name, !res.length, res.length ? toRaw(validateInfos[name].help[0]) : null);
      }
    });
    return promise;
  };
  const validate = (names, option) => {
    let keys = [];
    let strict = true;
    if (!names) {
      strict = false;
      keys = rulesKeys.value;
    } else if (Array.isArray(names)) {
      keys = names;
    } else {
      keys = [names];
    }
    const promises = validateFields(keys, option || {}, strict);
    promises.catch((e2) => e2);
    return promises;
  };
  const clearValidate = (names) => {
    let keys = [];
    if (!names) {
      keys = rulesKeys.value;
    } else if (Array.isArray(names)) {
      keys = names;
    } else {
      keys = [names];
    }
    keys.forEach((key) => {
      validateInfos[key] && _extends(validateInfos[key], {
        validateStatus: "",
        help: null
      });
    });
  };
  const mergeValidateInfo = (items) => {
    const info = {
      autoLink: false
    };
    const help = [];
    const infos = Array.isArray(items) ? items : [items];
    for (let i2 = 0; i2 < infos.length; i2++) {
      const arg = infos[i2];
      if ((arg === null || arg === void 0 ? void 0 : arg.validateStatus) === "error") {
        info.validateStatus = "error";
        arg.help && help.push(arg.help);
      }
      info.required = info.required || (arg === null || arg === void 0 ? void 0 : arg.required);
    }
    info.help = help;
    return info;
  };
  let oldModel = initialModel;
  let isFirstTime = true;
  const modelFn = (model) => {
    const names = [];
    rulesKeys.value.forEach((key) => {
      const prop = getPropByPath2(model, key, false);
      const oldProp = getPropByPath2(oldModel, key, false);
      const isFirstValidation = isFirstTime && (options === null || options === void 0 ? void 0 : options.immediate) && prop.isValid;
      if (isFirstValidation || !isEqual_default(prop.v, oldProp.v)) {
        names.push(key);
      }
    });
    validate(names, {
      trigger: "change"
    });
    isFirstTime = false;
    oldModel = cloneDeep_default(toRaw(model));
  };
  const debounceOptions = options === null || options === void 0 ? void 0 : options.debounce;
  let first = true;
  watch(rulesRef, () => {
    rulesKeys.value = rulesRef ? Object.keys(unref(rulesRef)) : [];
    if (!first && options && options.validateOnRuleChange) {
      validate();
    }
    first = false;
  }, {
    deep: true,
    immediate: true
  });
  watch(rulesKeys, () => {
    const newValidateInfos = {};
    rulesKeys.value.forEach((key) => {
      newValidateInfos[key] = _extends({}, validateInfos[key], {
        autoLink: false,
        required: isRequired(unref(rulesRef)[key])
      });
      delete validateInfos[key];
    });
    for (const key in validateInfos) {
      if (Object.prototype.hasOwnProperty.call(validateInfos, key)) {
        delete validateInfos[key];
      }
    }
    _extends(validateInfos, newValidateInfos);
  }, {
    immediate: true
  });
  watch(modelRef, debounceOptions && debounceOptions.wait ? debounce_default(modelFn, debounceOptions.wait, omit_default(debounceOptions, ["wait"])) : modelFn, {
    immediate: options && !!options.immediate,
    deep: true
  });
  return {
    modelRef,
    rulesRef,
    initialModel,
    validateInfos,
    resetFields,
    validate,
    validateField,
    mergeValidateInfo,
    clearValidate
  };
}
var useForm_default = useForm;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/form/Form.js
var formProps = () => ({
  layout: vue_types_default.oneOf(tuple("horizontal", "inline", "vertical")),
  labelCol: objectType(),
  wrapperCol: objectType(),
  colon: booleanType(),
  labelAlign: stringType(),
  labelWrap: booleanType(),
  prefixCls: String,
  requiredMark: someType([String, Boolean]),
  /** @deprecated Will warning in future branch. Pls use `requiredMark` instead. */
  hideRequiredMark: booleanType(),
  model: vue_types_default.object,
  rules: objectType(),
  validateMessages: objectType(),
  validateOnRuleChange: booleanType(),
  // 提交失败自动滚动到第一个错误字段
  scrollToFirstError: anyType(),
  onSubmit: functionType(),
  name: String,
  validateTrigger: someType([String, Array]),
  size: stringType(),
  disabled: booleanType(),
  onValuesChange: functionType(),
  onFieldsChange: functionType(),
  onFinish: functionType(),
  onFinishFailed: functionType(),
  onValidate: functionType()
});
function isEqualName(name1, name2) {
  return isEqual_default(toArray2(name1), toArray2(name2));
}
var Form = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "AForm",
  inheritAttrs: false,
  props: initDefaultProps_default(formProps(), {
    layout: "horizontal",
    hideRequiredMark: false,
    colon: true
  }),
  Item: FormItem_default,
  useForm: useForm_default,
  // emits: ['finishFailed', 'submit', 'finish', 'validate'],
  setup(props, _ref) {
    let {
      emit,
      slots,
      expose,
      attrs
    } = _ref;
    const {
      prefixCls,
      direction,
      form: contextForm,
      size,
      disabled
    } = useConfigInject_default("form", props);
    const requiredMark = computed(() => props.requiredMark === "" || props.requiredMark);
    const mergedRequiredMark = computed(() => {
      var _a;
      if (requiredMark.value !== void 0) {
        return requiredMark.value;
      }
      if (contextForm && ((_a = contextForm.value) === null || _a === void 0 ? void 0 : _a.requiredMark) !== void 0) {
        return contextForm.value.requiredMark;
      }
      if (props.hideRequiredMark) {
        return false;
      }
      return true;
    });
    useProviderSize(size);
    useProviderDisabled(disabled);
    const mergedColon = computed(() => {
      var _a, _b;
      return (_a = props.colon) !== null && _a !== void 0 ? _a : (_b = contextForm.value) === null || _b === void 0 ? void 0 : _b.colon;
    });
    const {
      validateMessages: globalValidateMessages
    } = useInjectGlobalForm();
    const validateMessages = computed(() => {
      return _extends(_extends(_extends({}, defaultValidateMessages), globalValidateMessages.value), props.validateMessages);
    });
    const [wrapSSR, hashId] = style_default2(prefixCls);
    const formClassName = computed(() => classNames_default(prefixCls.value, {
      [`${prefixCls.value}-${props.layout}`]: true,
      [`${prefixCls.value}-hide-required-mark`]: mergedRequiredMark.value === false,
      [`${prefixCls.value}-rtl`]: direction.value === "rtl",
      [`${prefixCls.value}-${size.value}`]: size.value
    }, hashId.value));
    const lastValidatePromise = ref();
    const fields = {};
    const addField = (eventKey, field) => {
      fields[eventKey] = field;
    };
    const removeField = (eventKey) => {
      delete fields[eventKey];
    };
    const getFieldsByNameList = (nameList) => {
      const provideNameList = !!nameList;
      const namePathList = provideNameList ? toArray2(nameList).map(getNamePath) : [];
      if (!provideNameList) {
        return Object.values(fields);
      } else {
        return Object.values(fields).filter((field) => namePathList.findIndex((namePath) => isEqualName(namePath, field.fieldName.value)) > -1);
      }
    };
    const resetFields = (name) => {
      if (!props.model) {
        warning_default(false, "Form", "model is required for resetFields to work.");
        return;
      }
      getFieldsByNameList(name).forEach((field) => {
        field.resetField();
      });
    };
    const clearValidate = (name) => {
      getFieldsByNameList(name).forEach((field) => {
        field.clearValidate();
      });
    };
    const handleFinishFailed = (errorInfo) => {
      const {
        scrollToFirstError
      } = props;
      emit("finishFailed", errorInfo);
      if (scrollToFirstError && errorInfo.errorFields.length) {
        let scrollToFieldOptions = {};
        if (typeof scrollToFirstError === "object") {
          scrollToFieldOptions = scrollToFirstError;
        }
        scrollToField(errorInfo.errorFields[0].name, scrollToFieldOptions);
      }
    };
    const validate = function() {
      return validateField(...arguments);
    };
    const scrollToField = function(name) {
      let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
      const fields2 = getFieldsByNameList(name ? [name] : void 0);
      if (fields2.length) {
        const fieldId = fields2[0].fieldId.value;
        const node = fieldId ? document.getElementById(fieldId) : null;
        if (node) {
          es_default(node, _extends({
            scrollMode: "if-needed",
            block: "nearest"
          }, options));
        }
      }
    };
    const getFieldsValue = function() {
      let nameList = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;
      if (nameList === true) {
        const allNameList = [];
        Object.values(fields).forEach((_ref2) => {
          let {
            namePath
          } = _ref2;
          allNameList.push(namePath.value);
        });
        return cloneByNamePathList(props.model, allNameList);
      } else {
        return cloneByNamePathList(props.model, nameList);
      }
    };
    const validateFields = (nameList, options) => {
      warning_default(!(nameList instanceof Function), "Form", "validateFields/validateField/validate not support callback, please use promise instead");
      if (!props.model) {
        warning_default(false, "Form", "model is required for validateFields to work.");
        return Promise.reject("Form `model` is required for validateFields to work.");
      }
      const provideNameList = !!nameList;
      const namePathList = provideNameList ? toArray2(nameList).map(getNamePath) : [];
      const promiseList = [];
      Object.values(fields).forEach((field) => {
        var _a;
        if (!provideNameList) {
          namePathList.push(field.namePath.value);
        }
        if (!((_a = field.rules) === null || _a === void 0 ? void 0 : _a.value.length)) {
          return;
        }
        const fieldNamePath = field.namePath.value;
        if (!provideNameList || containsNamePath(namePathList, fieldNamePath)) {
          const promise = field.validateRules(_extends({
            validateMessages: validateMessages.value
          }, options));
          promiseList.push(promise.then(() => ({
            name: fieldNamePath,
            errors: [],
            warnings: []
          })).catch((ruleErrors) => {
            const mergedErrors = [];
            const mergedWarnings = [];
            ruleErrors.forEach((_ref3) => {
              let {
                rule: {
                  warningOnly
                },
                errors
              } = _ref3;
              if (warningOnly) {
                mergedWarnings.push(...errors);
              } else {
                mergedErrors.push(...errors);
              }
            });
            if (mergedErrors.length) {
              return Promise.reject({
                name: fieldNamePath,
                errors: mergedErrors,
                warnings: mergedWarnings
              });
            }
            return {
              name: fieldNamePath,
              errors: mergedErrors,
              warnings: mergedWarnings
            };
          }));
        }
      });
      const summaryPromise = allPromiseFinish(promiseList);
      lastValidatePromise.value = summaryPromise;
      const returnPromise = summaryPromise.then(() => {
        if (lastValidatePromise.value === summaryPromise) {
          return Promise.resolve(getFieldsValue(namePathList));
        }
        return Promise.reject([]);
      }).catch((results) => {
        const errorList = results.filter((result) => result && result.errors.length);
        return Promise.reject({
          values: getFieldsValue(namePathList),
          errorFields: errorList,
          outOfDate: lastValidatePromise.value !== summaryPromise
        });
      });
      returnPromise.catch((e2) => e2);
      return returnPromise;
    };
    const validateField = function() {
      return validateFields(...arguments);
    };
    const handleSubmit = (e2) => {
      e2.preventDefault();
      e2.stopPropagation();
      emit("submit", e2);
      if (props.model) {
        const res = validateFields();
        res.then((values) => {
          emit("finish", values);
        }).catch((errors) => {
          handleFinishFailed(errors);
        });
      }
    };
    expose({
      resetFields,
      clearValidate,
      validateFields,
      getFieldsValue,
      validate,
      scrollToField
    });
    useProvideForm({
      model: computed(() => props.model),
      name: computed(() => props.name),
      labelAlign: computed(() => props.labelAlign),
      labelCol: computed(() => props.labelCol),
      labelWrap: computed(() => props.labelWrap),
      wrapperCol: computed(() => props.wrapperCol),
      vertical: computed(() => props.layout === "vertical"),
      colon: mergedColon,
      requiredMark: mergedRequiredMark,
      validateTrigger: computed(() => props.validateTrigger),
      rules: computed(() => props.rules),
      addField,
      removeField,
      onValidate: (name, status, errors) => {
        emit("validate", name, status, errors);
      },
      validateMessages
    });
    watch(() => props.rules, () => {
      if (props.validateOnRuleChange) {
        validateFields();
      }
    });
    return () => {
      var _a;
      return wrapSSR(createVNode("form", _objectSpread2(_objectSpread2({}, attrs), {}, {
        "onSubmit": handleSubmit,
        "class": [formClassName.value, attrs.class]
      }), [(_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots)]));
    };
  }
});
var Form_default = Form;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/form/index.js
Form_default.useInjectFormItemContext = useInjectFormItemContext;
Form_default.ItemRest = FormItemContext_default;
Form_default.install = function(app) {
  app.component(Form_default.name, Form_default);
  app.component(Form_default.Item.name, Form_default.Item);
  app.component(FormItemContext_default.name, FormItemContext_default);
  return app;
};
var form_default = Form_default;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/upload/style/dragger.js
var genDraggerStyle = (token) => {
  const {
    componentCls,
    iconCls
  } = token;
  return {
    [`${componentCls}-wrapper`]: {
      [`${componentCls}-drag`]: {
        position: "relative",
        width: "100%",
        height: "100%",
        textAlign: "center",
        background: token.colorFillAlter,
        border: `${token.lineWidth}px dashed ${token.colorBorder}`,
        borderRadius: token.borderRadiusLG,
        cursor: "pointer",
        transition: `border-color ${token.motionDurationSlow}`,
        [componentCls]: {
          padding: `${token.padding}px 0`
        },
        [`${componentCls}-btn`]: {
          display: "table",
          width: "100%",
          height: "100%",
          outline: "none"
        },
        [`${componentCls}-drag-container`]: {
          display: "table-cell",
          verticalAlign: "middle"
        },
        [`&:not(${componentCls}-disabled):hover`]: {
          borderColor: token.colorPrimaryHover
        },
        [`p${componentCls}-drag-icon`]: {
          marginBottom: token.margin,
          [iconCls]: {
            color: token.colorPrimary,
            fontSize: token.uploadThumbnailSize
          }
        },
        [`p${componentCls}-text`]: {
          margin: `0 0 ${token.marginXXS}px`,
          color: token.colorTextHeading,
          fontSize: token.fontSizeLG
        },
        [`p${componentCls}-hint`]: {
          color: token.colorTextDescription,
          fontSize: token.fontSize
        },
        // ===================== Disabled =====================
        [`&${componentCls}-disabled`]: {
          cursor: "not-allowed",
          [`p${componentCls}-drag-icon ${iconCls},
            p${componentCls}-text,
            p${componentCls}-hint
          `]: {
            color: token.colorTextDisabled
          }
        }
      }
    }
  };
};
var dragger_default = genDraggerStyle;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/upload/style/list.js
var genListStyle = (token) => {
  const {
    componentCls,
    antCls,
    iconCls,
    fontSize,
    lineHeight
  } = token;
  const itemCls = `${componentCls}-list-item`;
  const actionsCls = `${itemCls}-actions`;
  const actionCls = `${itemCls}-action`;
  const listItemHeightSM = Math.round(fontSize * lineHeight);
  return {
    [`${componentCls}-wrapper`]: {
      [`${componentCls}-list`]: _extends(_extends({}, clearFix()), {
        lineHeight: token.lineHeight,
        [itemCls]: {
          position: "relative",
          height: token.lineHeight * fontSize,
          marginTop: token.marginXS,
          fontSize,
          display: "flex",
          alignItems: "center",
          transition: `background-color ${token.motionDurationSlow}`,
          "&:hover": {
            backgroundColor: token.controlItemBgHover
          },
          [`${itemCls}-name`]: _extends(_extends({}, textEllipsis), {
            padding: `0 ${token.paddingXS}px`,
            lineHeight,
            flex: "auto",
            transition: `all ${token.motionDurationSlow}`
          }),
          [actionsCls]: {
            [actionCls]: {
              opacity: 0
            },
            [`${actionCls}${antCls}-btn-sm`]: {
              height: listItemHeightSM,
              border: 0,
              lineHeight: 1,
              // FIXME: should not override small button
              "> span": {
                transform: "scale(1)"
              }
            },
            [`
              ${actionCls}:focus,
              &.picture ${actionCls}
            `]: {
              opacity: 1
            },
            [iconCls]: {
              color: token.colorTextDescription,
              transition: `all ${token.motionDurationSlow}`
            },
            [`&:hover ${iconCls}`]: {
              color: token.colorText
            }
          },
          [`${componentCls}-icon ${iconCls}`]: {
            color: token.colorTextDescription,
            fontSize
          },
          [`${itemCls}-progress`]: {
            position: "absolute",
            bottom: -token.uploadProgressOffset,
            width: "100%",
            paddingInlineStart: fontSize + token.paddingXS,
            fontSize,
            lineHeight: 0,
            pointerEvents: "none",
            "> div": {
              margin: 0
            }
          }
        },
        [`${itemCls}:hover ${actionCls}`]: {
          opacity: 1,
          color: token.colorText
        },
        [`${itemCls}-error`]: {
          color: token.colorError,
          [`${itemCls}-name, ${componentCls}-icon ${iconCls}`]: {
            color: token.colorError
          },
          [actionsCls]: {
            [`${iconCls}, ${iconCls}:hover`]: {
              color: token.colorError
            },
            [actionCls]: {
              opacity: 1
            }
          }
        },
        [`${componentCls}-list-item-container`]: {
          transition: `opacity ${token.motionDurationSlow}, height ${token.motionDurationSlow}`,
          // For smooth removing animation
          "&::before": {
            display: "table",
            width: 0,
            height: 0,
            content: '""'
          }
        }
      })
    }
  };
};
var list_default = genListStyle;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/upload/style/motion.js
var uploadAnimateInlineIn = new Keyframes_default("uploadAnimateInlineIn", {
  from: {
    width: 0,
    height: 0,
    margin: 0,
    padding: 0,
    opacity: 0
  }
});
var uploadAnimateInlineOut = new Keyframes_default("uploadAnimateInlineOut", {
  to: {
    width: 0,
    height: 0,
    margin: 0,
    padding: 0,
    opacity: 0
  }
});
var genMotionStyle = (token) => {
  const {
    componentCls
  } = token;
  const inlineCls = `${componentCls}-animate-inline`;
  return [{
    [`${componentCls}-wrapper`]: {
      [`${inlineCls}-appear, ${inlineCls}-enter, ${inlineCls}-leave`]: {
        animationDuration: token.motionDurationSlow,
        animationTimingFunction: token.motionEaseInOutCirc,
        animationFillMode: "forwards"
      },
      [`${inlineCls}-appear, ${inlineCls}-enter`]: {
        animationName: uploadAnimateInlineIn
      },
      [`${inlineCls}-leave`]: {
        animationName: uploadAnimateInlineOut
      }
    }
  }, uploadAnimateInlineIn, uploadAnimateInlineOut];
};
var motion_default = genMotionStyle;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/upload/style/picture.js
var genPictureStyle = (token) => {
  const {
    componentCls,
    iconCls,
    uploadThumbnailSize,
    uploadProgressOffset
  } = token;
  const listCls = `${componentCls}-list`;
  const itemCls = `${listCls}-item`;
  return {
    [`${componentCls}-wrapper`]: {
      // ${listCls} 增加优先级
      [`${listCls}${listCls}-picture, ${listCls}${listCls}-picture-card`]: {
        [itemCls]: {
          position: "relative",
          height: uploadThumbnailSize + token.lineWidth * 2 + token.paddingXS * 2,
          padding: token.paddingXS,
          border: `${token.lineWidth}px ${token.lineType} ${token.colorBorder}`,
          borderRadius: token.borderRadiusLG,
          "&:hover": {
            background: "transparent"
          },
          [`${itemCls}-thumbnail`]: _extends(_extends({}, textEllipsis), {
            width: uploadThumbnailSize,
            height: uploadThumbnailSize,
            lineHeight: `${uploadThumbnailSize + token.paddingSM}px`,
            textAlign: "center",
            flex: "none",
            [iconCls]: {
              fontSize: token.fontSizeHeading2,
              color: token.colorPrimary
            },
            img: {
              display: "block",
              width: "100%",
              height: "100%",
              overflow: "hidden"
            }
          }),
          [`${itemCls}-progress`]: {
            bottom: uploadProgressOffset,
            width: `calc(100% - ${token.paddingSM * 2}px)`,
            marginTop: 0,
            paddingInlineStart: uploadThumbnailSize + token.paddingXS
          }
        },
        [`${itemCls}-error`]: {
          borderColor: token.colorError,
          // Adjust the color of the error icon : https://github.com/ant-design/ant-design/pull/24160
          [`${itemCls}-thumbnail ${iconCls}`]: {
            [`svg path[fill='#e6f7ff']`]: {
              fill: token.colorErrorBg
            },
            [`svg path[fill='#1890ff']`]: {
              fill: token.colorError
            }
          }
        },
        [`${itemCls}-uploading`]: {
          borderStyle: "dashed",
          [`${itemCls}-name`]: {
            marginBottom: uploadProgressOffset
          }
        }
      }
    }
  };
};
var genPictureCardStyle = (token) => {
  const {
    componentCls,
    iconCls,
    fontSizeLG,
    colorTextLightSolid
  } = token;
  const listCls = `${componentCls}-list`;
  const itemCls = `${listCls}-item`;
  const uploadPictureCardSize = token.uploadPicCardSize;
  return {
    [`${componentCls}-wrapper${componentCls}-picture-card-wrapper`]: _extends(_extends({}, clearFix()), {
      display: "inline-block",
      width: "100%",
      [`${componentCls}${componentCls}-select`]: {
        width: uploadPictureCardSize,
        height: uploadPictureCardSize,
        marginInlineEnd: token.marginXS,
        marginBottom: token.marginXS,
        textAlign: "center",
        verticalAlign: "top",
        backgroundColor: token.colorFillAlter,
        border: `${token.lineWidth}px dashed ${token.colorBorder}`,
        borderRadius: token.borderRadiusLG,
        cursor: "pointer",
        transition: `border-color ${token.motionDurationSlow}`,
        [`> ${componentCls}`]: {
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          height: "100%",
          textAlign: "center"
        },
        [`&:not(${componentCls}-disabled):hover`]: {
          borderColor: token.colorPrimary
        }
      },
      // list
      [`${listCls}${listCls}-picture-card`]: {
        [`${listCls}-item-container`]: {
          display: "inline-block",
          width: uploadPictureCardSize,
          height: uploadPictureCardSize,
          marginBlock: `0 ${token.marginXS}px`,
          marginInline: `0 ${token.marginXS}px`,
          verticalAlign: "top"
        },
        "&::after": {
          display: "none"
        },
        [itemCls]: {
          height: "100%",
          margin: 0,
          "&::before": {
            position: "absolute",
            zIndex: 1,
            width: `calc(100% - ${token.paddingXS * 2}px)`,
            height: `calc(100% - ${token.paddingXS * 2}px)`,
            backgroundColor: token.colorBgMask,
            opacity: 0,
            transition: `all ${token.motionDurationSlow}`,
            content: '" "'
          }
        },
        [`${itemCls}:hover`]: {
          [`&::before, ${itemCls}-actions`]: {
            opacity: 1
          }
        },
        [`${itemCls}-actions`]: {
          position: "absolute",
          insetInlineStart: 0,
          zIndex: 10,
          width: "100%",
          whiteSpace: "nowrap",
          textAlign: "center",
          opacity: 0,
          transition: `all ${token.motionDurationSlow}`,
          [`${iconCls}-eye, ${iconCls}-download, ${iconCls}-delete`]: {
            zIndex: 10,
            width: fontSizeLG,
            margin: `0 ${token.marginXXS}px`,
            fontSize: fontSizeLG,
            cursor: "pointer",
            transition: `all ${token.motionDurationSlow}`
          }
        },
        [`${itemCls}-actions, ${itemCls}-actions:hover`]: {
          [`${iconCls}-eye, ${iconCls}-download, ${iconCls}-delete`]: {
            color: new TinyColor(colorTextLightSolid).setAlpha(0.65).toRgbString(),
            "&:hover": {
              color: colorTextLightSolid
            }
          }
        },
        [`${itemCls}-thumbnail, ${itemCls}-thumbnail img`]: {
          position: "static",
          display: "block",
          width: "100%",
          height: "100%",
          objectFit: "contain"
        },
        [`${itemCls}-name`]: {
          display: "none",
          textAlign: "center"
        },
        [`${itemCls}-file + ${itemCls}-name`]: {
          position: "absolute",
          bottom: token.margin,
          display: "block",
          width: `calc(100% - ${token.paddingXS * 2}px)`
        },
        [`${itemCls}-uploading`]: {
          [`&${itemCls}`]: {
            backgroundColor: token.colorFillAlter
          },
          [`&::before, ${iconCls}-eye, ${iconCls}-download, ${iconCls}-delete`]: {
            display: "none"
          }
        },
        [`${itemCls}-progress`]: {
          bottom: token.marginXL,
          width: `calc(100% - ${token.paddingXS * 2}px)`,
          paddingInlineStart: 0
        }
      }
    })
  };
};

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/upload/style/rtl.js
var genRtlStyle = (token) => {
  const {
    componentCls
  } = token;
  return {
    [`${componentCls}-rtl`]: {
      direction: "rtl"
    }
  };
};
var rtl_default = genRtlStyle;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/upload/style/index.js
var genBaseStyle2 = (token) => {
  const {
    componentCls,
    colorTextDisabled
  } = token;
  return {
    [`${componentCls}-wrapper`]: _extends(_extends({}, resetComponent(token)), {
      [componentCls]: {
        outline: 0,
        "input[type='file']": {
          cursor: "pointer"
        }
      },
      [`${componentCls}-select`]: {
        display: "inline-block"
      },
      [`${componentCls}-disabled`]: {
        color: colorTextDisabled,
        cursor: "not-allowed"
      }
    })
  };
};
var style_default3 = genComponentStyleHook("Upload", (token) => {
  const {
    fontSizeHeading3,
    fontSize,
    lineHeight,
    lineWidth,
    controlHeightLG
  } = token;
  const listItemHeightSM = Math.round(fontSize * lineHeight);
  const uploadToken = merge(token, {
    uploadThumbnailSize: fontSizeHeading3 * 2,
    uploadProgressOffset: listItemHeightSM / 2 + lineWidth,
    uploadPicCardSize: controlHeightLG * 2.55
  });
  return [genBaseStyle2(uploadToken), dragger_default(uploadToken), genPictureStyle(uploadToken), genPictureCardStyle(uploadToken), list_default(uploadToken), motion_default(uploadToken), rtl_default(uploadToken), collapse_default(uploadToken)];
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/upload/Upload.js
var __awaiter3 = function(thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function(resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function(resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e2) {
        reject(e2);
      }
    }
    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e2) {
        reject(e2);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
};
var __rest6 = function(s, e2) {
  var t2 = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e2.indexOf(p) < 0) t2[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i2 = 0, p = Object.getOwnPropertySymbols(s); i2 < p.length; i2++) {
    if (e2.indexOf(p[i2]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i2])) t2[p[i2]] = s[p[i2]];
  }
  return t2;
};
var LIST_IGNORE = `__LIST_IGNORE_${Date.now()}__`;
var Upload_default2 = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "AUpload",
  inheritAttrs: false,
  props: initDefaultProps_default(uploadProps2(), {
    type: "select",
    multiple: false,
    action: "",
    data: {},
    accept: "",
    showUploadList: true,
    listType: "text",
    supportServerRender: true
  }),
  setup(props, _ref) {
    let {
      slots,
      attrs,
      expose
    } = _ref;
    const formItemContext = useInjectFormItemContext();
    const {
      prefixCls,
      direction,
      disabled
    } = useConfigInject_default("upload", props);
    const [wrapSSR, hashId] = style_default3(prefixCls);
    const disabledContext = useInjectDisabled();
    const mergedDisabled = computed(() => {
      var _a;
      return (_a = disabled.value) !== null && _a !== void 0 ? _a : disabledContext.value;
    });
    const [mergedFileList, setMergedFileList] = useMergedState(props.defaultFileList || [], {
      value: toRef(props, "fileList"),
      postState: (list) => {
        const timestamp = Date.now();
        return (list !== null && list !== void 0 ? list : []).map((file, index2) => {
          if (!file.uid && !Object.isFrozen(file)) {
            file.uid = `__AUTO__${timestamp}_${index2}__`;
          }
          return file;
        });
      }
    });
    const dragState = ref("drop");
    const upload2 = ref(null);
    onMounted(() => {
      devWarning_default(props.fileList !== void 0 || attrs.value === void 0, "Upload", "`value` is not a valid prop, do you mean `fileList`?");
      devWarning_default(props.transformFile === void 0, "Upload", "`transformFile` is deprecated. Please use `beforeUpload` directly.");
      devWarning_default(props.remove === void 0, "Upload", "`remove` props is deprecated. Please use `remove` event.");
    });
    const onInternalChange = (file, changedFileList, event) => {
      var _a, _b;
      let cloneList = [...changedFileList];
      if (props.maxCount === 1) {
        cloneList = cloneList.slice(-1);
      } else if (props.maxCount) {
        cloneList = cloneList.slice(0, props.maxCount);
      }
      setMergedFileList(cloneList);
      const changeInfo = {
        file,
        fileList: cloneList
      };
      if (event) {
        changeInfo.event = event;
      }
      (_a = props["onUpdate:fileList"]) === null || _a === void 0 ? void 0 : _a.call(props, changeInfo.fileList);
      (_b = props.onChange) === null || _b === void 0 ? void 0 : _b.call(props, changeInfo);
      formItemContext.onFieldChange();
    };
    const mergedBeforeUpload = (file, fileListArgs) => __awaiter3(this, void 0, void 0, function* () {
      const {
        beforeUpload,
        transformFile
      } = props;
      let parsedFile = file;
      if (beforeUpload) {
        const result = yield beforeUpload(file, fileListArgs);
        if (result === false) {
          return false;
        }
        delete file[LIST_IGNORE];
        if (result === LIST_IGNORE) {
          Object.defineProperty(file, LIST_IGNORE, {
            value: true,
            configurable: true
          });
          return false;
        }
        if (typeof result === "object" && result) {
          parsedFile = result;
        }
      }
      if (transformFile) {
        parsedFile = yield transformFile(parsedFile);
      }
      return parsedFile;
    });
    const onBatchStart = (batchFileInfoList) => {
      const filteredFileInfoList = batchFileInfoList.filter((info) => !info.file[LIST_IGNORE]);
      if (!filteredFileInfoList.length) {
        return;
      }
      const objectFileList = filteredFileInfoList.map((info) => file2Obj(info.file));
      let newFileList = [...mergedFileList.value];
      objectFileList.forEach((fileObj) => {
        newFileList = updateFileList(fileObj, newFileList);
      });
      objectFileList.forEach((fileObj, index2) => {
        let triggerFileObj = fileObj;
        if (!filteredFileInfoList[index2].parsedFile) {
          const {
            originFileObj
          } = fileObj;
          let clone;
          try {
            clone = new File([originFileObj], originFileObj.name, {
              type: originFileObj.type
            });
          } catch (e2) {
            clone = new Blob([originFileObj], {
              type: originFileObj.type
            });
            clone.name = originFileObj.name;
            clone.lastModifiedDate = /* @__PURE__ */ new Date();
            clone.lastModified = (/* @__PURE__ */ new Date()).getTime();
          }
          clone.uid = fileObj.uid;
          triggerFileObj = clone;
        } else {
          fileObj.status = "uploading";
        }
        onInternalChange(triggerFileObj, newFileList);
      });
    };
    const onSuccess = (response, file, xhr) => {
      try {
        if (typeof response === "string") {
          response = JSON.parse(response);
        }
      } catch (e2) {
      }
      if (!getFileItem(file, mergedFileList.value)) {
        return;
      }
      const targetItem = file2Obj(file);
      targetItem.status = "done";
      targetItem.percent = 100;
      targetItem.response = response;
      targetItem.xhr = xhr;
      const nextFileList = updateFileList(targetItem, mergedFileList.value);
      onInternalChange(targetItem, nextFileList);
    };
    const onProgress = (e2, file) => {
      if (!getFileItem(file, mergedFileList.value)) {
        return;
      }
      const targetItem = file2Obj(file);
      targetItem.status = "uploading";
      targetItem.percent = e2.percent;
      const nextFileList = updateFileList(targetItem, mergedFileList.value);
      onInternalChange(targetItem, nextFileList, e2);
    };
    const onError = (error, response, file) => {
      if (!getFileItem(file, mergedFileList.value)) {
        return;
      }
      const targetItem = file2Obj(file);
      targetItem.error = error;
      targetItem.response = response;
      targetItem.status = "error";
      const nextFileList = updateFileList(targetItem, mergedFileList.value);
      onInternalChange(targetItem, nextFileList);
    };
    const handleRemove = (file) => {
      let currentFile;
      const mergedRemove = props.onRemove || props.remove;
      Promise.resolve(typeof mergedRemove === "function" ? mergedRemove(file) : mergedRemove).then((ret) => {
        var _a, _b;
        if (ret === false) {
          return;
        }
        const removedFileList = removeFileItem(file, mergedFileList.value);
        if (removedFileList) {
          currentFile = _extends(_extends({}, file), {
            status: "removed"
          });
          (_a = mergedFileList.value) === null || _a === void 0 ? void 0 : _a.forEach((item) => {
            const matchKey = currentFile.uid !== void 0 ? "uid" : "name";
            if (item[matchKey] === currentFile[matchKey] && !Object.isFrozen(item)) {
              item.status = "removed";
            }
          });
          (_b = upload2.value) === null || _b === void 0 ? void 0 : _b.abort(currentFile);
          onInternalChange(currentFile, removedFileList);
        }
      });
    };
    const onFileDrop = (e2) => {
      var _a;
      dragState.value = e2.type;
      if (e2.type === "drop") {
        (_a = props.onDrop) === null || _a === void 0 ? void 0 : _a.call(props, e2);
      }
    };
    expose({
      onBatchStart,
      onSuccess,
      onProgress,
      onError,
      fileList: mergedFileList,
      upload: upload2
    });
    const [locale] = useLocaleReceiver("Upload", en_US_default.Upload, computed(() => props.locale));
    const renderUploadList = (button, buttonVisible) => {
      const {
        removeIcon,
        previewIcon,
        downloadIcon,
        previewFile,
        onPreview,
        onDownload,
        isImageUrl: isImageUrl2,
        progress,
        itemRender,
        iconRender,
        showUploadList
      } = props;
      const {
        showDownloadIcon,
        showPreviewIcon,
        showRemoveIcon
      } = typeof showUploadList === "boolean" ? {} : showUploadList;
      return showUploadList ? createVNode(UploadList_default, {
        "prefixCls": prefixCls.value,
        "listType": props.listType,
        "items": mergedFileList.value,
        "previewFile": previewFile,
        "onPreview": onPreview,
        "onDownload": onDownload,
        "onRemove": handleRemove,
        "showRemoveIcon": !mergedDisabled.value && showRemoveIcon,
        "showPreviewIcon": showPreviewIcon,
        "showDownloadIcon": showDownloadIcon,
        "removeIcon": removeIcon,
        "previewIcon": previewIcon,
        "downloadIcon": downloadIcon,
        "iconRender": iconRender,
        "locale": locale.value,
        "isImageUrl": isImageUrl2,
        "progress": progress,
        "itemRender": itemRender,
        "appendActionVisible": buttonVisible,
        "appendAction": button
      }, _extends({}, slots)) : button === null || button === void 0 ? void 0 : button();
    };
    return () => {
      var _a, _b, _c;
      const {
        listType,
        type: type4
      } = props;
      const {
        class: className,
        style: styleName
      } = attrs, transAttrs = __rest6(attrs, ["class", "style"]);
      const rcUploadProps = _extends(_extends(_extends({
        onBatchStart,
        onError,
        onProgress,
        onSuccess
      }, transAttrs), props), {
        id: (_a = props.id) !== null && _a !== void 0 ? _a : formItemContext.id.value,
        prefixCls: prefixCls.value,
        beforeUpload: mergedBeforeUpload,
        onChange: void 0,
        disabled: mergedDisabled.value
      });
      delete rcUploadProps.remove;
      if (!slots.default || mergedDisabled.value) {
        delete rcUploadProps.id;
      }
      const rtlCls = {
        [`${prefixCls.value}-rtl`]: direction.value === "rtl"
      };
      if (type4 === "drag") {
        const dragCls = classNames_default(prefixCls.value, {
          [`${prefixCls.value}-drag`]: true,
          [`${prefixCls.value}-drag-uploading`]: mergedFileList.value.some((file) => file.status === "uploading"),
          [`${prefixCls.value}-drag-hover`]: dragState.value === "dragover",
          [`${prefixCls.value}-disabled`]: mergedDisabled.value,
          [`${prefixCls.value}-rtl`]: direction.value === "rtl"
        }, attrs.class, hashId.value);
        return wrapSSR(createVNode("span", _objectSpread2(_objectSpread2({}, attrs), {}, {
          "class": classNames_default(`${prefixCls.value}-wrapper`, rtlCls, className, hashId.value)
        }), [createVNode("div", {
          "class": dragCls,
          "onDrop": onFileDrop,
          "onDragover": onFileDrop,
          "onDragleave": onFileDrop,
          "style": attrs.style
        }, [createVNode(vc_upload_default, _objectSpread2(_objectSpread2({}, rcUploadProps), {}, {
          "ref": upload2,
          "class": `${prefixCls.value}-btn`
        }), _objectSpread2({
          default: () => [createVNode("div", {
            "class": `${prefixCls.value}-drag-container`
          }, [(_b = slots.default) === null || _b === void 0 ? void 0 : _b.call(slots)])]
        }, slots))]), renderUploadList()]));
      }
      const uploadButtonCls = classNames_default(prefixCls.value, {
        [`${prefixCls.value}-select`]: true,
        [`${prefixCls.value}-select-${listType}`]: true,
        [`${prefixCls.value}-disabled`]: mergedDisabled.value,
        [`${prefixCls.value}-rtl`]: direction.value === "rtl"
      });
      const children = flattenChildren((_c = slots.default) === null || _c === void 0 ? void 0 : _c.call(slots));
      const renderUploadButton = (uploadButtonStyle) => createVNode("div", {
        "class": uploadButtonCls,
        "style": uploadButtonStyle
      }, [createVNode(vc_upload_default, _objectSpread2(_objectSpread2({}, rcUploadProps), {}, {
        "ref": upload2
      }), slots)]);
      if (listType === "picture-card") {
        return wrapSSR(createVNode("span", _objectSpread2(_objectSpread2({}, attrs), {}, {
          "class": classNames_default(`${prefixCls.value}-wrapper`, `${prefixCls.value}-picture-card-wrapper`, rtlCls, attrs.class, hashId.value)
        }), [renderUploadList(renderUploadButton, !!(children && children.length))]));
      }
      return wrapSSR(createVNode("span", _objectSpread2(_objectSpread2({}, attrs), {}, {
        "class": classNames_default(`${prefixCls.value}-wrapper`, rtlCls, attrs.class, hashId.value)
      }), [renderUploadButton(children && children.length ? void 0 : {
        display: "none"
      }), renderUploadList()]));
    };
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/upload/Dragger.js
var __rest7 = function(s, e2) {
  var t2 = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e2.indexOf(p) < 0) t2[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i2 = 0, p = Object.getOwnPropertySymbols(s); i2 < p.length; i2++) {
    if (e2.indexOf(p[i2]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i2])) t2[p[i2]] = s[p[i2]];
  }
  return t2;
};
var Dragger_default = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "AUploadDragger",
  inheritAttrs: false,
  props: uploadProps2(),
  setup(props, _ref) {
    let {
      slots,
      attrs
    } = _ref;
    return () => {
      const {
        height
      } = props, restProps = __rest7(props, ["height"]);
      const {
        style
      } = attrs, restAttrs = __rest7(attrs, ["style"]);
      const draggerProps = _extends(_extends(_extends({}, restProps), restAttrs), {
        type: "drag",
        style: _extends(_extends({}, style), {
          height: typeof height === "number" ? `${height}px` : height
        })
      });
      return createVNode(Upload_default2, draggerProps, slots);
    };
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/upload/index.js
var UploadDragger = Dragger_default;
var upload_default = _extends(Upload_default2, {
  Dragger: Dragger_default,
  LIST_IGNORE,
  install(app) {
    app.component(Upload_default2.name, Upload_default2);
    app.component(Dragger_default.name, Dragger_default);
    return app;
  }
});

export {
  es_default,
  CheckCircleFilled_default2 as CheckCircleFilled_default,
  ExclamationCircleFilled_default2 as ExclamationCircleFilled_default,
  responsiveArray,
  useResponsiveObserver,
  Row_default,
  Col_default,
  FormItem_default,
  form_default,
  progress_default2 as progress_default,
  DeleteOutlined_default2 as DeleteOutlined_default,
  UploadDragger,
  upload_default
};
//# sourceMappingURL=chunk-C2E6S54T.js.map
