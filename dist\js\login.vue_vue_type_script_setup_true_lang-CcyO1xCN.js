var _e=Object.defineProperty,$e=Object.defineProperties;var ke=Object.getOwnPropertyDescriptors;var Y=Object.getOwnPropertySymbols;var ue=Object.prototype.hasOwnProperty,de=Object.prototype.propertyIsEnumerable;var ce=(s,o,n)=>o in s?_e(s,o,{enumerable:!0,configurable:!0,writable:!0,value:n}):s[o]=n,z=(s,o)=>{for(var n in o||(o={}))ue.call(o,n)&&ce(s,n,o[n]);if(Y)for(var n of Y(o))de.call(o,n)&&ce(s,n,o[n]);return s},j=(s,o)=>$e(s,ke(o));var H=(s,o)=>{var n={};for(var a in s)ue.call(s,a)&&o.indexOf(a)<0&&(n[a]=s[a]);if(s!=null&&Y)for(var a of Y(s))o.indexOf(a)<0&&de.call(s,a)&&(n[a]=s[a]);return n};var Z=(s,o,n)=>new Promise((a,l)=>{var r=g=>{try{c(n.next(g))}catch(u){l(u)}},t=g=>{try{c(n.throw(g))}catch(u){l(u)}},c=g=>g.done?a(g.value):Promise.resolve(g.value).then(r,t);c((n=n.apply(s,o)).next())});import{a$ as Ce,bv as he,bw as Pe,bx as Be,by as Le,bz as xe,bA as Se,Y as Te,bB as Me,bC as be,bD as Re,bE as Ae,bc as Ee,bF as Ie,bG as De,bu as Ve,b0 as y,bh as ze,bj as ye,bH as Fe,bI as Oe,b2 as U,bJ as Ne,bK as fe,bL as qe,bM as G,bN as We,b1 as je,bO as Ue,bt as Ke,aq as Qe,bP as Xe}from"./bootstrap-CFDAkNgp.js";import{a4 as $,aa as v,ab as f,a7 as e,af as se,ag as ae,ac as p,a8 as k,av as S,J as R,P as q,x as b,aq as B,aw as Q,ad as le,aW as K,bx as N,aV as F,ai as D,aj as P,a_ as ee,a$ as ve,T as we,Y as Ge,ao as Je,bI as Ye,b6 as pe,k as J,aB as L,ah as te,n as He,a as Ze,bJ as et,a9 as tt,Q as ot}from"../jse/index-index-B2UBupFX.js";import{T as nt}from"./auth-title-lVew_PFZ.js";import{C as st,X as at,u as lt,R as it,M as rt,a as ct,b as ut,c as dt}from"./use-modal-DK8XHZ9A.js";import{C as ft,a as pt,u as mt,s as oe,b as gt}from"./use-vben-form-DwBeC3z-.js";import{_ as me}from"./render-content.vue_vue_type_script_lang-DAf0xCTA.js";const ht=Ce("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),bt=$({__name:"AlertDialog",props:{open:{type:Boolean},defaultOpen:{type:Boolean}},emits:["update:open"],setup(s,{emit:o}){const l=he(s,o);return(r,t)=>(f(),v(e(Pe),se(ae(e(l))),{default:p(()=>[k(r.$slots,"default")]),_:3},16))}}),yt=$({__name:"AlertDialogAction",props:{asChild:{type:Boolean},as:{}},setup(s){const o=s;return(n,a)=>(f(),v(e(Be),se(ae(o)),{default:p(()=>[k(n.$slots,"default")]),_:3},16))}}),ge=$({__name:"AlertDialogCancel",props:{asChild:{type:Boolean},as:{}},setup(s){const o=s;return(n,a)=>(f(),v(e(Le),se(ae(o)),{default:p(()=>[k(n.$slots,"default")]),_:3},16))}}),vt={class:"bg-overlay z-popup inset-0"},wt=$({__name:"AlertDialogOverlay",setup(s){return xe(),(o,n)=>(f(),S("div",vt))}}),_t=$({__name:"AlertDialogContent",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{},centered:{type:Boolean},class:{},modal:{type:Boolean,default:!0},open:{type:Boolean},overlayBlur:{},zIndex:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus","close","closed","opened"],setup(s,{expose:o,emit:n}){const a=s,l=n,r=R(()=>{const d=a,{class:u,modal:h,open:C}=d;return H(d,["class","modal","open"])}),t=he(r,l),c=q(null);function g(u){var h;u.target===((h=c.value)==null?void 0:h.$el)&&(a.open?l("opened"):l("closed"))}return o({getContentRef:()=>c.value}),(u,h)=>(f(),v(e(Se),null,{default:p(()=>[b(Te,{name:"fade",appear:""},{default:p(()=>[u.open&&u.modal?(f(),v(wt,{key:0,style:Q(j(z({},u.zIndex?{zIndex:u.zIndex}:{}),{position:"fixed",backdropFilter:u.overlayBlur&&u.overlayBlur>0?`blur(${u.overlayBlur}px)`:"none"})),onClick:h[0]||(h[0]=()=>l("close"))},null,8,["style"])):B("",!0)]),_:1}),b(e(Me),le({ref_key:"contentRef",ref:c,style:j(z({},u.zIndex?{zIndex:u.zIndex}:{}),{position:"fixed"}),onAnimationend:g},e(t),{class:e(K)("z-popup bg-background p-6 shadow-lg outline-none sm:rounded-xl","data-[state=open]:animate-in data-[state=open]:fade-in-0 data-[state=open]:zoom-in-95","data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95",{"data-[state=open]:slide-in-from-top-[48%] data-[state=closed]:slide-out-to-top-[48%]":!u.centered,"data-[state=open]:slide-in-from-top-[98%] data-[state=closed]:slide-out-to-top-[148%]":u.centered,"top-[10vh]":!u.centered,"top-1/2 -translate-y-1/2":u.centered},a.class)}),{default:p(()=>[k(u.$slots,"default")]),_:3},16,["style","class"])]),_:3}))}}),$t=$({__name:"AlertDialogDescription",props:{asChild:{type:Boolean},as:{},class:{}},setup(s){const o=s,n=R(()=>{const t=o,{class:l}=t;return H(t,["class"])}),a=be(n);return(l,r)=>(f(),v(e(Re),le(e(a),{class:e(K)("text-muted-foreground text-sm",o.class)}),{default:p(()=>[k(l.$slots,"default")]),_:3},16,["class"]))}}),kt=$({__name:"AlertDialogTitle",props:{asChild:{type:Boolean},as:{},class:{}},setup(s){const o=s,n=R(()=>{const t=o,{class:l}=t;return H(t,["class"])}),a=be(n);return(l,r)=>(f(),v(e(Ae),le(e(a),{class:e(K)("text-lg font-semibold leading-none tracking-tight",o.class)}),{default:p(()=>[k(l.$slots,"default")]),_:3},16,["class"]))}}),Ct=$({__name:"spine-text",props:{animationDuration:{default:2},animationIterationCount:{default:"infinite"}},setup(s){const o=R(()=>({animation:`shine ${s.animationDuration}s linear ${s.animationIterationCount}`}));return(n,a)=>(f(),S("div",{style:Q(o.value),class:"vben-spine-text !bg-clip-text text-transparent"},[k(n.$slots,"default")],4))}}),Pt=$({__name:"slider-captcha-action",props:{actionStyle:{},isPassing:{type:Boolean},toLeft:{type:Boolean}},setup(s,{expose:o}){const n=s,a=N("actionRef"),l=q("0"),r=R(()=>{const{actionStyle:c}=n;return j(z({},c),{left:l.value})}),t=R(()=>Number.parseInt(l.value)>10&&!n.isPassing);return o({getEl:()=>a.value,getStyle:()=>{var c;return(c=a==null?void 0:a.value)==null?void 0:c.style},setLeft:c=>{l.value=c}}),(c,g)=>(f(),S("div",{ref_key:"actionRef",ref:a,class:F([{"transition-width !left-0 duration-300":c.toLeft,"rounded-md":t.value},"bg-background dark:bg-accent absolute left-0 top-0 flex h-full cursor-move items-center justify-center px-3.5 shadow-md"]),style:Q(r.value),name:"captcha-action"},[b(e(Ee),{"is-passing":c.isPassing,class:"text-foreground/60 size-4"},{default:p(()=>[k(c.$slots,"icon",{},()=>[c.isPassing?(f(),v(e(De),{key:1})):(f(),v(e(Ie),{key:0}))])]),_:3},8,["is-passing"])],6))}}),Bt=$({__name:"slider-captcha-bar",props:{barStyle:{},toLeft:{type:Boolean}},setup(s,{expose:o}){const n=s,a=N("barRef"),l=q("0"),r=R(()=>{const{barStyle:t}=n;return j(z({},t),{width:l.value})});return o({getEl:()=>a.value,setWidth:t=>{l.value=t}}),(t,c)=>(f(),S("div",{ref_key:"barRef",ref:a,class:F([t.toLeft&&"transition-width !w-0 duration-300","bg-success absolute h-full"]),style:Q(r.value)},null,6))}}),Lt=$({__name:"slider-captcha-content",props:{contentStyle:{},isPassing:{type:Boolean},successText:{},text:{}},setup(s,{expose:o}){const n=s,a=N("contentRef"),l=R(()=>{const{contentStyle:r}=n;return z({},r)});return o({getEl:()=>a.value}),(r,t)=>(f(),S("div",{ref_key:"contentRef",ref:a,class:F([{[r.$style.success]:r.isPassing},"absolute top-0 flex size-full select-none items-center justify-center text-xs"]),style:Q(l.value)},[k(r.$slots,"text",{},()=>[b(e(Ct),{class:"flex h-full items-center"},{default:p(()=>[D(P(r.isPassing?r.successText:r.text),1)]),_:1})])],6))}}),xt="_success_fwxn1_2",St={success:xt},Tt={$style:St},Mt=Ve(Lt,[["__cssModules",Tt]]),Rt=$({__name:"index",props:ee({class:{},actionStyle:{default:()=>({})},barStyle:{default:()=>({})},contentStyle:{default:()=>({})},wrapperStyle:{default:()=>({})},isSlot:{type:Boolean,default:!1},successText:{default:""},text:{default:""}},{modelValue:{type:Boolean,default:!1},modelModifiers:{}}),emits:ee(["end","move","start","success"],["update:modelValue"]),setup(s,{expose:o,emit:n}){const a=s,l=n,r=ve(s,"modelValue"),t=we({endTime:0,isMoving:!1,isPassing:!1,moveDistance:0,startTime:0,toLeft:!1});o({resume:m});const c=N("wrapperRef"),g=N("barRef"),u=N("contentRef"),h=N("actionRef");Ge(()=>t.isPassing,i=>{if(i){const{endTime:x,startTime:M}=t,A=(x-M)/1e3;l("success",{isPassing:i,time:A.toFixed(1)}),r.value=i}}),Je(()=>{t.isPassing=!!r.value});function C(i){return"pageX"in i?i.pageX:"touches"in i&&i.touches[0]?i.touches[0].pageX:0}function T(i){t.isPassing||h.value&&(l("start",i),t.moveDistance=C(i)-Number.parseInt(h.value.getStyle().left.replace("px","")||"0",10),t.startTime=Date.now(),t.isMoving=!0)}function d(i){var E,I,O;const x=(I=(E=c.value)==null?void 0:E.offsetWidth)!=null?I:220,M=(O=i==null?void 0:i.offsetWidth)!=null?O:40,A=x-M-6;return{actionWidth:M,offset:A,wrapperWidth:x}}function w(i){const{isMoving:x,moveDistance:M}=t;if(x){const A=e(h),E=e(g);if(!A||!E)return;const{actionWidth:I,offset:O,wrapperWidth:X}=d(A.getEl()),V=C(i)-M;l("move",{event:i,moveDistance:M,moveX:V}),V>0&&V<=O?(A.setLeft(`${V}px`),E.setWidth(`${V+I/2}px`)):V>O&&(A.setLeft(`${X-I}px`),E.setWidth(`${X-I/2}px`),a.isSlot||W())}}function _(i){const{isMoving:x,isPassing:M,moveDistance:A}=t;if(x&&!M){l("end",i);const E=h.value,I=e(g);if(!E||!I)return;const O=C(i)-A,{actionWidth:X,offset:V,wrapperWidth:ie}=d(E.getEl());O<V?a.isSlot?setTimeout(()=>{if(r.value){const re=e(u);re&&(re.getEl().style.width=`${Number.parseInt(I.getEl().style.width)}px`)}else m()},0):m():(E.setLeft(`${ie-X}px`),I.setWidth(`${ie-X/2}px`),W()),t.isMoving=!1}}function W(){if(a.isSlot){m();return}t.endTime=Date.now(),t.isPassing=!0,t.isMoving=!1}function m(){t.isMoving=!1,t.isPassing=!1,t.moveDistance=0,t.toLeft=!1,t.startTime=0,t.endTime=0;const i=e(h),x=e(g),M=e(u);!i||!x||!M||(M.getEl().style.width="100%",t.toLeft=!0,Ye(()=>{t.toLeft=!1,i.setLeft("0"),x.setWidth("0")},300))}return(i,x)=>(f(),S("div",{ref_key:"wrapperRef",ref:c,class:F(e(K)("border-border bg-background-deep relative flex h-10 w-full items-center overflow-hidden rounded-md border text-center",a.class)),style:Q(i.wrapperStyle),onMouseleave:_,onMousemove:w,onMouseup:_,onTouchend:_,onTouchmove:w},[b(Bt,{ref_key:"barRef",ref:g,"bar-style":i.barStyle,"to-left":t.toLeft},null,8,["bar-style","to-left"]),b(Mt,{ref_key:"contentRef",ref:u,"content-style":i.contentStyle,"is-passing":t.isPassing,"success-text":i.successText||e(y)("ui.captcha.sliderSuccessText"),text:i.text||e(y)("ui.captcha.sliderDefaultText")},pe({_:2},[i.$slots.text?{name:"text",fn:p(()=>[k(i.$slots,"text",{isPassing:t.isPassing})]),key:"0"}:void 0]),1032,["content-style","is-passing","success-text","text"]),b(Pt,{ref_key:"actionRef",ref:h,"action-style":i.actionStyle,"is-passing":t.isPassing,"to-left":t.toLeft,onMousedown:T,onTouchstart:T},pe({_:2},[i.$slots.actionIcon?{name:"icon",fn:p(()=>[k(i.$slots,"actionIcon",{isPassing:t.isPassing})]),key:"0"}:void 0]),1032,["action-style","is-passing","to-left"])],38))}}),[lo,At]=ze("VbenAlertContext"),Et={class:"flex items-center"},It={class:"flex-auto"},Dt={class:"m-4 min-h-[30px]"},Vt=$({__name:"alert",props:ee({beforeClose:{},bordered:{type:Boolean,default:!0},buttonAlign:{default:"end"},cancelText:{},centered:{type:Boolean,default:!0},confirmText:{},containerClass:{},content:{},contentClass:{},contentMasking:{type:Boolean},footer:{},icon:{},overlayBlur:{},showCancel:{type:Boolean},title:{}},{open:{type:Boolean,default:!1},openModifiers:{}}),emits:ee(["closed","confirm","opened"],["update:open"]),setup(s,{emit:o}){const n=s,a=o,l=ve(s,"open"),{$t:r}=ye(),t=Fe.getComponents(),c=q(!1);function g(){a("closed",c.value),c.value=!1}function u(){c.value=!1}const h=R(()=>{var i;let m=null;if(n.icon){if(typeof n.icon=="string")switch(n.icon){case"error":{m=J(Oe,{style:{color:"hsl(var(--destructive))"}});break}case"info":{m=J(ht,{style:{color:"hsl(var(--info))"}});break}case"question":{m=pt;break}case"success":{m=J(st,{style:{color:"hsl(var(--success))"}});break}case"warning":{m=J(ft,{style:{color:"hsl(var(--warning))"}});break}default:{m=null;break}}}else m=(i=n.icon)!=null?i:null;return m});function C(){w(),W(!1)}function T(){d(),W(!1)}At({doCancel:C,doConfirm:T});function d(){c.value=!0,a("confirm")}function w(){c.value=!1}const _=q(!1);function W(m){return Z(this,null,function*(){if(yield He(),!m&&n.beforeClose){_.value=!0;try{(yield n.beforeClose({isConfirm:c.value}))!==!1&&(l.value=!1)}finally{_.value=!1}}else l.value=m})}return(m,i)=>(f(),v(e(bt),{open:l.value,"onUpdate:open":W},{default:p(()=>[b(e(_t),{open:l.value,centered:m.centered,"overlay-blur":m.overlayBlur,onOpened:i[0]||(i[0]=x=>a("opened")),onClosed:g,onEscapeKeyDown:u,class:F(e(K)(m.containerClass,"left-0 right-0 mx-auto flex max-h-[80%] flex-col p-0 duration-300 sm:w-[520px] sm:max-w-[80%] sm:rounded-[var(--radius)]",{"border-border border":m.bordered,"shadow-3xl":!m.bordered}))},{default:p(()=>[L("div",{class:F(e(K)("relative flex-1 overflow-y-auto p-3",m.contentClass))},[m.title?(f(),v(e(kt),{key:0},{default:p(()=>[L("div",Et,[(f(),v(te(h.value),{class:"mr-2"})),L("span",It,P(e(r)(m.title)),1),m.showCancel?(f(),v(e(ge),{key:0,"as-child":""},{default:p(()=>[b(e(U),{variant:"ghost",size:"icon",class:"rounded-full",disabled:_.value,onClick:w},{default:p(()=>[b(e(at),{class:"text-muted-foreground size-4"})]),_:1},8,["disabled"])]),_:1})):B("",!0)])]),_:1})):B("",!0),b(e($t),null,{default:p(()=>[L("div",Dt,[b(e(me),{content:m.content,"render-br":""},null,8,["content"])]),_.value&&m.contentMasking?(f(),v(e(Ne),{key:0,spinning:_.value},null,8,["spinning"])):B("",!0)]),_:1}),L("div",{class:F(["flex items-center justify-end gap-x-2",`justify-${m.buttonAlign}`])},[b(e(me),{content:m.footer},null,8,["content"]),m.showCancel?(f(),v(e(ge),{key:0,"as-child":""},{default:p(()=>[(f(),v(te(e(t).DefaultButton||e(U)),{disabled:_.value,variant:"ghost",onClick:w},{default:p(()=>[D(P(m.cancelText||e(r)("cancel")),1)]),_:1},8,["disabled"]))]),_:1})):B("",!0),b(e(yt),{"as-child":""},{default:p(()=>[(f(),v(te(e(t).PrimaryButton||e(U)),{loading:_.value,onClick:d},{default:p(()=>[D(P(m.confirmText||e(r)("confirm")),1)]),_:1},8,["loading"]))]),_:1})],2)],2)]),_:1},8,["open","centered","overlay-blur","class"])]),_:1},8,["open"]))}}),ne=q([]),{$t:zt}=ye();function Ft(s,o,n){return new Promise((a,l)=>{var h,C;const r=Ze(s)?{content:s}:z({},s),t=document.createElement("div");document.body.append(t);const c={container:t,instance:null},g=j(z({onClosed:T=>{ne.value=ne.value.filter(d=>d!==c),fe(null,t),t.parentNode&&t.remove(),T?a():l(new Error("dialog cancelled"))}},r),{open:!0,title:(h=r.title)!=null?h:zt.value("prompt")}),u=J(Vt,g);fe(u,t),c.instance=(C=u.component)==null?void 0:C.proxy,ne.value.push(c)})}const Ot=$({__name:"dingding-login",props:{clientId:{},corpId:{},redirectUri:{},isQrCode:{type:Boolean}},setup(s){const o=s,n=qe(),[a,l]=lt({header:!1,footer:!1,fullscreenButton:!1,class:"w-[302px] h-[302px] dingding-qrcode-login-modal",onOpened(){t()}}),r=()=>{const{redirectUri:g}=o;return g||window.location.origin+n.fullPath},t=()=>Z(null,null,function*(){const{clientId:g,corpId:u}=o;window.DTFrameLogin||(yield et("https://g.alicdn.com/dingding/h5-dingtalk-login/0.21.0/ddlogin.js")),window.DTFrameLogin({id:"dingding_qrcode_login_element",width:300,height:300},{redirect_uri:encodeURIComponent(r()),client_id:g,scope:"openid corpid",response_type:"code",state:"1",prompt:"consent",corpId:u},h=>{const{redirectUrl:C}=h;window.location.href=C},h=>{Ft(`Login Error: ${h}`)})}),c=()=>{const{clientId:g,corpId:u,isQrCode:h}=o;h?l.open():window.location.href=`https://login.dingtalk.com/oauth2/auth?redirect_uri=${encodeURIComponent(r())}&response_type=code&client_id=${g}&scope=openid&corpid=${u}&prompt=consent`};return(g,u)=>(f(),S("div",null,[b(e(G),{onClick:c,tooltip:e(y)("authentication.dingdingLogin"),"tooltip-side":"top"},{default:p(()=>[b(e(it))]),_:1},8,["tooltip"]),b(e(a),null,{default:p(()=>u[0]||(u[0]=[L("div",{id:"dingding_qrcode_login_element"},null,-1)])),_:1,__:[0]})]))}}),Nt={class:"w-full sm:mx-auto md:max-w-md"},qt={class:"mt-4 flex items-center justify-between"},Wt={class:"text-muted-foreground text-center text-xs uppercase"},jt={class:"mt-4 flex flex-wrap justify-center"},Ut=$({name:"ThirdPartyLogin",__name:"third-party-login",setup(s){const{auth:{dingding:o}}=We();return(n,a)=>(f(),S("div",Nt,[L("div",qt,[a[0]||(a[0]=L("span",{class:"border-input w-[35%] border-b dark:border-gray-600"},null,-1)),L("span",Wt,P(e(y)("authentication.thirdPartyLogin")),1),a[1]||(a[1]=L("span",{class:"border-input w-[35%] border-b dark:border-gray-600"},null,-1))]),L("div",jt,[b(e(G),{tooltip:e(y)("authentication.wechatLogin"),"tooltip-side":"top",class:"mb-3"},{default:p(()=>[b(e(rt))]),_:1},8,["tooltip"]),b(e(G),{tooltip:e(y)("authentication.qqLogin"),"tooltip-side":"top",class:"mb-3"},{default:p(()=>[b(e(ct))]),_:1},8,["tooltip"]),b(e(G),{tooltip:e(y)("authentication.githubLogin"),"tooltip-side":"top",class:"mb-3"},{default:p(()=>[b(e(ut))]),_:1},8,["tooltip"]),b(e(G),{tooltip:e(y)("authentication.googleLogin"),"tooltip-side":"top",class:"mb-3"},{default:p(()=>[b(e(dt))]),_:1},8,["tooltip"]),e(o)?(f(),v(Ot,{key:0,"corp-id":e(o).corpId,"client-id":e(o).clientId,class:"mb-3"},null,8,["corp-id","client-id"])):B("",!0)])]))}}),Kt=["onKeydown"],Qt={class:"text-muted-foreground"},Xt={key:0,class:"mb-6 flex justify-between"},Gt={class:"flex-center"},Jt={key:1,class:"mb-2 mt-4 flex items-center justify-between"},Yt={key:0,class:"mt-3 text-center text-sm"},Ht=$({name:"AuthenticationLogin",__name:"login",props:{formSchema:{default:()=>[]},codeLoginPath:{default:"/auth/code-login"},forgetPasswordPath:{default:"/auth/forget-password"},loading:{type:Boolean,default:!1},qrCodeLoginPath:{default:"/auth/qrcode-login"},registerPath:{default:"/auth/register"},showCodeLogin:{type:Boolean,default:!0},showForgetPassword:{type:Boolean,default:!0},showQrcodeLogin:{type:Boolean,default:!0},showRegister:{type:Boolean,default:!0},showRememberMe:{type:Boolean,default:!0},showThirdPartyLogin:{type:Boolean,default:!0},subTitle:{default:""},title:{default:""},submitButtonText:{default:""}},emits:["submit"],setup(s,{expose:o,emit:n}){const a=s,l=n,[r,t]=mt(we({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:R(()=>a.formSchema),showDefaultActions:!1})),c=je(),g=`REMEMBER_ME_USERNAME_${location.hostname}`,u=localStorage.getItem(g)||"",h=q(!!u);function C(){return Z(this,null,function*(){const{valid:d}=yield t.validate(),w=yield t.getValues();d&&(localStorage.setItem(g,h.value?w==null?void 0:w.username:""),l("submit",w))})}function T(d){c.push(d)}return tt(()=>{u&&t.setFieldValue("username",u)}),o({getFormApi:()=>t}),(d,w)=>(f(),S("div",{onKeydown:Ke(Qe(C,["prevent"]),["enter"])},[k(d.$slots,"title",{},()=>[b(nt,null,{desc:p(()=>[L("span",Qt,[k(d.$slots,"subTitle",{},()=>[D(P(d.subTitle||e(y)("authentication.loginSubtitle")),1)])])]),default:p(()=>[k(d.$slots,"title",{},()=>[D(P(d.title||`${e(y)("authentication.welcomeBack")} 👋🏻`),1)])]),_:3})]),b(e(r)),d.showRememberMe||d.showForgetPassword?(f(),S("div",Xt,[L("div",Gt,[d.showRememberMe?(f(),v(e(Ue),{key:0,checked:h.value,"onUpdate:checked":w[0]||(w[0]=_=>h.value=_),name:"rememberMe"},{default:p(()=>[D(P(e(y)("authentication.rememberMe")),1)]),_:1},8,["checked"])):B("",!0)]),d.showForgetPassword?(f(),S("span",{key:0,class:"vben-link text-sm font-normal",onClick:w[1]||(w[1]=_=>T(d.forgetPasswordPath))},P(e(y)("authentication.forgetPassword")),1)):B("",!0)])):B("",!0),b(e(U),{class:F([{"cursor-wait":d.loading},"w-full"]),loading:d.loading,"aria-label":"login",onClick:C},{default:p(()=>[D(P(d.submitButtonText||e(y)("common.login")),1)]),_:1},8,["class","loading"]),d.showCodeLogin||d.showQrcodeLogin?(f(),S("div",Jt,[d.showCodeLogin?(f(),v(e(U),{key:0,class:"w-1/2",variant:"outline",onClick:w[2]||(w[2]=_=>T(d.codeLoginPath))},{default:p(()=>[D(P(e(y)("authentication.mobileLogin")),1)]),_:1})):B("",!0),d.showQrcodeLogin?(f(),v(e(U),{key:1,class:"ml-4 w-1/2",variant:"outline",onClick:w[3]||(w[3]=_=>T(d.qrCodeLoginPath))},{default:p(()=>[D(P(e(y)("authentication.qrcodeLogin")),1)]),_:1})):B("",!0)])):B("",!0),k(d.$slots,"third-party-login",{},()=>[d.showThirdPartyLogin?(f(),v(Ut,{key:0})):B("",!0)]),k(d.$slots,"to-register",{},()=>[d.showRegister?(f(),S("div",Yt,[D(P(e(y)("authentication.accountTip"))+" ",1),L("span",{class:"vben-link text-sm font-normal",onClick:w[4]||(w[4]=_=>T(d.registerPath))},P(e(y)("authentication.createAccount")),1)])):B("",!0)])],40,Kt))}}),io=$({name:"Login",__name:"login",setup(s){const o=Xe(),n=[{label:"Super",value:"vben"},{label:"Admin",value:"admin"},{label:"User",value:"jack"}],a=R(()=>[{component:"VbenSelect",componentProps:{options:n,placeholder:y("authentication.selectAccount")},fieldName:"selectAccount",label:y("authentication.selectAccount"),rules:oe().min(1,{message:y("authentication.selectAccount")}).optional().default("vben")},{component:"VbenInput",componentProps:{placeholder:y("authentication.usernameTip")},dependencies:{trigger(l,r){if(l.selectAccount){const t=n.find(c=>c.value===l.selectAccount);t&&r.setValues({password:"123456",username:t.value})}},triggerFields:["selectAccount"]},fieldName:"username",label:y("authentication.username"),rules:oe().min(1,{message:y("authentication.usernameTip")})},{component:"VbenInputPassword",componentProps:{placeholder:y("authentication.password")},fieldName:"password",label:y("authentication.password"),rules:oe().min(1,{message:y("authentication.passwordTip")})},{component:ot(Rt),fieldName:"captcha",rules:gt().refine(l=>l,{message:y("authentication.verifyRequiredTip")})}]);return(l,r)=>(f(),v(e(Ht),{"form-schema":a.value,loading:e(o).loginLoading,onSubmit:e(o).authLogin},null,8,["form-schema","loading","onSubmit"]))}});export{io as _};
