var Fs=Object.defineProperty,As=Object.defineProperties;var Ns=Object.getOwnPropertyDescriptors;var et=Object.getOwnPropertySymbols;var jt=Object.prototype.hasOwnProperty,Zt=Object.prototype.propertyIsEnumerable;var Mt=Math.pow,mt=(s,e,t)=>e in s?Fs(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t,l=(s,e)=>{for(var t in e||(e={}))jt.call(e,t)&&mt(s,t,e[t]);if(et)for(var t of et(e))Zt.call(e,t)&&mt(s,t,e[t]);return s},w=(s,e)=>As(s,Ns(e));var pt=(s,e)=>{var t={};for(var r in s)jt.call(s,r)&&e.indexOf(r)<0&&(t[r]=s[r]);if(s!=null&&et)for(var r of et(s))e.indexOf(r)<0&&Zt.call(s,r)&&(t[r]=s[r]);return t};var J=(s,e,t)=>mt(s,typeof e!="symbol"?e+"":e,t);var T=(s,e,t)=>new Promise((r,n)=>{var a=c=>{try{o(t.next(c))}catch(u){n(u)}},i=c=>{try{o(t.throw(c))}catch(u){n(u)}},o=c=>c.done?r(c.value):Promise.resolve(c.value).then(a,i);o((t=t.apply(s,e)).next())});import{R as Pt,a4 as Y,aa as P,ab as R,a7 as f,ac as $,a8 as M,av as ce,aV as Q,aW as U,an as Vs,aF as Es,J as N,ad as ne,aX as Is,x as re,a_ as $s,a$ as us,aB as He,ai as st,aj as Le,P as fe,Y as ke,am as js,r as Se,by as Zs,bn as Ms,U as Dt,i as G,bz as qe,bA as Ps,bB as Lt,o as Rt,bC as Ds,bD as Ls,as as Bs,bE as _t,a as he,aq as L,F as bt,ah as We,bF as zs,bG as yt,bx as ds,ax as Us,aA as qs,aw as Ws,b6 as fs,aC as Ft,af as ve,ag as ge,n as at,bH as Hs,aP as Gs,aQ as Ys,a9 as hs,b3 as Bt,b5 as Js,a3 as Qs,aI as Xs,V as Ks,az as er,k as tr}from"../jse/index-index-DYNcUVMZ.js";import{aR as ms,aZ as sr,a_ as rr,a$ as nr,b0 as ps,b1 as ar,b2 as ir,b3 as or,b4 as lr,b5 as ys,b6 as cr,b7 as At,b8 as ur,b9 as dr,ba as rt,bb as fr,bc as hr,bd as vs,be as mr,a8 as pr,a7 as yr,bf as vr,bg as gr,bh as _r,bi as br,bj as kr}from"./bootstrap-DlHXJWd_.js";import{_ as Be}from"./render-content.vue_vue_type_script_lang-D2VaNJ52.js";const xr=ms("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);const wr=ms("circle-help",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),gs=Symbol();function dt(){const s=Pt(ar),e=Pt(gs);if(!s)throw new Error("useFormField should be used within <FormField>");const{name:t}=s,r=e,n={error:ps(t),isDirty:nr(t),isTouched:rr(t),valid:sr(t)};return l({formDescriptionId:`${r}-form-item-description`,formItemId:`${r}-form-item`,formMessageId:`${r}-form-item-message`,id:r,name:t},n)}const Cr=Y({__name:"FormControl",setup(s){const{error:e,formDescriptionId:t,formItemId:r,formMessageId:n}=dt();return(a,i)=>(R(),P(f(ir),{id:f(r),"aria-describedby":f(e)?`${f(t)} ${f(n)}`:`${f(t)}`,"aria-invalid":!!f(e)},{default:$(()=>[M(a.$slots,"default")]),_:3},8,["id","aria-describedby","aria-invalid"]))}}),Sr=["id"],Or=Y({__name:"FormDescription",props:{class:{}},setup(s){const e=s,{formDescriptionId:t}=dt();return(r,n)=>(R(),ce("p",{id:f(t),class:Q(f(U)("text-muted-foreground text-sm",e.class))},[M(r.$slots,"default")],10,Sr))}}),Tr=Y({__name:"FormItem",props:{class:{}},setup(s){const e=s,t=Vs();return Es(gs,t),(r,n)=>(R(),ce("div",{class:Q(f(U)(e.class))},[M(r.$slots,"default")],2))}}),Rr=Y({__name:"Label",props:{for:{},asChild:{type:Boolean},as:{},class:{}},setup(s){const e=s,t=N(()=>{const a=e,{class:r}=a;return pt(a,["class"])});return(r,n)=>(R(),P(f(or),ne(t.value,{class:f(U)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e.class)}),{default:$(()=>[M(r.$slots,"default")]),_:3},16,["class"]))}}),Fr=Y({__name:"FormLabel",props:{for:{},asChild:{type:Boolean},as:{},class:{}},setup(s){const e=s,{formItemId:t}=dt();return(r,n)=>(R(),P(f(Rr),{class:Q(f(U)(e.class)),for:f(t)},{default:$(()=>[M(r.$slots,"default")]),_:3},8,["class","for"]))}}),zt=Y({__name:"FormMessage",setup(s){const{formMessageId:e,name:t}=dt();return(r,n)=>(R(),P(f(lr),{id:f(e),name:Is(f(t)),as:"p",class:"text-destructive text-[0.8rem]"},null,8,["id","name"]))}}),Ar=Y({inheritAttrs:!1,__name:"help-tooltip",props:{triggerClass:{}},setup(s){return(e,t)=>(R(),P(ys,{"delay-duration":300,side:"right"},{trigger:$(()=>[M(e.$slots,"trigger",{},()=>[re(f(wr),{class:Q(f(U)("text-foreground/80 hover:text-foreground inline-flex size-5 cursor-pointer",e.triggerClass))},null,8,["class"])])]),default:$(()=>[M(e.$slots,"default")]),_:3}))}}),Nr=Y({__name:"expandable-arrow",props:$s({class:{}},{modelValue:{default:!1},modelModifiers:{}}),emits:["update:modelValue"],setup(s){const e=s,t=us(s,"modelValue");return(r,n)=>(R(),ce("div",{class:Q(f(U)("vben-link inline-flex items-center",e.class)),onClick:n[0]||(n[0]=a=>t.value=!t.value)},[M(r.$slots,"default",{isExpanded:t.value},()=>[st(Le(t.value),1)]),He("div",{class:Q([{"rotate-180":!t.value},"transition-transform duration-300"])},[M(r.$slots,"icon",{},()=>[re(f(cr),{class:"size-4"})])],2)],2))}}),Ne=new WeakMap,nt=new WeakMap,it={current:[]};let vt=!1;const tt=new Set,Ut=new Map;function _s(s){const e=Array.from(s).sort((t,r)=>t instanceof Ve&&t.options.deps.includes(r)?1:r instanceof Ve&&r.options.deps.includes(t)?-1:0);for(const t of e){if(it.current.includes(t))continue;it.current.push(t),t.recompute();const r=nt.get(t);if(r)for(const n of r){const a=Ne.get(n);a&&_s(a)}}}function Vr(s){s.listeners.forEach(e=>e({prevVal:s.prevState,currentVal:s.state}))}function Er(s){s.listeners.forEach(e=>e({prevVal:s.prevState,currentVal:s.state}))}function Ir(s){var e;if(tt.add(s),!vt)try{for(vt=!0;tt.size>0;){const t=Array.from(tt);tt.clear();for(const r of t){const n=(e=Ut.get(r))!=null?e:r.prevState;r.prevState=n,Vr(r)}for(const r of t){const n=Ne.get(r);n&&(it.current.push(r),_s(n))}for(const r of t){const n=Ne.get(r);if(n)for(const a of n)Er(a)}}}finally{vt=!1,it.current=[],Ut.clear()}}function $r(s){return typeof s=="function"}class kt{constructor(e,t){this.listeners=new Set,this.subscribe=r=>{var n,a;this.listeners.add(r);const i=(a=(n=this.options)==null?void 0:n.onSubscribe)==null?void 0:a.call(n,r,this);return()=>{this.listeners.delete(r),i==null||i()}},this.prevState=e,this.state=e,this.options=t}setState(e){var t,r,n;this.prevState=this.state,(t=this.options)!=null&&t.updateFn?this.state=this.options.updateFn(this.prevState)(e):$r(e)?this.state=e(this.prevState):this.state=e,(n=(r=this.options)==null?void 0:r.onUpdate)==null||n.call(r),Ir(this)}}class Ve{constructor(e){this.listeners=new Set,this._subscriptions=[],this.lastSeenDepValues=[],this.getDepVals=()=>{var n;const t=[],r=[];for(const a of this.options.deps)t.push(a.prevState),r.push(a.state);return this.lastSeenDepValues=r,{prevDepVals:t,currDepVals:r,prevVal:(n=this.prevState)!=null?n:void 0}},this.recompute=()=>{var t,r;this.prevState=this.state;const{prevDepVals:n,currDepVals:a,prevVal:i}=this.getDepVals();this.state=this.options.fn({prevDepVals:n,currDepVals:a,prevVal:i}),(r=(t=this.options).onUpdate)==null||r.call(t)},this.checkIfRecalculationNeededDeeply=()=>{for(const a of this.options.deps)a instanceof Ve&&a.checkIfRecalculationNeededDeeply();let t=!1;const r=this.lastSeenDepValues,{currDepVals:n}=this.getDepVals();for(let a=0;a<n.length;a++)if(n[a]!==r[a]){t=!0;break}t&&this.recompute()},this.mount=()=>(this.registerOnGraph(),this.checkIfRecalculationNeededDeeply(),()=>{this.unregisterFromGraph();for(const t of this._subscriptions)t()}),this.subscribe=t=>{var r,n;this.listeners.add(t);const a=(n=(r=this.options).onSubscribe)==null?void 0:n.call(r,t,this);return()=>{this.listeners.delete(t),a==null||a()}},this.options=e,this.state=e.fn({prevDepVals:void 0,prevVal:void 0,currDepVals:this.getDepVals().currDepVals})}registerOnGraph(e=this.options.deps){for(const t of e)if(t instanceof Ve)t.registerOnGraph(),this.registerOnGraph(t.options.deps);else if(t instanceof kt){let r=Ne.get(t);r||(r=new Set,Ne.set(t,r)),r.add(this);let n=nt.get(this);n||(n=new Set,nt.set(this,n)),n.add(t)}}unregisterFromGraph(e=this.options.deps){for(const t of e)if(t instanceof Ve)this.unregisterFromGraph(t.options.deps);else if(t instanceof kt){const r=Ne.get(t);r&&r.delete(this);const n=nt.get(this);n&&n.delete(t)}}}function jr(s,e=t=>t){const t=fe(e(s.state));return ke(()=>s,(r,n,a)=>{const i=r.subscribe(()=>{const o=e(r.state);Zr(Se(t.value),o)||(t.value=o)});a(()=>{i()})},{immediate:!0}),js(t)}function Zr(s,e){if(Object.is(s,e))return!0;if(typeof s!="object"||s===null||typeof e!="object"||e===null)return!1;if(s instanceof Map&&e instanceof Map){if(s.size!==e.size)return!1;for(const[r,n]of s)if(!e.has(r)||!Object.is(n,e.get(r)))return!1;return!0}if(s instanceof Set&&e instanceof Set){if(s.size!==e.size)return!1;for(const r of s)if(!e.has(r))return!1;return!0}const t=Object.keys(s);if(t.length!==Object.keys(e).length)return!1;for(let r=0;r<t.length;r++)if(!Object.prototype.hasOwnProperty.call(e,t[r])||!Object.is(s[t[r]],e[t[r]]))return!1;return!0}function Mr(){return{actionWrapperClass:"",collapsed:!1,collapsedRows:1,collapseTriggerResize:!1,commonConfig:{},handleReset:void 0,handleSubmit:void 0,handleValuesChange:void 0,layout:"horizontal",resetButtonOptions:{},schema:[],scrollToFirstError:!1,showCollapseButton:!1,showDefaultActions:!0,submitButtonOptions:{},submitOnChange:!1,submitOnEnter:!1,wrapperClass:"grid-cols-1"}}class Pr{constructor(e={}){J(this,"form",{});J(this,"isMounted",!1);J(this,"state",null);J(this,"stateHandler");J(this,"store");J(this,"componentRefMap",new Map);J(this,"latestSubmissionValues",null);J(this,"prevState",null);J(this,"handleArrayToStringFields",e=>{var n;const t=(n=this.state)==null?void 0:n.arrayToStringFields;if(!t||!Array.isArray(t))return;const r=(a,i=",")=>{this.processFields(a,i,e,(o,c)=>Array.isArray(o)?o.join(c):o)};if(t.every(a=>typeof a=="string")){const a=t[t.length-1]||"",i=a.length===1?t.slice(0,-1):t,o=a.length===1?a:",";r(i,o);return}t.forEach(a=>{if(Array.isArray(a)){const[i,o=","]=a;if(!Array.isArray(i)){console.warn(`Invalid field configuration: fields should be an array of strings, got ${typeof i}`);return}r(i,o)}})});J(this,"handleRangeTimeValue",e=>{var n;const t=l({},e),r=(n=this.state)==null?void 0:n.fieldMappingTime;return this.handleStringToArrayFields(t),!r||!Array.isArray(r)||r.forEach(([a,[i,o],c="YYYY-MM-DD"])=>{if(i&&o&&t[a]===null&&(Reflect.deleteProperty(t,i),Reflect.deleteProperty(t,o)),!t[a]){Reflect.deleteProperty(t,a);return}const[u,m]=t[a];if(c===null)t[i]=u,t[o]=m;else if(G(c))t[i]=c(u,i),t[o]=c(m,o);else{const[v,_]=Array.isArray(c)?c:[c,c];t[i]=u?Lt(u,v):void 0,t[o]=m?Lt(m,_):void 0}Reflect.deleteProperty(t,a)}),t});J(this,"handleStringToArrayFields",e=>{var n;const t=(n=this.state)==null?void 0:n.arrayToStringFields;if(!t||!Array.isArray(t))return;const r=(a,i=",")=>{this.processFields(a,i,e,(o,c)=>{if(typeof o!="string")return o;if(o==="")return[];const u=c.replaceAll(/[.*+?^${}()|[\]\\]/g,String.raw`\$&`);return o.split(new RegExp(u))})};if(t.every(a=>typeof a=="string")){const a=t[t.length-1]||"",i=a.length===1?t.slice(0,-1):t,o=a.length===1?a:",";r(i,o);return}t.forEach(a=>{if(Array.isArray(a)){const[i,o=","]=a;if(Array.isArray(i))r(i,o);else if(typeof e[i]=="string"){const c=e[i];if(c==="")e[i]=[];else{const u=o.replaceAll(/[.*+?^${}()|[\]\\]/g,String.raw`\$&`);e[i]=c.split(new RegExp(u))}}}})});J(this,"processFields",(e,t,r,n)=>{e.forEach(a=>{const i=r[a];i!=null&&(r[a]=n(i,t))})});const t=pt(e,[]),r=Mr();this.store=new kt(l(l({},r),t),{onUpdate:()=>{this.prevState=this.state,this.state=this.store.state,this.updateState()}}),this.state=this.store.state,this.stateHandler=new Zs,Ms(this)}getFieldComponentRef(e){var r,n;let t=this.componentRefMap.has(e)?this.componentRefMap.get(e):void 0;return t&&t.$.type.name==="AsyncComponentWrapper"&&t.$.subTree.ref&&(Array.isArray(t.$.subTree.ref)?t.$.subTree.ref.length>0&&Dt((r=t.$.subTree.ref[0])==null?void 0:r.r)&&(t=(n=t.$.subTree.ref[0])==null?void 0:n.r.value):Dt(t.$.subTree.ref.r)&&(t=t.$.subTree.ref.r.value)),t}getFocusedField(){for(const e of this.componentRefMap.keys()){const t=this.getFieldComponentRef(e);if(t){let r=null;if(t instanceof HTMLElement?r=t:t.$el instanceof HTMLElement&&(r=t.$el),!r)continue;if(r===document.activeElement||r.contains(document.activeElement))return e}}}getLatestSubmissionValues(){return this.latestSubmissionValues||{}}getState(){return this.state}getValues(){return T(this,null,function*(){const e=yield this.getForm();return e.values?this.handleRangeTimeValue(e.values):{}})}isFieldValid(e){return T(this,null,function*(){return(yield this.getForm()).isFieldValid(e)})}merge(e){const t=[this,e],r=new Proxy(e,{get(n,a){return a==="merge"?i=>(t.push(i),r):a==="submitAllForm"?(i=!0)=>T(null,null,function*(){try{const o=yield Promise.all(t.map(c=>T(null,null,function*(){return(yield c.validate()).valid?Se((yield c.getValues())||{}):void 0})));return i?Object.assign({},...o):o}catch(o){console.error("Validation error:",o)}}):n[a]}});return r}mount(e,t){this.isMounted||(Object.assign(this.form,e),this.stateHandler.setConditionTrue(),this.setLatestSubmissionValues(l({},Se(this.handleRangeTimeValue(this.form.values)))),this.componentRefMap=t,this.isMounted=!0)}removeSchemaByFields(e){return T(this,null,function*(){var a,i;const t=new Set(e),n=((i=(a=this.state)==null?void 0:a.schema)!=null?i:[]).filter(o=>!t.has(o.fieldName));this.setState({schema:n})})}resetForm(e,t){return T(this,null,function*(){return(yield this.getForm()).resetForm(e,t)})}resetValidate(){return T(this,null,function*(){const e=yield this.getForm();Object.keys(e.errors.value).forEach(r=>{e.setFieldError(r,void 0)})})}scrollToFirstError(e){const t=typeof e=="string"?e:Object.keys(e)[0];if(!t)return;let r=document.querySelector(`[name="${t}"]`);if(!r){const n=this.getFieldComponentRef(t);n&&n.$el instanceof HTMLElement&&(r=n.$el)}r&&r.scrollIntoView({behavior:"smooth",block:"center",inline:"nearest"})}setFieldValue(e,t,r){return T(this,null,function*(){(yield this.getForm()).setFieldValue(e,t,r)})}setLatestSubmissionValues(e){this.latestSubmissionValues=l({},Se(e))}setState(e){G(e)?this.store.setState(t=>qe(e(t),t)):this.store.setState(t=>qe(e,t))}setValues(e,t=!0,r=!1){return T(this,null,function*(){const n=yield this.getForm();if(!t){n.setValues(e,r);return}const a=Ps((o,c,u)=>(c in o&&(o[c]=!Array.isArray(o[c])&&Rt(o[c])&&!Ds(o[c])&&!Ls(o[c])?a(o[c],u):u),!0)),i=a(e,n.values);this.handleStringToArrayFields(i),n.setValues(i,r)})}submitForm(e){return T(this,null,function*(){var n,a;e==null||e.preventDefault(),e==null||e.stopPropagation(),yield(yield this.getForm()).submitForm();const r=Se(yield this.getValues());return this.handleArrayToStringFields(r),yield(a=(n=this.state)==null?void 0:n.handleSubmit)==null?void 0:a.call(n,r),r})}unmount(){var e,t;(t=(e=this.form)==null?void 0:e.resetForm)==null||t.call(e),this.latestSubmissionValues=null,this.isMounted=!1,this.stateHandler.reset()}updateSchema(e){var i,o;const t=[...e];if(!t.every(c=>Reflect.has(c,"fieldName")&&c.fieldName)){console.error("All items in the schema array must have a valid `fieldName` property to be updated");return}const n=[...(o=(i=this.state)==null?void 0:i.schema)!=null?o:[]],a={};t.forEach(c=>{c.fieldName&&(a[c.fieldName]=c)}),n.forEach((c,u)=>{const m=a[c.fieldName];m&&(n[u]=qe(m,c))}),this.setState({schema:n})}validate(e){return T(this,null,function*(){var n,a;const r=yield(yield this.getForm()).validate(e);return Object.keys((n=r==null?void 0:r.errors)!=null?n:{}).length>0&&(console.error("validate error",r==null?void 0:r.errors),(a=this.state)!=null&&a.scrollToFirstError&&this.scrollToFirstError(r.errors)),r})}validateAndSubmitForm(){return T(this,null,function*(){var n;const e=yield this.getForm(),{valid:t,errors:r}=yield e.validate();if(!t){(n=this.state)!=null&&n.scrollToFirstError&&this.scrollToFirstError(r);return}return yield this.submitForm()})}validateField(e,t){return T(this,null,function*(){var a,i;const n=yield(yield this.getForm()).validateField(e,t);return Object.keys((a=n==null?void 0:n.errors)!=null?a:{}).length>0&&(console.error("validate error",n==null?void 0:n.errors),(i=this.state)!=null&&i.scrollToFirstError&&this.scrollToFirstError(e)),n})}getForm(){return T(this,null,function*(){var e;if(this.isMounted||(yield this.stateHandler.waitForCondition()),!((e=this.form)!=null&&e.meta))throw new Error("<VbenForm /> is not mounted");return this.form})}updateState(){var r,n,a,i,o,c;const e=(n=(r=this.state)==null?void 0:r.schema)!=null?n:[],t=(i=(a=this.prevState)==null?void 0:a.schema)!=null?i:[];if(e.length<t.length){const u=new Set(e.map(v=>v.fieldName)),m=t.filter(v=>!u.has(v.fieldName));for(const v of m)(c=(o=this.form)==null?void 0:o.setFieldValue)==null||c.call(o,v.fieldName,void 0)}}}var F;(function(s){s.assertEqual=n=>{};function e(n){}s.assertIs=e;function t(n){throw new Error}s.assertNever=t,s.arrayToEnum=n=>{const a={};for(const i of n)a[i]=i;return a},s.getValidEnumValues=n=>{const a=s.objectKeys(n).filter(o=>typeof n[n[o]]!="number"),i={};for(const o of a)i[o]=n[o];return s.objectValues(i)},s.objectValues=n=>s.objectKeys(n).map(function(a){return n[a]}),s.objectKeys=typeof Object.keys=="function"?n=>Object.keys(n):n=>{const a=[];for(const i in n)Object.prototype.hasOwnProperty.call(n,i)&&a.push(i);return a},s.find=(n,a)=>{for(const i of n)if(a(i))return i},s.isInteger=typeof Number.isInteger=="function"?n=>Number.isInteger(n):n=>typeof n=="number"&&Number.isFinite(n)&&Math.floor(n)===n;function r(n,a=" | "){return n.map(i=>typeof i=="string"?`'${i}'`:i).join(a)}s.joinValues=r,s.jsonStringifyReplacer=(n,a)=>typeof a=="bigint"?a.toString():a})(F||(F={}));var qt;(function(s){s.mergeShapes=(e,t)=>l(l({},e),t)})(qt||(qt={}));const y=F.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),_e=s=>{switch(typeof s){case"undefined":return y.undefined;case"string":return y.string;case"number":return Number.isNaN(s)?y.nan:y.number;case"boolean":return y.boolean;case"function":return y.function;case"bigint":return y.bigint;case"symbol":return y.symbol;case"object":return Array.isArray(s)?y.array:s===null?y.null:s.then&&typeof s.then=="function"&&s.catch&&typeof s.catch=="function"?y.promise:typeof Map!="undefined"&&s instanceof Map?y.map:typeof Set!="undefined"&&s instanceof Set?y.set:typeof Date!="undefined"&&s instanceof Date?y.date:y.object;default:return y.unknown}},d=F.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class pe extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=r=>{this.issues=[...this.issues,r]},this.addIssues=(r=[])=>{this.issues=[...this.issues,...r]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(a){return a.message},r={_errors:[]},n=a=>{for(const i of a.issues)if(i.code==="invalid_union")i.unionErrors.map(n);else if(i.code==="invalid_return_type")n(i.returnTypeError);else if(i.code==="invalid_arguments")n(i.argumentsError);else if(i.path.length===0)r._errors.push(t(i));else{let o=r,c=0;for(;c<i.path.length;){const u=i.path[c];c===i.path.length-1?(o[u]=o[u]||{_errors:[]},o[u]._errors.push(t(i))):o[u]=o[u]||{_errors:[]},o=o[u],c++}}};return n(this),r}static assert(e){if(!(e instanceof pe))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,F.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=t=>t.message){const t={},r=[];for(const n of this.issues)n.path.length>0?(t[n.path[0]]=t[n.path[0]]||[],t[n.path[0]].push(e(n))):r.push(e(n));return{formErrors:r,fieldErrors:t}}get formErrors(){return this.flatten()}}pe.create=s=>new pe(s);const xt=(s,e)=>{let t;switch(s.code){case d.invalid_type:s.received===y.undefined?t="Required":t=`Expected ${s.expected}, received ${s.received}`;break;case d.invalid_literal:t=`Invalid literal value, expected ${JSON.stringify(s.expected,F.jsonStringifyReplacer)}`;break;case d.unrecognized_keys:t=`Unrecognized key(s) in object: ${F.joinValues(s.keys,", ")}`;break;case d.invalid_union:t="Invalid input";break;case d.invalid_union_discriminator:t=`Invalid discriminator value. Expected ${F.joinValues(s.options)}`;break;case d.invalid_enum_value:t=`Invalid enum value. Expected ${F.joinValues(s.options)}, received '${s.received}'`;break;case d.invalid_arguments:t="Invalid function arguments";break;case d.invalid_return_type:t="Invalid function return type";break;case d.invalid_date:t="Invalid date";break;case d.invalid_string:typeof s.validation=="object"?"includes"in s.validation?(t=`Invalid input: must include "${s.validation.includes}"`,typeof s.validation.position=="number"&&(t=`${t} at one or more positions greater than or equal to ${s.validation.position}`)):"startsWith"in s.validation?t=`Invalid input: must start with "${s.validation.startsWith}"`:"endsWith"in s.validation?t=`Invalid input: must end with "${s.validation.endsWith}"`:F.assertNever(s.validation):s.validation!=="regex"?t=`Invalid ${s.validation}`:t="Invalid";break;case d.too_small:s.type==="array"?t=`Array must contain ${s.exact?"exactly":s.inclusive?"at least":"more than"} ${s.minimum} element(s)`:s.type==="string"?t=`String must contain ${s.exact?"exactly":s.inclusive?"at least":"over"} ${s.minimum} character(s)`:s.type==="number"?t=`Number must be ${s.exact?"exactly equal to ":s.inclusive?"greater than or equal to ":"greater than "}${s.minimum}`:s.type==="date"?t=`Date must be ${s.exact?"exactly equal to ":s.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(s.minimum))}`:t="Invalid input";break;case d.too_big:s.type==="array"?t=`Array must contain ${s.exact?"exactly":s.inclusive?"at most":"less than"} ${s.maximum} element(s)`:s.type==="string"?t=`String must contain ${s.exact?"exactly":s.inclusive?"at most":"under"} ${s.maximum} character(s)`:s.type==="number"?t=`Number must be ${s.exact?"exactly":s.inclusive?"less than or equal to":"less than"} ${s.maximum}`:s.type==="bigint"?t=`BigInt must be ${s.exact?"exactly":s.inclusive?"less than or equal to":"less than"} ${s.maximum}`:s.type==="date"?t=`Date must be ${s.exact?"exactly":s.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(s.maximum))}`:t="Invalid input";break;case d.custom:t="Invalid input";break;case d.invalid_intersection_types:t="Intersection results could not be merged";break;case d.not_multiple_of:t=`Number must be a multiple of ${s.multipleOf}`;break;case d.not_finite:t="Number must be finite";break;default:t=e.defaultError,F.assertNever(s)}return{message:t}};let Dr=xt;function Lr(){return Dr}const Br=s=>{const{data:e,path:t,errorMaps:r,issueData:n}=s,a=[...t,...n.path||[]],i=w(l({},n),{path:a});if(n.message!==void 0)return w(l({},n),{path:a,message:n.message});let o="";const c=r.filter(u=>!!u).slice().reverse();for(const u of c)o=u(i,{data:e,defaultError:o}).message;return w(l({},n),{path:a,message:o})};function p(s,e){const t=Lr(),r=Br({issueData:e,data:s.data,path:s.path,errorMaps:[s.common.contextualErrorMap,s.schemaErrorMap,t,t===xt?void 0:xt].filter(n=>!!n)});s.common.issues.push(r)}class W{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,t){const r=[];for(const n of t){if(n.status==="aborted")return x;n.status==="dirty"&&e.dirty(),r.push(n.value)}return{status:e.value,value:r}}static mergeObjectAsync(e,t){return T(this,null,function*(){const r=[];for(const n of t){const a=yield n.key,i=yield n.value;r.push({key:a,value:i})}return W.mergeObjectSync(e,r)})}static mergeObjectSync(e,t){const r={};for(const n of t){const{key:a,value:i}=n;if(a.status==="aborted"||i.status==="aborted")return x;a.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),a.value!=="__proto__"&&(typeof i.value!="undefined"||n.alwaysSet)&&(r[a.value]=i.value)}return{status:e.value,value:r}}}const x=Object.freeze({status:"aborted"}),ze=s=>({status:"dirty",value:s}),ee=s=>({status:"valid",value:s}),Wt=s=>s.status==="aborted",Ht=s=>s.status==="dirty",Ee=s=>s.status==="valid",ot=s=>typeof Promise!="undefined"&&s instanceof Promise;var g;(function(s){s.errToObj=e=>typeof e=="string"?{message:e}:e||{},s.toString=e=>typeof e=="string"?e:e==null?void 0:e.message})(g||(g={}));class ue{constructor(e,t,r,n){this._cachedPath=[],this.parent=e,this.data=t,this._path=r,this._key=n}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const Gt=(s,e)=>{if(Ee(e))return{success:!0,data:e.value};if(!s.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new pe(s.common.issues);return this._error=t,this._error}}};function S(s){if(!s)return{};const{errorMap:e,invalid_type_error:t,required_error:r,description:n}=s;if(e&&(t||r))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:n}:{errorMap:(i,o)=>{var u,m;const{message:c}=s;return i.code==="invalid_enum_value"?{message:c!=null?c:o.defaultError}:typeof o.data=="undefined"?{message:(u=c!=null?c:r)!=null?u:o.defaultError}:i.code!=="invalid_type"?{message:o.defaultError}:{message:(m=c!=null?c:t)!=null?m:o.defaultError}},description:n}}class O{get description(){return this._def.description}_getType(e){return _e(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:_e(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new W,ctx:{common:e.parent.common,data:e.data,parsedType:_e(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(ot(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const r=this.safeParse(e,t);if(r.success)return r.data;throw r.error}safeParse(e,t){var a;const r={common:{issues:[],async:(a=t==null?void 0:t.async)!=null?a:!1,contextualErrorMap:t==null?void 0:t.errorMap},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:_e(e)},n=this._parseSync({data:e,path:r.path,parent:r});return Gt(r,n)}"~validate"(e){var r,n;const t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:_e(e)};if(!this["~standard"].async)try{const a=this._parseSync({data:e,path:[],parent:t});return Ee(a)?{value:a.value}:{issues:t.common.issues}}catch(a){(n=(r=a==null?void 0:a.message)==null?void 0:r.toLowerCase())!=null&&n.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(a=>Ee(a)?{value:a.value}:{issues:t.common.issues})}parseAsync(e,t){return T(this,null,function*(){const r=yield this.safeParseAsync(e,t);if(r.success)return r.data;throw r.error})}safeParseAsync(e,t){return T(this,null,function*(){const r={common:{issues:[],contextualErrorMap:t==null?void 0:t.errorMap,async:!0},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:_e(e)},n=this._parse({data:e,path:r.path,parent:r}),a=yield ot(n)?n:Promise.resolve(n);return Gt(r,a)})}refine(e,t){const r=n=>typeof t=="string"||typeof t=="undefined"?{message:t}:typeof t=="function"?t(n):t;return this._refinement((n,a)=>{const i=e(n),o=()=>a.addIssue(l({code:d.custom},r(n)));return typeof Promise!="undefined"&&i instanceof Promise?i.then(c=>c?!0:(o(),!1)):i?!0:(o(),!1)})}refinement(e,t){return this._refinement((r,n)=>e(r)?!0:(n.addIssue(typeof t=="function"?t(r,n):t),!1))}_refinement(e){return new oe({schema:this,typeName:k.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:t=>this["~validate"](t)}}optional(){return me.create(this,this._def)}nullable(){return je.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return ie.create(this)}promise(){return ut.create(this,this._def)}or(e){return Ie.create([this,e],this._def)}and(e){return Te.create(this,e,this._def)}transform(e){return new oe(w(l({},S(this._def)),{schema:this,typeName:k.ZodEffects,effect:{type:"transform",transform:e}}))}default(e){const t=typeof e=="function"?e:()=>e;return new Ze(w(l({},S(this._def)),{innerType:this,defaultValue:t,typeName:k.ZodDefault}))}brand(){return new fn(l({typeName:k.ZodBranded,type:this},S(this._def)))}catch(e){const t=typeof e=="function"?e:()=>e;return new Ct(w(l({},S(this._def)),{innerType:this,catchValue:t,typeName:k.ZodCatch}))}describe(e){const t=this.constructor;return new t(w(l({},this._def),{description:e}))}pipe(e){return Nt.create(this,e)}readonly(){return St.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const zr=/^c[^\s-]{8,}$/i,Ur=/^[0-9a-z]+$/,qr=/^[0-9A-HJKMNP-TV-Z]{26}$/i,Wr=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,Hr=/^[a-z0-9_-]{21}$/i,Gr=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,Yr=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Jr=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,Qr="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let gt;const Xr=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Kr=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,en=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,tn=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,sn=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,rn=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,bs="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",nn=new RegExp(`^${bs}$`);function ks(s){let e="[0-5]\\d";s.precision?e=`${e}\\.\\d{${s.precision}}`:s.precision==null&&(e=`${e}(\\.\\d+)?`);const t=s.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${e})${t}`}function an(s){return new RegExp(`^${ks(s)}$`)}function on(s){let e=`${bs}T${ks(s)}`;const t=[];return t.push(s.local?"Z?":"Z"),s.offset&&t.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${t.join("|")})`,new RegExp(`^${e}$`)}function ln(s,e){return!!((e==="v4"||!e)&&Xr.test(s)||(e==="v6"||!e)&&en.test(s))}function cn(s,e){if(!Gr.test(s))return!1;try{const[t]=s.split("."),r=t.replace(/-/g,"+").replace(/_/g,"/").padEnd(t.length+(4-t.length%4)%4,"="),n=JSON.parse(atob(r));return!(typeof n!="object"||n===null||"typ"in n&&(n==null?void 0:n.typ)!=="JWT"||!n.alg||e&&n.alg!==e)}catch(t){return!1}}function un(s,e){return!!((e==="v4"||!e)&&Kr.test(s)||(e==="v6"||!e)&&tn.test(s))}class ae extends O{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==y.string){const a=this._getOrReturnCtx(e);return p(a,{code:d.invalid_type,expected:y.string,received:a.parsedType}),x}const r=new W;let n;for(const a of this._def.checks)if(a.kind==="min")e.data.length<a.value&&(n=this._getOrReturnCtx(e,n),p(n,{code:d.too_small,minimum:a.value,type:"string",inclusive:!0,exact:!1,message:a.message}),r.dirty());else if(a.kind==="max")e.data.length>a.value&&(n=this._getOrReturnCtx(e,n),p(n,{code:d.too_big,maximum:a.value,type:"string",inclusive:!0,exact:!1,message:a.message}),r.dirty());else if(a.kind==="length"){const i=e.data.length>a.value,o=e.data.length<a.value;(i||o)&&(n=this._getOrReturnCtx(e,n),i?p(n,{code:d.too_big,maximum:a.value,type:"string",inclusive:!0,exact:!0,message:a.message}):o&&p(n,{code:d.too_small,minimum:a.value,type:"string",inclusive:!0,exact:!0,message:a.message}),r.dirty())}else if(a.kind==="email")Jr.test(e.data)||(n=this._getOrReturnCtx(e,n),p(n,{validation:"email",code:d.invalid_string,message:a.message}),r.dirty());else if(a.kind==="emoji")gt||(gt=new RegExp(Qr,"u")),gt.test(e.data)||(n=this._getOrReturnCtx(e,n),p(n,{validation:"emoji",code:d.invalid_string,message:a.message}),r.dirty());else if(a.kind==="uuid")Wr.test(e.data)||(n=this._getOrReturnCtx(e,n),p(n,{validation:"uuid",code:d.invalid_string,message:a.message}),r.dirty());else if(a.kind==="nanoid")Hr.test(e.data)||(n=this._getOrReturnCtx(e,n),p(n,{validation:"nanoid",code:d.invalid_string,message:a.message}),r.dirty());else if(a.kind==="cuid")zr.test(e.data)||(n=this._getOrReturnCtx(e,n),p(n,{validation:"cuid",code:d.invalid_string,message:a.message}),r.dirty());else if(a.kind==="cuid2")Ur.test(e.data)||(n=this._getOrReturnCtx(e,n),p(n,{validation:"cuid2",code:d.invalid_string,message:a.message}),r.dirty());else if(a.kind==="ulid")qr.test(e.data)||(n=this._getOrReturnCtx(e,n),p(n,{validation:"ulid",code:d.invalid_string,message:a.message}),r.dirty());else if(a.kind==="url")try{new URL(e.data)}catch(i){n=this._getOrReturnCtx(e,n),p(n,{validation:"url",code:d.invalid_string,message:a.message}),r.dirty()}else a.kind==="regex"?(a.regex.lastIndex=0,a.regex.test(e.data)||(n=this._getOrReturnCtx(e,n),p(n,{validation:"regex",code:d.invalid_string,message:a.message}),r.dirty())):a.kind==="trim"?e.data=e.data.trim():a.kind==="includes"?e.data.includes(a.value,a.position)||(n=this._getOrReturnCtx(e,n),p(n,{code:d.invalid_string,validation:{includes:a.value,position:a.position},message:a.message}),r.dirty()):a.kind==="toLowerCase"?e.data=e.data.toLowerCase():a.kind==="toUpperCase"?e.data=e.data.toUpperCase():a.kind==="startsWith"?e.data.startsWith(a.value)||(n=this._getOrReturnCtx(e,n),p(n,{code:d.invalid_string,validation:{startsWith:a.value},message:a.message}),r.dirty()):a.kind==="endsWith"?e.data.endsWith(a.value)||(n=this._getOrReturnCtx(e,n),p(n,{code:d.invalid_string,validation:{endsWith:a.value},message:a.message}),r.dirty()):a.kind==="datetime"?on(a).test(e.data)||(n=this._getOrReturnCtx(e,n),p(n,{code:d.invalid_string,validation:"datetime",message:a.message}),r.dirty()):a.kind==="date"?nn.test(e.data)||(n=this._getOrReturnCtx(e,n),p(n,{code:d.invalid_string,validation:"date",message:a.message}),r.dirty()):a.kind==="time"?an(a).test(e.data)||(n=this._getOrReturnCtx(e,n),p(n,{code:d.invalid_string,validation:"time",message:a.message}),r.dirty()):a.kind==="duration"?Yr.test(e.data)||(n=this._getOrReturnCtx(e,n),p(n,{validation:"duration",code:d.invalid_string,message:a.message}),r.dirty()):a.kind==="ip"?ln(e.data,a.version)||(n=this._getOrReturnCtx(e,n),p(n,{validation:"ip",code:d.invalid_string,message:a.message}),r.dirty()):a.kind==="jwt"?cn(e.data,a.alg)||(n=this._getOrReturnCtx(e,n),p(n,{validation:"jwt",code:d.invalid_string,message:a.message}),r.dirty()):a.kind==="cidr"?un(e.data,a.version)||(n=this._getOrReturnCtx(e,n),p(n,{validation:"cidr",code:d.invalid_string,message:a.message}),r.dirty()):a.kind==="base64"?sn.test(e.data)||(n=this._getOrReturnCtx(e,n),p(n,{validation:"base64",code:d.invalid_string,message:a.message}),r.dirty()):a.kind==="base64url"?rn.test(e.data)||(n=this._getOrReturnCtx(e,n),p(n,{validation:"base64url",code:d.invalid_string,message:a.message}),r.dirty()):F.assertNever(a);return{status:r.value,value:e.data}}_regex(e,t,r){return this.refinement(n=>e.test(n),l({validation:t,code:d.invalid_string},g.errToObj(r)))}_addCheck(e){return new ae(w(l({},this._def),{checks:[...this._def.checks,e]}))}email(e){return this._addCheck(l({kind:"email"},g.errToObj(e)))}url(e){return this._addCheck(l({kind:"url"},g.errToObj(e)))}emoji(e){return this._addCheck(l({kind:"emoji"},g.errToObj(e)))}uuid(e){return this._addCheck(l({kind:"uuid"},g.errToObj(e)))}nanoid(e){return this._addCheck(l({kind:"nanoid"},g.errToObj(e)))}cuid(e){return this._addCheck(l({kind:"cuid"},g.errToObj(e)))}cuid2(e){return this._addCheck(l({kind:"cuid2"},g.errToObj(e)))}ulid(e){return this._addCheck(l({kind:"ulid"},g.errToObj(e)))}base64(e){return this._addCheck(l({kind:"base64"},g.errToObj(e)))}base64url(e){return this._addCheck(l({kind:"base64url"},g.errToObj(e)))}jwt(e){return this._addCheck(l({kind:"jwt"},g.errToObj(e)))}ip(e){return this._addCheck(l({kind:"ip"},g.errToObj(e)))}cidr(e){return this._addCheck(l({kind:"cidr"},g.errToObj(e)))}datetime(e){var t,r;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck(l({kind:"datetime",precision:typeof(e==null?void 0:e.precision)=="undefined"?null:e==null?void 0:e.precision,offset:(t=e==null?void 0:e.offset)!=null?t:!1,local:(r=e==null?void 0:e.local)!=null?r:!1},g.errToObj(e==null?void 0:e.message)))}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck(l({kind:"time",precision:typeof(e==null?void 0:e.precision)=="undefined"?null:e==null?void 0:e.precision},g.errToObj(e==null?void 0:e.message)))}duration(e){return this._addCheck(l({kind:"duration"},g.errToObj(e)))}regex(e,t){return this._addCheck(l({kind:"regex",regex:e},g.errToObj(t)))}includes(e,t){return this._addCheck(l({kind:"includes",value:e,position:t==null?void 0:t.position},g.errToObj(t==null?void 0:t.message)))}startsWith(e,t){return this._addCheck(l({kind:"startsWith",value:e},g.errToObj(t)))}endsWith(e,t){return this._addCheck(l({kind:"endsWith",value:e},g.errToObj(t)))}min(e,t){return this._addCheck(l({kind:"min",value:e},g.errToObj(t)))}max(e,t){return this._addCheck(l({kind:"max",value:e},g.errToObj(t)))}length(e,t){return this._addCheck(l({kind:"length",value:e},g.errToObj(t)))}nonempty(e){return this.min(1,g.errToObj(e))}trim(){return new ae(w(l({},this._def),{checks:[...this._def.checks,{kind:"trim"}]}))}toLowerCase(){return new ae(w(l({},this._def),{checks:[...this._def.checks,{kind:"toLowerCase"}]}))}toUpperCase(){return new ae(w(l({},this._def),{checks:[...this._def.checks,{kind:"toUpperCase"}]}))}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}ae.create=s=>{var e;return new ae(l({checks:[],typeName:k.ZodString,coerce:(e=s==null?void 0:s.coerce)!=null?e:!1},S(s)))};function dn(s,e){const t=(s.toString().split(".")[1]||"").length,r=(e.toString().split(".")[1]||"").length,n=t>r?t:r,a=Number.parseInt(s.toFixed(n).replace(".","")),i=Number.parseInt(e.toFixed(n).replace(".",""));return a%i/Mt(10,n)}class Oe extends O{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==y.number){const a=this._getOrReturnCtx(e);return p(a,{code:d.invalid_type,expected:y.number,received:a.parsedType}),x}let r;const n=new W;for(const a of this._def.checks)a.kind==="int"?F.isInteger(e.data)||(r=this._getOrReturnCtx(e,r),p(r,{code:d.invalid_type,expected:"integer",received:"float",message:a.message}),n.dirty()):a.kind==="min"?(a.inclusive?e.data<a.value:e.data<=a.value)&&(r=this._getOrReturnCtx(e,r),p(r,{code:d.too_small,minimum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),n.dirty()):a.kind==="max"?(a.inclusive?e.data>a.value:e.data>=a.value)&&(r=this._getOrReturnCtx(e,r),p(r,{code:d.too_big,maximum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),n.dirty()):a.kind==="multipleOf"?dn(e.data,a.value)!==0&&(r=this._getOrReturnCtx(e,r),p(r,{code:d.not_multiple_of,multipleOf:a.value,message:a.message}),n.dirty()):a.kind==="finite"?Number.isFinite(e.data)||(r=this._getOrReturnCtx(e,r),p(r,{code:d.not_finite,message:a.message}),n.dirty()):F.assertNever(a);return{status:n.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,g.toString(t))}gt(e,t){return this.setLimit("min",e,!1,g.toString(t))}lte(e,t){return this.setLimit("max",e,!0,g.toString(t))}lt(e,t){return this.setLimit("max",e,!1,g.toString(t))}setLimit(e,t,r,n){return new Oe(w(l({},this._def),{checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:g.toString(n)}]}))}_addCheck(e){return new Oe(w(l({},this._def),{checks:[...this._def.checks,e]}))}int(e){return this._addCheck({kind:"int",message:g.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:g.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:g.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:g.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:g.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:g.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:g.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:g.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:g.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&F.isInteger(e.value))}get isFinite(){let e=null,t=null;for(const r of this._def.checks){if(r.kind==="finite"||r.kind==="int"||r.kind==="multipleOf")return!0;r.kind==="min"?(t===null||r.value>t)&&(t=r.value):r.kind==="max"&&(e===null||r.value<e)&&(e=r.value)}return Number.isFinite(t)&&Number.isFinite(e)}}Oe.create=s=>new Oe(l({checks:[],typeName:k.ZodNumber,coerce:(s==null?void 0:s.coerce)||!1},S(s)));class Ge extends O{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch(a){return this._getInvalidInput(e)}if(this._getType(e)!==y.bigint)return this._getInvalidInput(e);let r;const n=new W;for(const a of this._def.checks)a.kind==="min"?(a.inclusive?e.data<a.value:e.data<=a.value)&&(r=this._getOrReturnCtx(e,r),p(r,{code:d.too_small,type:"bigint",minimum:a.value,inclusive:a.inclusive,message:a.message}),n.dirty()):a.kind==="max"?(a.inclusive?e.data>a.value:e.data>=a.value)&&(r=this._getOrReturnCtx(e,r),p(r,{code:d.too_big,type:"bigint",maximum:a.value,inclusive:a.inclusive,message:a.message}),n.dirty()):a.kind==="multipleOf"?e.data%a.value!==BigInt(0)&&(r=this._getOrReturnCtx(e,r),p(r,{code:d.not_multiple_of,multipleOf:a.value,message:a.message}),n.dirty()):F.assertNever(a);return{status:n.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return p(t,{code:d.invalid_type,expected:y.bigint,received:t.parsedType}),x}gte(e,t){return this.setLimit("min",e,!0,g.toString(t))}gt(e,t){return this.setLimit("min",e,!1,g.toString(t))}lte(e,t){return this.setLimit("max",e,!0,g.toString(t))}lt(e,t){return this.setLimit("max",e,!1,g.toString(t))}setLimit(e,t,r,n){return new Ge(w(l({},this._def),{checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:g.toString(n)}]}))}_addCheck(e){return new Ge(w(l({},this._def),{checks:[...this._def.checks,e]}))}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:g.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:g.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:g.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:g.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:g.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}Ge.create=s=>{var e;return new Ge(l({checks:[],typeName:k.ZodBigInt,coerce:(e=s==null?void 0:s.coerce)!=null?e:!1},S(s)))};class lt extends O{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==y.boolean){const r=this._getOrReturnCtx(e);return p(r,{code:d.invalid_type,expected:y.boolean,received:r.parsedType}),x}return ee(e.data)}}lt.create=s=>new lt(l({typeName:k.ZodBoolean,coerce:(s==null?void 0:s.coerce)||!1},S(s)));class ct extends O{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==y.date){const a=this._getOrReturnCtx(e);return p(a,{code:d.invalid_type,expected:y.date,received:a.parsedType}),x}if(Number.isNaN(e.data.getTime())){const a=this._getOrReturnCtx(e);return p(a,{code:d.invalid_date}),x}const r=new W;let n;for(const a of this._def.checks)a.kind==="min"?e.data.getTime()<a.value&&(n=this._getOrReturnCtx(e,n),p(n,{code:d.too_small,message:a.message,inclusive:!0,exact:!1,minimum:a.value,type:"date"}),r.dirty()):a.kind==="max"?e.data.getTime()>a.value&&(n=this._getOrReturnCtx(e,n),p(n,{code:d.too_big,message:a.message,inclusive:!0,exact:!1,maximum:a.value,type:"date"}),r.dirty()):F.assertNever(a);return{status:r.value,value:new Date(e.data.getTime())}}_addCheck(e){return new ct(w(l({},this._def),{checks:[...this._def.checks,e]}))}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:g.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:g.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e!=null?new Date(e):null}}ct.create=s=>new ct(l({checks:[],coerce:(s==null?void 0:s.coerce)||!1,typeName:k.ZodDate},S(s)));class Yt extends O{_parse(e){if(this._getType(e)!==y.symbol){const r=this._getOrReturnCtx(e);return p(r,{code:d.invalid_type,expected:y.symbol,received:r.parsedType}),x}return ee(e.data)}}Yt.create=s=>new Yt(l({typeName:k.ZodSymbol},S(s)));class Jt extends O{_parse(e){if(this._getType(e)!==y.undefined){const r=this._getOrReturnCtx(e);return p(r,{code:d.invalid_type,expected:y.undefined,received:r.parsedType}),x}return ee(e.data)}}Jt.create=s=>new Jt(l({typeName:k.ZodUndefined},S(s)));class Qt extends O{_parse(e){if(this._getType(e)!==y.null){const r=this._getOrReturnCtx(e);return p(r,{code:d.invalid_type,expected:y.null,received:r.parsedType}),x}return ee(e.data)}}Qt.create=s=>new Qt(l({typeName:k.ZodNull},S(s)));class Xt extends O{constructor(){super(...arguments),this._any=!0}_parse(e){return ee(e.data)}}Xt.create=s=>new Xt(l({typeName:k.ZodAny},S(s)));class Kt extends O{constructor(){super(...arguments),this._unknown=!0}_parse(e){return ee(e.data)}}Kt.create=s=>new Kt(l({typeName:k.ZodUnknown},S(s)));class xe extends O{_parse(e){const t=this._getOrReturnCtx(e);return p(t,{code:d.invalid_type,expected:y.never,received:t.parsedType}),x}}xe.create=s=>new xe(l({typeName:k.ZodNever},S(s)));class es extends O{_parse(e){if(this._getType(e)!==y.undefined){const r=this._getOrReturnCtx(e);return p(r,{code:d.invalid_type,expected:y.void,received:r.parsedType}),x}return ee(e.data)}}es.create=s=>new es(l({typeName:k.ZodVoid},S(s)));class ie extends O{_parse(e){const{ctx:t,status:r}=this._processInputParams(e),n=this._def;if(t.parsedType!==y.array)return p(t,{code:d.invalid_type,expected:y.array,received:t.parsedType}),x;if(n.exactLength!==null){const i=t.data.length>n.exactLength.value,o=t.data.length<n.exactLength.value;(i||o)&&(p(t,{code:i?d.too_big:d.too_small,minimum:o?n.exactLength.value:void 0,maximum:i?n.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:n.exactLength.message}),r.dirty())}if(n.minLength!==null&&t.data.length<n.minLength.value&&(p(t,{code:d.too_small,minimum:n.minLength.value,type:"array",inclusive:!0,exact:!1,message:n.minLength.message}),r.dirty()),n.maxLength!==null&&t.data.length>n.maxLength.value&&(p(t,{code:d.too_big,maximum:n.maxLength.value,type:"array",inclusive:!0,exact:!1,message:n.maxLength.message}),r.dirty()),t.common.async)return Promise.all([...t.data].map((i,o)=>n.type._parseAsync(new ue(t,i,t.path,o)))).then(i=>W.mergeArray(r,i));const a=[...t.data].map((i,o)=>n.type._parseSync(new ue(t,i,t.path,o)));return W.mergeArray(r,a)}get element(){return this._def.type}min(e,t){return new ie(w(l({},this._def),{minLength:{value:e,message:g.toString(t)}}))}max(e,t){return new ie(w(l({},this._def),{maxLength:{value:e,message:g.toString(t)}}))}length(e,t){return new ie(w(l({},this._def),{exactLength:{value:e,message:g.toString(t)}}))}nonempty(e){return this.min(1,e)}}ie.create=(s,e)=>new ie(l({type:s,minLength:null,maxLength:null,exactLength:null,typeName:k.ZodArray},S(e)));function Ae(s){if(s instanceof I){const e={};for(const t in s.shape){const r=s.shape[t];e[t]=me.create(Ae(r))}return new I(w(l({},s._def),{shape:()=>e}))}else return s instanceof ie?new ie(w(l({},s._def),{type:Ae(s.element)})):s instanceof me?me.create(Ae(s.unwrap())):s instanceof je?je.create(Ae(s.unwrap())):s instanceof we?we.create(s.items.map(e=>Ae(e))):s}class I extends O{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),t=F.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==y.object){const u=this._getOrReturnCtx(e);return p(u,{code:d.invalid_type,expected:y.object,received:u.parsedType}),x}const{status:r,ctx:n}=this._processInputParams(e),{shape:a,keys:i}=this._getCached(),o=[];if(!(this._def.catchall instanceof xe&&this._def.unknownKeys==="strip"))for(const u in n.data)i.includes(u)||o.push(u);const c=[];for(const u of i){const m=a[u],v=n.data[u];c.push({key:{status:"valid",value:u},value:m._parse(new ue(n,v,n.path,u)),alwaysSet:u in n.data})}if(this._def.catchall instanceof xe){const u=this._def.unknownKeys;if(u==="passthrough")for(const m of o)c.push({key:{status:"valid",value:m},value:{status:"valid",value:n.data[m]}});else if(u==="strict")o.length>0&&(p(n,{code:d.unrecognized_keys,keys:o}),r.dirty());else if(u!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const u=this._def.catchall;for(const m of o){const v=n.data[m];c.push({key:{status:"valid",value:m},value:u._parse(new ue(n,v,n.path,m)),alwaysSet:m in n.data})}}return n.common.async?Promise.resolve().then(()=>T(this,null,function*(){const u=[];for(const m of c){const v=yield m.key,_=yield m.value;u.push({key:v,value:_,alwaysSet:m.alwaysSet})}return u})).then(u=>W.mergeObjectSync(r,u)):W.mergeObjectSync(r,c)}get shape(){return this._def.shape()}strict(e){return g.errToObj,new I(l(w(l({},this._def),{unknownKeys:"strict"}),e!==void 0?{errorMap:(t,r)=>{var a,i,o,c;const n=(o=(i=(a=this._def).errorMap)==null?void 0:i.call(a,t,r).message)!=null?o:r.defaultError;return t.code==="unrecognized_keys"?{message:(c=g.errToObj(e).message)!=null?c:n}:{message:n}}}:{}))}strip(){return new I(w(l({},this._def),{unknownKeys:"strip"}))}passthrough(){return new I(w(l({},this._def),{unknownKeys:"passthrough"}))}extend(e){return new I(w(l({},this._def),{shape:()=>l(l({},this._def.shape()),e)}))}merge(e){return new I({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>l(l({},this._def.shape()),e._def.shape()),typeName:k.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new I(w(l({},this._def),{catchall:e}))}pick(e){const t={};for(const r of F.objectKeys(e))e[r]&&this.shape[r]&&(t[r]=this.shape[r]);return new I(w(l({},this._def),{shape:()=>t}))}omit(e){const t={};for(const r of F.objectKeys(this.shape))e[r]||(t[r]=this.shape[r]);return new I(w(l({},this._def),{shape:()=>t}))}deepPartial(){return Ae(this)}partial(e){const t={};for(const r of F.objectKeys(this.shape)){const n=this.shape[r];e&&!e[r]?t[r]=n:t[r]=n.optional()}return new I(w(l({},this._def),{shape:()=>t}))}required(e){const t={};for(const r of F.objectKeys(this.shape))if(e&&!e[r])t[r]=this.shape[r];else{let a=this.shape[r];for(;a instanceof me;)a=a._def.innerType;t[r]=a}return new I(w(l({},this._def),{shape:()=>t}))}keyof(){return xs(F.objectKeys(this.shape))}}I.create=(s,e)=>new I(l({shape:()=>s,unknownKeys:"strip",catchall:xe.create(),typeName:k.ZodObject},S(e)));I.strictCreate=(s,e)=>new I(l({shape:()=>s,unknownKeys:"strict",catchall:xe.create(),typeName:k.ZodObject},S(e)));I.lazycreate=(s,e)=>new I(l({shape:s,unknownKeys:"strip",catchall:xe.create(),typeName:k.ZodObject},S(e)));class Ie extends O{_parse(e){const{ctx:t}=this._processInputParams(e),r=this._def.options;function n(a){for(const o of a)if(o.result.status==="valid")return o.result;for(const o of a)if(o.result.status==="dirty")return t.common.issues.push(...o.ctx.common.issues),o.result;const i=a.map(o=>new pe(o.ctx.common.issues));return p(t,{code:d.invalid_union,unionErrors:i}),x}if(t.common.async)return Promise.all(r.map(a=>T(this,null,function*(){const i=w(l({},t),{common:w(l({},t.common),{issues:[]}),parent:null});return{result:yield a._parseAsync({data:t.data,path:t.path,parent:i}),ctx:i}}))).then(n);{let a;const i=[];for(const c of r){const u=w(l({},t),{common:w(l({},t.common),{issues:[]}),parent:null}),m=c._parseSync({data:t.data,path:t.path,parent:u});if(m.status==="valid")return m;m.status==="dirty"&&!a&&(a={result:m,ctx:u}),u.common.issues.length&&i.push(u.common.issues)}if(a)return t.common.issues.push(...a.ctx.common.issues),a.result;const o=i.map(c=>new pe(c));return p(t,{code:d.invalid_union,unionErrors:o}),x}}get options(){return this._def.options}}Ie.create=(s,e)=>new Ie(l({options:s,typeName:k.ZodUnion},S(e)));function wt(s,e){const t=_e(s),r=_e(e);if(s===e)return{valid:!0,data:s};if(t===y.object&&r===y.object){const n=F.objectKeys(e),a=F.objectKeys(s).filter(o=>n.indexOf(o)!==-1),i=l(l({},s),e);for(const o of a){const c=wt(s[o],e[o]);if(!c.valid)return{valid:!1};i[o]=c.data}return{valid:!0,data:i}}else if(t===y.array&&r===y.array){if(s.length!==e.length)return{valid:!1};const n=[];for(let a=0;a<s.length;a++){const i=s[a],o=e[a],c=wt(i,o);if(!c.valid)return{valid:!1};n.push(c.data)}return{valid:!0,data:n}}else return t===y.date&&r===y.date&&+s==+e?{valid:!0,data:s}:{valid:!1}}class Te extends O{_parse(e){const{status:t,ctx:r}=this._processInputParams(e),n=(a,i)=>{if(Wt(a)||Wt(i))return x;const o=wt(a.value,i.value);return o.valid?((Ht(a)||Ht(i))&&t.dirty(),{status:t.value,value:o.data}):(p(r,{code:d.invalid_intersection_types}),x)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then(([a,i])=>n(a,i)):n(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}Te.create=(s,e,t)=>new Te(l({left:s,right:e,typeName:k.ZodIntersection},S(t)));class we extends O{_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==y.array)return p(r,{code:d.invalid_type,expected:y.array,received:r.parsedType}),x;if(r.data.length<this._def.items.length)return p(r,{code:d.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),x;!this._def.rest&&r.data.length>this._def.items.length&&(p(r,{code:d.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const a=[...r.data].map((i,o)=>{const c=this._def.items[o]||this._def.rest;return c?c._parse(new ue(r,i,r.path,o)):null}).filter(i=>!!i);return r.common.async?Promise.all(a).then(i=>W.mergeArray(t,i)):W.mergeArray(t,a)}get items(){return this._def.items}rest(e){return new we(w(l({},this._def),{rest:e}))}}we.create=(s,e)=>{if(!Array.isArray(s))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new we(l({items:s,typeName:k.ZodTuple,rest:null},S(e)))};class Re extends O{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==y.object)return p(r,{code:d.invalid_type,expected:y.object,received:r.parsedType}),x;const n=[],a=this._def.keyType,i=this._def.valueType;for(const o in r.data)n.push({key:a._parse(new ue(r,o,r.path,o)),value:i._parse(new ue(r,r.data[o],r.path,o)),alwaysSet:o in r.data});return r.common.async?W.mergeObjectAsync(t,n):W.mergeObjectSync(t,n)}get element(){return this._def.valueType}static create(e,t,r){return t instanceof O?new Re(l({keyType:e,valueType:t,typeName:k.ZodRecord},S(r))):new Re(l({keyType:ae.create(),valueType:e,typeName:k.ZodRecord},S(t)))}}class ts extends O{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==y.map)return p(r,{code:d.invalid_type,expected:y.map,received:r.parsedType}),x;const n=this._def.keyType,a=this._def.valueType,i=[...r.data.entries()].map(([o,c],u)=>({key:n._parse(new ue(r,o,r.path,[u,"key"])),value:a._parse(new ue(r,c,r.path,[u,"value"]))}));if(r.common.async){const o=new Map;return Promise.resolve().then(()=>T(this,null,function*(){for(const c of i){const u=yield c.key,m=yield c.value;if(u.status==="aborted"||m.status==="aborted")return x;(u.status==="dirty"||m.status==="dirty")&&t.dirty(),o.set(u.value,m.value)}return{status:t.value,value:o}}))}else{const o=new Map;for(const c of i){const u=c.key,m=c.value;if(u.status==="aborted"||m.status==="aborted")return x;(u.status==="dirty"||m.status==="dirty")&&t.dirty(),o.set(u.value,m.value)}return{status:t.value,value:o}}}}ts.create=(s,e,t)=>new ts(l({valueType:e,keyType:s,typeName:k.ZodMap},S(t)));class Ye extends O{_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==y.set)return p(r,{code:d.invalid_type,expected:y.set,received:r.parsedType}),x;const n=this._def;n.minSize!==null&&r.data.size<n.minSize.value&&(p(r,{code:d.too_small,minimum:n.minSize.value,type:"set",inclusive:!0,exact:!1,message:n.minSize.message}),t.dirty()),n.maxSize!==null&&r.data.size>n.maxSize.value&&(p(r,{code:d.too_big,maximum:n.maxSize.value,type:"set",inclusive:!0,exact:!1,message:n.maxSize.message}),t.dirty());const a=this._def.valueType;function i(c){const u=new Set;for(const m of c){if(m.status==="aborted")return x;m.status==="dirty"&&t.dirty(),u.add(m.value)}return{status:t.value,value:u}}const o=[...r.data.values()].map((c,u)=>a._parse(new ue(r,c,r.path,u)));return r.common.async?Promise.all(o).then(c=>i(c)):i(o)}min(e,t){return new Ye(w(l({},this._def),{minSize:{value:e,message:g.toString(t)}}))}max(e,t){return new Ye(w(l({},this._def),{maxSize:{value:e,message:g.toString(t)}}))}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}Ye.create=(s,e)=>new Ye(l({valueType:s,minSize:null,maxSize:null,typeName:k.ZodSet},S(e)));class ss extends O{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}ss.create=(s,e)=>new ss(l({getter:s,typeName:k.ZodLazy},S(e)));class rs extends O{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return p(t,{received:t.data,code:d.invalid_literal,expected:this._def.value}),x}return{status:"valid",value:e.data}}get value(){return this._def.value}}rs.create=(s,e)=>new rs(l({value:s,typeName:k.ZodLiteral},S(e)));function xs(s,e){return new $e(l({values:s,typeName:k.ZodEnum},S(e)))}class $e extends O{_parse(e){if(typeof e.data!="string"){const t=this._getOrReturnCtx(e),r=this._def.values;return p(t,{expected:F.joinValues(r),received:t.parsedType,code:d.invalid_type}),x}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){const t=this._getOrReturnCtx(e),r=this._def.values;return p(t,{received:t.data,code:d.invalid_enum_value,options:r}),x}return ee(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return $e.create(e,l(l({},this._def),t))}exclude(e,t=this._def){return $e.create(this.options.filter(r=>!e.includes(r)),l(l({},this._def),t))}}$e.create=xs;class ns extends O{_parse(e){const t=F.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(e);if(r.parsedType!==y.string&&r.parsedType!==y.number){const n=F.objectValues(t);return p(r,{expected:F.joinValues(n),received:r.parsedType,code:d.invalid_type}),x}if(this._cache||(this._cache=new Set(F.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){const n=F.objectValues(t);return p(r,{received:r.data,code:d.invalid_enum_value,options:n}),x}return ee(e.data)}get enum(){return this._def.values}}ns.create=(s,e)=>new ns(l({values:s,typeName:k.ZodNativeEnum},S(e)));class ut extends O{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==y.promise&&t.common.async===!1)return p(t,{code:d.invalid_type,expected:y.promise,received:t.parsedType}),x;const r=t.parsedType===y.promise?t.data:Promise.resolve(t.data);return ee(r.then(n=>this._def.type.parseAsync(n,{path:t.path,errorMap:t.common.contextualErrorMap})))}}ut.create=(s,e)=>new ut(l({type:s,typeName:k.ZodPromise},S(e)));class oe extends O{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===k.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:r}=this._processInputParams(e),n=this._def.effect||null,a={addIssue:i=>{p(r,i),i.fatal?t.abort():t.dirty()},get path(){return r.path}};if(a.addIssue=a.addIssue.bind(a),n.type==="preprocess"){const i=n.transform(r.data,a);if(r.common.async)return Promise.resolve(i).then(o=>T(this,null,function*(){if(t.value==="aborted")return x;const c=yield this._def.schema._parseAsync({data:o,path:r.path,parent:r});return c.status==="aborted"?x:c.status==="dirty"||t.value==="dirty"?ze(c.value):c}));{if(t.value==="aborted")return x;const o=this._def.schema._parseSync({data:i,path:r.path,parent:r});return o.status==="aborted"?x:o.status==="dirty"||t.value==="dirty"?ze(o.value):o}}if(n.type==="refinement"){const i=o=>{const c=n.refinement(o,a);if(r.common.async)return Promise.resolve(c);if(c instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return o};if(r.common.async===!1){const o=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return o.status==="aborted"?x:(o.status==="dirty"&&t.dirty(),i(o.value),{status:t.value,value:o.value})}else return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(o=>o.status==="aborted"?x:(o.status==="dirty"&&t.dirty(),i(o.value).then(()=>({status:t.value,value:o.value}))))}if(n.type==="transform")if(r.common.async===!1){const i=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!Ee(i))return x;const o=n.transform(i.value,a);if(o instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:o}}else return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(i=>Ee(i)?Promise.resolve(n.transform(i.value,a)).then(o=>({status:t.value,value:o})):x);F.assertNever(n)}}oe.create=(s,e,t)=>new oe(l({schema:s,typeName:k.ZodEffects,effect:e},S(t)));oe.createWithPreprocess=(s,e,t)=>new oe(l({schema:e,effect:{type:"preprocess",transform:s},typeName:k.ZodEffects},S(t)));class me extends O{_parse(e){return this._getType(e)===y.undefined?ee(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}me.create=(s,e)=>new me(l({innerType:s,typeName:k.ZodOptional},S(e)));class je extends O{_parse(e){return this._getType(e)===y.null?ee(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}je.create=(s,e)=>new je(l({innerType:s,typeName:k.ZodNullable},S(e)));class Ze extends O{_parse(e){const{ctx:t}=this._processInputParams(e);let r=t.data;return t.parsedType===y.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}Ze.create=(s,e)=>new Ze(l({innerType:s,typeName:k.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default},S(e)));class Ct extends O{_parse(e){const{ctx:t}=this._processInputParams(e),r=w(l({},t),{common:w(l({},t.common),{issues:[]})}),n=this._def.innerType._parse({data:r.data,path:r.path,parent:l({},r)});return ot(n)?n.then(a=>({status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new pe(r.common.issues)},input:r.data})})):{status:"valid",value:n.status==="valid"?n.value:this._def.catchValue({get error(){return new pe(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}Ct.create=(s,e)=>new Ct(l({innerType:s,typeName:k.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch},S(e)));class as extends O{_parse(e){if(this._getType(e)!==y.nan){const r=this._getOrReturnCtx(e);return p(r,{code:d.invalid_type,expected:y.nan,received:r.parsedType}),x}return{status:"valid",value:e.data}}}as.create=s=>new as(l({typeName:k.ZodNaN},S(s)));class fn extends O{_parse(e){const{ctx:t}=this._processInputParams(e),r=t.data;return this._def.type._parse({data:r,path:t.path,parent:t})}unwrap(){return this._def.type}}class Nt extends O{_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.common.async)return T(this,null,function*(){const a=yield this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return a.status==="aborted"?x:a.status==="dirty"?(t.dirty(),ze(a.value)):this._def.out._parseAsync({data:a.value,path:r.path,parent:r})});{const n=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return n.status==="aborted"?x:n.status==="dirty"?(t.dirty(),{status:"dirty",value:n.value}):this._def.out._parseSync({data:n.value,path:r.path,parent:r})}}static create(e,t){return new Nt({in:e,out:t,typeName:k.ZodPipeline})}}class St extends O{_parse(e){const t=this._def.innerType._parse(e),r=n=>(Ee(n)&&(n.value=Object.freeze(n.value)),n);return ot(t)?t.then(n=>r(n)):r(t)}unwrap(){return this._def.innerType}}St.create=(s,e)=>new St(l({innerType:s,typeName:k.ZodReadonly},S(e)));I.lazycreate;var k;(function(s){s.ZodString="ZodString",s.ZodNumber="ZodNumber",s.ZodNaN="ZodNaN",s.ZodBigInt="ZodBigInt",s.ZodBoolean="ZodBoolean",s.ZodDate="ZodDate",s.ZodSymbol="ZodSymbol",s.ZodUndefined="ZodUndefined",s.ZodNull="ZodNull",s.ZodAny="ZodAny",s.ZodUnknown="ZodUnknown",s.ZodNever="ZodNever",s.ZodVoid="ZodVoid",s.ZodArray="ZodArray",s.ZodObject="ZodObject",s.ZodUnion="ZodUnion",s.ZodDiscriminatedUnion="ZodDiscriminatedUnion",s.ZodIntersection="ZodIntersection",s.ZodTuple="ZodTuple",s.ZodRecord="ZodRecord",s.ZodMap="ZodMap",s.ZodSet="ZodSet",s.ZodFunction="ZodFunction",s.ZodLazy="ZodLazy",s.ZodLiteral="ZodLiteral",s.ZodEnum="ZodEnum",s.ZodEffects="ZodEffects",s.ZodNativeEnum="ZodNativeEnum",s.ZodOptional="ZodOptional",s.ZodNullable="ZodNullable",s.ZodDefault="ZodDefault",s.ZodCatch="ZodCatch",s.ZodPromise="ZodPromise",s.ZodBranded="ZodBranded",s.ZodPipeline="ZodPipeline",s.ZodReadonly="ZodReadonly"})(k||(k={}));const zn=ae.create,Un=lt.create;xe.create;ie.create;const hn=I.create;I.strictCreate;Ie.create;Te.create;we.create;Re.create;$e.create;ut.create;oe.create;me.create;je.create;oe.createWithPreprocess;const Ce=(s,e)=>s.constructor.name===e.name,z=new Map;z.set(lt.name,()=>!1),z.set(Oe.name,()=>0),z.set(ae.name,()=>""),z.set(ie.name,()=>[]),z.set(Re.name,()=>({})),z.set(Ze.name,s=>s._def.defaultValue()),z.set(oe.name,s=>Ue(s._def.schema)),z.set(me.name,s=>Ce(s._def.innerType,Ze)?s._def.innerType._def.defaultValue():void 0),z.set(we.name,s=>{const e=[];for(const t of s._def.items)e.push(Ue(t));return e}),z.set(oe.name,s=>Ue(s._def.schema)),z.set(Ie.name,s=>Ue(s._def.options[0])),z.set(I.name,s=>be(s)),z.set(Re.name,s=>be(s)),z.set(Te.name,s=>be(s));function Ue(s){const e=s.constructor.name;if(!z.has(e)){console.warn("getSchemaDefaultForField: Unhandled type",s.constructor.name);return}return z.get(e)(s)}function be(s){if(Ce(s,Re))return{};if(Ce(s,oe))return be(s._def.schema);if(Ce(s,Te))return l(l({},be(s._def.left)),be(s._def.right));if(Ce(s,Ie)){for(const e of s._def.options)if(Ce(e,I))return be(e);return console.warn("getSchemaDefaultObject: No object found in union, returning empty object"),{}}return Ce(s,I)?Object.fromEntries(Object.entries(s.shape).map(([e,t])=>[e,Ue(t)]).filter(e=>e[1]!==void 0)):(console.warn(`getSchemaDefaultObject: Expected object schema, got ${s.constructor.name}`),{})}function mn(s){return be(s)}const[pn,yn]=At("VbenFormProps"),[vn,gn]=At("ComponentRefMap");function _n(s){var o;const e=Bs(),t=a(),r=ur(l({},(o=Object.keys(t))!=null&&o.length?{initialValues:t}:{})),n=N(()=>{const c=[];for(const u of Object.keys(e))u!=="default"&&c.push(u);return c});function a(){const c={},u={};(f(s).schema||[]).forEach(_=>{if(Reflect.has(_,"defaultValue"))_t(c,_.fieldName,_.defaultValue);else if(_.rules&&!he(_.rules)){const b=i(_.rules);u[_.fieldName]=_.rules,b!==void 0&&(c[_.fieldName]=b)}});const m=mn(hn(u)),v={};for(const _ in m)_t(v,_,m[_]);return qe(c,v)}function i(c){if(c instanceof ae)return"";if(c instanceof Oe)return null;if(c instanceof I){const u={};for(const[m,v]of Object.entries(c.shape))u[m]=i(v);return u}else if(c instanceof Te){const u=i(c._def.left),m=i(c._def.right);return typeof u=="object"&&typeof m=="object"?l(l({},u),m):u!=null?u:m}else return}return{delegatedSlots:n,form:r}}const bn=Y({__name:"form-actions",props:{modelValue:{default:!1},modelModifiers:{}},emits:["update:modelValue"],setup(s,{expose:e}){const{$t:t}=dr(),[r,n]=pn(),a=us(s,"modelValue"),i=N(()=>l({content:`${t.value("reset")}`,show:!0},f(r).resetButtonOptions)),o=N(()=>l({content:`${t.value("submit")}`,show:!0},f(r).submitButtonOptions));function c(v){return T(this,null,function*(){var C;v==null||v.preventDefault(),v==null||v.stopPropagation();const _=f(r);if(!_.formApi)return;const{valid:b}=yield _.formApi.validate();if(!b)return;const Z=Se(yield _.formApi.getValues());yield(C=_.handleSubmit)==null?void 0:C.call(_,Z)})}function u(v){return T(this,null,function*(){var Z,C;v==null||v.preventDefault(),v==null||v.stopPropagation();const _=f(r),b=Se(yield(Z=_.formApi)==null?void 0:Z.getValues());G(_.handleReset)?yield(C=_.handleReset)==null?void 0:C.call(_,b):n.resetForm()})}ke(()=>a.value,()=>{f(r).collapseTriggerResize&&zs()});const m=N(()=>{const v=f(r),_=v.actionLayout||"rowEnd",b=v.actionPosition||"right",Z=["flex","items-center","gap-3",v.compact?"pb-2":"pb-4",v.layout==="vertical"?"self-end":"self-center",v.layout==="inline"?"":"w-full",v.actionWrapperClass];switch(_){case"newLine":{Z.push("col-span-full");break}case"rowEnd":{Z.push("col-[-2/-1]");break}}switch(b){case"center":{Z.push("justify-center");break}case"left":{Z.push("justify-start");break}default:{Z.push("justify-end");break}}return Z.join(" ")});return e({handleReset:u,handleSubmit:c}),(v,_)=>(R(),ce("div",{class:Q(f(U)(m.value))},[f(r).actionButtonsReverse?(R(),ce(bt,{key:0},[M(v.$slots,"submit-before"),o.value.show?(R(),P(We(f(rt).PrimaryButton),ne({key:0,type:"button",onClick:c},o.value),{default:$(()=>[st(Le(o.value.content),1)]),_:1},16)):L("",!0)],64)):L("",!0),M(v.$slots,"reset-before"),i.value.show?(R(),P(We(f(rt).DefaultButton),ne({key:1,type:"button",onClick:u},i.value),{default:$(()=>[st(Le(i.value.content),1)]),_:1},16)):L("",!0),f(r).actionButtonsReverse?L("",!0):(R(),ce(bt,{key:2},[M(v.$slots,"submit-before"),o.value.show?(R(),P(We(f(rt).PrimaryButton),ne({key:0,type:"button",onClick:c},o.value),{default:$(()=>[st(Le(o.value.content),1)]),_:1},16)):L("",!0)],64)),M(v.$slots,"expand-before"),f(r).showCollapseButton?(R(),P(f(Nr),{key:3,class:"ml-[-0.3em]","model-value":a.value,"onUpdate:modelValue":_[0]||(_[0]=b=>a.value=b)},{default:$(()=>[He("span",null,Le(a.value?f(t)("expand"):f(t)("collapse")),1)]),_:1},8,["model-value"])):L("",!0),M(v.$slots,"expand-after")],2))}});const is=s=>s!==null&&!!s&&typeof s=="object"&&!Array.isArray(s);function ws(s){return Number(s)>=0}function kn(s){return typeof s=="object"&&s!==null}function xn(s){return s==null?s===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(s)}function os(s){if(!kn(s)||xn(s)!=="[object Object]")return!1;if(Object.getPrototypeOf(s)===null)return!0;let e=s;for(;Object.getPrototypeOf(e)!==null;)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(s)===e}function Cs(s,e){return Object.keys(e).forEach(t=>{if(os(e[t])&&os(s[t])){s[t]||(s[t]={}),Cs(s[t],e[t]);return}s[t]=e[t]}),s}function wn(s){const e=s.split(".");if(!e.length)return"";let t=String(e[0]);for(let r=1;r<e.length;r++){if(ws(e[r])){t+=`[${e[r]}]`;continue}t+=`.${e[r]}`}return t}function Cn(s,e){return{__type:"VVTypedSchema",parse(n){return T(this,null,function*(){const a=yield s.safeParseAsync(n,e);if(a.success)return{value:a.data,errors:[]};const i={};return Ss(a.error.issues,i),{errors:Object.values(i)}})},cast(n){try{return s.parse(n)}catch(a){const i=Os(s);return is(i)&&is(n)?Cs(i,n):n}},describe(n){try{if(!n)return{required:!s.isOptional(),exists:!0};const a=Sn(n,s);return a?{required:!a.isOptional(),exists:!0}:{required:!1,exists:!1}}catch(a){return{required:!1,exists:!1}}}}}function Ss(s,e){s.forEach(t=>{const r=wn(t.path.join("."));t.code==="invalid_union"&&(Ss(t.unionErrors.flatMap(n=>n.issues),e),!r)||(e[r]||(e[r]={errors:[],path:r}),e[r].errors.push(t.message))})}function Os(s){if(s instanceof I)return Object.fromEntries(Object.entries(s.shape).map(([e,t])=>t instanceof Ze?[e,t._def.defaultValue()]:t instanceof I?[e,Os(t)]:[e,void 0]))}function Sn(s,e){if(!ls(e))return null;if(fr(s))return e.shape[hr(s)];const t=(s||"").split(/\.|\[(\d+)\]/).filter(Boolean);let r=e;for(let n=0;n<=t.length;n++){const a=t[n];if(!a||!r)return r;if(ls(r)){r=r.shape[a]||null;continue}ws(a)&&On(r)&&(r=r._def.type)}return null}function Ts(s){return s._def.typeName}function On(s){return Ts(s)===k.ZodArray}function ls(s){return Ts(s)===k.ZodObject}const[Vt,Tn]=At("FormRenderProps"),Rn=()=>{const s=Vt(),e=N(()=>s.layout==="vertical"),t=N(()=>s.componentMap);return{componentBindEventMap:N(()=>s.componentBindEventMap),componentMap:t,isVertical:e}};function Fn(s){const e=vs(),r=Vt().form;if(!e)throw new Error("useDependencies should be used within <VbenForm>");const n=fe(!0),a=fe(!1),i=fe(!0),o=fe(!1),c=fe({}),u=fe(),m=N(()=>{var b,Z;return((Z=(b=s())==null?void 0:b.triggerFields)!=null?Z:[]).map(C=>e.value[C])}),v=()=>{a.value=!1,n.value=!0,i.value=!0,o.value=!1,u.value=void 0,c.value={}};return ke([m,s],Z=>T(null,[Z],function*([_,b]){var K;if(!b||!((K=b==null?void 0:b.triggerFields)!=null&&K.length))return;v();const{componentProps:C,disabled:A,if:j,required:V,rules:E,show:H,trigger:X}=b,te=e.value;if(G(j)){if(n.value=!!(yield j(te,r)),!n.value)return}else if(yt(j)&&(n.value=j,!n.value))return;if(G(H)){if(i.value=!!(yield H(te,r)),!i.value)return}else if(yt(H)&&(i.value=H,!i.value))return;G(C)&&(c.value=yield C(te,r)),G(E)&&(u.value=yield E(te,r)),G(A)?a.value=!!(yield A(te,r)):yt(A)&&(a.value=A),G(V)&&(o.value=!!(yield V(te,r))),G(X)&&(yield X(te,r))}),{deep:!0,immediate:!0}),{dynamicComponentProps:c,dynamicRules:u,isDisabled:a,isIf:n,isRequired:o,isShow:i}}const An={key:0,class:"text-destructive mr-[2px]"},Nn={key:2,class:"ml-[2px]"},Vn=Y({__name:"form-label",props:{class:{},colon:{type:Boolean},help:{type:[Function,String]},label:{type:[Function,String]},required:{type:Boolean}},setup(s){const e=s;return(t,r)=>(R(),P(f(Fr),{class:Q(f(U)("flex items-center",e.class))},{default:$(()=>[t.required?(R(),ce("span",An,"*")):L("",!0),M(t.$slots,"default"),t.help?(R(),P(f(Ar),{key:1,"trigger-class":"size-3.5 ml-1"},{default:$(()=>[re(f(Be),{content:t.help},null,8,["content"])]),_:1})):L("",!0),t.colon&&t.label?(R(),ce("span",Nn,":")):L("",!0)]),_:3},8,["class"]))}});function Ot(s){return!s||he(s)?null:"innerType"in s._def?Ot(s._def.innerType):"schema"in s._def?Ot(s._def.schema):s}function Tt(s){if(!s||he(s))return;const e=s;if(e._def.typeName==="ZodDefault")return e._def.defaultValue();if("innerType"in e._def)return Tt(e._def.innerType);if("schema"in e._def)return Tt(e._def.schema)}function cs(s){return!s||!Rt(s)?!1:Reflect.has(s,"target")&&Reflect.has(s,"stopPropagation")}const En={class:"flex-auto overflow-hidden p-[1px]"},In={key:0,class:"ml-1"},$n=Y({__name:"form-field",props:{component:{},componentProps:{type:Function},defaultValue:{},dependencies:{},description:{type:[Function,String]},fieldName:{},help:{type:[Function,String]},label:{type:[Function,String]},renderComponentContent:{type:Function},rules:{},suffix:{type:[Function,String]},colon:{type:Boolean},controlClass:{},disabled:{type:Boolean},disabledOnChangeListener:{type:Boolean},disabledOnInputListener:{type:Boolean},emptyStateValue:{},formFieldProps:{},formItemClass:{type:[Function,String]},hideLabel:{type:Boolean},hideRequiredMark:{type:Boolean},labelClass:{},labelWidth:{},modelPropName:{},wrapperClass:{},commonComponentProps:{}},setup(s){const{componentBindEventMap:e,componentMap:t,isVertical:r}=Rn(),n=Vt(),a=vs(),i=ps(s.fieldName),o=ds("fieldComponentRef"),c=n.form,u=N(()=>n.compact),m=N(()=>{var h;return((h=i.value)==null?void 0:h.length)>0}),v=N(()=>{const h=he(s.component)?t.value[s.component]:s.component;return h||console.warn(`Component ${s.component} is not registered`),h}),{dynamicComponentProps:_,dynamicRules:b,isDisabled:Z,isIf:C,isRequired:A,isShow:j}=Fn(()=>s.dependencies),V=N(()=>{var h;return(h=s.labelClass)!=null&&h.includes("w-")||r.value?{}:{width:`${s.labelWidth}px`}}),E=N(()=>b.value||s.rules),H=N(()=>C.value&&j.value),X=N(()=>{var q,B,ye,Me,Pe,De;if(!H.value)return!1;if(!E.value)return A.value;if(A.value)return!0;if(he(E.value))return["required","selectRequired"].includes(E.value);let h=(B=(q=E==null?void 0:E.value)==null?void 0:q.isOptional)==null?void 0:B.call(q);if(((Me=(ye=E==null?void 0:E.value)==null?void 0:ye._def)==null?void 0:Me.typeName)==="ZodDefault"){const se=(Pe=E==null?void 0:E.value)==null?void 0:Pe._def.innerType;se&&(h=(De=se.isOptional)==null?void 0:De.call(se))}return!h}),te=N(()=>{var q;if(!H.value)return null;let h=E.value;if(!h)return A.value?"required":null;if(he(h))return h;if(!!X.value){const B=(q=h==null?void 0:h.unwrap)==null?void 0:q.call(h);B&&(h=B)}return Cn(h)}),K=N(()=>{const h=G(s.componentProps)?s.componentProps(a.value,c):s.componentProps;return l(l(l({},s.commonComponentProps),h),_.value)});ke(()=>{var h;return(h=K.value)==null?void 0:h.autofocus},h=>{h===!0&&at(()=>{Ke()})},{immediate:!0});const Je=N(()=>{var h;return Z.value||s.disabled||((h=K.value)==null?void 0:h.disabled)}),Qe=N(()=>G(s.renderComponentContent)?s.renderComponentContent(a.value,c):{}),ft=N(()=>Object.keys(Qe.value)),ht=N(()=>{const h=te.value;return l(l({keepValue:!0,label:he(s.label)?s.label:""},h?{rules:h}:{}),s.formFieldProps)});function le(h){var Me,Pe,De;const D=h.componentField.modelValue,q=h.componentField["onUpdate:modelValue"],B=s.modelPropName||(he(s.component)?(Me=e.value)==null?void 0:Me[s.component]:null);let ye=D;return D&&Rt(D)&&B&&(ye=cs(D)?(Pe=D==null?void 0:D.target)==null?void 0:Pe[B]:(De=D==null?void 0:D[B])!=null?De:D),B?l({[`onUpdate:${B}`]:q,[B]:ye===void 0?s.emptyStateValue:ye,onChange:s.disabledOnChangeListener?void 0:se=>{var Et,It,$t;const Rs=cs(se),Fe=(Et=h==null?void 0:h.componentField)==null?void 0:Et.onChange;return Rs?Fe==null?void 0:Fe(($t=(It=se==null?void 0:se.target)==null?void 0:It[B])!=null?$t:se):Fe==null?void 0:Fe(se)}},s.disabledOnInputListener?{onInput:void 0}:{}):l(l({},s.disabledOnInputListener?{onInput:void 0}:{}),s.disabledOnChangeListener?{onChange:void 0}:{})}function Xe(h){const D=le(h);return l(l(l(l(l({},h.componentField),K.value),D),Reflect.has(K.value,"onChange")?{onChange:K.value.onChange}:{}),Reflect.has(K.value,"onInput")?{onInput:K.value.onInput}:{})}function Ke(){var h,D;o.value&&G(o.value.focus)&&document.activeElement!==o.value&&((D=(h=o.value)==null?void 0:h.focus)==null||D.call(h))}const de=vn();return ke(o,h=>{de==null||de.set(s.fieldName,h)}),Us(()=>{de!=null&&de.has(s.fieldName)&&de.delete(s.fieldName)}),(h,D)=>f(C)?(R(),P(f(mr),ne({key:0},ht.value,{name:h.fieldName}),{default:$(q=>[qs(re(f(Tr),ne({class:[{"form-valid-error":m.value,"form-is-required":X.value,"flex-col":f(r),"flex-row items-center":!f(r),"pb-4":!u.value,"pb-2":u.value},"relative flex"]},h.$attrs),{default:$(()=>[h.hideLabel?L("",!0):(R(),P(Vn,{key:0,class:Q(f(U)("flex leading-6",{"mr-2 flex-shrink-0 justify-end":!f(r),"mb-1 flex-row":f(r)},h.labelClass)),help:h.help,colon:h.colon,label:h.label,required:X.value&&!h.hideRequiredMark,style:Ws(V.value)},{default:$(()=>[h.label?(R(),P(f(Be),{key:0,content:h.label},null,8,["content"])):L("",!0)]),_:1},8,["class","help","colon","label","required","style"])),He("div",En,[He("div",{class:Q(f(U)("relative flex w-full items-center",h.wrapperClass))},[re(f(Cr),{class:Q(f(U)(h.controlClass))},{default:$(()=>[M(h.$slots,"default",ve(ge(w(l(l({},q),Xe(q)),{disabled:Je.value,isInValid:m.value}))),()=>[(R(),P(We(v.value),ne({ref_key:"fieldComponentRef",ref:o,class:{"border-destructive focus:border-destructive hover:border-destructive/80 focus:shadow-[0_0_0_2px_rgba(255,38,5,0.06)]":m.value}},Xe(q),{disabled:Je.value}),fs({_:2},[Ft(ft.value,B=>({name:B,fn:$(ye=>[re(f(Be),ne({content:Qe.value[B]},w(l({},ye),{formContext:q})),null,16,["content"])])}))]),1040,["class","disabled"])),u.value&&m.value?(R(),P(f(ys),{key:0,"delay-duration":300,side:"left"},{trigger:$(()=>[M(h.$slots,"trigger",{},()=>[re(f(xr),{class:Q(f(U)("text-foreground/80 hover:text-foreground inline-flex size-5 cursor-pointer"))},null,8,["class"])])]),default:$(()=>[re(f(zt))]),_:3})):L("",!0)])]),_:2},1032,["class"]),h.suffix?(R(),ce("div",In,[re(f(Be),{content:h.suffix},null,8,["content"])])):L("",!0),h.description?(R(),P(f(Or),{key:1,class:"ml-1"},{default:$(()=>[re(f(Be),{content:h.description},null,8,["content"])]),_:1})):L("",!0)],2),u.value?L("",!0):(R(),P(yr,{key:0,name:"slide-up"},{default:$(()=>[re(f(zt),{class:"absolute"})]),_:1}))])]),_:2},1040,["class"]),[[pr,f(j)]])]),_:3},16,["name"])):L("",!0)}});function jn(s){const e=ds("wrapperRef"),t=Hs(e),r=fe({}),n=fe(!1),a=Gs(Ys),i=N(()=>{var v,_;const c=(v=s.collapsedRows)!=null?v:1,u=r.value;let m=0;for(let b=1;b<=c;b++)m+=(_=u==null?void 0:u[b])!=null?_:0;return m-1||1});ke([()=>s.showCollapseButton,()=>a.active().value,()=>{var c;return(c=s.schema)==null?void 0:c.length},()=>t.value],u=>T(null,[u],function*([c]){c&&(yield at(),r.value={},n.value=!1,yield o())}));function o(){return T(this,null,function*(){if(!s.showCollapseButton||(yield at(),!e.value))return;const c=[...e.value.children],u=e.value,v=window.getComputedStyle(u).getPropertyValue("grid-template-rows").split(" "),_=u==null?void 0:u.getBoundingClientRect();c.forEach(b=>{var V,E;const C=b.getBoundingClientRect().top-_.top;let A=0,j=0;for(const[H,X]of v.entries())if(j+=Number.parseFloat(X),C<j){A=H+1;break}A>((V=s==null?void 0:s.collapsedRows)!=null?V:1)||(r.value[A]=((E=r.value[A])!=null?E:0)+1,n.value=!0)})})}return hs(()=>{o()}),{isCalculated:n,keepFormItemIndex:i,wrapperRef:e}}const Zn=Y({__name:"form",props:{arrayToStringFields:{},collapsed:{type:Boolean},collapsedRows:{default:1},collapseTriggerResize:{type:Boolean},commonConfig:{default:()=>({})},compact:{type:Boolean},componentBindEventMap:{},componentMap:{},fieldMappingTime:{},form:{},layout:{},schema:{},showCollapseButton:{type:Boolean,default:!1},wrapperClass:{default:"grid-cols-1 sm:grid-cols-2 md:grid-cols-3"},globalCommonConfig:{default:()=>({})}},emits:["submit"],setup(s,{emit:e}){const t=s,r=e,n=N(()=>{const b=["flex"];return t.layout==="inline"?b.push("flex-wrap gap-x-2"):b.push(t.compact?"gap-x-2":"gap-x-4","flex-col grid"),U(...b,t.wrapperClass)});Tn(t);const{isCalculated:a,keepFormItemIndex:i,wrapperRef:o}=jn(t),c=N(()=>{var Z;const b=[];return(Z=t.schema)==null||Z.forEach(C=>{const{fieldName:A}=C,j=C.rules;let V="";j&&!he(j)&&(V=j._def.typeName);const E=Ot(j);b.push({default:Tt(j),fieldName:A,required:!["ZodNullable","ZodOptional"].includes(V),rules:E})}),b}),u=N(()=>t.form?"form":vr),m=N(()=>t.form?{onSubmit:t.form.handleSubmit(b=>r("submit",b))}:{onSubmit:b=>r("submit",b)}),v=N(()=>t.collapsed&&a.value),_=N(()=>{const{colon:b=!1,componentProps:Z={},controlClass:C="",disabled:A,disabledOnChangeListener:j=!0,disabledOnInputListener:V=!0,emptyStateValue:E=void 0,formFieldProps:H={},formItemClass:X="",hideLabel:te=!1,hideRequiredMark:K=!1,labelClass:Je="",labelWidth:Qe=100,modelPropName:ft="",wrapperClass:ht=""}=qe(t.commonConfig,t.globalCommonConfig);return(t.schema||[]).map((le,Xe)=>{const Ke=i.value,de=t.showCollapseButton&&v.value&&Ke?Ke<=Xe:!1;let h=le.formItemClass;if(G(le.formItemClass))try{h=le.formItemClass()}catch(D){console.error("Error calling formItemClass function:",D),h=""}return w(l({colon:b,disabled:A,disabledOnChangeListener:j,disabledOnInputListener:V,emptyStateValue:E,hideLabel:te,hideRequiredMark:K,labelWidth:Qe,modelPropName:ft,wrapperClass:ht},le),{commonComponentProps:Z,componentProps:le.componentProps,controlClass:U(C,le.controlClass),formFieldProps:l(l({},H),le.formFieldProps),formItemClass:U("flex-shrink-0",{hidden:de},X,h),labelClass:U(Je,le.labelClass)})})});return(b,Z)=>(R(),P(We(u.value),ve(ge(m.value)),{default:$(()=>[He("div",{ref_key:"wrapperRef",ref:o,class:Q(n.value)},[(R(!0),ce(bt,null,Ft(_.value,C=>(R(),P($n,ne({key:C.fieldName,ref_for:!0},C,{class:C.formItemClass,rules:C.rules}),{default:$(A=>[M(b.$slots,C.fieldName,ne({ref_for:!0},A))]),_:2},1040,["class","rules"]))),128)),M(b.$slots,"default",{shapes:c.value})],2)]),_:3},16))}}),Mn=Y({__name:"vben-use-form",props:{formApi:{},actionButtonsReverse:{type:Boolean},actionLayout:{},actionPosition:{},actionWrapperClass:{},arrayToStringFields:{},fieldMappingTime:{},handleReset:{type:Function},handleSubmit:{type:Function},handleValuesChange:{type:Function},resetButtonOptions:{},scrollToFirstError:{type:Boolean},showDefaultActions:{type:Boolean},submitButtonOptions:{},submitOnChange:{type:Boolean},submitOnEnter:{type:Boolean},collapsed:{type:Boolean},collapsedRows:{},collapseTriggerResize:{type:Boolean},commonConfig:{},compact:{type:Boolean},layout:{},schema:{},showCollapseButton:{type:Boolean},wrapperClass:{}},setup(s){var v,_,b,Z;const e=s,t=(_=(v=e.formApi)==null?void 0:v.useStore)==null?void 0:_.call(v),r=gr(e,t),n=new Map,{delegatedSlots:a,form:i}=_n(r);yn([r,i]),gn(n),(Z=(b=e.formApi)==null?void 0:b.mount)==null||Z.call(b,i,n);const o=C=>{var A;(A=e.formApi)==null||A.setState({collapsed:!!C})};function c(C){var A;!t.value.submitOnEnter||!((A=r.value.formApi)!=null&&A.isMounted)||C.target instanceof HTMLTextAreaElement||(C.preventDefault(),r.value.formApi.validateAndSubmitForm())}const u=Xs(()=>T(null,null,function*(){var C;t.value.submitOnChange&&((C=r.value.formApi)==null||C.validateAndSubmitForm())}),300),m={};return hs(()=>T(null,null,function*(){yield at(),ke(()=>i.values,C=>T(null,null,function*(){var A;if(r.value.handleValuesChange){const j=(A=t.value.schema)==null?void 0:A.map(V=>V.fieldName);if(j&&j.length>0){const V=[];j.forEach(E=>{const H=Bt(C,E),X=Bt(m,E);Js(H,X)||(V.push(E),_t(m,E,H))}),V.length>0&&r.value.handleValuesChange(Qs(yield r.value.formApi.getValues()),V)}}u()}),{deep:!0})})),(C,A)=>(R(),P(f(Zn),ne({onKeydown:kr(c,["enter"])},f(r),{collapsed:f(t).collapsed,"component-bind-event-map":f(br),"component-map":f(rt),form:f(i),"global-common-config":f(_r)}),fs({default:$(j=>[M(C.$slots,"default",ve(ge(j)),()=>[f(r).showDefaultActions?(R(),P(bn,{key:0,"model-value":f(t).collapsed,"onUpdate:modelValue":o},{"reset-before":$(V=>[M(C.$slots,"reset-before",ve(ge(V)))]),"submit-before":$(V=>[M(C.$slots,"submit-before",ve(ge(V)))]),"expand-before":$(V=>[M(C.$slots,"expand-before",ve(ge(V)))]),"expand-after":$(V=>[M(C.$slots,"expand-after",ve(ge(V)))]),_:3},8,["model-value"])):L("",!0)])]),_:2},[Ft(f(a),j=>({name:j,fn:$(V=>[M(C.$slots,j,ve(ge(V)))])}))]),1040,["collapsed","component-bind-event-map","component-map","form","global-common-config"]))}});function qn(s){const e=Ks(s),t=new Pr(s),r=t;r.useStore=a=>jr(t.store,a);const n=Y((a,{attrs:i,slots:o})=>(er(()=>{t.unmount()}),t.setState(l(l({},a),i)),()=>tr(Mn,w(l(l({},a),i),{formApi:r}),o)),{name:"VbenUseForm",inheritAttrs:!1});return e&&ke(()=>s.schema,()=>{t.setState({schema:s.schema})},{immediate:!0}),[n,r]}export{xr as C,kt as S,Ar as _,wr as a,Un as b,jr as c,zn as s,qn as u};
