<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref, reactive, computed } from 'vue';
import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

defineOptions({ name: 'IrrigationOverview' });

// 当前时间
const currentTime = ref('');
const updateTime = () => {
  const now = new Date();
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
};

// 定时更新时间
setInterval(updateTime, 1000);
updateTime();

// 实时数据
const realTimeData = reactive({
  totalFlow: 12856.8, // 总流量 m³/h
  totalPower: 68245.6, // 总功率 kW
  irrigationArea: 125680, // 灌溉面积 亩
  waterLevel: 85.2, // 水位 %
});

// 图表配置
const chartTabs = [
  { label: '流量趋势', value: 'flow' },
  { label: '功率监控', value: 'power' },
];

const activeTab = ref('flow');

// 流量趋势图表
const flowChartRef = ref<EchartsUIType>();
const { renderEcharts: renderFlowChart } = useEcharts(flowChartRef);

// 功率监控图表
const powerChartRef = ref<EchartsUIType>();
const { renderEcharts: renderPowerChart } = useEcharts(powerChartRef);

// 最新报警数据
const alarmData = ref([
  {
    key: '1',
    stationName: '东风灌站',
    alarmType: '水泵故障',
    alarmTime: '2024-01-15 14:30:25',
    status: 'processing',
  },
  {
    key: '2',
    stationName: '红旗灌站',
    alarmType: '电压异常',
    alarmTime: '2024-01-15 13:45:12',
    status: 'processing',
  },
  {
    key: '3',
    stationName: '胜利灌站',
    alarmType: '流量异常',
    alarmTime: '2024-01-15 12:20:08',
    status: 'resolved',
  },
  {
    key: '4',
    stationName: '新华灌站',
    alarmType: '压力异常',
    alarmTime: '2024-01-15 11:15:33',
    status: 'processing',
  },
  {
    key: '5',
    stationName: '建设灌站',
    alarmType: '温度过高',
    alarmTime: '2024-01-15 10:45:18',
    status: 'resolved',
  },
]);

onMounted(() => {
  // 渲染流量趋势图
  renderFlowChart({
    backgroundColor: 'transparent',
    title: {
      text: '24小时流量趋势',
      left: 'center',
      textStyle: {
        color: '#ffffff',
        fontSize: 14,
      },
    },
    tooltip: {
      trigger: 'axis',
      formatter: '{b}: {c} m³/h',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#1890ff',
      textStyle: {
        color: '#ffffff',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: Array.from({ length: 24 }).map((_, i) => `${i}:00`),
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)',
        },
      },
      axisLabel: {
        color: '#b3d9ff',
      },
    },
    yAxis: {
      type: 'value',
      name: '流量(m³/h)',
      nameTextStyle: {
        color: '#b3d9ff',
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)',
        },
      },
      axisLabel: {
        color: '#b3d9ff',
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
    },
    series: [
      {
        name: '流量',
        type: 'line',
        smooth: true,
        data: [
          2200, 2100, 2000, 1950, 1900, 2000, 2200, 2400, 2600, 2800, 2900,
          3000, 3100, 3200, 3150, 3100, 3000, 2900, 2800, 2700, 2600, 2500,
          2400, 2300,
        ],
        lineStyle: {
          color: '#1890ff',
          width: 3,
        },
        itemStyle: {
          color: '#1890ff',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(24, 144, 255, 0.6)' },
              { offset: 1, color: 'rgba(24, 144, 255, 0.1)' },
            ],
          },
        },
      },
    ],
  });

  // 渲染功率监控图
  renderPowerChart({
    backgroundColor: 'transparent',
    title: {
      text: '功率分布',
      left: 'center',
      textStyle: {
        color: '#ffffff',
        fontSize: 14,
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} kW ({d}%)',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#1890ff',
      textStyle: {
        color: '#ffffff',
      },
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        color: '#b3d9ff',
      },
    },
    series: [
      {
        name: '功率分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        data: [
          { value: 28500, name: '大型灌站', itemStyle: { color: '#1890ff' } },
          { value: 22800, name: '中型灌站', itemStyle: { color: '#52c41a' } },
          { value: 12600, name: '小型灌站', itemStyle: { color: '#faad14' } },
          { value: 4345, name: '微型灌站', itemStyle: { color: '#722ed1' } },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
        label: {
          color: '#ffffff',
        },
        labelLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)',
          },
        },
      },
    ],
  });
});
</script>

<template>
  <div class="dashboard-container">
    <!-- 大屏头部 -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="header-left">
          <div class="logo">🌾</div>
          <div class="title-group">
            <h1 class="main-title">三农大数据平台</h1>
            <h2 class="sub-title">提灌站智能化升级改造监控大屏</h2>
          </div>
        </div>
        <div class="header-right">
          <div class="time-display">{{ currentTime }}</div>
          <div class="status-indicator">
            <span class="status-dot online"></span>
            系统运行正常
          </div>
        </div>
      </div>
    </div>

    <!-- 大屏主体内容 -->
    <div class="dashboard-content">
      <!-- 顶部统计卡片 -->
      <div class="top-stats">
        <div class="stat-card primary">
          <div class="stat-icon">🏭</div>
          <div class="stat-content">
            <div class="stat-value">154</div>
            <div class="stat-label">总灌站数</div>
          </div>
          <div class="stat-trend">
            <div class="trend-value">142</div>
            <div class="trend-label">在线</div>
          </div>
        </div>

        <div class="stat-card success">
          <div class="stat-icon">⚡</div>
          <div class="stat-content">
            <div class="stat-value">92.2%</div>
            <div class="stat-label">运行率</div>
          </div>
          <div class="stat-trend">
            <div class="trend-value">142</div>
            <div class="trend-label">运行中</div>
          </div>
        </div>

        <div class="stat-card warning">
          <div class="stat-icon">⚠️</div>
          <div class="stat-content">
            <div class="stat-value">8</div>
            <div class="stat-label">故障报警</div>
          </div>
          <div class="stat-trend">
            <div class="trend-value">3</div>
            <div class="trend-label">今日新增</div>
          </div>
        </div>

        <div class="stat-card info">
          <div class="stat-icon">🔧</div>
          <div class="stat-content">
            <div class="stat-value">85.5%</div>
            <div class="stat-label">维修完成率</div>
          </div>
          <div class="stat-trend">
            <div class="trend-value">12</div>
            <div class="trend-label">维修中</div>
          </div>
        </div>
      </div>

      <!-- 中间内容区域 -->
      <div class="middle-content">
        <!-- 左侧区域 -->
        <div class="left-panel">
          <!-- 实时数据 -->
          <div class="panel-card">
            <div class="panel-header">
              <h3>实时监控数据</h3>
              <div class="refresh-indicator">
                <span class="refresh-dot"></span>
                实时更新
              </div>
            </div>
            <div class="panel-content">
              <div class="data-grid">
                <div class="data-item">
                  <div class="data-label">总流量</div>
                  <div class="data-value blue">
                    {{ realTimeData.totalFlow.toLocaleString() }}
                  </div>
                  <div class="data-unit">m³/h</div>
                </div>
                <div class="data-item">
                  <div class="data-label">总功率</div>
                  <div class="data-value green">
                    {{ realTimeData.totalPower.toLocaleString() }}
                  </div>
                  <div class="data-unit">kW</div>
                </div>
                <div class="data-item">
                  <div class="data-label">灌溉面积</div>
                  <div class="data-value orange">
                    {{ realTimeData.irrigationArea.toLocaleString() }}
                  </div>
                  <div class="data-unit">亩</div>
                </div>
                <div class="data-item">
                  <div class="data-label">水位监控</div>
                  <div class="data-value purple">
                    {{ realTimeData.waterLevel }}%
                  </div>
                  <div class="water-level-bar">
                    <div
                      class="water-level-fill"
                      :style="{ width: realTimeData.waterLevel + '%' }"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 报警信息 -->
          <div class="panel-card">
            <div class="panel-header">
              <h3>最新报警信息</h3>
              <div class="alarm-indicator">
                <span class="alarm-dot"></span>
                实时监控
              </div>
            </div>
            <div class="panel-content">
              <div class="alarm-list">
                <div
                  v-for="alarm in alarmData.slice(0, 5)"
                  :key="alarm.key"
                  class="alarm-item"
                  :class="{ processing: alarm.status === 'processing' }"
                >
                  <div class="alarm-time">
                    {{ alarm.alarmTime.split(' ')[1] }}
                  </div>
                  <div class="alarm-station">{{ alarm.stationName }}</div>
                  <div class="alarm-type">{{ alarm.alarmType }}</div>
                  <div class="alarm-status">
                    <span
                      v-if="alarm.status === 'processing'"
                      class="status-processing"
                      >处理中</span
                    >
                    <span v-else class="status-resolved">已解决</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 中间图表区域 -->
        <div class="center-panel">
          <div class="chart-container">
            <div class="chart-header">
              <h3>24小时流量趋势</h3>
              <div class="chart-tabs">
                <span
                  v-for="tab in chartTabs"
                  :key="tab.value"
                  class="chart-tab"
                  :class="{ active: activeTab === tab.value }"
                  @click="activeTab = tab.value"
                >
                  {{ tab.label }}
                </span>
              </div>
            </div>
            <div class="chart-content">
              <EchartsUI
                v-show="activeTab === 'flow'"
                ref="flowChartRef"
                style="height: 100%; width: 100%"
              />
              <EchartsUI
                v-show="activeTab === 'power'"
                ref="powerChartRef"
                style="height: 100%; width: 100%"
              />
            </div>
          </div>
        </div>

        <!-- 右侧区域 -->
        <div class="right-panel">
          <!-- 运行状态分布 -->
          <div class="panel-card">
            <div class="panel-header">
              <h3>运行状态分布</h3>
            </div>
            <div class="panel-content">
              <div class="status-chart">
                <div class="status-item">
                  <div class="status-circle online"></div>
                  <div class="status-info">
                    <div class="status-label">在线运行</div>
                    <div class="status-count">142座</div>
                  </div>
                  <div class="status-percent">92.2%</div>
                </div>
                <div class="status-item">
                  <div class="status-circle offline"></div>
                  <div class="status-info">
                    <div class="status-label">离线停机</div>
                    <div class="status-count">8座</div>
                  </div>
                  <div class="status-percent">5.2%</div>
                </div>
                <div class="status-item">
                  <div class="status-circle maintenance"></div>
                  <div class="status-info">
                    <div class="status-label">维修中</div>
                    <div class="status-count">4座</div>
                  </div>
                  <div class="status-percent">2.6%</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 关键指标 -->
          <div class="panel-card">
            <div class="panel-header">
              <h3>关键指标</h3>
            </div>
            <div class="panel-content">
              <div class="indicator-list">
                <div class="indicator-item">
                  <div class="indicator-label">设备完好率</div>
                  <div class="indicator-value">95.8%</div>
                  <div class="indicator-bar">
                    <div class="indicator-fill" style="width: 95.8%"></div>
                  </div>
                </div>
                <div class="indicator-item">
                  <div class="indicator-label">能耗效率</div>
                  <div class="indicator-value">88.3%</div>
                  <div class="indicator-bar">
                    <div class="indicator-fill" style="width: 88.3%"></div>
                  </div>
                </div>
                <div class="indicator-item">
                  <div class="indicator-label">安全等级</div>
                  <div class="indicator-value">92.1%</div>
                  <div class="indicator-bar">
                    <div class="indicator-fill" style="width: 92.1%"></div>
                  </div>
                </div>
                <div class="indicator-item">
                  <div class="indicator-label">维护及时率</div>
                  <div class="indicator-value">89.7%</div>
                  <div class="indicator-bar">
                    <div class="indicator-fill" style="width: 89.7%"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 大屏容器 */
.dashboard-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0c1426 100%);
  color: #ffffff;
  font-family: 'Microsoft YaHei', sans-serif;
  overflow: hidden;
}

/* 头部样式 */
.dashboard-header {
  height: 80px;
  background: linear-gradient(
    90deg,
    rgba(24, 144, 255, 0.1) 0%,
    rgba(24, 144, 255, 0.05) 100%
  );
  border-bottom: 2px solid rgba(24, 144, 255, 0.3);
  padding: 0 30px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.logo {
  font-size: 36px;
  filter: drop-shadow(0 0 10px rgba(24, 144, 255, 0.5));
}

.title-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.main-title {
  font-size: 28px;
  font-weight: bold;
  color: #1890ff;
  margin: 0;
  text-shadow: 0 0 10px rgba(24, 144, 255, 0.5);
}

.sub-title {
  font-size: 16px;
  color: #8cc8ff;
  margin: 0;
  font-weight: normal;
}

.header-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.time-display {
  font-size: 18px;
  color: #52c41a;
  font-weight: bold;
  text-shadow: 0 0 8px rgba(82, 196, 26, 0.5);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #95de64;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-dot.online {
  background: #52c41a;
  box-shadow: 0 0 10px rgba(82, 196, 26, 0.8);
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* 主体内容 */
.dashboard-content {
  padding: 20px 30px;
  height: calc(100vh - 80px);
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 顶部统计卡片 */
.top-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, transparent, currentColor, transparent);
  opacity: 0.6;
}

.stat-card.primary::before {
  color: #1890ff;
}
.stat-card.success::before {
  color: #52c41a;
}
.stat-card.warning::before {
  color: #faad14;
}
.stat-card.info::before {
  color: #722ed1;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.stat-icon {
  font-size: 32px;
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.3));
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #ffffff;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #b3d9ff;
  opacity: 0.8;
}

.stat-trend {
  text-align: center;
}

.trend-value {
  font-size: 18px;
  font-weight: bold;
  color: #52c41a;
  display: block;
}

.trend-label {
  font-size: 12px;
  color: #95de64;
  opacity: 0.8;
}

/* 中间内容区域 */
.middle-content {
  display: grid;
  grid-template-columns: 300px 1fr 300px;
  gap: 20px;
  flex: 1;
}

/* 面板卡片 */
.panel-card {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  overflow: hidden;
  margin-bottom: 20px;
}

.panel-header {
  padding: 15px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(
    90deg,
    rgba(24, 144, 255, 0.1) 0%,
    transparent 100%
  );
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  color: #ffffff;
  font-weight: bold;
}

.refresh-indicator,
.alarm-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #95de64;
}

.refresh-dot,
.alarm-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #52c41a;
  animation: pulse 2s infinite;
}

.alarm-dot {
  background: #ff4d4f;
}

.panel-content {
  padding: 20px;
}

/* 数据网格 */
.data-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.data-item {
  text-align: center;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.data-label {
  font-size: 12px;
  color: #b3d9ff;
  margin-bottom: 8px;
}

.data-value {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 4px;
}

.data-value.blue {
  color: #1890ff;
}
.data-value.green {
  color: #52c41a;
}
.data-value.orange {
  color: #faad14;
}
.data-value.purple {
  color: #722ed1;
}

.data-unit {
  font-size: 12px;
  color: #8cc8ff;
  opacity: 0.8;
}

.water-level-bar {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin-top: 8px;
}

.water-level-fill {
  height: 100%;
  background: linear-gradient(90deg, #1890ff, #52c41a);
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* 报警列表 */
.alarm-list {
  max-height: 200px;
  overflow-y: auto;
}

.alarm-item {
  display: grid;
  grid-template-columns: 60px 1fr 80px 60px;
  gap: 10px;
  padding: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 12px;
  align-items: center;
}

.alarm-item:last-child {
  border-bottom: none;
}

.alarm-item.processing {
  background: rgba(255, 77, 79, 0.1);
  border-left: 3px solid #ff4d4f;
}

.alarm-time {
  color: #95de64;
  font-weight: bold;
}

.alarm-station {
  color: #ffffff;
}

.alarm-type {
  color: #ff7875;
}

.status-processing {
  color: #ff4d4f;
  font-weight: bold;
}

.status-resolved {
  color: #52c41a;
}

/* 图表容器 */
.chart-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-header {
  padding: 15px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(
    90deg,
    rgba(24, 144, 255, 0.1) 0%,
    transparent 100%
  );
}

.chart-header h3 {
  margin: 0;
  font-size: 16px;
  color: #ffffff;
  font-weight: bold;
}

.chart-tabs {
  display: flex;
  gap: 10px;
}

.chart-tab {
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: #b3d9ff;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
}

.chart-tab:hover {
  background: rgba(24, 144, 255, 0.2);
  color: #ffffff;
}

.chart-tab.active {
  background: #1890ff;
  color: #ffffff;
  border-color: #1890ff;
}

.chart-content {
  flex: 1;
  padding: 20px;
}

/* 状态图表 */
.status-chart {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-circle {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-circle.online {
  background: #52c41a;
  box-shadow: 0 0 8px rgba(82, 196, 26, 0.6);
}

.status-circle.offline {
  background: #ff4d4f;
  box-shadow: 0 0 8px rgba(255, 77, 79, 0.6);
}

.status-circle.maintenance {
  background: #faad14;
  box-shadow: 0 0 8px rgba(250, 173, 20, 0.6);
}

.status-info {
  flex: 1;
}

.status-label {
  font-size: 14px;
  color: #ffffff;
  margin-bottom: 2px;
}

.status-count {
  font-size: 12px;
  color: #b3d9ff;
}

.status-percent {
  font-size: 16px;
  font-weight: bold;
  color: #52c41a;
}

/* 指标列表 */
.indicator-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.indicator-item {
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.indicator-label {
  font-size: 14px;
  color: #b3d9ff;
  margin-bottom: 8px;
}

.indicator-value {
  font-size: 18px;
  font-weight: bold;
  color: #52c41a;
  margin-bottom: 8px;
}

.indicator-bar {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.indicator-fill {
  height: 100%;
  background: linear-gradient(90deg, #1890ff, #52c41a);
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .middle-content {
    grid-template-columns: 280px 1fr 280px;
  }
}

@media (max-width: 1200px) {
  .top-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .middle-content {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
  }

  .data-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    height: 60px;
    padding: 0 15px;
  }

  .main-title {
    font-size: 20px;
  }

  .sub-title {
    font-size: 14px;
  }

  .dashboard-content {
    padding: 15px;
  }

  .top-stats {
    grid-template-columns: 1fr;
  }

  .data-grid {
    grid-template-columns: 1fr 1fr;
  }
}
</style>
