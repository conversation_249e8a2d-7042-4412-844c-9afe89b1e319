<script lang="ts" setup>
import type { AnalysisOverviewItem } from '@vben/common-ui';
import type { TabOption } from '@vben/types';
import type { EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref, reactive } from 'vue';
import {
  AnalysisChartCard,
  AnalysisChartsTabs,
  AnalysisOverview,
} from '@vben/common-ui';
import {
  SvgBellIcon,
  SvgCakeIcon,
  SvgCardIcon,
  SvgDownloadIcon,
} from '@vben/icons';
import { EchartsUI, useEcharts } from '@vben/plugins/echarts';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Badge,
  Table,
  Tag,
} from 'ant-design-vue';

defineOptions({ name: 'IrrigationOverview' });

// 模拟数据
const overviewItems: AnalysisOverviewItem[] = [
  {
    icon: SvgCardIcon,
    title: '在线灌站',
    totalTitle: '总灌站数',
    totalValue: 154,
    value: 142,
  },
  {
    icon: SvgCakeIcon,
    title: '运行中',
    totalTitle: '运行率',
    totalValue: 92.2,
    value: 142,
  },
  {
    icon: SvgBellIcon,
    title: '故障报警',
    totalTitle: '今日报警',
    totalValue: 8,
    value: 3,
  },
  {
    icon: SvgDownloadIcon,
    title: '维修中',
    totalTitle: '维修完成率',
    totalValue: 85.5,
    value: 12,
  },
];

// 实时数据
const realTimeData = reactive({
  totalFlow: 2856.8, // 总流量 m³/h
  totalPower: 1245.6, // 总功率 kW
  irrigationArea: 8520, // 灌溉面积 亩
  waterLevel: 85.2, // 水位 %
});

// 图表配置
const chartTabs: TabOption[] = [
  {
    label: '流量趋势',
    value: 'flow',
  },
  {
    label: '功率监控',
    value: 'power',
  },
];

// 流量趋势图表
const flowChartRef = ref<EchartsUIType>();
const { renderEcharts: renderFlowChart } = useEcharts(flowChartRef);

// 功率监控图表
const powerChartRef = ref<EchartsUIType>();
const { renderEcharts: renderPowerChart } = useEcharts(powerChartRef);

// 最新报警数据
const alarmColumns = [
  { title: '灌站名称', dataIndex: 'stationName', key: 'stationName' },
  { title: '报警类型', dataIndex: 'alarmType', key: 'alarmType' },
  { title: '报警时间', dataIndex: 'alarmTime', key: 'alarmTime' },
  { title: '状态', dataIndex: 'status', key: 'status' },
];

const alarmData = ref([
  {
    key: '1',
    stationName: '东风灌站',
    alarmType: '水泵故障',
    alarmTime: '2024-01-15 14:30:25',
    status: 'processing',
  },
  {
    key: '2',
    stationName: '红旗灌站',
    alarmType: '电压异常',
    alarmTime: '2024-01-15 13:45:12',
    status: 'processing',
  },
  {
    key: '3',
    stationName: '胜利灌站',
    alarmType: '流量异常',
    alarmTime: '2024-01-15 12:20:08',
    status: 'resolved',
  },
]);

onMounted(() => {
  // 渲染流量趋势图
  renderFlowChart({
    title: {
      text: '24小时流量趋势',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      formatter: '{b}: {c} m³/h',
    },
    xAxis: {
      type: 'category',
      data: Array.from({ length: 24 }).map((_, i) => `${i}:00`),
    },
    yAxis: {
      type: 'value',
      name: '流量(m³/h)',
    },
    series: [
      {
        name: '流量',
        type: 'line',
        smooth: true,
        data: [
          2200, 2100, 2000, 1950, 1900, 2000, 2200, 2400, 2600, 2800, 2900,
          3000, 3100, 3200, 3150, 3100, 3000, 2900, 2800, 2700, 2600, 2500,
          2400, 2300,
        ],
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(24, 144, 255, 0.6)' },
              { offset: 1, color: 'rgba(24, 144, 255, 0.1)' },
            ],
          },
        },
      },
    ],
  });

  // 渲染功率监控图
  renderPowerChart({
    title: {
      text: '功率分布',
      left: 'center',
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} kW ({d}%)',
    },
    series: [
      {
        name: '功率分布',
        type: 'pie',
        radius: ['40%', '70%'],
        data: [
          { value: 450, name: '大型灌站' },
          { value: 380, name: '中型灌站' },
          { value: 280, name: '小型灌站' },
          { value: 135, name: '微型灌站' },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  });
});
</script>

<template>
  <div class="p-5">
    <!-- 大数据平台标题 -->
    <Card class="mb-5">
      <div class="text-center">
        <h1 class="mb-2 text-3xl font-bold text-blue-600">三农大数据平台</h1>
        <h2 class="mb-4 text-xl text-gray-600">提灌站智能化升级改造监控大屏</h2>
        <div class="text-sm text-gray-500">
          通过大数据平台实现154座提灌站的精准操作和监测，全面掌握运行状态、故障报警、流量监控等关键信息
        </div>
      </div>
    </Card>

    <!-- 概览统计 -->
    <AnalysisOverview :items="overviewItems" />

    <!-- 实时数据展示 -->
    <Card title="实时监控数据" class="mt-5">
      <Row :gutter="16">
        <Col :span="6">
          <Statistic
            title="总流量"
            :value="realTimeData.totalFlow"
            suffix="m³/h"
            :value-style="{ color: '#1890ff' }"
          />
        </Col>
        <Col :span="6">
          <Statistic
            title="总功率"
            :value="realTimeData.totalPower"
            suffix="kW"
            :value-style="{ color: '#52c41a' }"
          />
        </Col>
        <Col :span="6">
          <Statistic
            title="灌溉面积"
            :value="realTimeData.irrigationArea"
            suffix="亩"
            :value-style="{ color: '#faad14' }"
          />
        </Col>
        <Col :span="6">
          <div>
            <div class="mb-2 text-sm text-gray-500">水位监控</div>
            <Progress
              :percent="realTimeData.waterLevel"
              :stroke-color="{
                '0%': '#108ee9',
                '100%': '#87d068',
              }"
            />
          </div>
        </Col>
      </Row>
    </Card>

    <!-- 图表展示 -->
    <AnalysisChartsTabs :tabs="chartTabs" class="mt-5">
      <template #flow>
        <EchartsUI ref="flowChartRef" style="height: 400px" />
      </template>
      <template #power>
        <EchartsUI ref="powerChartRef" style="height: 400px" />
      </template>
    </AnalysisChartsTabs>

    <!-- 最新报警信息 -->
    <Card title="最新报警信息" class="mt-5">
      <Table
        :columns="alarmColumns"
        :data-source="alarmData"
        :pagination="false"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <Badge
              v-if="record.status === 'processing'"
              status="processing"
              text="处理中"
            />
            <Badge
              v-else-if="record.status === 'resolved'"
              status="success"
              text="已解决"
            />
          </template>
          <template v-else-if="column.key === 'alarmType'">
            <Tag color="red">{{ record.alarmType }}</Tag>
          </template>
        </template>
      </Table>
    </Card>
  </div>
</template>

<style scoped>
.ant-statistic-content {
  font-size: 24px;
  font-weight: bold;
}
</style>
