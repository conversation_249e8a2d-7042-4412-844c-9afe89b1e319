{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-checkbox/Checkbox.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { createVNode as _createVNode } from \"vue\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { defineComponent, ref, watch } from 'vue';\nimport classNames from '../_util/classNames';\nimport PropTypes from '../_util/vue-types';\nimport { initDefaultProps } from '../_util/props-util';\nexport const checkboxProps = {\n  prefixCls: String,\n  name: String,\n  id: String,\n  type: String,\n  defaultChecked: {\n    type: [Boolean, Number],\n    default: undefined\n  },\n  checked: {\n    type: [Boolean, Number],\n    default: undefined\n  },\n  disabled: Boolean,\n  tabindex: {\n    type: [Number, String]\n  },\n  readonly: Boolean,\n  autofocus: Boolean,\n  value: PropTypes.any,\n  required: Boolean\n};\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'Checkbox',\n  inheritAttrs: false,\n  props: initDefaultProps(checkboxProps, {\n    prefixCls: 'rc-checkbox',\n    type: 'checkbox',\n    defaultChecked: false\n  }),\n  emits: ['click', 'change'],\n  setup(props, _ref) {\n    let {\n      attrs,\n      emit,\n      expose\n    } = _ref;\n    const checked = ref(props.checked === undefined ? props.defaultChecked : props.checked);\n    const inputRef = ref();\n    watch(() => props.checked, () => {\n      checked.value = props.checked;\n    });\n    expose({\n      focus() {\n        var _a;\n        (_a = inputRef.value) === null || _a === void 0 ? void 0 : _a.focus();\n      },\n      blur() {\n        var _a;\n        (_a = inputRef.value) === null || _a === void 0 ? void 0 : _a.blur();\n      }\n    });\n    const eventShiftKey = ref();\n    const handleChange = e => {\n      if (props.disabled) {\n        return;\n      }\n      if (props.checked === undefined) {\n        checked.value = e.target.checked;\n      }\n      e.shiftKey = eventShiftKey.value;\n      const eventObj = {\n        target: _extends(_extends({}, props), {\n          checked: e.target.checked\n        }),\n        stopPropagation() {\n          e.stopPropagation();\n        },\n        preventDefault() {\n          e.preventDefault();\n        },\n        nativeEvent: e\n      };\n      // fix https://github.com/vueComponent/ant-design-vue/issues/3047\n      // 受控模式下维持现有状态\n      if (props.checked !== undefined) {\n        inputRef.value.checked = !!props.checked;\n      }\n      emit('change', eventObj);\n      eventShiftKey.value = false;\n    };\n    const onClick = e => {\n      emit('click', e);\n      // onChange没能获取到shiftKey，使用onClick hack\n      eventShiftKey.value = e.shiftKey;\n    };\n    return () => {\n      const {\n          prefixCls,\n          name,\n          id,\n          type,\n          disabled,\n          readonly,\n          tabindex,\n          autofocus,\n          value,\n          required\n        } = props,\n        others = __rest(props, [\"prefixCls\", \"name\", \"id\", \"type\", \"disabled\", \"readonly\", \"tabindex\", \"autofocus\", \"value\", \"required\"]);\n      const {\n        class: className,\n        onFocus,\n        onBlur,\n        onKeydown,\n        onKeypress,\n        onKeyup\n      } = attrs;\n      const othersAndAttrs = _extends(_extends({}, others), attrs);\n      const globalProps = Object.keys(othersAndAttrs).reduce((prev, key) => {\n        if (key.startsWith('data-') || key.startsWith('aria-') || key === 'role') {\n          prev[key] = othersAndAttrs[key];\n        }\n        return prev;\n      }, {});\n      const classString = classNames(prefixCls, className, {\n        [`${prefixCls}-checked`]: checked.value,\n        [`${prefixCls}-disabled`]: disabled\n      });\n      const inputProps = _extends(_extends({\n        name,\n        id,\n        type,\n        readonly,\n        disabled,\n        tabindex,\n        class: `${prefixCls}-input`,\n        checked: !!checked.value,\n        autofocus,\n        value\n      }, globalProps), {\n        onChange: handleChange,\n        onClick,\n        onFocus,\n        onBlur,\n        onKeydown,\n        onKeypress,\n        onKeyup,\n        required\n      });\n      return _createVNode(\"span\", {\n        \"class\": classString\n      }, [_createVNode(\"input\", _objectSpread({\n        \"ref\": inputRef\n      }, inputProps), null), _createVNode(\"span\", {\n        \"class\": `${prefixCls}-inner`\n      }, null)]);\n    };\n  }\n});"], "mappings": ";;;;;;;;;;;;;;;;;;;AAGA,IAAI,SAAgC,SAAU,GAAG,GAAG;AAClD,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAC/F,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B,WAAY,UAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC3I,QAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC,EAAG,GAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,EAClG;AACA,SAAO;AACT;AAKO,IAAM,gBAAgB;AAAA,EAC3B,WAAW;AAAA,EACX,MAAM;AAAA,EACN,IAAI;AAAA,EACJ,MAAM;AAAA,EACN,gBAAgB;AAAA,IACd,MAAM,CAAC,SAAS,MAAM;AAAA,IACtB,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,MAAM,CAAC,SAAS,MAAM;AAAA,IACtB,SAAS;AAAA,EACX;AAAA,EACA,UAAU;AAAA,EACV,UAAU;AAAA,IACR,MAAM,CAAC,QAAQ,MAAM;AAAA,EACvB;AAAA,EACA,UAAU;AAAA,EACV,WAAW;AAAA,EACX,OAAO,kBAAU;AAAA,EACjB,UAAU;AACZ;AACA,IAAO,mBAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO,yBAAiB,eAAe;AAAA,IACrC,WAAW;AAAA,IACX,MAAM;AAAA,IACN,gBAAgB;AAAA,EAClB,CAAC;AAAA,EACD,OAAO,CAAC,SAAS,QAAQ;AAAA,EACzB,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,UAAU,IAAI,MAAM,YAAY,SAAY,MAAM,iBAAiB,MAAM,OAAO;AACtF,UAAM,WAAW,IAAI;AACrB,UAAM,MAAM,MAAM,SAAS,MAAM;AAC/B,cAAQ,QAAQ,MAAM;AAAA,IACxB,CAAC;AACD,WAAO;AAAA,MACL,QAAQ;AACN,YAAI;AACJ,SAAC,KAAK,SAAS,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAAA,MACtE;AAAA,MACA,OAAO;AACL,YAAI;AACJ,SAAC,KAAK,SAAS,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AAAA,MACrE;AAAA,IACF,CAAC;AACD,UAAM,gBAAgB,IAAI;AAC1B,UAAM,eAAe,OAAK;AACxB,UAAI,MAAM,UAAU;AAClB;AAAA,MACF;AACA,UAAI,MAAM,YAAY,QAAW;AAC/B,gBAAQ,QAAQ,EAAE,OAAO;AAAA,MAC3B;AACA,QAAE,WAAW,cAAc;AAC3B,YAAM,WAAW;AAAA,QACf,QAAQ,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG;AAAA,UACpC,SAAS,EAAE,OAAO;AAAA,QACpB,CAAC;AAAA,QACD,kBAAkB;AAChB,YAAE,gBAAgB;AAAA,QACpB;AAAA,QACA,iBAAiB;AACf,YAAE,eAAe;AAAA,QACnB;AAAA,QACA,aAAa;AAAA,MACf;AAGA,UAAI,MAAM,YAAY,QAAW;AAC/B,iBAAS,MAAM,UAAU,CAAC,CAAC,MAAM;AAAA,MACnC;AACA,WAAK,UAAU,QAAQ;AACvB,oBAAc,QAAQ;AAAA,IACxB;AACA,UAAM,UAAU,OAAK;AACnB,WAAK,SAAS,CAAC;AAEf,oBAAc,QAAQ,EAAE;AAAA,IAC1B;AACA,WAAO,MAAM;AACX,YAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,OACJ,SAAS,OAAO,OAAO,CAAC,aAAa,QAAQ,MAAM,QAAQ,YAAY,YAAY,YAAY,aAAa,SAAS,UAAU,CAAC;AAClI,YAAM;AAAA,QACJ,OAAO;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,iBAAiB,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,KAAK;AAC3D,YAAM,cAAc,OAAO,KAAK,cAAc,EAAE,OAAO,CAAC,MAAM,QAAQ;AACpE,YAAI,IAAI,WAAW,OAAO,KAAK,IAAI,WAAW,OAAO,KAAK,QAAQ,QAAQ;AACxE,eAAK,GAAG,IAAI,eAAe,GAAG;AAAA,QAChC;AACA,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AACL,YAAM,cAAc,mBAAW,WAAW,WAAW;AAAA,QACnD,CAAC,GAAG,SAAS,UAAU,GAAG,QAAQ;AAAA,QAClC,CAAC,GAAG,SAAS,WAAW,GAAG;AAAA,MAC7B,CAAC;AACD,YAAM,aAAa,SAAS,SAAS;AAAA,QACnC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,OAAO,GAAG,SAAS;AAAA,QACnB,SAAS,CAAC,CAAC,QAAQ;AAAA,QACnB;AAAA,QACA;AAAA,MACF,GAAG,WAAW,GAAG;AAAA,QACf,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD,aAAO,YAAa,QAAQ;AAAA,QAC1B,SAAS;AAAA,MACX,GAAG,CAAC,YAAa,SAAS,eAAc;AAAA,QACtC,OAAO;AAAA,MACT,GAAG,UAAU,GAAG,IAAI,GAAG,YAAa,QAAQ;AAAA,QAC1C,SAAS,GAAG,SAAS;AAAA,MACvB,GAAG,IAAI,CAAC,CAAC;AAAA,IACX;AAAA,EACF;AACF,CAAC;", "names": []}