import {
  require_xe_utils
} from "./chunk-RWHEUJNV.js";
import {
  __toESM
} from "./chunk-V4OQ3NZ2.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.9.29_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/ui/src/dom.js
var import_xe_utils = __toESM(require_xe_utils());
var tpImgEl;
function initTpImg() {
  if (!tpImgEl) {
    tpImgEl = new Image();
    tpImgEl.src = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=";
  }
  return tpImgEl;
}
function getTpImg() {
  if (!tpImgEl) {
    return initTpImg();
  }
  return tpImgEl;
}
var reClsMap = {};
function getClsRE(cls) {
  if (!reClsMap[cls]) {
    reClsMap[cls] = new RegExp(`(?:^|\\s)${cls}(?!\\S)`, "g");
  }
  return reClsMap[cls];
}
function hasClass(elem, cls) {
  return !!(elem && elem.className && elem.className.match && elem.className.match(getClsRE(cls)));
}
function hasControlKey(evnt) {
  return evnt.ctrlKey || evnt.metaKey;
}
function toCssUnit(val, unit = "px") {
  if (import_xe_utils.default.isNumber(val) || /^\d+$/.test(`${val}`)) {
    return `${val}${unit}`;
  }
  return `${val || ""}`;
}
function getDomNode() {
  const documentElement = document.documentElement;
  const bodyElem = document.body;
  return {
    scrollTop: documentElement.scrollTop || bodyElem.scrollTop,
    scrollLeft: documentElement.scrollLeft || bodyElem.scrollLeft,
    visibleHeight: documentElement.clientHeight || bodyElem.clientHeight,
    visibleWidth: documentElement.clientWidth || bodyElem.clientWidth
  };
}
function getEventTargetNode(evnt, container, queryCls, queryMethod) {
  let targetElem;
  let target = evnt.target.shadowRoot && evnt.composed ? evnt.composedPath()[0] || evnt.target : evnt.target;
  while (target && target.nodeType && target !== document) {
    if (queryCls && hasClass(target, queryCls) && (!queryMethod || queryMethod(target))) {
      targetElem = target;
    } else if (target === container) {
      return { flag: queryCls ? !!targetElem : true, container, targetElem };
    }
    target = target.parentNode;
  }
  return { flag: false };
}
function getAbsolutePos(elem) {
  const bounding = elem.getBoundingClientRect();
  const boundingTop = bounding.top;
  const boundingLeft = bounding.left;
  const { scrollTop, scrollLeft, visibleHeight, visibleWidth } = getDomNode();
  return { boundingTop, top: scrollTop + boundingTop, boundingLeft, left: scrollLeft + boundingLeft, visibleHeight, visibleWidth };
}
function updatePanelPlacement(targetElem, panelElem, options) {
  const { placement, teleportTo, marginSize } = Object.assign({ teleportTo: false, marginSize: 18 }, options);
  let panelPlacement = "bottom";
  let top = "";
  let bottom = "";
  let left = "";
  const right = "";
  let minWidth = "";
  const stys = {};
  if (panelElem && targetElem) {
    const documentElement = document.documentElement;
    const bodyElem = document.body;
    const targetHeight = targetElem.offsetHeight;
    const panelHeight = panelElem.offsetHeight;
    const panelWidth = panelElem.offsetWidth;
    const panelRect = panelElem.getBoundingClientRect();
    const targetRect = targetElem.getBoundingClientRect();
    const visibleHeight = documentElement.clientHeight || bodyElem.clientHeight;
    const visibleWidth = documentElement.clientWidth || bodyElem.clientWidth;
    minWidth = targetElem.offsetWidth;
    if (teleportTo) {
      left = targetRect.left;
      top = targetRect.top + targetHeight;
      if (placement === "top") {
        panelPlacement = "top";
        top = targetRect.top - panelHeight;
      } else if (!placement) {
        if (top + panelHeight + marginSize > visibleHeight) {
          panelPlacement = "top";
          top = targetRect.top - panelHeight;
        }
        if (top < marginSize) {
          panelPlacement = "bottom";
          top = targetRect.top + targetHeight;
        }
      }
      if (left + panelWidth + marginSize > visibleWidth) {
        left -= left + panelWidth + marginSize - visibleWidth;
      }
      if (left < marginSize) {
        left = marginSize;
      }
    } else {
      if (placement === "top") {
        panelPlacement = "top";
        bottom = targetHeight;
      } else if (!placement) {
        top = targetHeight;
        if (targetRect.top + targetRect.height + marginSize > visibleHeight) {
          if (targetRect.top - targetHeight - panelHeight > marginSize) {
            panelPlacement = "top";
            top = "";
            bottom = targetHeight;
          }
        }
      }
      if (panelRect.left + panelRect.width + marginSize > visibleWidth) {
        left = -(panelRect.left + panelRect.width + marginSize - visibleWidth);
      }
    }
    if (import_xe_utils.default.isNumber(top)) {
      stys.top = toCssUnit(top);
    }
    if (import_xe_utils.default.isNumber(bottom)) {
      stys.bottom = toCssUnit(bottom);
    }
    if (import_xe_utils.default.isNumber(left)) {
      stys.left = toCssUnit(left);
    }
    if (import_xe_utils.default.isNumber(right)) {
      stys.right = toCssUnit(right);
    }
    if (import_xe_utils.default.isNumber(minWidth)) {
      stys.minWidth = toCssUnit(minWidth);
    }
  }
  return {
    top: top || 0,
    bottom: bottom || 0,
    left: left || 0,
    right: right || 0,
    style: stys,
    placement: panelPlacement
  };
}

export {
  initTpImg,
  getTpImg,
  hasClass,
  hasControlKey,
  toCssUnit,
  getDomNode,
  getEventTargetNode,
  getAbsolutePos,
  updatePanelPlacement
};
//# sourceMappingURL=chunk-TOENFBLC.js.map
