import {
  require_xe_utils
} from "./chunk-RWHEUJNV.js";
import {
  __toESM
} from "./chunk-V4OQ3NZ2.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.9.29_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/ui/src/vn.js
var import_xe_utils = __toESM(require_xe_utils());
function getSlotVNs(vns) {
  if (import_xe_utils.default.isArray(vns)) {
    return vns;
  }
  return vns ? [vns] : [];
}

export {
  getSlotVNs
};
//# sourceMappingURL=chunk-6CXTBDBP.js.map
