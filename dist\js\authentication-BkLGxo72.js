const e="Welcome Back",o="Plug-and-play Admin system",n="Efficient, versatile frontend template",t="Login Successful",s="Welcome Back",c="Enter your account details to manage your projects",i="Quick Select Account",r="Username",a="Password",l="Please enter username",g="Password is incorrect",u="Please enter password",d="Please complete the verification first",m="Remember Me",p="Create an Account",h="Create Account",y="Already have an account?",L="Don't have an account?",P="Sign Up",b="Make managing your applications simple and fun",f="Confirm Password",w="The passwords do not match",T="I agree to",S="Privacy-policy",A="Terms",v="Please agree to the Privacy Policy and Terms",R="Login instead",k="Use 8 or more characters with a mix of letters, numbers & symbols",C="Forget Password?",E="Enter your email and we'll send you instructions to reset your password",q="Please enter email",M="The email format you entered is incorrect",Q="Send Reset Link",U="Email",x="Scan the QR code with your phone to login",D="Click 'Confirm' after scanning to complete login",B="QR Code Login",G="Wechat Login",W="QQ Login",j="Github Login",I="Google Login",F="Dingding Login",H="Enter your phone number to start managing your project",O="Security code",V="Security code required {0} characters",Y="Mobile",z="Mobile Login",J="Please enter mobile number",K="The phone number format is incorrect",N="Get Security code",X="Resend in {0}s",Z="Or continue with",_="Please Log In Again",$="Your login session has expired. Please log in again to continue.",ee={center:"Align Center",alignLeft:"Align Left",alignRight:"Align Right"},oe={welcomeBack:e,pageTitle:o,pageDesc:n,loginSuccess:t,loginSuccessDesc:s,loginSubtitle:c,selectAccount:i,username:r,password:a,usernameTip:l,passwordErrorTip:g,passwordTip:u,verifyRequiredTip:d,rememberMe:m,createAnAccount:p,createAccount:h,alreadyHaveAccount:y,accountTip:L,signUp:P,signUpSubtitle:b,confirmPassword:f,confirmPasswordTip:w,agree:T,privacyPolicy:S,terms:A,agreeTip:v,goToLogin:R,passwordStrength:k,forgetPassword:C,forgetPasswordSubtitle:E,emailTip:q,emailValidErrorTip:M,sendResetLink:Q,email:U,qrcodeSubtitle:x,qrcodePrompt:D,qrcodeLogin:B,wechatLogin:G,qqLogin:W,githubLogin:j,googleLogin:I,dingdingLogin:F,codeSubtitle:H,code:O,codeTip:V,mobile:Y,mobileLogin:z,mobileTip:J,mobileErrortip:K,sendCode:N,sendText:X,thirdPartyLogin:Z,loginAgainTitle:_,loginAgainSubTitle:$,layout:ee};export{L as accountTip,T as agree,v as agreeTip,y as alreadyHaveAccount,O as code,H as codeSubtitle,V as codeTip,f as confirmPassword,w as confirmPasswordTip,h as createAccount,p as createAnAccount,oe as default,F as dingdingLogin,U as email,q as emailTip,M as emailValidErrorTip,C as forgetPassword,E as forgetPasswordSubtitle,j as githubLogin,R as goToLogin,I as googleLogin,ee as layout,$ as loginAgainSubTitle,_ as loginAgainTitle,c as loginSubtitle,t as loginSuccess,s as loginSuccessDesc,Y as mobile,K as mobileErrortip,z as mobileLogin,J as mobileTip,n as pageDesc,o as pageTitle,a as password,g as passwordErrorTip,k as passwordStrength,u as passwordTip,S as privacyPolicy,W as qqLogin,B as qrcodeLogin,D as qrcodePrompt,x as qrcodeSubtitle,m as rememberMe,i as selectAccount,N as sendCode,Q as sendResetLink,X as sendText,P as signUp,b as signUpSubtitle,A as terms,Z as thirdPartyLogin,r as username,l as usernameTip,d as verifyRequiredTip,G as wechatLogin,e as welcomeBack};
