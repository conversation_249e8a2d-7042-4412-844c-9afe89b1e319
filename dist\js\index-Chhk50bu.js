import{e as ve,P as B,_ as c,t as he,h as ie,o as ae,j as q,b as k,K as I,k as xe,l as be,g as Se,r as Q,n as ye,a as Ce,f as we,p as Pe}from"./bootstrap-CFDAkNgp.js";import{T as Ie}from"./Trigger-D2zZP_An.js";import{M as Oe,a as Y,u as Te}from"./index-1BiZfdtR.js";import{S as se}from"./index-DfHX-m0D.js";import{a4 as H,R as Fe,a5 as W,az as Me,x as h,J as j,P as X,T as Ee,ao as Ne,aF as $e,_ as Z,p as Be,n as je,Y as Ae}from"../jse/index-index-B2UBupFX.js";import{B as Le}from"./BaseInput-Dslq5mxC.js";import{u as Re,F as De}from"./FormItemContext-CoieKSxA.js";import{g as ze,a as ee}from"./statusUtils-D62pPzYs.js";import{i as _e,g as He,a as Ke,b as Ve,c as We,d as Ue}from"./index-C5ScQeGh.js";import"./ResizeObserver.es-CDE7jhPe.js";import"./shallowequal-CNCY1mYq.js";import"./_arrayIncludes-B8uzE354.js";import"./Overflow--DaGju1E.js";import"./index-DQZjs6Lb.js";import"./index-tPQFuBU-.js";import"./colors-KzMfSzFw.js";import"./collapseMotion-DiwOar_A.js";import"./slide-BhgK1D9k.js";function ke(e){const{selectionStart:r}=e;return e.value.slice(0,r)}function Ye(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";return(Array.isArray(r)?r:[r]).reduce((t,i)=>{const p=e.lastIndexOf(i);return p>t.location?{location:p,prefix:i}:t},{location:-1,prefix:""})}function te(e){return(e||"").toLowerCase()}function Xe(e,r,n){const t=e[0];if(!t||t===n)return e;let i=e;const p=r.length;for(let a=0;a<p;a+=1)if(te(i[a])!==te(r[a])){i=i.slice(a);break}else a===p-1&&(i=i.slice(p));return i}function Ge(e,r){const{measureLocation:n,prefix:t,targetText:i,selectionStart:p,split:a}=r;let l=e.slice(0,n);l[l.length-a.length]===a&&(l=l.slice(0,l.length-a.length)),l&&(l=`${l}${a}`);let d=Xe(e.slice(p),i.slice(p-n-t.length),a);d.slice(0,a.length)===a&&(d=d.slice(a.length));const u=`${l}${t}${i}${a}`;return{text:`${u}${d}`,selectionLocation:u.length}}function Je(e,r){e.setSelectionRange(r,r),e.blur(),e.focus()}function qe(e,r){const{split:n}=r;return!n||e.indexOf(n)===-1}function Qe(e,r){let{value:n=""}=r;const t=e.toLowerCase();return n.toLowerCase().indexOf(t)!==-1}const le=Symbol("MentionsContextKey");function Ze(){}const et=H({compatConfig:{MODE:3},name:"DropdownMenu",props:{prefixCls:String,options:{type:Array,default:()=>[]}},setup(e,r){let{slots:n}=r;const{activeIndex:t,setActiveIndex:i,selectOption:p,onFocus:a=Ze,loading:l}=Fe(le,{activeIndex:W(),loading:W(!1)});let d;const u=b=>{clearTimeout(d),d=setTimeout(()=>{a(b)})};return Me(()=>{clearTimeout(d)}),()=>{var b;const{prefixCls:O,options:S}=e,g=S[t.value]||{};return h(Oe,{prefixCls:`${O}-menu`,activeKey:g.value,onSelect:x=>{let{key:y}=x;const C=S.find(w=>{let{value:T}=w;return T===y});p(C)},onMousedown:u},{default:()=>[!l.value&&S.map((x,y)=>{var C,w;const{value:T,disabled:A,label:F=x.value,class:L,style:K}=x;return h(Y,{key:T,disabled:A,onMouseenter:()=>{i(y)},class:L,style:K},{default:()=>[(w=(C=n.option)===null||C===void 0?void 0:C.call(n,x))!==null&&w!==void 0?w:typeof F=="function"?F(x):F]})}),!l.value&&S.length===0?h(Y,{key:"notFoundContent",disabled:!0},{default:()=>[(b=n.notFoundContent)===null||b===void 0?void 0:b.call(n)]}):null,l.value&&h(Y,{key:"loading",disabled:!0},{default:()=>[h(se,{size:"small"},null)]})]})}}}),tt={bottomRight:{points:["tl","br"],offset:[0,4],overflow:{adjustX:0,adjustY:1}},bottomLeft:{points:["tr","bl"],offset:[0,4],overflow:{adjustX:0,adjustY:1}},topRight:{points:["bl","tr"],offset:[0,-4],overflow:{adjustX:0,adjustY:1}},topLeft:{points:["br","tl"],offset:[0,-4],overflow:{adjustX:0,adjustY:1}}},nt=H({compatConfig:{MODE:3},name:"KeywordTrigger",props:{loading:{type:Boolean,default:void 0},options:{type:Array,default:()=>[]},prefixCls:String,placement:String,visible:{type:Boolean,default:void 0},transitionName:String,getPopupContainer:Function,direction:String,dropdownClassName:String},setup(e,r){let{slots:n}=r;const t=()=>`${e.prefixCls}-dropdown`,i=()=>{const{options:a}=e;return h(et,{prefixCls:t(),options:a},{notFoundContent:n.notFoundContent,option:n.option})},p=j(()=>{const{placement:a,direction:l}=e;let d="topRight";return l==="rtl"?d=a==="top"?"topLeft":"bottomLeft":d=a==="top"?"topRight":"bottomRight",d});return()=>{const{visible:a,transitionName:l,getPopupContainer:d}=e;return h(Ie,{prefixCls:t(),popupVisible:a,popup:i(),popupClassName:e.dropdownClassName,popupPlacement:p.value,popupTransitionName:l,builtinPlacements:tt,getPopupContainer:d},{default:n.default})}}}),ot=he("top","bottom"),ue={autofocus:{type:Boolean,default:void 0},prefix:B.oneOfType([B.string,B.arrayOf(B.string)]),prefixCls:String,value:String,disabled:{type:Boolean,default:void 0},split:String,transitionName:String,placement:B.oneOf(ot),character:B.any,characterRender:Function,filterOption:{type:[Boolean,Function]},validateSearch:Function,getPopupContainer:{type:Function},options:ve(),loading:{type:Boolean,default:void 0},rows:[Number,String],direction:{type:String}},ce=c(c({},ue),{dropdownClassName:String}),de={prefix:"@",split:" ",rows:1,validateSearch:qe,filterOption:()=>Qe};ie(ce,de);var ne=function(e,r){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&r.indexOf(t)<0&&(n[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,t=Object.getOwnPropertySymbols(e);i<t.length;i++)r.indexOf(t[i])<0&&Object.prototype.propertyIsEnumerable.call(e,t[i])&&(n[t[i]]=e[t[i]]);return n};function oe(){}const rt=H({compatConfig:{MODE:3},name:"Mentions",inheritAttrs:!1,props:ie(ce,de),emits:["change","select","search","focus","blur","pressenter"],setup(e,r){let{emit:n,attrs:t,expose:i,slots:p}=r;const a=X(null),l=X(null),d=X(),u=Ee({value:e.value||"",measuring:!1,measureLocation:0,measureText:null,measurePrefix:"",activeIndex:0,isFocus:!1});Ne(()=>{u.value=e.value});const b=s=>{n("change",s)},O=s=>{let{target:{value:o}}=s;b(o)},S=(s,o,f)=>{c(u,{measuring:!0,measureText:s,measurePrefix:o,measureLocation:f,activeIndex:0})},g=s=>{c(u,{measuring:!1,measureLocation:0,measureText:null}),s==null||s()},x=s=>{const{which:o}=s;if(u.measuring){if(o===I.UP||o===I.DOWN){const f=R.value.length,m=o===I.UP?-1:1,v=(u.activeIndex+m+f)%f;u.activeIndex=v,s.preventDefault()}else if(o===I.ESC)g();else if(o===I.ENTER){if(s.preventDefault(),!R.value.length){g();return}const f=R.value[u.activeIndex];L(f)}}},y=s=>{const{key:o,which:f}=s,{measureText:m,measuring:v}=u,{prefix:V,validateSearch:E}=e,D=s.target;if(D.composing)return;const N=ke(D),{location:$,prefix:M}=Ye(N,V);if([I.ESC,I.UP,I.DOWN,I.ENTER].indexOf(f)===-1)if($!==-1){const P=N.slice($+M.length),z=E(P,e),_=!!U(P).length;z?(o===M||o==="Shift"||v||P!==m&&_)&&S(P,M,$):v&&g(),z&&n("search",P,M)}else v&&g()},C=s=>{u.measuring||n("pressenter",s)},w=s=>{A(s)},T=s=>{F(s)},A=s=>{clearTimeout(d.value);const{isFocus:o}=u;!o&&s&&n("focus",s),u.isFocus=!0},F=s=>{d.value=setTimeout(()=>{u.isFocus=!1,g(),n("blur",s)},100)},L=s=>{const{split:o}=e,{value:f=""}=s,{text:m,selectionLocation:v}=Ge(u.value,{measureLocation:u.measureLocation,targetText:f,prefix:u.measurePrefix,selectionStart:l.value.getSelectionStart(),split:o});b(m),g(()=>{Je(l.value.input,v)}),n("select",s,u.measurePrefix)},K=s=>{u.activeIndex=s},U=s=>{const o=s||u.measureText||"",{filterOption:f}=e;return e.options.filter(v=>f?f(o,v):!0)},R=j(()=>U());return i({blur:()=>{l.value.blur()},focus:()=>{l.value.focus()}}),$e(le,{activeIndex:Z(u,"activeIndex"),setActiveIndex:K,selectOption:L,onFocus:A,onBlur:F,loading:Z(e,"loading")}),Be(()=>{je(()=>{u.measuring&&(a.value.scrollTop=l.value.getScrollTop())})}),()=>{const{measureLocation:s,measurePrefix:o,measuring:f}=u,{prefixCls:m,placement:v,transitionName:V,getPopupContainer:E,direction:D}=e,N=ne(e,["prefixCls","placement","transitionName","getPopupContainer","direction"]),{class:$,style:M}=t,P=ne(t,["class","style"]),z=ae(N,["value","prefix","split","validateSearch","filterOption","options","loading"]),_=c(c(c({},z),P),{onChange:oe,onSelect:oe,value:u.value,onInput:O,onBlur:T,onKeydown:x,onKeyup:y,onFocus:w,onPressenter:C});return h("div",{class:q(m,$),style:M},[h(Le,k(k({},_),{},{ref:l,tag:"textarea"}),null),f&&h("div",{ref:a,class:`${m}-measure`},[u.value.slice(0,s),h(nt,{prefixCls:m,transitionName:V,dropdownClassName:e.dropdownClassName,placement:v,options:f?R.value:[],visible:!0,direction:D,getPopupContainer:E},{default:()=>[h("span",null,[o])],notFoundContent:p.notFoundContent,option:p.option}),u.value.slice(s+o.length)])])}}}),it={value:String,disabled:Boolean,payload:xe()},pe=c(c({},it),{label:be([])}),fe={name:"Option",props:pe,render(e,r){let{slots:n}=r;var t;return(t=n.default)===null||t===void 0?void 0:t.call(n)}};H(c({compatConfig:{MODE:3}},fe));const at=e=>{const{componentCls:r,colorTextDisabled:n,controlItemBgHover:t,controlPaddingHorizontal:i,colorText:p,motionDurationSlow:a,lineHeight:l,controlHeight:d,inputPaddingHorizontal:u,inputPaddingVertical:b,fontSize:O,colorBgElevated:S,borderRadiusLG:g,boxShadowSecondary:x}=e,y=Math.round((e.controlHeight-e.fontSize*e.lineHeight)/2);return{[r]:c(c(c(c(c({},Q(e)),He(e)),{position:"relative",display:"inline-block",height:"auto",padding:0,overflow:"hidden",lineHeight:l,whiteSpace:"pre-wrap",verticalAlign:"bottom"}),Ke(e,r)),{"&-disabled":{"> textarea":c({},Ue(e))},"&-focused":c({},We(e)),[`&-affix-wrapper ${r}-suffix`]:{position:"absolute",top:0,insetInlineEnd:u,bottom:0,zIndex:1,display:"inline-flex",alignItems:"center",margin:"auto"},[`> textarea, ${r}-measure`]:{color:p,boxSizing:"border-box",minHeight:d-2,margin:0,padding:`${b}px ${u}px`,overflow:"inherit",overflowX:"hidden",overflowY:"auto",fontWeight:"inherit",fontSize:"inherit",fontFamily:"inherit",fontStyle:"inherit",fontVariant:"inherit",fontSizeAdjust:"inherit",fontStretch:"inherit",lineHeight:"inherit",direction:"inherit",letterSpacing:"inherit",whiteSpace:"inherit",textAlign:"inherit",verticalAlign:"top",wordWrap:"break-word",wordBreak:"inherit",tabSize:"inherit"},"> textarea":c({width:"100%",border:"none",outline:"none",resize:"none",backgroundColor:"inherit"},Ve(e.colorTextPlaceholder)),[`${r}-measure`]:{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,zIndex:-1,color:"transparent",pointerEvents:"none","> span":{display:"inline-block",minHeight:"1em"}},"&-dropdown":c(c({},Q(e)),{position:"absolute",top:-9999,insetInlineStart:-9999,zIndex:e.zIndexPopup,boxSizing:"border-box",fontSize:O,fontVariant:"initial",backgroundColor:S,borderRadius:g,outline:"none",boxShadow:x,"&-hidden":{display:"none"},[`${r}-dropdown-menu`]:{maxHeight:e.dropdownHeight,marginBottom:0,paddingInlineStart:0,overflow:"auto",listStyle:"none",outline:"none","&-item":c(c({},ye),{position:"relative",display:"block",minWidth:e.controlItemWidth,padding:`${y}px ${i}px`,color:p,fontWeight:"normal",lineHeight:l,cursor:"pointer",transition:`background ${a} ease`,"&:hover":{backgroundColor:t},"&:first-child":{borderStartStartRadius:g,borderStartEndRadius:g,borderEndStartRadius:0,borderEndEndRadius:0},"&:last-child":{borderStartStartRadius:0,borderStartEndRadius:0,borderEndStartRadius:g,borderEndEndRadius:g},"&-disabled":{color:n,cursor:"not-allowed","&:hover":{color:n,backgroundColor:t,cursor:"not-allowed"}},"&-selected":{color:p,fontWeight:e.fontWeightStrong,backgroundColor:t},"&-active":{backgroundColor:t}})}})})}},st=Se("Mentions",e=>{const r=_e(e);return[at(r)]},e=>({dropdownHeight:250,controlItemWidth:100,zIndexPopup:e.zIndexPopupBase+50}));var re=function(e,r){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&r.indexOf(t)<0&&(n[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,t=Object.getOwnPropertySymbols(e);i<t.length;i++)r.indexOf(t[i])<0&&Object.prototype.propertyIsEnumerable.call(e,t[i])&&(n[t[i]]=e[t[i]]);return n};function lt(){return!0}const ut=function(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const{prefix:n="@",split:t=" "}=r,i=Array.isArray(n)?n:[n];return e.split(t).map(function(){let p=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",a=null;return i.some(l=>p.slice(0,l.length)===l?(a=l,!0):!1),a!==null?{prefix:a,value:p.slice(a.length)}:null}).filter(p=>!!p&&!!p.value)},ct=()=>c(c({},ue),{loading:{type:Boolean,default:void 0},onFocus:{type:Function},onBlur:{type:Function},onSelect:{type:Function},onChange:{type:Function},onPressenter:{type:Function},"onUpdate:value":{type:Function},notFoundContent:B.any,defaultValue:String,id:String,status:String}),G=H({compatConfig:{MODE:3},name:"AMentions",inheritAttrs:!1,props:ct(),slots:Object,setup(e,r){let{slots:n,emit:t,attrs:i,expose:p}=r;var a,l;const{prefixCls:d,renderEmpty:u,direction:b}=Ce("mentions",e),[O,S]=st(d),g=W(!1),x=W(null),y=W((l=(a=e.value)!==null&&a!==void 0?a:e.defaultValue)!==null&&l!==void 0?l:""),C=Re(),w=De.useInject(),T=j(()=>ze(w.status,e.status));Te({prefixCls:j(()=>`${d.value}-menu`),mode:j(()=>"vertical"),selectable:j(()=>!1),onClick:()=>{},validator:o=>{let{mode:f}=o}}),Ae(()=>e.value,o=>{y.value=o});const A=o=>{g.value=!0,t("focus",o)},F=o=>{g.value=!1,t("blur",o),C.onFieldBlur()},L=function(){for(var o=arguments.length,f=new Array(o),m=0;m<o;m++)f[m]=arguments[m];t("select",...f),g.value=!0},K=o=>{e.value===void 0&&(y.value=o),t("update:value",o),t("change",o),C.onFieldChange()},U=()=>{const o=e.notFoundContent;return o!==void 0?o:n.notFoundContent?n.notFoundContent():u("Select")},R=()=>{var o;return we(((o=n.default)===null||o===void 0?void 0:o.call(n))||[]).map(f=>{var m,v;return c(c({},Pe(f)),{label:(v=(m=f.children)===null||m===void 0?void 0:m.default)===null||v===void 0?void 0:v.call(m)})})};p({focus:()=>{x.value.focus()},blur:()=>{x.value.blur()}});const s=j(()=>e.loading?lt:e.filterOption);return()=>{const{disabled:o,getPopupContainer:f,rows:m=1,id:v=C.id.value}=e,V=re(e,["disabled","getPopupContainer","rows","id"]),{hasFeedback:E,feedbackIcon:D}=w,{class:N}=i,$=re(i,["class"]),M=ae(V,["defaultValue","onUpdate:value","prefixCls"]),P=q({[`${d.value}-disabled`]:o,[`${d.value}-focused`]:g.value,[`${d.value}-rtl`]:b.value==="rtl"},ee(d.value,T.value),!E&&N,S.value),z=c(c(c(c({prefixCls:d.value},M),{disabled:o,direction:b.value,filterOption:s.value,getPopupContainer:f,options:e.loading?[{value:"ANTDV_SEARCHING",disabled:!0,label:h(se,{size:"small"},null)}]:e.options||R(),class:P}),$),{rows:m,onChange:K,onSelect:L,onFocus:A,onBlur:F,ref:x,value:y.value,id:v}),_=h(rt,k(k({},z),{},{dropdownClassName:S.value}),{notFoundContent:U,option:n.option});return O(E?h("div",{class:q(`${d.value}-affix-wrapper`,ee(`${d.value}-affix-wrapper`,T.value,E),N,S.value)},[_,h("span",{class:`${d.value}-suffix`},[D])]):_)}}}),J=H(c(c({compatConfig:{MODE:3}},fe),{name:"AMentionsOption",props:pe})),Mt=c(G,{Option:J,getMentions:ut,install:e=>(e.component(G.name,G),e.component(J.name,J),e)});export{J as MentionsOption,Mt as default,ct as mentionsProps};
