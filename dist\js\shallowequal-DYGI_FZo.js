import{r as a}from"../jse/index-index-DYNcUVMZ.js";function i(t,e,p,y){let r;if(r!==void 0)return!!r;if(t===e)return!0;if(typeof t!="object"||!t||typeof e!="object"||!e)return!1;const n=Object.keys(t),l=Object.keys(e);if(n.length!==l.length)return!1;const f=Object.prototype.hasOwnProperty.bind(e);for(let s=0;s<n.length;s++){const o=n[s];if(!f(o))return!1;const u=t[o],c=e[o];if(r=void 0,r===!1||r===void 0&&u!==c)return!1}return!0}function h(t,e){return i(a(t),a(e))}export{h as s};
