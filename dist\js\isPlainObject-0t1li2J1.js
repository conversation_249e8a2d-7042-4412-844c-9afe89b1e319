import{aC as a,ao as c,aD as n}from"./bootstrap-CFDAkNgp.js";var i=a(Object.getPrototypeOf,Object),s="[object Object]",b=Function.prototype,p=Object.prototype,e=b.toString,f=p.hasOwnProperty,j=e.call(Object);function O(r){if(!c(r)||n(r)!=s)return!1;var o=i(r);if(o===null)return!0;var t=f.call(o,"constructor")&&o.constructor;return typeof t=="function"&&t instanceof t&&e.call(t)==j}export{i as g,O as i};
