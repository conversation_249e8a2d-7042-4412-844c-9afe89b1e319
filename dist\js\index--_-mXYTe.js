import{_ as n}from"./bootstrap-CFDAkNgp.js";import{g as c,a}from"./index-CPEaeSbW.js";import"../jse/index-index-B2UBupFX.js";import"./index-BhH5F5SY.js";import"./colors-KzMfSzFw.js";import"./useMergedState-C4x1IDb9.js";import"./Trigger-D2zZP_An.js";import"./ResizeObserver.es-CDE7jhPe.js";import"./move-IXaXzbNk.js";import"./shallowequal-CNCY1mYq.js";import"./FormItemContext-CoieKSxA.js";import"./statusUtils-D62pPzYs.js";import"./index-C5ScQeGh.js";import"./slide-BhgK1D9k.js";const{DatePicker:r,WeekPicker:t,MonthPicker:o,YearPicker:k,TimePicker:p,QuarterPicker:m,RangePicker:i}=c(a),R=n(r,{WeekPicker:t,MonthPicker:o,YearPicker:k,RangePicker:i,TimePicker:p,QuarterPicker:m,install:e=>(e.component(r.name,r),e.component(i.name,i),e.component(o.name,o),e.component(t.name,t),e.component(m.name,m),e)});export{o as MonthPicker,m as QuarterPicker,i as RangePicker,t as WeekPicker,R as default};
