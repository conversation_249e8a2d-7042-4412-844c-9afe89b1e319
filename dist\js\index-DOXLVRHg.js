import{aO as $t,P as A,b as z,j as W,B as at,cb as Yt,z as Ae,K as re,_ as d,G as ae,D as j,k as Ee,cc as Re,I as Ut,g as rt,m as lt,r as st,n as dt,aN as Lt,h as Me,f as Mt,o as ct,i as Zt,Q as Jt,x as ke,a as ue,a9 as Qt,e as ea,s as ze,v as ta,H as aa,a2 as je,a3 as na,cd as Ie,ce as oa,cf as ia,cg as Ue,aG as Ze}from"./bootstrap-CFDAkNgp.js";import{a4 as k,P as Q,Y as ce,J as D,x as s,F as ra,a5 as V,az as ut,ao as We,a9 as Ge,R as la,aF as sa,ba as yt}from"../jse/index-index-B2UBupFX.js";import{u as da,E as ca,M as ua,a as va}from"./index-1BiZfdtR.js";import{m as pa,T as fa}from"./Trigger-D2zZP_An.js";import{R as St}from"./index-DQZjs6Lb.js";import{u as ba}from"./useRefs-f0KzY-v7.js";import{f as Ot,t as ga,a as ha,c as ma,h as $a,g as ya}from"./hasIn-Bt_d2Zq4.js";import{i as Sa}from"./isMobile-8sZ0LT6r.js";import{u as xt}from"./useMergedState-C4x1IDb9.js";import{i as Ct}from"./slide-BhgK1D9k.js";import{i as xa}from"./isPlainObject-0t1li2J1.js";function Ca(e,t,a,n){if(!$t(e))return e;t=Ot(t,e);for(var o=-1,r=t.length,i=r-1,c=e;c!=null&&++o<r;){var g=ga(t[o]),v=a;if(g==="__proto__"||g==="constructor"||g==="prototype")return e;if(o!=i){var b=c[g];v=void 0,v===void 0&&(v=$t(b)?b:pa(t[o+1])?[]:{})}ha(c,g,v),c=c[g]}return e}function wa(e,t,a){for(var n=-1,o=t.length,r={};++n<o;){var i=t[n],c=ma(e,i);a(c,i)&&Ca(r,Ot(i,e),c)}return r}function Ta(e,t){return wa(e,t,function(a,n){return $a(e,n)})}var Dt=ya(function(e,t){return e==null?{}:Ta(e,t)});const we={adjustX:1,adjustY:1},Te=[0,0],_a={topLeft:{points:["bl","tl"],overflow:we,offset:[0,-4],targetOffset:Te},topCenter:{points:["bc","tc"],overflow:we,offset:[0,-4],targetOffset:Te},topRight:{points:["br","tr"],overflow:we,offset:[0,-4],targetOffset:Te},bottomLeft:{points:["tl","bl"],overflow:we,offset:[0,4],targetOffset:Te},bottomCenter:{points:["tc","bc"],overflow:we,offset:[0,4],targetOffset:Te},bottomRight:{points:["tr","br"],overflow:we,offset:[0,4],targetOffset:Te}};var Pa=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(a[n[o]]=e[n[o]]);return a};const Ba=k({compatConfig:{MODE:3},props:{minOverlayWidthMatchTrigger:{type:Boolean,default:void 0},arrow:{type:Boolean,default:!1},prefixCls:A.string.def("rc-dropdown"),transitionName:String,overlayClassName:A.string.def(""),openClassName:String,animation:A.any,align:A.object,overlayStyle:{type:Object,default:void 0},placement:A.string.def("bottomLeft"),overlay:A.any,trigger:A.oneOfType([A.string,A.arrayOf(A.string)]).def("hover"),alignPoint:{type:Boolean,default:void 0},showAction:A.array,hideAction:A.array,getPopupContainer:Function,visible:{type:Boolean,default:void 0},defaultVisible:{type:Boolean,default:!1},mouseEnterDelay:A.number.def(.15),mouseLeaveDelay:A.number.def(.1)},emits:["visibleChange","overlayClick"],setup(e,t){let{slots:a,emit:n,expose:o}=t;const r=Q(!!e.visible);ce(()=>e.visible,l=>{l!==void 0&&(r.value=l)});const i=Q();o({triggerRef:i});const c=l=>{e.visible===void 0&&(r.value=!1),n("overlayClick",l)},g=l=>{e.visible===void 0&&(r.value=l),n("visibleChange",l)},v=()=>{var l;const u=(l=a.overlay)===null||l===void 0?void 0:l.call(a),m={prefixCls:`${e.prefixCls}-menu`,onClick:c};return s(ra,{key:Yt},[e.arrow&&s("div",{class:`${e.prefixCls}-arrow`},null),at(u,m,!1)])},b=D(()=>{const{minOverlayWidthMatchTrigger:l=!e.alignPoint}=e;return l}),y=()=>{var l;const u=(l=a.default)===null||l===void 0?void 0:l.call(a);return r.value&&u?at(u[0],{class:e.openClassName||`${e.prefixCls}-open`},!1):u},h=D(()=>!e.hideAction&&e.trigger.indexOf("contextmenu")!==-1?["click"]:e.hideAction);return()=>{const{prefixCls:l,arrow:u,showAction:m,overlayStyle:w,trigger:x,placement:R,align:M,getPopupContainer:O,transitionName:f,animation:S,overlayClassName:p}=e,T=Pa(e,["prefixCls","arrow","showAction","overlayStyle","trigger","placement","align","getPopupContainer","transitionName","animation","overlayClassName"]);return s(fa,z(z({},T),{},{prefixCls:l,ref:i,popupClassName:W(p,{[`${l}-show-arrow`]:u}),popupStyle:w,builtinPlacements:_a,action:x,showAction:m,hideAction:h.value||[],popupPlacement:R,popupAlign:M,popupTransitionName:f,popupAnimation:S,popupVisible:r.value,stretch:b.value?"minWidth":"",onPopupVisibleChange:g,getPopupContainer:O}),{popup:v,default:y})}}});function Ra(e){const t=V(),a=V(!1);function n(){for(var o=arguments.length,r=new Array(o),i=0;i<o;i++)r[i]=arguments[i];a.value||(Ae.cancel(t.value),t.value=Ae(()=>{e(...r)}))}return ut(()=>{a.value=!0,Ae.cancel(t.value)}),n}function Ia(e){const t=V([]),a=V(typeof e=="function"?e():e),n=Ra(()=>{let r=a.value;t.value.forEach(i=>{r=i(r)}),t.value=[],a.value=r});function o(r){t.value.push(r),n()}return[a,o]}const Aa=k({compatConfig:{MODE:3},name:"TabNode",props:{id:{type:String},prefixCls:{type:String},tab:{type:Object},active:{type:Boolean},closable:{type:Boolean},editable:{type:Object},onClick:{type:Function},onResize:{type:Function},renderWrapper:{type:Function},removeAriaLabel:{type:String},onFocus:{type:Function}},emits:["click","resize","remove","focus"],setup(e,t){let{expose:a,attrs:n}=t;const o=Q();function r(g){var v;!((v=e.tab)===null||v===void 0)&&v.disabled||e.onClick(g)}a({domRef:o});function i(g){var v;g.preventDefault(),g.stopPropagation(),e.editable.onEdit("remove",{key:(v=e.tab)===null||v===void 0?void 0:v.key,event:g})}const c=D(()=>{var g;return e.editable&&e.closable!==!1&&!(!((g=e.tab)===null||g===void 0)&&g.disabled)});return()=>{var g;const{prefixCls:v,id:b,active:y,tab:{key:h,tab:l,disabled:u,closeIcon:m},renderWrapper:w,removeAriaLabel:x,editable:R,onFocus:M}=e,O=`${v}-tab`,f=s("div",{key:h,ref:o,class:W(O,{[`${O}-with-remove`]:c.value,[`${O}-active`]:y,[`${O}-disabled`]:u}),style:n.style,onClick:r},[s("div",{role:"tab","aria-selected":y,id:b&&`${b}-tab-${h}`,class:`${O}-btn`,"aria-controls":b&&`${b}-panel-${h}`,"aria-disabled":u,tabindex:u?null:0,onClick:S=>{S.stopPropagation(),r(S)},onKeydown:S=>{[re.SPACE,re.ENTER].includes(S.which)&&(S.preventDefault(),r(S))},onFocus:M},[typeof l=="function"?l():l]),c.value&&s("button",{type:"button","aria-label":x||"remove",tabindex:0,class:`${O}-remove`,onClick:S=>{S.stopPropagation(),i(S)}},[(m==null?void 0:m())||((g=R.removeIcon)===null||g===void 0?void 0:g.call(R))||"×"])]);return w?w(f):f}}}),wt={width:0,height:0,left:0,top:0};function Ea(e,t){const a=Q(new Map);return We(()=>{var n,o;const r=new Map,i=e.value,c=t.value.get((n=i[0])===null||n===void 0?void 0:n.key)||wt,g=c.left+c.width;for(let v=0;v<i.length;v+=1){const{key:b}=i[v];let y=t.value.get(b);y||(y=t.value.get((o=i[v-1])===null||o===void 0?void 0:o.key)||wt);const h=r.get(b)||d({},y);h.right=g-h.left-h.width,r.set(b,h)}a.value=new Map(r)}),a}const Ht=k({compatConfig:{MODE:3},name:"AddButton",inheritAttrs:!1,props:{prefixCls:String,editable:{type:Object},locale:{type:Object,default:void 0}},setup(e,t){let{expose:a,attrs:n}=t;const o=Q();return a({domRef:o}),()=>{const{prefixCls:r,editable:i,locale:c}=e;return!i||i.showAdd===!1?null:s("button",{ref:o,type:"button",class:`${r}-nav-add`,style:n.style,"aria-label":(c==null?void 0:c.addAriaLabel)||"Add tab",onClick:g=>{i.onEdit("add",{event:g})}},[i.addIcon?i.addIcon():"+"])}}}),La={prefixCls:{type:String},id:{type:String},tabs:{type:Object},rtl:{type:Boolean},tabBarGutter:{type:Number},activeKey:{type:[String,Number]},mobile:{type:Boolean},moreIcon:A.any,moreTransitionName:{type:String},editable:{type:Object},locale:{type:Object,default:void 0},removeAriaLabel:String,onTabClick:{type:Function},popupClassName:String,getPopupContainer:ae()},Ma=k({compatConfig:{MODE:3},name:"OperationNode",inheritAttrs:!1,props:La,emits:["tabClick"],slots:Object,setup(e,t){let{attrs:a,slots:n}=t;const[o,r]=j(!1),[i,c]=j(null),g=l=>{const u=e.tabs.filter(x=>!x.disabled);let m=u.findIndex(x=>x.key===i.value)||0;const w=u.length;for(let x=0;x<w;x+=1){m=(m+l+w)%w;const R=u[m];if(!R.disabled){c(R.key);return}}},v=l=>{const{which:u}=l;if(!o.value){[re.DOWN,re.SPACE,re.ENTER].includes(u)&&(r(!0),l.preventDefault());return}switch(u){case re.UP:g(-1),l.preventDefault();break;case re.DOWN:g(1),l.preventDefault();break;case re.ESC:r(!1);break;case re.SPACE:case re.ENTER:i.value!==null&&e.onTabClick(i.value,l);break}},b=D(()=>`${e.id}-more-popup`),y=D(()=>i.value!==null?`${b.value}-${i.value}`:null),h=(l,u)=>{l.preventDefault(),l.stopPropagation(),e.editable.onEdit("remove",{key:u,event:l})};return Ge(()=>{ce(i,()=>{const l=document.getElementById(y.value);l&&l.scrollIntoView&&l.scrollIntoView(!1)},{flush:"post",immediate:!0})}),ce(o,()=>{o.value||c(null)}),da({}),()=>{var l;const{prefixCls:u,id:m,tabs:w,locale:x,mobile:R,moreIcon:M=((l=n.moreIcon)===null||l===void 0?void 0:l.call(n))||s(ca,null,null),moreTransitionName:O,editable:f,tabBarGutter:S,rtl:p,onTabClick:T,popupClassName:E}=e;if(!w.length)return null;const I=`${u}-dropdown`,K=x==null?void 0:x.dropdownAriaLabel,ee={[p?"marginRight":"marginLeft"]:S};w.length||(ee.visibility="hidden",ee.order=1);const ne=W({[`${I}-rtl`]:p,[`${E}`]:!0}),le=R?null:s(Ba,{prefixCls:I,trigger:["hover"],visible:o.value,transitionName:O,onVisibleChange:r,overlayClassName:ne,mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:e.getPopupContainer},{overlay:()=>s(ua,{onClick:B=>{let{key:X,domEvent:_}=B;T(X,_),r(!1)},id:b.value,tabindex:-1,role:"listbox","aria-activedescendant":y.value,selectedKeys:[i.value],"aria-label":K!==void 0?K:"expanded dropdown"},{default:()=>[w.map(B=>{var X,_;const U=f&&B.closable!==!1&&!B.disabled;return s(va,{key:B.key,id:`${b.value}-${B.key}`,role:"option","aria-controls":m&&`${m}-panel-${B.key}`,disabled:B.disabled},{default:()=>[s("span",null,[typeof B.tab=="function"?B.tab():B.tab]),U&&s("button",{type:"button","aria-label":e.removeAriaLabel||"remove",tabindex:0,class:`${I}-menu-item-remove`,onClick:Z=>{Z.stopPropagation(),h(Z,B.key)}},[((X=B.closeIcon)===null||X===void 0?void 0:X.call(B))||((_=f.removeIcon)===null||_===void 0?void 0:_.call(f))||"×"])]})})]}),default:()=>s("button",{type:"button",class:`${u}-nav-more`,style:ee,tabindex:-1,"aria-hidden":"true","aria-haspopup":"listbox","aria-controls":b.value,id:`${m}-more`,"aria-expanded":o.value,onKeydown:v},[M])});return s("div",{class:W(`${u}-nav-operations`,a.class),style:a.style},[le,s(Ht,{prefixCls:u,locale:x,editable:f},null)])}}}),zt=Symbol("tabsContextKey"),Oa=e=>{sa(zt,e)},kt=()=>la(zt,{tabs:Q([]),prefixCls:Q()}),Da=.1,Tt=.01,Ne=20,_t=Math.pow(.995,Ne);function Ha(e,t){const[a,n]=j(),[o,r]=j(0),[i,c]=j(0),[g,v]=j(),b=Q();function y(f){const{screenX:S,screenY:p}=f.touches[0];n({x:S,y:p}),clearInterval(b.value)}function h(f){if(!a.value)return;f.preventDefault();const{screenX:S,screenY:p}=f.touches[0],T=S-a.value.x,E=p-a.value.y;t(T,E),n({x:S,y:p});const I=Date.now();c(I-o.value),r(I),v({x:T,y:E})}function l(){if(!a.value)return;const f=g.value;if(n(null),v(null),f){const S=f.x/i.value,p=f.y/i.value,T=Math.abs(S),E=Math.abs(p);if(Math.max(T,E)<Da)return;let I=S,K=p;b.value=setInterval(()=>{if(Math.abs(I)<Tt&&Math.abs(K)<Tt){clearInterval(b.value);return}I*=_t,K*=_t,t(I*Ne,K*Ne)},Ne)}}const u=Q();function m(f){const{deltaX:S,deltaY:p}=f;let T=0;const E=Math.abs(S),I=Math.abs(p);E===I?T=u.value==="x"?S:p:E>I?(T=S,u.value="x"):(T=p,u.value="y"),t(-T,-T)&&f.preventDefault()}const w=Q({onTouchStart:y,onTouchMove:h,onTouchEnd:l,onWheel:m});function x(f){w.value.onTouchStart(f)}function R(f){w.value.onTouchMove(f)}function M(f){w.value.onTouchEnd(f)}function O(f){w.value.onWheel(f)}Ge(()=>{var f,S;document.addEventListener("touchmove",R,{passive:!1}),document.addEventListener("touchend",M,{passive:!1}),(f=e.value)===null||f===void 0||f.addEventListener("touchstart",x,{passive:!1}),(S=e.value)===null||S===void 0||S.addEventListener("wheel",O,{passive:!1})}),ut(()=>{document.removeEventListener("touchmove",R),document.removeEventListener("touchend",M)})}function Pt(e,t){const a=Q(e);function n(o){const r=typeof o=="function"?o(a.value):o;r!==a.value&&t(r,a.value),a.value=r}return[a,n]}const Bt={width:0,height:0,left:0,top:0,right:0},za=()=>({id:{type:String},tabPosition:{type:String},activeKey:{type:[String,Number]},rtl:{type:Boolean},animated:Ee(),editable:Ee(),moreIcon:A.any,moreTransitionName:{type:String},mobile:{type:Boolean},tabBarGutter:{type:Number},renderTabBar:{type:Function},locale:Ee(),popupClassName:String,getPopupContainer:ae(),onTabClick:{type:Function},onTabScroll:{type:Function}}),ka=(e,t)=>{const{offsetWidth:a,offsetHeight:n,offsetTop:o,offsetLeft:r}=e,{width:i,height:c,x:g,y:v}=e.getBoundingClientRect();return Math.abs(i-a)<1?[i,c,g-t.x,v-t.y]:[a,n,r,o]},Rt=k({compatConfig:{MODE:3},name:"TabNavList",inheritAttrs:!1,props:za(),slots:Object,emits:["tabClick","tabScroll"],setup(e,t){let{attrs:a,slots:n}=t;const{tabs:o,prefixCls:r}=kt(),i=V(),c=V(),g=V(),v=V(),[b,y]=ba(),h=D(()=>e.tabPosition==="top"||e.tabPosition==="bottom"),[l,u]=Pt(0,(C,$)=>{h.value&&e.onTabScroll&&e.onTabScroll({direction:C>$?"left":"right"})}),[m,w]=Pt(0,(C,$)=>{!h.value&&e.onTabScroll&&e.onTabScroll({direction:C>$?"top":"bottom"})}),[x,R]=j(0),[M,O]=j(0),[f,S]=j(null),[p,T]=j(null),[E,I]=j(0),[K,ee]=j(0),[ne,le]=Ia(new Map),B=Ea(o,ne),X=D(()=>`${r.value}-nav-operations-hidden`),_=V(0),U=V(0);We(()=>{h.value?e.rtl?(_.value=0,U.value=Math.max(0,x.value-f.value)):(_.value=Math.min(0,f.value-x.value),U.value=0):(_.value=Math.min(0,p.value-M.value),U.value=0)});const Z=C=>C<_.value?_.value:C>U.value?U.value:C,ve=V(),[F,pe]=j(),fe=()=>{pe(Date.now())},me=()=>{clearTimeout(ve.value)},ye=(C,$)=>{C(P=>Z(P+$))};Ha(i,(C,$)=>{if(h.value){if(f.value>=x.value)return!1;ye(u,C)}else{if(p.value>=M.value)return!1;ye(w,$)}return me(),fe(),!0}),ce(F,()=>{me(),F.value&&(ve.value=setTimeout(()=>{pe(0)},100))});const be=function(){let C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:e.activeKey;const $=B.value.get(C)||{width:0,height:0,left:0,right:0,top:0};if(h.value){let P=l.value;e.rtl?$.right<l.value?P=$.right:$.right+$.width>l.value+f.value&&(P=$.right+$.width-f.value):$.left<-l.value?P=-$.left:$.left+$.width>-l.value+f.value&&(P=-($.left+$.width-f.value)),w(0),u(Z(P))}else{let P=m.value;$.top<-m.value?P=-$.top:$.top+$.height>-m.value+p.value&&(P=-($.top+$.height-p.value)),u(0),w(Z(P))}},se=V(0),Se=V(0);We(()=>{let C,$,P,L,N,H;const oe=B.value;["top","bottom"].includes(e.tabPosition)?(C="width",L=f.value,N=x.value,H=E.value,$=e.rtl?"right":"left",P=Math.abs(l.value)):(C="height",L=p.value,N=x.value,H=K.value,$="top",P=-m.value);let q=L;N+H>L&&N<L&&(q=L-H);const te=o.value;if(!te.length)return[se.value,Se.value]=[0,0];const ie=te.length;let he=ie;for(let J=0;J<ie;J+=1){const de=oe.get(te[J].key)||Bt;if(de[$]+de[C]>P+q){he=J-1;break}}let G=0;for(let J=ie-1;J>=0;J-=1)if((oe.get(te[J].key)||Bt)[$]<P){G=J+1;break}return[se.value,Se.value]=[G,he]});const $e=()=>{le(()=>{var C;const $=new Map,P=(C=c.value)===null||C===void 0?void 0:C.getBoundingClientRect();return o.value.forEach(L=>{let{key:N}=L;const H=y.value.get(N),oe=(H==null?void 0:H.$el)||H;if(oe){const[q,te,ie,he]=ka(oe,P);$.set(N,{width:q,height:te,left:ie,top:he})}}),$})};ce(()=>o.value.map(C=>C.key).join("%%"),()=>{$e()},{flush:"post"});const xe=()=>{var C,$,P,L,N;const H=((C=i.value)===null||C===void 0?void 0:C.offsetWidth)||0,oe=(($=i.value)===null||$===void 0?void 0:$.offsetHeight)||0,q=((P=v.value)===null||P===void 0?void 0:P.$el)||{},te=q.offsetWidth||0,ie=q.offsetHeight||0;S(H),T(oe),I(te),ee(ie);const he=(((L=c.value)===null||L===void 0?void 0:L.offsetWidth)||0)-te,G=(((N=c.value)===null||N===void 0?void 0:N.offsetHeight)||0)-ie;R(he),O(G),$e()},Ce=D(()=>[...o.value.slice(0,se.value),...o.value.slice(Se.value+1)]),[Xt,Ft]=j(),ge=D(()=>B.value.get(e.activeKey)),ft=V(),bt=()=>{Ae.cancel(ft.value)};ce([ge,h,()=>e.rtl],()=>{const C={};ge.value&&(h.value?(e.rtl?C.right=Re(ge.value.right):C.left=Re(ge.value.left),C.width=Re(ge.value.width)):(C.top=Re(ge.value.top),C.height=Re(ge.value.height))),bt(),ft.value=Ae(()=>{Ft(C)})}),ce([()=>e.activeKey,ge,B,h],()=>{be()},{flush:"post"}),ce([()=>e.rtl,()=>e.tabBarGutter,()=>e.activeKey,()=>o.value],()=>{xe()},{flush:"post"});const Fe=C=>{let{position:$,prefixCls:P,extra:L}=C;if(!L)return null;const N=L==null?void 0:L({position:$});return N?s("div",{class:`${P}-extra-content`},[N]):null};return ut(()=>{me(),bt()}),()=>{const{id:C,animated:$,activeKey:P,rtl:L,editable:N,locale:H,tabPosition:oe,tabBarGutter:q,onTabClick:te}=e,{class:ie,style:he}=a,G=r.value,J=!!Ce.value.length,de=`${G}-nav-wrap`;let qe,Ve,gt,ht;h.value?L?(Ve=l.value>0,qe=l.value+f.value<x.value):(qe=l.value<0,Ve=-l.value+f.value<x.value):(gt=m.value<0,ht=-m.value+p.value<M.value);const He={};oe==="top"||oe==="bottom"?He[L?"marginRight":"marginLeft"]=typeof q=="number"?`${q}px`:q:He.marginTop=typeof q=="number"?`${q}px`:q;const mt=o.value.map((Ye,qt)=>{const{key:Be}=Ye;return s(Aa,{id:C,prefixCls:G,key:Be,tab:Ye,style:qt===0?void 0:He,closable:Ye.closable,editable:N,active:Be===P,removeAriaLabel:H==null?void 0:H.removeAriaLabel,ref:b(Be),onClick:Vt=>{te(Be,Vt)},onFocus:()=>{be(Be),fe(),i.value&&(L||(i.value.scrollLeft=0),i.value.scrollTop=0)}},n)});return s("div",{role:"tablist",class:W(`${G}-nav`,ie),style:he,onKeydown:()=>{fe()}},[s(Fe,{position:"left",prefixCls:G,extra:n.leftExtra},null),s(St,{onResize:xe},{default:()=>[s("div",{class:W(de,{[`${de}-ping-left`]:qe,[`${de}-ping-right`]:Ve,[`${de}-ping-top`]:gt,[`${de}-ping-bottom`]:ht}),ref:i},[s(St,{onResize:xe},{default:()=>[s("div",{ref:c,class:`${G}-nav-list`,style:{transform:`translate(${l.value}px, ${m.value}px)`,transition:F.value?"none":void 0}},[mt,s(Ht,{ref:v,prefixCls:G,locale:H,editable:N,style:d(d({},mt.length===0?void 0:He),{visibility:J?"hidden":null})},null),s("div",{class:W(`${G}-ink-bar`,{[`${G}-ink-bar-animated`]:$.inkBar}),style:Xt.value},null)])]})])]}),s(Ma,z(z({},e),{},{removeAriaLabel:H==null?void 0:H.removeAriaLabel,ref:g,prefixCls:G,tabs:Ce.value,class:!J&&X.value}),Dt(n,["moreIcon"])),s(Fe,{position:"right",prefixCls:G,extra:n.rightExtra},null),s(Fe,{position:"right",prefixCls:G,extra:n.tabBarExtraContent},null)])}}}),Na=k({compatConfig:{MODE:3},name:"TabPanelList",inheritAttrs:!1,props:{activeKey:{type:[String,Number]},id:{type:String},rtl:{type:Boolean},animated:{type:Object,default:void 0},tabPosition:{type:String},destroyInactiveTabPane:{type:Boolean}},setup(e){const{tabs:t,prefixCls:a}=kt();return()=>{const{id:n,activeKey:o,animated:r,tabPosition:i,rtl:c,destroyInactiveTabPane:g}=e,v=r.tabPane,b=a.value,y=t.value.findIndex(h=>h.key===o);return s("div",{class:`${b}-content-holder`},[s("div",{class:[`${b}-content`,`${b}-content-${i}`,{[`${b}-content-animated`]:v}],style:y&&v?{[c?"marginRight":"marginLeft"]:`-${y}00%`}:null},[t.value.map(h=>at(h.node,{key:h.key,prefixCls:b,tabKey:h.key,id:n,animated:v,active:h.key===o,destroyInactiveTabPane:g}))])])}}});var Wa={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"};function It(e){for(var t=1;t<arguments.length;t++){var a=arguments[t]!=null?Object(arguments[t]):{},n=Object.keys(a);typeof Object.getOwnPropertySymbols=="function"&&(n=n.concat(Object.getOwnPropertySymbols(a).filter(function(o){return Object.getOwnPropertyDescriptor(a,o).enumerable}))),n.forEach(function(o){Ga(e,o,a[o])})}return e}function Ga(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}var vt=function(t,a){var n=It({},t,a.attrs);return s(Ut,It({},n,{icon:Wa}),null)};vt.displayName="PlusOutlined";vt.inheritAttrs=!1;const ja=e=>{const{componentCls:t,motionDurationSlow:a}=e;return[{[t]:{[`${t}-switch`]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:`opacity ${a}`}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:`opacity ${a}`}}}}},[Ct(e,"slide-up"),Ct(e,"slide-down")]]},Ka=e=>{const{componentCls:t,tabsCardHorizontalPadding:a,tabsCardHeadBackground:n,tabsCardGutter:o,colorSplit:r}=e;return{[`${t}-card`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{margin:0,padding:a,background:n,border:`${e.lineWidth}px ${e.lineType} ${r}`,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`},[`${t}-tab-active`]:{color:e.colorPrimary,background:e.colorBgContainer},[`${t}-ink-bar`]:{visibility:"hidden"}},[`&${t}-top, &${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginLeft:{_skip_check_:!0,value:`${o}px`}}}},[`&${t}-top`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`${e.borderRadiusLG}px ${e.borderRadiusLG}px 0 0`},[`${t}-tab-active`]:{borderBottomColor:e.colorBgContainer}}},[`&${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`0 0 ${e.borderRadiusLG}px ${e.borderRadiusLG}px`},[`${t}-tab-active`]:{borderTopColor:e.colorBgContainer}}},[`&${t}-left, &${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginTop:`${o}px`}}},[`&${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${e.borderRadiusLG}px 0 0 ${e.borderRadiusLG}px`}},[`${t}-tab-active`]:{borderRightColor:{_skip_check_:!0,value:e.colorBgContainer}}}},[`&${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${e.borderRadiusLG}px ${e.borderRadiusLG}px 0`}},[`${t}-tab-active`]:{borderLeftColor:{_skip_check_:!0,value:e.colorBgContainer}}}}}}},Xa=e=>{const{componentCls:t,tabsHoverColor:a,dropdownEdgeChildVerticalPadding:n}=e;return{[`${t}-dropdown`]:d(d({},st(e)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:e.zIndexPopup,display:"block","&-hidden":{display:"none"},[`${t}-dropdown-menu`]:{maxHeight:e.tabsDropdownHeight,margin:0,padding:`${n}px 0`,overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:e.colorBgContainer,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,"&-item":d(d({},dt),{display:"flex",alignItems:"center",minWidth:e.tabsDropdownWidth,margin:0,padding:`${e.paddingXXS}px ${e.paddingSM}px`,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:e.marginSM},color:e.colorTextDescription,fontSize:e.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:a}},"&:hover":{background:e.controlItemBgHover},"&-disabled":{"&, &:hover":{color:e.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},Fa=e=>{const{componentCls:t,margin:a,colorSplit:n}=e;return{[`${t}-top, ${t}-bottom`]:{flexDirection:"column",[`> ${t}-nav, > div > ${t}-nav`]:{margin:`0 0 ${a}px 0`,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:`${e.lineWidth}px ${e.lineType} ${n}`,content:"''"},[`${t}-ink-bar`]:{height:e.lineWidthBold,"&-animated":{transition:`width ${e.motionDurationSlow}, left ${e.motionDurationSlow},
            right ${e.motionDurationSlow}`}},[`${t}-nav-wrap`]:{"&::before, &::after":{top:0,bottom:0,width:e.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowRight},[`&${t}-nav-wrap-ping-left::before`]:{opacity:1},[`&${t}-nav-wrap-ping-right::after`]:{opacity:1}}}},[`${t}-top`]:{[`> ${t}-nav,
        > div > ${t}-nav`]:{"&::before":{bottom:0},[`${t}-ink-bar`]:{bottom:0}}},[`${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,marginTop:`${a}px`,marginBottom:0,"&::before":{top:0},[`${t}-ink-bar`]:{top:0}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0}},[`${t}-left, ${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{flexDirection:"column",minWidth:e.controlHeight*1.25,[`${t}-tab`]:{padding:`${e.paddingXS}px ${e.paddingLG}px`,textAlign:"center"},[`${t}-tab + ${t}-tab`]:{margin:`${e.margin}px 0 0 0`},[`${t}-nav-wrap`]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:e.controlHeight},"&::before":{top:0,boxShadow:e.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:e.boxShadowTabsOverflowBottom},[`&${t}-nav-wrap-ping-top::before`]:{opacity:1},[`&${t}-nav-wrap-ping-bottom::after`]:{opacity:1}},[`${t}-ink-bar`]:{width:e.lineWidthBold,"&-animated":{transition:`height ${e.motionDurationSlow}, top ${e.motionDurationSlow}`}},[`${t}-nav-list, ${t}-nav-operations`]:{flex:"1 0 auto",flexDirection:"column"}}},[`${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-ink-bar`]:{right:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{marginLeft:{_skip_check_:!0,value:`-${e.lineWidth}px`},borderLeft:{_skip_check_:!0,value:`${e.lineWidth}px ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingLeft:{_skip_check_:!0,value:e.paddingLG}}}},[`${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,[`${t}-ink-bar`]:{left:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0,marginRight:{_skip_check_:!0,value:-e.lineWidth},borderRight:{_skip_check_:!0,value:`${e.lineWidth}px ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingRight:{_skip_check_:!0,value:e.paddingLG}}}}}},qa=e=>{const{componentCls:t,padding:a}=e;return{[t]:{"&-small":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:`${e.paddingXS}px 0`,fontSize:e.fontSize}}},"&-large":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:`${a}px 0`,fontSize:e.fontSizeLG}}}},[`${t}-card`]:{[`&${t}-small`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:`${e.paddingXXS*1.5}px ${a}px`}},[`&${t}-bottom`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`0 0 ${e.borderRadius}px ${e.borderRadius}px`}},[`&${t}-top`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`${e.borderRadius}px ${e.borderRadius}px 0 0`}},[`&${t}-right`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${e.borderRadius}px ${e.borderRadius}px 0`}}},[`&${t}-left`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${e.borderRadius}px 0 0 ${e.borderRadius}px`}}}},[`&${t}-large`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:`${e.paddingXS}px ${a}px ${e.paddingXXS*1.5}px`}}}}}},Va=e=>{const{componentCls:t,tabsActiveColor:a,tabsHoverColor:n,iconCls:o,tabsHorizontalGutter:r}=e,i=`${t}-tab`;return{[i]:{position:"relative",display:"inline-flex",alignItems:"center",padding:`${e.paddingSM}px 0`,fontSize:`${e.fontSize}px`,background:"transparent",border:0,outline:"none",cursor:"pointer","&-btn, &-remove":d({"&:focus:not(:focus-visible), &:active":{color:a}},Lt(e)),"&-btn":{outline:"none",transition:"all 0.3s"},"&-remove":{flex:"none",marginRight:{_skip_check_:!0,value:-e.marginXXS},marginLeft:{_skip_check_:!0,value:e.marginXS},color:e.colorTextDescription,fontSize:e.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"&:hover":{color:e.colorTextHeading}},"&:hover":{color:n},[`&${i}-active ${i}-btn`]:{color:e.colorPrimary,textShadow:e.tabsActiveTextShadow},[`&${i}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed"},[`&${i}-disabled ${i}-btn, &${i}-disabled ${t}-remove`]:{"&:focus, &:active":{color:e.colorTextDisabled}},[`& ${i}-remove ${o}`]:{margin:0},[o]:{marginRight:{_skip_check_:!0,value:e.marginSM}}},[`${i} + ${i}`]:{margin:{_skip_check_:!0,value:`0 0 0 ${r}px`}}}},Ya=e=>{const{componentCls:t,tabsHorizontalGutter:a,iconCls:n,tabsCardGutter:o}=e;return{[`${t}-rtl`]:{direction:"rtl",[`${t}-nav`]:{[`${t}-tab`]:{margin:{_skip_check_:!0,value:`0 0 0 ${a}px`},[`${t}-tab:last-of-type`]:{marginLeft:{_skip_check_:!0,value:0}},[n]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:`${e.marginSM}px`}},[`${t}-tab-remove`]:{marginRight:{_skip_check_:!0,value:`${e.marginXS}px`},marginLeft:{_skip_check_:!0,value:`-${e.marginXXS}px`},[n]:{margin:0}}}},[`&${t}-left`]:{[`> ${t}-nav`]:{order:1},[`> ${t}-content-holder`]:{order:0}},[`&${t}-right`]:{[`> ${t}-nav`]:{order:0},[`> ${t}-content-holder`]:{order:1}},[`&${t}-card${t}-top, &${t}-card${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginRight:{_skip_check_:!0,value:`${o}px`},marginLeft:{_skip_check_:!0,value:0}}}}},[`${t}-dropdown-rtl`]:{direction:"rtl"},[`${t}-menu-item`]:{[`${t}-dropdown-rtl`]:{textAlign:{_skip_check_:!0,value:"right"}}}}},Ua=e=>{const{componentCls:t,tabsCardHorizontalPadding:a,tabsCardHeight:n,tabsCardGutter:o,tabsHoverColor:r,tabsActiveColor:i,colorSplit:c}=e;return{[t]:d(d(d(d({},st(e)),{display:"flex",[`> ${t}-nav, > div > ${t}-nav`]:{position:"relative",display:"flex",flex:"none",alignItems:"center",[`${t}-nav-wrap`]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:`opacity ${e.motionDurationSlow}`,content:"''",pointerEvents:"none"}},[`${t}-nav-list`]:{position:"relative",display:"flex",transition:`opacity ${e.motionDurationSlow}`},[`${t}-nav-operations`]:{display:"flex",alignSelf:"stretch"},[`${t}-nav-operations-hidden`]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},[`${t}-nav-more`]:{position:"relative",padding:a,background:"transparent",border:0,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:e.controlHeightLG/8,transform:"translateY(100%)",content:"''"}},[`${t}-nav-add`]:d({minWidth:`${n}px`,marginLeft:{_skip_check_:!0,value:`${o}px`},padding:`0 ${e.paddingXS}px`,background:"transparent",border:`${e.lineWidth}px ${e.lineType} ${c}`,borderRadius:`${e.borderRadiusLG}px ${e.borderRadiusLG}px 0 0`,outline:"none",cursor:"pointer",color:e.colorText,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`,"&:hover":{color:r},"&:active, &:focus:not(:focus-visible)":{color:i}},Lt(e))},[`${t}-extra-content`]:{flex:"none"},[`${t}-ink-bar`]:{position:"absolute",background:e.colorPrimary,pointerEvents:"none"}}),Va(e)),{[`${t}-content`]:{position:"relative",display:"flex",width:"100%","&-animated":{transition:"margin 0.3s"}},[`${t}-content-holder`]:{flex:"auto",minWidth:0,minHeight:0},[`${t}-tabpane`]:{outline:"none",flex:"none",width:"100%"}}),[`${t}-centered`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-nav-wrap`]:{[`&:not([class*='${t}-nav-wrap-ping'])`]:{justifyContent:"center"}}}}}},Za=rt("Tabs",e=>{const t=e.controlHeightLG,a=lt(e,{tabsHoverColor:e.colorPrimaryHover,tabsActiveColor:e.colorPrimaryActive,tabsCardHorizontalPadding:`${(t-Math.round(e.fontSize*e.lineHeight))/2-e.lineWidth}px ${e.padding}px`,tabsCardHeight:t,tabsCardGutter:e.marginXXS/2,tabsHorizontalGutter:32,tabsCardHeadBackground:e.colorFillAlter,dropdownEdgeChildVerticalPadding:e.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120});return[qa(a),Ya(a),Fa(a),Xa(a),Ka(a),Ua(a),ja(a)]},e=>({zIndexPopup:e.zIndexPopupBase+50}));let At=0;const Nt=()=>({prefixCls:{type:String},id:{type:String},popupClassName:String,getPopupContainer:ae(),activeKey:{type:[String,Number]},defaultActiveKey:{type:[String,Number]},direction:ze(),animated:aa([Boolean,Object]),renderTabBar:ae(),tabBarGutter:{type:Number},tabBarStyle:Ee(),tabPosition:ze(),destroyInactiveTabPane:ta(),hideAdd:Boolean,type:ze(),size:ze(),centered:Boolean,onEdit:ae(),onChange:ae(),onTabClick:ae(),onTabScroll:ae(),"onUpdate:activeKey":ae(),locale:Ee(),onPrevClick:ae(),onNextClick:ae(),tabBarExtraContent:A.any});function Ja(e){return e.map(t=>{if(Zt(t)){const a=d({},t.props||{});for(const[h,l]of Object.entries(a))delete a[h],a[Jt(h)]=l;const n=t.children||{},o=t.key!==void 0?t.key:void 0,{tab:r=n.tab,disabled:i,forceRender:c,closable:g,animated:v,active:b,destroyInactiveTabPane:y}=a;return d(d({key:o},a),{node:t,closeIcon:n.closeIcon,tab:r,disabled:i===""||i,forceRender:c===""||c,closable:g===""||g,animated:v===""||v,active:b===""||b,destroyInactiveTabPane:y===""||y})}return null}).filter(t=>t)}const Qa=k({compatConfig:{MODE:3},name:"InternalTabs",inheritAttrs:!1,props:d(d({},Me(Nt(),{tabPosition:"top",animated:{inkBar:!0,tabPane:!1}})),{tabs:ea()}),slots:Object,setup(e,t){let{attrs:a,slots:n}=t;ke(e.onPrevClick===void 0&&e.onNextClick===void 0,"Tabs","`onPrevClick / @prevClick` and `onNextClick / @nextClick` has been removed. Please use `onTabScroll / @tabScroll` instead."),ke(e.tabBarExtraContent===void 0,"Tabs","`tabBarExtraContent` prop has been removed. Please use `rightExtra` slot instead."),ke(n.tabBarExtraContent===void 0,"Tabs","`tabBarExtraContent` slot is deprecated. Please use `rightExtra` slot instead.");const{prefixCls:o,direction:r,size:i,rootPrefixCls:c,getPopupContainer:g}=ue("tabs",e),[v,b]=Za(o),y=D(()=>r.value==="rtl"),h=D(()=>{const{animated:p,tabPosition:T}=e;return p===!1||["left","right"].includes(T)?{inkBar:!1,tabPane:!1}:p===!0?{inkBar:!0,tabPane:!0}:d({inkBar:!0,tabPane:!1},typeof p=="object"?p:{})}),[l,u]=j(!1);Ge(()=>{u(Sa())});const[m,w]=xt(()=>{var p;return(p=e.tabs[0])===null||p===void 0?void 0:p.key},{value:D(()=>e.activeKey),defaultValue:e.defaultActiveKey}),[x,R]=j(()=>e.tabs.findIndex(p=>p.key===m.value));We(()=>{var p;let T=e.tabs.findIndex(E=>E.key===m.value);T===-1&&(T=Math.max(0,Math.min(x.value,e.tabs.length-1)),w((p=e.tabs[T])===null||p===void 0?void 0:p.key)),R(T)});const[M,O]=xt(null,{value:D(()=>e.id)}),f=D(()=>l.value&&!["left","right"].includes(e.tabPosition)?"top":e.tabPosition);Ge(()=>{e.id||(O(`rc-tabs-${At}`),At+=1)});const S=(p,T)=>{var E,I;(E=e.onTabClick)===null||E===void 0||E.call(e,p,T);const K=p!==m.value;w(p),K&&((I=e.onChange)===null||I===void 0||I.call(e,p))};return Oa({tabs:D(()=>e.tabs),prefixCls:o}),()=>{const{id:p,type:T,tabBarGutter:E,tabBarStyle:I,locale:K,destroyInactiveTabPane:ee,renderTabBar:ne=n.renderTabBar,onTabScroll:le,hideAdd:B,centered:X}=e,_={id:M.value,activeKey:m.value,animated:h.value,tabPosition:f.value,rtl:y.value,mobile:l.value};let U;T==="editable-card"&&(U={onEdit:(pe,fe)=>{let{key:me,event:ye}=fe;var be;(be=e.onEdit)===null||be===void 0||be.call(e,pe==="add"?ye:me,pe)},removeIcon:()=>s(Qt,null,null),addIcon:n.addIcon?n.addIcon:()=>s(vt,null,null),showAdd:B!==!0});let Z;const ve=d(d({},_),{moreTransitionName:`${c.value}-slide-up`,editable:U,locale:K,tabBarGutter:E,onTabClick:S,onTabScroll:le,style:I,getPopupContainer:g.value,popupClassName:W(e.popupClassName,b.value)});ne?Z=ne(d(d({},ve),{DefaultTabBar:Rt})):Z=s(Rt,ve,Dt(n,["moreIcon","leftExtra","rightExtra","tabBarExtraContent"]));const F=o.value;return v(s("div",z(z({},a),{},{id:p,class:W(F,`${F}-${f.value}`,{[b.value]:!0,[`${F}-${i.value}`]:i.value,[`${F}-card`]:["card","editable-card"].includes(T),[`${F}-editable-card`]:T==="editable-card",[`${F}-centered`]:X,[`${F}-mobile`]:l.value,[`${F}-editable`]:T==="editable-card",[`${F}-rtl`]:y.value},a.class)}),[Z,s(Na,z(z({destroyInactiveTabPane:ee},_),{},{animated:h.value}),null)]))}}}),_e=k({compatConfig:{MODE:3},name:"ATabs",inheritAttrs:!1,props:Me(Nt(),{tabPosition:"top",animated:{inkBar:!0,tabPane:!1}}),slots:Object,setup(e,t){let{attrs:a,slots:n,emit:o}=t;const r=i=>{o("update:activeKey",i),o("change",i)};return()=>{var i;const c=Ja(Mt((i=n.default)===null||i===void 0?void 0:i.call(n)));return s(Qa,z(z(z({},ct(e,["onUpdate:activeKey"])),a),{},{onChange:r,tabs:c}),n)}}}),en=()=>({tab:A.any,disabled:{type:Boolean},forceRender:{type:Boolean},closable:{type:Boolean},animated:{type:Boolean},active:{type:Boolean},destroyInactiveTabPane:{type:Boolean},prefixCls:{type:String},tabKey:{type:[String,Number]},id:{type:String}}),nt=k({compatConfig:{MODE:3},name:"ATabPane",inheritAttrs:!1,__ANT_TAB_PANE:!0,props:en(),slots:Object,setup(e,t){let{attrs:a,slots:n}=t;const o=Q(e.forceRender);ce([()=>e.active,()=>e.destroyInactiveTabPane],()=>{e.active?o.value=!0:e.destroyInactiveTabPane&&(o.value=!1)},{immediate:!0});const r=D(()=>e.active?{}:e.animated?{visibility:"hidden",height:0,overflowY:"hidden"}:{display:"none"});return()=>{var i;const{prefixCls:c,forceRender:g,id:v,active:b,tabKey:y}=e;return s("div",{id:v&&`${v}-panel-${y}`,role:"tabpanel",tabindex:b?0:-1,"aria-labelledby":v&&`${v}-tab-${y}`,"aria-hidden":!b,style:[r.value,a.style],class:[`${c}-tabpane`,b&&`${c}-tabpane-active`,a.class]},[(b||o.value||g)&&((i=n.default)===null||i===void 0?void 0:i.call(n))])}}});_e.TabPane=nt;_e.install=function(e){return e.component(_e.name,_e),e.component(nt.name,nt),e};const tn=e=>{const{antCls:t,componentCls:a,cardHeadHeight:n,cardPaddingBase:o,cardHeadTabsMarginBottom:r}=e;return d(d({display:"flex",justifyContent:"center",flexDirection:"column",minHeight:n,marginBottom:-1,padding:`0 ${o}px`,color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG,background:"transparent",borderBottom:`${e.lineWidth}px ${e.lineType} ${e.colorBorderSecondary}`,borderRadius:`${e.borderRadiusLG}px ${e.borderRadiusLG}px 0 0`},je()),{"&-wrapper":{width:"100%",display:"flex",alignItems:"center"},"&-title":d(d({display:"inline-block",flex:1},dt),{[`
          > ${a}-typography,
          > ${a}-typography-edit-content
        `]:{insetInlineStart:0,marginTop:0,marginBottom:0}}),[`${t}-tabs-top`]:{clear:"both",marginBottom:r,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,"&-bar":{borderBottom:`${e.lineWidth}px ${e.lineType} ${e.colorBorderSecondary}`}}})},an=e=>{const{cardPaddingBase:t,colorBorderSecondary:a,cardShadow:n,lineWidth:o}=e;return{width:"33.33%",padding:t,border:0,borderRadius:0,boxShadow:`
      ${o}px 0 0 0 ${a},
      0 ${o}px 0 0 ${a},
      ${o}px ${o}px 0 0 ${a},
      ${o}px 0 0 0 ${a} inset,
      0 ${o}px 0 0 ${a} inset;
    `,transition:`all ${e.motionDurationMid}`,"&-hoverable:hover":{position:"relative",zIndex:1,boxShadow:n}}},nn=e=>{const{componentCls:t,iconCls:a,cardActionsLiMargin:n,cardActionsIconSize:o,colorBorderSecondary:r}=e;return d(d({margin:0,padding:0,listStyle:"none",background:e.colorBgContainer,borderTop:`${e.lineWidth}px ${e.lineType} ${r}`,display:"flex",borderRadius:`0 0 ${e.borderRadiusLG}px ${e.borderRadiusLG}px `},je()),{"& > li":{margin:n,color:e.colorTextDescription,textAlign:"center","> span":{position:"relative",display:"block",minWidth:e.cardActionsIconSize*2,fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer","&:hover":{color:e.colorPrimary,transition:`color ${e.motionDurationMid}`},[`a:not(${t}-btn), > ${a}`]:{display:"inline-block",width:"100%",color:e.colorTextDescription,lineHeight:`${e.fontSize*e.lineHeight}px`,transition:`color ${e.motionDurationMid}`,"&:hover":{color:e.colorPrimary}},[`> ${a}`]:{fontSize:o,lineHeight:`${o*e.lineHeight}px`}},"&:not(:last-child)":{borderInlineEnd:`${e.lineWidth}px ${e.lineType} ${r}`}}})},on=e=>d(d({margin:`-${e.marginXXS}px 0`,display:"flex"},je()),{"&-avatar":{paddingInlineEnd:e.padding},"&-detail":{overflow:"hidden",flex:1,"> div:not(:last-child)":{marginBottom:e.marginXS}},"&-title":d({color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG},dt),"&-description":{color:e.colorTextDescription}}),rn=e=>{const{componentCls:t,cardPaddingBase:a,colorFillAlter:n}=e;return{[`${t}-head`]:{padding:`0 ${a}px`,background:n,"&-title":{fontSize:e.fontSize}},[`${t}-body`]:{padding:`${e.padding}px ${a}px`}}},ln=e=>{const{componentCls:t}=e;return{overflow:"hidden",[`${t}-body`]:{userSelect:"none"}}},sn=e=>{const{componentCls:t,cardShadow:a,cardHeadPadding:n,colorBorderSecondary:o,boxShadow:r,cardPaddingBase:i}=e;return{[t]:d(d({},st(e)),{position:"relative",background:e.colorBgContainer,borderRadius:e.borderRadiusLG,[`&:not(${t}-bordered)`]:{boxShadow:r},[`${t}-head`]:tn(e),[`${t}-extra`]:{marginInlineStart:"auto",color:"",fontWeight:"normal",fontSize:e.fontSize},[`${t}-body`]:d({padding:i,borderRadius:` 0 0 ${e.borderRadiusLG}px ${e.borderRadiusLG}px`},je()),[`${t}-grid`]:an(e),[`${t}-cover`]:{"> *":{display:"block",width:"100%"},img:{borderRadius:`${e.borderRadiusLG}px ${e.borderRadiusLG}px 0 0`}},[`${t}-actions`]:nn(e),[`${t}-meta`]:on(e)}),[`${t}-bordered`]:{border:`${e.lineWidth}px ${e.lineType} ${o}`,[`${t}-cover`]:{marginTop:-1,marginInlineStart:-1,marginInlineEnd:-1}},[`${t}-hoverable`]:{cursor:"pointer",transition:`box-shadow ${e.motionDurationMid}, border-color ${e.motionDurationMid}`,"&:hover":{borderColor:"transparent",boxShadow:a}},[`${t}-contain-grid`]:{[`${t}-body`]:{display:"flex",flexWrap:"wrap"},[`&:not(${t}-loading) ${t}-body`]:{marginBlockStart:-e.lineWidth,marginInlineStart:-e.lineWidth,padding:0}},[`${t}-contain-tabs`]:{[`> ${t}-head`]:{[`${t}-head-title, ${t}-extra`]:{paddingTop:n}}},[`${t}-type-inner`]:rn(e),[`${t}-loading`]:ln(e),[`${t}-rtl`]:{direction:"rtl"}}},dn=e=>{const{componentCls:t,cardPaddingSM:a,cardHeadHeightSM:n}=e;return{[`${t}-small`]:{[`> ${t}-head`]:{minHeight:n,padding:`0 ${a}px`,fontSize:e.fontSize,[`> ${t}-head-wrapper`]:{[`> ${t}-extra`]:{fontSize:e.fontSize}}},[`> ${t}-body`]:{padding:a}},[`${t}-small${t}-contain-tabs`]:{[`> ${t}-head`]:{[`${t}-head-title, ${t}-extra`]:{minHeight:n,paddingTop:0,display:"flex",alignItems:"center"}}}}},cn=rt("Card",e=>{const t=lt(e,{cardShadow:e.boxShadowCard,cardHeadHeight:e.fontSizeLG*e.lineHeightLG+e.padding*2,cardHeadHeightSM:e.fontSize*e.lineHeight+e.paddingXS*2,cardHeadPadding:e.padding,cardPaddingBase:e.paddingLG,cardHeadTabsMarginBottom:-e.padding-e.lineWidth,cardActionsLiMargin:`${e.paddingSM}px 0`,cardActionsIconSize:e.fontSize,cardPaddingSM:12});return[sn(t),dn(t)]}),un=()=>({prefixCls:String,width:{type:[Number,String]}}),pt=k({compatConfig:{MODE:3},name:"SkeletonTitle",props:un(),setup(e){return()=>{const{prefixCls:t,width:a}=e,n=typeof a=="number"?`${a}px`:a;return s("h3",{class:t,style:{width:n}},null)}}}),vn=()=>({prefixCls:String,width:{type:[Number,String,Array]},rows:Number}),pn=k({compatConfig:{MODE:3},name:"SkeletonParagraph",props:vn(),setup(e){const t=a=>{const{width:n,rows:o=2}=e;if(Array.isArray(n))return n[a];if(o-1===a)return n};return()=>{const{prefixCls:a,rows:n}=e,o=[...Array(n)].map((r,i)=>{const c=t(i);return s("li",{key:i,style:{width:typeof c=="number"?`${c}px`:c}},null)});return s("ul",{class:a},[o])}}}),Ke=()=>({prefixCls:String,size:[String,Number],shape:String,active:{type:Boolean,default:void 0}}),Oe=e=>{const{prefixCls:t,size:a,shape:n}=e,o=W({[`${t}-lg`]:a==="large",[`${t}-sm`]:a==="small"}),r=W({[`${t}-circle`]:n==="circle",[`${t}-square`]:n==="square",[`${t}-round`]:n==="round"}),i=typeof a=="number"?{width:`${a}px`,height:`${a}px`,lineHeight:`${a}px`}:{};return s("span",{class:W(t,o,r),style:i},null)};Oe.displayName="SkeletonElement";const fn=new na("ant-skeleton-loading",{"0%":{transform:"translateX(-37.5%)"},"100%":{transform:"translateX(37.5%)"}}),Xe=e=>({height:e,lineHeight:`${e}px`}),Pe=e=>d({width:e},Xe(e)),bn=e=>({position:"relative",zIndex:0,overflow:"hidden",background:"transparent","&::after":{position:"absolute",top:0,insetInlineEnd:"-150%",bottom:0,insetInlineStart:"-150%",background:e.skeletonLoadingBackground,animationName:fn,animationDuration:e.skeletonLoadingMotionDuration,animationTimingFunction:"ease",animationIterationCount:"infinite",content:'""'}}),Je=e=>d({width:e*5,minWidth:e*5},Xe(e)),gn=e=>{const{skeletonAvatarCls:t,color:a,controlHeight:n,controlHeightLG:o,controlHeightSM:r}=e;return{[`${t}`]:d({display:"inline-block",verticalAlign:"top",background:a},Pe(n)),[`${t}${t}-circle`]:{borderRadius:"50%"},[`${t}${t}-lg`]:d({},Pe(o)),[`${t}${t}-sm`]:d({},Pe(r))}},hn=e=>{const{controlHeight:t,borderRadiusSM:a,skeletonInputCls:n,controlHeightLG:o,controlHeightSM:r,color:i}=e;return{[`${n}`]:d({display:"inline-block",verticalAlign:"top",background:i,borderRadius:a},Je(t)),[`${n}-lg`]:d({},Je(o)),[`${n}-sm`]:d({},Je(r))}},Et=e=>d({width:e},Xe(e)),mn=e=>{const{skeletonImageCls:t,imageSizeBase:a,color:n,borderRadiusSM:o}=e;return{[`${t}`]:d(d({display:"flex",alignItems:"center",justifyContent:"center",verticalAlign:"top",background:n,borderRadius:o},Et(a*2)),{[`${t}-path`]:{fill:"#bfbfbf"},[`${t}-svg`]:d(d({},Et(a)),{maxWidth:a*4,maxHeight:a*4}),[`${t}-svg${t}-svg-circle`]:{borderRadius:"50%"}}),[`${t}${t}-circle`]:{borderRadius:"50%"}}},Qe=(e,t,a)=>{const{skeletonButtonCls:n}=e;return{[`${a}${n}-circle`]:{width:t,minWidth:t,borderRadius:"50%"},[`${a}${n}-round`]:{borderRadius:t}}},et=e=>d({width:e*2,minWidth:e*2},Xe(e)),$n=e=>{const{borderRadiusSM:t,skeletonButtonCls:a,controlHeight:n,controlHeightLG:o,controlHeightSM:r,color:i}=e;return d(d(d(d(d({[`${a}`]:d({display:"inline-block",verticalAlign:"top",background:i,borderRadius:t,width:n*2,minWidth:n*2},et(n))},Qe(e,n,a)),{[`${a}-lg`]:d({},et(o))}),Qe(e,o,`${a}-lg`)),{[`${a}-sm`]:d({},et(r))}),Qe(e,r,`${a}-sm`))},yn=e=>{const{componentCls:t,skeletonAvatarCls:a,skeletonTitleCls:n,skeletonParagraphCls:o,skeletonButtonCls:r,skeletonInputCls:i,skeletonImageCls:c,controlHeight:g,controlHeightLG:v,controlHeightSM:b,color:y,padding:h,marginSM:l,borderRadius:u,skeletonTitleHeight:m,skeletonBlockRadius:w,skeletonParagraphLineHeight:x,controlHeightXS:R,skeletonParagraphMarginTop:M}=e;return{[`${t}`]:{display:"table",width:"100%",[`${t}-header`]:{display:"table-cell",paddingInlineEnd:h,verticalAlign:"top",[`${a}`]:d({display:"inline-block",verticalAlign:"top",background:y},Pe(g)),[`${a}-circle`]:{borderRadius:"50%"},[`${a}-lg`]:d({},Pe(v)),[`${a}-sm`]:d({},Pe(b))},[`${t}-content`]:{display:"table-cell",width:"100%",verticalAlign:"top",[`${n}`]:{width:"100%",height:m,background:y,borderRadius:w,[`+ ${o}`]:{marginBlockStart:b}},[`${o}`]:{padding:0,"> li":{width:"100%",height:x,listStyle:"none",background:y,borderRadius:w,"+ li":{marginBlockStart:R}}},[`${o}> li:last-child:not(:first-child):not(:nth-child(2))`]:{width:"61%"}},[`&-round ${t}-content`]:{[`${n}, ${o} > li`]:{borderRadius:u}}},[`${t}-with-avatar ${t}-content`]:{[`${n}`]:{marginBlockStart:l,[`+ ${o}`]:{marginBlockStart:M}}},[`${t}${t}-element`]:d(d(d(d({display:"inline-block",width:"auto"},$n(e)),gn(e)),hn(e)),mn(e)),[`${t}${t}-block`]:{width:"100%",[`${r}`]:{width:"100%"},[`${i}`]:{width:"100%"}},[`${t}${t}-active`]:{[`
        ${n},
        ${o} > li,
        ${a},
        ${r},
        ${i},
        ${c}
      `]:d({},bn(e))}}},De=rt("Skeleton",e=>{const{componentCls:t}=e,a=lt(e,{skeletonAvatarCls:`${t}-avatar`,skeletonTitleCls:`${t}-title`,skeletonParagraphCls:`${t}-paragraph`,skeletonButtonCls:`${t}-button`,skeletonInputCls:`${t}-input`,skeletonImageCls:`${t}-image`,imageSizeBase:e.controlHeight*1.5,skeletonTitleHeight:e.controlHeight/2,skeletonBlockRadius:e.borderRadiusSM,skeletonParagraphLineHeight:e.controlHeight/2,skeletonParagraphMarginTop:e.marginLG+e.marginXXS,borderRadius:100,skeletonLoadingBackground:`linear-gradient(90deg, ${e.color} 25%, ${e.colorGradientEnd} 37%, ${e.color} 63%)`,skeletonLoadingMotionDuration:"1.4s"});return[yn(a)]},e=>{const{colorFillContent:t,colorFill:a}=e;return{color:t,colorGradientEnd:a}}),Sn=()=>({active:{type:Boolean,default:void 0},loading:{type:Boolean,default:void 0},prefixCls:String,avatar:{type:[Boolean,Object],default:void 0},title:{type:[Boolean,Object],default:void 0},paragraph:{type:[Boolean,Object],default:void 0},round:{type:Boolean,default:void 0}});function tt(e){return e&&typeof e=="object"?e:{}}function xn(e,t){return e&&!t?{size:"large",shape:"square"}:{size:"large",shape:"circle"}}function Cn(e,t){return!e&&t?{width:"38%"}:e&&t?{width:"50%"}:{}}function wn(e,t){const a={};return(!e||!t)&&(a.width="61%"),!e&&t?a.rows=3:a.rows=2,a}const Y=k({compatConfig:{MODE:3},name:"ASkeleton",props:Me(Sn(),{avatar:!1,title:!0,paragraph:!0}),setup(e,t){let{slots:a}=t;const{prefixCls:n,direction:o}=ue("skeleton",e),[r,i]=De(n);return()=>{var c;const{loading:g,avatar:v,title:b,paragraph:y,active:h,round:l}=e,u=n.value;if(g||e.loading===void 0){const m=!!v||v==="",w=!!b||b==="",x=!!y||y==="";let R;if(m){const f=d(d({prefixCls:`${u}-avatar`},xn(w,x)),tt(v));R=s("div",{class:`${u}-header`},[s(Oe,f,null)])}let M;if(w||x){let f;if(w){const p=d(d({prefixCls:`${u}-title`},Cn(m,x)),tt(b));f=s(pt,p,null)}let S;if(x){const p=d(d({prefixCls:`${u}-paragraph`},wn(m,w)),tt(y));S=s(pn,p,null)}M=s("div",{class:`${u}-content`},[f,S])}const O=W(u,{[`${u}-with-avatar`]:m,[`${u}-active`]:h,[`${u}-rtl`]:o.value==="rtl",[`${u}-round`]:l,[i.value]:!0});return r(s("div",{class:O},[R,M]))}return(c=a.default)===null||c===void 0?void 0:c.call(a)}}}),Tn=()=>d(d({},Ke()),{size:String,block:Boolean}),Wt=k({compatConfig:{MODE:3},name:"ASkeletonButton",props:Me(Tn(),{size:"default"}),setup(e){const{prefixCls:t}=ue("skeleton",e),[a,n]=De(t),o=D(()=>W(t.value,`${t.value}-element`,{[`${t.value}-active`]:e.active,[`${t.value}-block`]:e.block},n.value));return()=>a(s("div",{class:o.value},[s(Oe,z(z({},e),{},{prefixCls:`${t.value}-button`}),null)]))}}),Gt=k({compatConfig:{MODE:3},name:"ASkeletonInput",props:d(d({},ct(Ke(),["shape"])),{size:String,block:Boolean}),setup(e){const{prefixCls:t}=ue("skeleton",e),[a,n]=De(t),o=D(()=>W(t.value,`${t.value}-element`,{[`${t.value}-active`]:e.active,[`${t.value}-block`]:e.block},n.value));return()=>a(s("div",{class:o.value},[s(Oe,z(z({},e),{},{prefixCls:`${t.value}-input`}),null)]))}}),_n="M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z",jt=k({compatConfig:{MODE:3},name:"ASkeletonImage",props:ct(Ke(),["size","shape","active"]),setup(e){const{prefixCls:t}=ue("skeleton",e),[a,n]=De(t),o=D(()=>W(t.value,`${t.value}-element`,n.value));return()=>a(s("div",{class:o.value},[s("div",{class:`${t.value}-image`},[s("svg",{viewBox:"0 0 1098 1024",xmlns:"http://www.w3.org/2000/svg",class:`${t.value}-image-svg`},[s("path",{d:_n,class:`${t.value}-image-path`},null)])])]))}}),Pn=()=>d(d({},Ke()),{shape:String}),Kt=k({compatConfig:{MODE:3},name:"ASkeletonAvatar",props:Me(Pn(),{size:"default",shape:"circle"}),setup(e){const{prefixCls:t}=ue("skeleton",e),[a,n]=De(t),o=D(()=>W(t.value,`${t.value}-element`,{[`${t.value}-active`]:e.active},n.value));return()=>a(s("div",{class:o.value},[s(Oe,z(z({},e),{},{prefixCls:`${t.value}-avatar`}),null)]))}});Y.Button=Wt;Y.Avatar=Kt;Y.Input=Gt;Y.Image=jt;Y.Title=pt;Y.install=function(e){return e.component(Y.name,Y),e.component(Y.Button.name,Wt),e.component(Y.Avatar.name,Kt),e.component(Y.Input.name,Gt),e.component(Y.Image.name,jt),e.component(Y.Title.name,pt),e};const{TabPane:Bn}=_e,Rn=()=>({prefixCls:String,title:A.any,extra:A.any,bordered:{type:Boolean,default:!0},bodyStyle:{type:Object,default:void 0},headStyle:{type:Object,default:void 0},loading:{type:Boolean,default:!1},hoverable:{type:Boolean,default:!1},type:{type:String},size:{type:String},actions:A.any,tabList:{type:Array},tabBarExtraContent:A.any,activeTabKey:String,defaultActiveTabKey:String,cover:A.any,onTabChange:{type:Function}}),Le=k({compatConfig:{MODE:3},name:"ACard",inheritAttrs:!1,props:Rn(),slots:Object,setup(e,t){let{slots:a,attrs:n}=t;const{prefixCls:o,direction:r,size:i}=ue("card",e),[c,g]=cn(o),v=h=>h.map((u,m)=>yt(u)&&!oa(u)||!yt(u)?s("li",{style:{width:`${100/h.length}%`},key:`action-${m}`},[s("span",null,[u])]):null),b=h=>{var l;(l=e.onTabChange)===null||l===void 0||l.call(e,h)},y=function(){let h=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],l;return h.forEach(u=>{u&&xa(u.type)&&u.type.__ANT_CARD_GRID&&(l=!0)}),l};return()=>{var h,l,u,m,w,x;const{headStyle:R={},bodyStyle:M={},loading:O,bordered:f=!0,type:S,tabList:p,hoverable:T,activeTabKey:E,defaultActiveTabKey:I,tabBarExtraContent:K=Ie((h=a.tabBarExtraContent)===null||h===void 0?void 0:h.call(a)),title:ee=Ie((l=a.title)===null||l===void 0?void 0:l.call(a)),extra:ne=Ie((u=a.extra)===null||u===void 0?void 0:u.call(a)),actions:le=Ie((m=a.actions)===null||m===void 0?void 0:m.call(a)),cover:B=Ie((w=a.cover)===null||w===void 0?void 0:w.call(a))}=e,X=Mt((x=a.default)===null||x===void 0?void 0:x.call(a)),_=o.value,U={[`${_}`]:!0,[g.value]:!0,[`${_}-loading`]:O,[`${_}-bordered`]:f,[`${_}-hoverable`]:!!T,[`${_}-contain-grid`]:y(X),[`${_}-contain-tabs`]:p&&p.length,[`${_}-${i.value}`]:i.value,[`${_}-type-${S}`]:!!S,[`${_}-rtl`]:r.value==="rtl"},Z=s(Y,{loading:!0,active:!0,paragraph:{rows:4},title:!1},{default:()=>[X]}),ve=E!==void 0,F={size:"large",[ve?"activeKey":"defaultActiveKey"]:ve?E:I,onChange:b,class:`${_}-head-tabs`};let pe;const fe=p&&p.length?s(_e,F,{default:()=>[p.map(se=>{const{tab:Se,slots:$e}=se,xe=$e==null?void 0:$e.tab;ke(!$e,"Card","tabList slots is deprecated, Please use `customTab` instead.");let Ce=Se!==void 0?Se:a[xe]?a[xe](se):null;return Ce=ia(a,"customTab",se,()=>[Ce]),s(Bn,{tab:Ce,key:se.key,disabled:se.disabled},null)})],rightExtra:K?()=>K:null}):null;(ee||ne||fe)&&(pe=s("div",{class:`${_}-head`,style:R},[s("div",{class:`${_}-head-wrapper`},[ee&&s("div",{class:`${_}-head-title`},[ee]),ne&&s("div",{class:`${_}-extra`},[ne])]),fe]));const me=B?s("div",{class:`${_}-cover`},[B]):null,ye=s("div",{class:`${_}-body`,style:M},[O?Z:X]),be=le&&le.length?s("ul",{class:`${_}-actions`},[v(le)]):null;return c(s("div",z(z({ref:"cardContainerRef"},n),{},{class:[U,n.class]}),[pe,me,X&&X.length?ye:null,be]))}}}),In=()=>({prefixCls:String,title:Ue(),description:Ue(),avatar:Ue()}),ot=k({compatConfig:{MODE:3},name:"ACardMeta",props:In(),slots:Object,setup(e,t){let{slots:a}=t;const{prefixCls:n}=ue("card",e);return()=>{const o={[`${n.value}-meta`]:!0},r=Ze(a,e,"avatar"),i=Ze(a,e,"title"),c=Ze(a,e,"description"),g=r?s("div",{class:`${n.value}-meta-avatar`},[r]):null,v=i?s("div",{class:`${n.value}-meta-title`},[i]):null,b=c?s("div",{class:`${n.value}-meta-description`},[c]):null,y=v||b?s("div",{class:`${n.value}-meta-detail`},[v,b]):null;return s("div",{class:o},[g,y])}}}),An=()=>({prefixCls:String,hoverable:{type:Boolean,default:!0}}),it=k({compatConfig:{MODE:3},name:"ACardGrid",__ANT_CARD_GRID:!0,props:An(),setup(e,t){let{slots:a}=t;const{prefixCls:n}=ue("card",e),o=D(()=>({[`${n.value}-grid`]:!0,[`${n.value}-grid-hoverable`]:e.hoverable}));return()=>{var r;return s("div",{class:o.value},[(r=a.default)===null||r===void 0?void 0:r.call(a)])}}});Le.Meta=ot;Le.Grid=it;Le.install=function(e){return e.component(Le.name,Le),e.component(ot.name,ot),e.component(it.name,it),e};export{Le as C,Ba as D,Y as S,_e as T,nt as a};
