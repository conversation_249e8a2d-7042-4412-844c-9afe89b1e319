var T=Object.defineProperty;var L=Object.getOwnPropertySymbols;var $=Object.prototype.hasOwnProperty,W=Object.prototype.propertyIsEnumerable;var S=(o,a,e)=>a in o?T(o,a,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[a]=e,y=(o,a)=>{for(var e in a||(a={}))$.call(a,e)&&S(o,e,a[e]);if(L)for(var e of L(a))W.call(a,e)&&S(o,e,a[e]);return o};var x=(o,a,e)=>new Promise((i,r)=>{var u=l=>{try{f(e.next(l))}catch(t){r(t)}},p=l=>{try{f(e.throw(l))}catch(t){r(t)}},f=l=>l.done?i(l.value):Promise.resolve(l.value).then(u,p);f((e=e.apply(o,a)).next())});import{u as z,b as V}from"./use-modal-C76TZrr_.js";import{aR as O,b2 as U,aS as k,bW as q,bF as H,c6 as Z,aW as D,aV as N}from"./bootstrap-DlHXJWd_.js";import{_ as G}from"./login.vue_vue_type_script_setup_true_lang-CzW-QY1z.js";import{P as B,ax as j,am as F,n as J,a0 as Q,a4 as C,a_ as Y,a$ as K,Y as E,J as b,av as X,ab as R,x as m,ac as g,a7 as n,a8 as ee,bT as M,a1 as P,aa as ae}from"../jse/index-index-DYNcUVMZ.js";import{_ as te,N as oe,a as se,b as ne}from"./layout.vue_vue_type_script_setup_true_lang-DxruNl34.js";import{_ as re}from"./avatar.vue_vue_type_script_setup_true_lang-CiaVJ8BJ.js";import{a as le}from"./use-vben-form-D_KgkLrU.js";import"./index-cbK0tipA.js";import"./auth-title-CuTIjiqG.js";import"./render-content.vue_vue_type_script_lang-D2VaNJ52.js";import"./theme-toggle.vue_vue_type_script_setup_true_lang-BkcvcZZW.js";import"./TabsList.vue_vue_type_script_setup_true_lang-DsT_45_5.js";import"./rotate-cw-C3yB0h8-.js";const ie=O("book-open-text",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M16 12h2",key:"7q9ll5"}],["path",{d:"M16 8h2",key:"msurwy"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}],["path",{d:"M6 12h2",key:"32wvfc"}],["path",{d:"M6 8h2",key:"30oboj"}]]),d=B(),A=B(!1),w=B({advancedStyle:{colorStops:[{color:"gray",offset:0},{color:"gray",offset:1}],type:"linear"},content:"",contentType:"multi-line-text",globalAlpha:.25,gridLayoutOptions:{cols:2,gap:[20,20],matrix:[[1,0],[0,1]],rows:2},height:200,layout:"grid",rotate:30,width:160});function ue(){function o(i){return x(this,null,function*(){var u;const{Watermark:r}=yield Q(()=>x(null,null,function*(){const{Watermark:p}=yield import("./index.esm-Ofvdfudw.js");return{Watermark:p}}),[]);w.value=y(y({},w.value),i),d.value=new r(w.value),yield(u=d.value)==null?void 0:u.create()})}function a(i){return x(this,null,function*(){var r;d.value?(yield J(),yield(r=d.value)==null?void 0:r.changeOptions(y(y({},w.value),i))):yield o(i)})}function e(){d.value&&(d.value.destroy(),d.value=void 0)}return A.value||(A.value=!0,j(()=>{e()})),{destroyWatermark:e,updateWatermark:a,watermark:F(d)}}const ce=C({name:"LoginExpiredModal",__name:"login-expired-modal",props:Y({avatar:{default:""},zIndex:{default:0},codeLoginPath:{},forgetPasswordPath:{},loading:{type:Boolean},qrCodeLoginPath:{},registerPath:{},showCodeLogin:{type:Boolean},showForgetPassword:{type:Boolean},showQrcodeLogin:{type:Boolean},showRegister:{type:Boolean},showRememberMe:{type:Boolean},showThirdPartyLogin:{type:Boolean},subTitle:{},title:{},submitButtonText:{}},{open:{type:Boolean},openModifiers:{}}),emits:["update:open"],setup(o){const a=o,e=K(o,"open"),[i,r]=z();E(()=>e.value,t=>{r.setState({isOpen:t})});const u=b(()=>a.zIndex||l()),p=["ant-message","loading"];function f(t){return p.some(h=>t.classList.contains(h))}function l(){let t=0;return[...document.querySelectorAll("*")].forEach(_=>{const s=window.getComputedStyle(_).getPropertyValue("z-index");s&&!Number.isNaN(Number.parseInt(s))&&!f(_)&&(t=Math.max(t,Number.parseInt(s)))}),t+1}return(t,h)=>(R(),X("div",null,[m(n(i),{closable:!1,"close-on-click-modal":!1,"close-on-press-escape":!1,footer:!1,"fullscreen-button":!1,header:!1,"z-index":u.value,class:"border-none px-10 py-6 text-center shadow-xl sm:w-[600px] sm:rounded-2xl md:h-[unset]"},{default:g(()=>[m(n(re),{src:t.avatar,class:"mx-auto mb-6 size-20"},null,8,["src"]),m(n(U),{"show-forget-password":!1,"show-register":!1,"show-remember-me":!1,"sub-title":n(k)("authentication.loginAgainSubTitle"),title:n(k)("authentication.loginAgainTitle")},{default:g(()=>[ee(t.$slots,"default")]),_:3},8,["sub-title","title"])]),_:3},8,["z-index"])]))}}),Me=C({__name:"basic",setup(o){const a=B([{avatar:"https://avatar.vercel.sh/vercel.svg?text=VB",date:"3小时前",isRead:!0,message:"描述信息描述信息描述信息",title:"收到了 14 份新周报"},{avatar:"https://avatar.vercel.sh/1",date:"刚刚",isRead:!1,message:"描述信息描述信息描述信息",title:"朱偏右 回复了你"},{avatar:"https://avatar.vercel.sh/1",date:"2024-01-01",isRead:!1,message:"描述信息描述信息描述信息",title:"曲丽丽 评论了你"},{avatar:"https://avatar.vercel.sh/satori",date:"1天前",isRead:!1,message:"描述信息描述信息描述信息",title:"代办提醒"}]),e=q(),i=H(),r=Z(),{destroyWatermark:u,updateWatermark:p}=ue(),f=b(()=>a.value.some(s=>!s.isRead)),l=b(()=>[{handler:()=>{M(D,{target:"_blank"})},icon:ie,text:k("ui.widgets.document")},{handler:()=>{M(N,{target:"_blank"})},icon:V,text:"GitHub"},{handler:()=>{M(`${N}/issues`,{target:"_blank"})},icon:le,text:k("ui.widgets.qa")}]),t=b(()=>{var s,c;return(c=(s=e.userInfo)==null?void 0:s.avatar)!=null?c:P.app.defaultAvatar});function h(){return x(this,null,function*(){yield i.logout(!1)})}function _(){a.value=[]}function I(){a.value.forEach(s=>s.isRead=!0)}return E(()=>P.app.watermark,s=>x(null,null,function*(){var c,v;s?yield p({content:`${(c=e.userInfo)==null?void 0:c.username} - ${(v=e.userInfo)==null?void 0:v.realName}`}):u()}),{immediate:!0}),(s,c)=>(R(),ae(n(ne),{onClearPreferencesAndLogout:h},{"user-dropdown":g(()=>{var v;return[m(n(se),{avatar:t.value,menus:l.value,text:(v=n(e).userInfo)==null?void 0:v.realName,description:"<EMAIL>","tag-text":"Pro",onLogout:h},null,8,["avatar","menus","text"])]}),notification:g(()=>[m(n(oe),{dot:f.value,notifications:a.value,onClear:_,onMakeAll:I},null,8,["dot","notifications"])]),extra:g(()=>[m(n(ce),{open:n(r).loginExpired,"onUpdate:open":c[0]||(c[0]=v=>n(r).loginExpired=v),avatar:t.value},{default:g(()=>[m(G)]),_:1},8,["open","avatar"])]),"lock-screen":g(()=>[m(n(te),{avatar:t.value,onToLogin:h},null,8,["avatar"])]),_:1}))}});export{Me as default};
