var d=Object.defineProperty,m=Object.defineProperties;var C=Object.getOwnPropertyDescriptors;var c=Object.getOwnPropertySymbols;var y=Object.prototype.hasOwnProperty,B=Object.prototype.propertyIsEnumerable;var r=(n,t,e)=>t in n?d(n,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[t]=e,o=(n,t)=>{for(var e in t||(t={}))y.call(t,e)&&r(n,e,t[e]);if(c)for(var e of c(t))B.call(t,e)&&r(n,e,t[e]);return n},s=(n,t)=>m(n,C(t));import{a4 as _,o as b,i as g,a as h,k as u}from"../jse/index-index-B2UBupFX.js";const O=_({name:"RenderContent",props:{content:{default:void 0,type:[Object,String,Function]},renderBr:{default:!1,type:Boolean}},setup(n,{attrs:t,slots:e}){return()=>{if(!n.content)return null;if(!((b(n.content)||g(n.content))&&n.content!==null))if(n.renderBr&&h(n.content)){const a=n.content.split(`
`),i=[];for(const[l,f]of a.entries())i.push(u("p",{key:l},f));return i}else return n.content;return u(n.content,s(o({},t),{props:o(o({},n),t),slots:e}))}}});export{O as _};
