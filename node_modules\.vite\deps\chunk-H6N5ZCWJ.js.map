{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-table@4.16.11_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/ui/src/comp.js", "../../../../../node_modules/.pnpm/vxe-table@4.16.11_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/ui/src/log.js", "../../../../../node_modules/.pnpm/vxe-table@4.16.11_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/ui/src/vn.js"], "sourcesContent": ["import { defineComponent } from 'vue';\nexport const defineVxeComponent = defineComponent;\n", "import { VxeUI } from '@vxe-ui/core';\nconst { log } = VxeUI;\nconst version = `table v${\"4.16.11\"}`;\nexport const warnLog = log.create('warn', version);\nexport const errLog = log.create('error', version);\n", "import XEUtils from 'xe-utils';\nexport function getOnName(type) {\n    return 'on' + type.substring(0, 1).toLocaleUpperCase() + type.substring(1);\n}\nexport function getModelEvent(renderOpts) {\n    switch (renderOpts.name) {\n        case 'input':\n        case 'textarea':\n            return 'input';\n        case 'select':\n            return 'change';\n    }\n    return 'update:modelValue';\n}\nexport function getChangeEvent(renderOpts) {\n    switch (renderOpts.name) {\n        case 'input':\n        case 'textarea':\n        case 'VxeInput':\n        case 'VxeNumberInput':\n        case 'VxeTextarea':\n        case '$input':\n        case '$textarea':\n            return 'input';\n    }\n    return 'change';\n}\nexport function getSlotVNs(vns) {\n    if (vns === null || vns === undefined) {\n        return [];\n    }\n    if (XEUtils.isArray(vns)) {\n        return vns;\n    }\n    return [vns];\n}\n"], "mappings": ";;;;;;;;;;;;;;AACO,IAAM,qBAAqB;;;ACAlC,IAAM,EAAE,IAAI,IAAI;AAChB,IAAM,UAAU,UAAU,SAAS;AAC5B,IAAM,UAAU,IAAI,OAAO,QAAQ,OAAO;AAC1C,IAAM,SAAS,IAAI,OAAO,SAAS,OAAO;;;ACJjD,sBAAoB;AACb,SAAS,UAAU,MAAM;AAC5B,SAAO,OAAO,KAAK,UAAU,GAAG,CAAC,EAAE,kBAAkB,IAAI,KAAK,UAAU,CAAC;AAC7E;AACO,SAAS,cAAc,YAAY;AACtC,UAAQ,WAAW,MAAM;AAAA,IACrB,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,EACf;AACA,SAAO;AACX;AACO,SAAS,eAAe,YAAY;AACvC,UAAQ,WAAW,MAAM;AAAA,IACrB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,EACf;AACA,SAAO;AACX;AACO,SAAS,WAAW,KAAK;AAC5B,MAAI,QAAQ,QAAQ,QAAQ,QAAW;AACnC,WAAO,CAAC;AAAA,EACZ;AACA,MAAI,gBAAAA,QAAQ,QAAQ,GAAG,GAAG;AACtB,WAAO;AAAA,EACX;AACA,SAAO,CAAC,GAAG;AACf;", "names": ["XEUtils"]}