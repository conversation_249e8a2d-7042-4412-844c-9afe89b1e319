import{_ as l,a as n,b as r,c as _}from"./CardTitle.vue_vue_type_script_setup_true_lang-DM2nXXp9.js";import{a4 as o,aa as c,ab as i,ac as a,x as e,a7 as s,ai as f,aj as m,a8 as u}from"../jse/index-index-B2UBupFX.js";const h=o({name:"AnalysisChartCard",__name:"analysis-chart-card",props:{title:{}},setup(d){return(t,p)=>(i(),c(s(_),null,{default:a(()=>[e(s(l),null,{default:a(()=>[e(s(n),{class:"text-xl"},{default:a(()=>[f(m(t.title),1)]),_:1})]),_:1}),e(s(r),null,{default:a(()=>[u(t.$slots,"default")]),_:3})]),_:3}))}});export{h as _};
