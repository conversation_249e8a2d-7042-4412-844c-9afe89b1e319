<script lang="ts" setup>
import { ref, reactive, computed } from 'vue';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Modal,
  Form,
  FormItem,
  InputNumber,
  DatePicker,
  message,
  Popconfirm,
  Tag,
  Badge,
  Tooltip,
} from 'ant-design-vue';
import type { TableColumnsType } from 'ant-design-vue';

defineOptions({ name: 'IrrigationDevice' });

// 表单引用
const formRef = ref();

// 模态框控制
const modalVisible = ref(false);
const modalTitle = ref('');
const isEdit = ref(false);

// 搜索条件
const searchForm = reactive({
  name: '',
  status: '',
  type: '',
});

// 设备表单数据
const deviceForm = reactive({
  id: null,
  name: '',
  type: '',
  model: '',
  power: null,
  voltage: null,
  current: null,
  manufacturer: '',
  installDate: null,
  lastMaintenance: null,
  nextMaintenance: null,
  status: 'normal',
  location: '',
  manager: '',
  phone: '',
  remark: '',
});

// 模拟设备数据
const deviceData = ref([
  {
    id: 1,
    name: '东风灌站主水泵',
    type: '离心泵',
    model: 'IS200-150-400',
    power: 450,
    voltage: 380,
    current: 125.8,
    manufacturer: '上海水泵厂',
    installDate: '2018-05-15',
    lastMaintenance: '2024-01-10',
    nextMaintenance: '2024-04-10',
    status: 'normal',
    location: '东风灌站泵房',
    manager: '张三',
    phone: '13800138001',
    remark: '主要供水设备',
  },
  {
    id: 2,
    name: '红旗灌站变频器',
    type: '变频器',
    model: 'VFD-M-55kW',
    power: 55,
    voltage: 380,
    current: 0,
    manufacturer: '施耐德电气',
    installDate: '2015-08-20',
    lastMaintenance: '2023-12-15',
    nextMaintenance: '2024-03-15',
    status: 'maintenance',
    location: '红旗灌站控制室',
    manager: '李四',
    phone: '13800138002',
    remark: '需要更换滤波器',
  },
  {
    id: 3,
    name: '胜利灌站流量计',
    type: '流量计',
    model: 'EMF-DN300',
    power: 24,
    voltage: 24,
    current: 4.2,
    manufacturer: '横河电机',
    installDate: '2020-03-10',
    lastMaintenance: '2024-01-05',
    nextMaintenance: '2024-07-05',
    status: 'normal',
    location: '胜利灌站出水口',
    manager: '王五',
    phone: '13800138003',
    remark: '精度良好',
  },
]);

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '设备名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
  },
  {
    title: '设备类型',
    dataIndex: 'type',
    key: 'type',
    width: 120,
  },
  {
    title: '型号',
    dataIndex: 'model',
    key: 'model',
    width: 150,
  },
  {
    title: '功率(kW)',
    dataIndex: 'power',
    key: 'power',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '安装位置',
    dataIndex: 'location',
    key: 'location',
    width: 150,
  },
  {
    title: '管理员',
    dataIndex: 'manager',
    key: 'manager',
    width: 100,
  },
  {
    title: '下次维护',
    dataIndex: 'nextMaintenance',
    key: 'nextMaintenance',
    width: 120,
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right',
  },
];

// 筛选后的数据
const filteredData = computed(() => {
  return deviceData.value.filter(item => {
    const nameMatch = !searchForm.name || item.name.includes(searchForm.name);
    const statusMatch = !searchForm.status || item.status === searchForm.status;
    const typeMatch = !searchForm.type || item.type === searchForm.type;
    return nameMatch && statusMatch && typeMatch;
  });
});

// 获取状态标签
const getStatusTag = (status: string) => {
  switch (status) {
    case 'normal':
      return { color: 'green', text: '正常' };
    case 'maintenance':
      return { color: 'orange', text: '维修中' };
    case 'fault':
      return { color: 'red', text: '故障' };
    case 'offline':
      return { color: 'gray', text: '离线' };
    default:
      return { color: 'default', text: '未知' };
  }
};

// 重置搜索表单
const resetSearch = () => {
  Object.assign(searchForm, {
    name: '',
    status: '',
    type: '',
  });
};

// 新增设备
const addDevice = () => {
  modalTitle.value = '新增设备';
  isEdit.value = false;
  resetForm();
  modalVisible.value = true;
};

// 编辑设备
const editDevice = (record: any) => {
  modalTitle.value = '编辑设备';
  isEdit.value = true;
  Object.assign(deviceForm, record);
  modalVisible.value = true;
};

// 删除设备
const deleteDevice = (id: number) => {
  const index = deviceData.value.findIndex(item => item.id === id);
  if (index > -1) {
    deviceData.value.splice(index, 1);
    message.success('删除成功');
  }
};

// 重置表单
const resetForm = () => {
  Object.assign(deviceForm, {
    id: null,
    name: '',
    type: '',
    model: '',
    power: null,
    voltage: null,
    current: null,
    manufacturer: '',
    installDate: null,
    lastMaintenance: null,
    nextMaintenance: null,
    status: 'normal',
    location: '',
    manager: '',
    phone: '',
    remark: '',
  });
  formRef.value?.resetFields();
};

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    
    if (isEdit.value) {
      // 编辑
      const index = deviceData.value.findIndex(item => item.id === deviceForm.id);
      if (index > -1) {
        deviceData.value[index] = { ...deviceForm };
        message.success('编辑成功');
      }
    } else {
      // 新增
      const newDevice = {
        ...deviceForm,
        id: Date.now(), // 简单的ID生成
      };
      deviceData.value.push(newDevice);
      message.success('新增成功');
    }
    
    modalVisible.value = false;
    resetForm();
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

// 取消操作
const handleCancel = () => {
  modalVisible.value = false;
  resetForm();
};

// 维护记录
const maintenanceRecord = (record: any) => {
  Modal.info({
    title: '维护记录',
    content: `${record.name} 的维护记录功能开发中...`,
    width: 600,
  });
};

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入设备名称' }],
  type: [{ required: true, message: '请选择设备类型' }],
  model: [{ required: true, message: '请输入设备型号' }],
  power: [{ required: true, message: '请输入设备功率' }],
  location: [{ required: true, message: '请输入安装位置' }],
  manager: [{ required: true, message: '请输入管理员' }],
  phone: [{ required: true, message: '请输入联系电话' }],
};
</script>

<template>
  <div class="p-5">
    <!-- 搜索区域 -->
    <Card title="设备查询" class="mb-5">
      <Form layout="inline" :model="searchForm">
        <FormItem label="设备名称">
          <Input
            v-model:value="searchForm.name"
            placeholder="请输入设备名称"
            style="width: 200px"
          />
        </FormItem>
        <FormItem label="设备状态">
          <Select
            v-model:value="searchForm.status"
            placeholder="请选择状态"
            style="width: 120px"
            allow-clear
          >
            <Select.Option value="normal">正常</Select.Option>
            <Select.Option value="maintenance">维修中</Select.Option>
            <Select.Option value="fault">故障</Select.Option>
            <Select.Option value="offline">离线</Select.Option>
          </Select>
        </FormItem>
        <FormItem label="设备类型">
          <Select
            v-model:value="searchForm.type"
            placeholder="请选择类型"
            style="width: 120px"
            allow-clear
          >
            <Select.Option value="离心泵">离心泵</Select.Option>
            <Select.Option value="变频器">变频器</Select.Option>
            <Select.Option value="流量计">流量计</Select.Option>
            <Select.Option value="压力表">压力表</Select.Option>
            <Select.Option value="控制器">控制器</Select.Option>
          </Select>
        </FormItem>
        <FormItem>
          <Space>
            <Button type="primary">查询</Button>
            <Button @click="resetSearch">重置</Button>
          </Space>
        </FormItem>
      </Form>
    </Card>

    <!-- 设备列表 -->
    <Card title="设备列表">
      <template #extra>
        <Button type="primary" @click="addDevice">新增设备</Button>
      </template>
      
      <Table
        :columns="columns"
        :data-source="filteredData"
        :scroll="{ x: 1200 }"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <Tag :color="getStatusTag(record.status).color">
              {{ getStatusTag(record.status).text }}
            </Tag>
          </template>
          <template v-else-if="column.key === 'action'">
            <Space>
              <Button type="link" size="small" @click="editDevice(record)">
                编辑
              </Button>
              <Button type="link" size="small" @click="maintenanceRecord(record)">
                维护记录
              </Button>
              <Popconfirm
                title="确定要删除这个设备吗？"
                @confirm="deleteDevice(record.id)"
              >
                <Button type="link" size="small" danger>删除</Button>
              </Popconfirm>
            </Space>
          </template>
        </template>
      </Table>
    </Card>

    <!-- 设备表单模态框 -->
    <Modal
      v-model:open="modalVisible"
      :title="modalTitle"
      width="800px"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <Form
        ref="formRef"
        :model="deviceForm"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <FormItem label="设备名称" name="name">
          <Input v-model:value="deviceForm.name" placeholder="请输入设备名称" />
        </FormItem>
        <FormItem label="设备类型" name="type">
          <Select v-model:value="deviceForm.type" placeholder="请选择设备类型">
            <Select.Option value="离心泵">离心泵</Select.Option>
            <Select.Option value="变频器">变频器</Select.Option>
            <Select.Option value="流量计">流量计</Select.Option>
            <Select.Option value="压力表">压力表</Select.Option>
            <Select.Option value="控制器">控制器</Select.Option>
          </Select>
        </FormItem>
        <FormItem label="设备型号" name="model">
          <Input v-model:value="deviceForm.model" placeholder="请输入设备型号" />
        </FormItem>
        <FormItem label="设备功率(kW)" name="power">
          <InputNumber
            v-model:value="deviceForm.power"
            placeholder="请输入设备功率"
            style="width: 100%"
            :min="0"
          />
        </FormItem>
        <FormItem label="额定电压(V)">
          <InputNumber
            v-model:value="deviceForm.voltage"
            placeholder="请输入额定电压"
            style="width: 100%"
            :min="0"
          />
        </FormItem>
        <FormItem label="制造商">
          <Input v-model:value="deviceForm.manufacturer" placeholder="请输入制造商" />
        </FormItem>
        <FormItem label="安装位置" name="location">
          <Input v-model:value="deviceForm.location" placeholder="请输入安装位置" />
        </FormItem>
        <FormItem label="管理员" name="manager">
          <Input v-model:value="deviceForm.manager" placeholder="请输入管理员" />
        </FormItem>
        <FormItem label="联系电话" name="phone">
          <Input v-model:value="deviceForm.phone" placeholder="请输入联系电话" />
        </FormItem>
        <FormItem label="设备状态">
          <Select v-model:value="deviceForm.status">
            <Select.Option value="normal">正常</Select.Option>
            <Select.Option value="maintenance">维修中</Select.Option>
            <Select.Option value="fault">故障</Select.Option>
            <Select.Option value="offline">离线</Select.Option>
          </Select>
        </FormItem>
        <FormItem label="备注">
          <Input.TextArea
            v-model:value="deviceForm.remark"
            placeholder="请输入备注信息"
            :rows="3"
          />
        </FormItem>
      </Form>
    </Modal>
  </div>
</template>

<style scoped>
.ant-table-tbody > tr > td {
  padding: 8px;
}
</style>
