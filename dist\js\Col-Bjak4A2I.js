import{aZ as k,_ as y,g as L,m as B,H as A,a as E,j as X,b as C}from"./bootstrap-CFDAkNgp.js";import{J as g,R as T,aF as _,a4 as F,P as R,a9 as D,az as P,x as H}from"../jse/index-index-B2UBupFX.js";import{u as V}from"./useFlexGapSupport-TvfJoknb.js";const M=["xxxl","xxl","xl","lg","md","sm","xs"],q=e=>({xs:`(max-width: ${e.screenXSMax}px)`,sm:`(min-width: ${e.screenSM}px)`,md:`(min-width: ${e.screenMD}px)`,lg:`(min-width: ${e.screenLG}px)`,xl:`(min-width: ${e.screenXL}px)`,xxl:`(min-width: ${e.screenXXL}px)`,xxxl:`{min-width: ${e.screenXXXL}px}`});function J(){const[,e]=k();return g(()=>{const t=q(e.value),r=new Map;let i=-1,s={};return{matchHandlers:{},dispatch(n){return s=n,r.forEach(x=>x(s)),r.size>=1},subscribe(n){return r.size||this.register(),i+=1,r.set(i,n),n(s),i},unsubscribe(n){r.delete(n),r.size||this.unregister()},unregister(){Object.keys(t).forEach(n=>{const x=t[n],c=this.matchHandlers[x];c==null||c.mql.removeListener(c==null?void 0:c.listener)}),r.clear()},register(){Object.keys(t).forEach(n=>{const x=t[n],c=v=>{let{matches:h}=v;this.dispatch(y(y({},s),{[n]:h}))},b=window.matchMedia(x);b.addListener(c),this.matchHandlers[x]={mql:b,listener:c},c(b)})},responsiveMap:t}})}const W=Symbol("rowContextKey"),K=e=>{_(W,e)},Q=()=>T(W,{gutter:g(()=>{}),wrap:g(()=>{}),supportFlexGap:g(()=>{})}),U=e=>{const{componentCls:t}=e;return{[t]:{display:"flex",flexFlow:"row wrap",minWidth:0,"&::before, &::after":{display:"flex"},"&-no-wrap":{flexWrap:"nowrap"},"&-start":{justifyContent:"flex-start"},"&-center":{justifyContent:"center"},"&-end":{justifyContent:"flex-end"},"&-space-between":{justifyContent:"space-between"},"&-space-around ":{justifyContent:"space-around"},"&-space-evenly ":{justifyContent:"space-evenly"},"&-top":{alignItems:"flex-start"},"&-middle":{alignItems:"center"},"&-bottom":{alignItems:"flex-end"}}}},Z=e=>{const{componentCls:t}=e;return{[t]:{position:"relative",maxWidth:"100%",minHeight:1}}},Y=(e,t)=>{const{componentCls:r,gridColumns:i}=e,s={};for(let n=i;n>=0;n--)n===0?(s[`${r}${t}-${n}`]={display:"none"},s[`${r}-push-${n}`]={insetInlineStart:"auto"},s[`${r}-pull-${n}`]={insetInlineEnd:"auto"},s[`${r}${t}-push-${n}`]={insetInlineStart:"auto"},s[`${r}${t}-pull-${n}`]={insetInlineEnd:"auto"},s[`${r}${t}-offset-${n}`]={marginInlineEnd:0},s[`${r}${t}-order-${n}`]={order:0}):(s[`${r}${t}-${n}`]={display:"block",flex:`0 0 ${n/i*100}%`,maxWidth:`${n/i*100}%`},s[`${r}${t}-push-${n}`]={insetInlineStart:`${n/i*100}%`},s[`${r}${t}-pull-${n}`]={insetInlineEnd:`${n/i*100}%`},s[`${r}${t}-offset-${n}`]={marginInlineStart:`${n/i*100}%`},s[`${r}${t}-order-${n}`]={order:n});return s},I=(e,t)=>Y(e,t),z=(e,t,r)=>({[`@media (min-width: ${t}px)`]:y({},I(e,r))}),ee=L("Grid",e=>[U(e)]),te=L("Grid",e=>{const t=B(e,{gridColumns:24}),r={"-sm":t.screenSMMin,"-md":t.screenMDMin,"-lg":t.screenLGMin,"-xl":t.screenXLMin,"-xxl":t.screenXXLMin};return[Z(t),I(t,""),I(t,"-xs"),Object.keys(r).map(i=>z(t,r[i],i)).reduce((i,s)=>y(y({},i),s),{})]}),ne=()=>({align:A([String,Object]),justify:A([String,Object]),prefixCls:String,gutter:A([Number,Array,Object],0),wrap:{type:Boolean,default:void 0}}),ue=F({compatConfig:{MODE:3},name:"ARow",inheritAttrs:!1,props:ne(),setup(e,t){let{slots:r,attrs:i}=t;const{prefixCls:s,direction:n}=E("row",e),[x,c]=ee(s);let b;const v=J(),h=R({xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0}),S=R({xs:!1,sm:!1,md:!1,lg:!1,xl:!1,xxl:!1}),w=o=>g(()=>{if(typeof e[o]=="string")return e[o];if(typeof e[o]!="object")return"";for(let a=0;a<M.length;a++){const l=M[a];if(!S.value[l])continue;const u=e[o][l];if(u!==void 0)return u}return""}),p=w("align"),m=w("justify"),d=V();D(()=>{b=v.value.subscribe(o=>{S.value=o;const a=e.gutter||0;(!Array.isArray(a)&&typeof a=="object"||Array.isArray(a)&&(typeof a[0]=="object"||typeof a[1]=="object"))&&(h.value=o)})}),P(()=>{v.value.unsubscribe(b)});const $=g(()=>{const o=[void 0,void 0],{gutter:a=0}=e;return(Array.isArray(a)?a:[a,void 0]).forEach((u,N)=>{if(typeof u=="object")for(let G=0;G<M.length;G++){const O=M[G];if(h.value[O]&&u[O]!==void 0){o[N]=u[O];break}}else o[N]=u}),o});K({gutter:$,supportFlexGap:d,wrap:g(()=>e.wrap)});const j=g(()=>X(s.value,{[`${s.value}-no-wrap`]:e.wrap===!1,[`${s.value}-${m.value}`]:m.value,[`${s.value}-${p.value}`]:p.value,[`${s.value}-rtl`]:n.value==="rtl"},i.class,c.value)),f=g(()=>{const o=$.value,a={},l=o[0]!=null&&o[0]>0?`${o[0]/-2}px`:void 0,u=o[1]!=null&&o[1]>0?`${o[1]/-2}px`:void 0;return l&&(a.marginLeft=l,a.marginRight=l),d.value?a.rowGap=`${o[1]}px`:u&&(a.marginTop=u,a.marginBottom=u),a});return()=>{var o;return x(H("div",C(C({},i),{},{class:j.value,style:y(y({},f.value),i.style)}),[(o=r.default)===null||o===void 0?void 0:o.call(r)]))}}});function re(e){return typeof e=="number"?`${e} ${e} auto`:/^\d+(\.\d+)?(px|em|rem|%)$/.test(e)?`0 0 ${e}`:e}const se=()=>({span:[String,Number],order:[String,Number],offset:[String,Number],push:[String,Number],pull:[String,Number],xs:{type:[String,Number,Object],default:void 0},sm:{type:[String,Number,Object],default:void 0},md:{type:[String,Number,Object],default:void 0},lg:{type:[String,Number,Object],default:void 0},xl:{type:[String,Number,Object],default:void 0},xxl:{type:[String,Number,Object],default:void 0},prefixCls:String,flex:[String,Number]}),oe=["xs","sm","md","lg","xl","xxl"],ce=F({compatConfig:{MODE:3},name:"ACol",inheritAttrs:!1,props:se(),setup(e,t){let{slots:r,attrs:i}=t;const{gutter:s,supportFlexGap:n,wrap:x}=Q(),{prefixCls:c,direction:b}=E("col",e),[v,h]=te(c),S=g(()=>{const{span:p,order:m,offset:d,push:$,pull:j}=e,f=c.value;let o={};return oe.forEach(a=>{let l={};const u=e[a];typeof u=="number"?l.span=u:typeof u=="object"&&(l=u||{}),o=y(y({},o),{[`${f}-${a}-${l.span}`]:l.span!==void 0,[`${f}-${a}-order-${l.order}`]:l.order||l.order===0,[`${f}-${a}-offset-${l.offset}`]:l.offset||l.offset===0,[`${f}-${a}-push-${l.push}`]:l.push||l.push===0,[`${f}-${a}-pull-${l.pull}`]:l.pull||l.pull===0,[`${f}-rtl`]:b.value==="rtl"})}),X(f,{[`${f}-${p}`]:p!==void 0,[`${f}-order-${m}`]:m,[`${f}-offset-${d}`]:d,[`${f}-push-${$}`]:$,[`${f}-pull-${j}`]:j},o,i.class,h.value)}),w=g(()=>{const{flex:p}=e,m=s.value,d={};if(m&&m[0]>0){const $=`${m[0]/2}px`;d.paddingLeft=$,d.paddingRight=$}if(m&&m[1]>0&&!n.value){const $=`${m[1]/2}px`;d.paddingTop=$,d.paddingBottom=$}return p&&(d.flex=re(p),x.value===!1&&!d.minWidth&&(d.minWidth=0)),d});return()=>{var p;return v(H("div",C(C({},i),{},{class:S.value,style:[w.value,i.style]}),[(p=r.default)===null||p===void 0?void 0:p.call(r)]))}}});export{ue as A,ce as C,M as r,J as u};
