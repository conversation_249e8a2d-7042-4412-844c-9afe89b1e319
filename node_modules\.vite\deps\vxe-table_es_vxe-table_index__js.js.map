{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-table@4.16.11_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/table/src/use/cell-view.js", "../../../../../node_modules/.pnpm/vxe-table@4.16.11_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/table/index.js", "../../../../../node_modules/.pnpm/vxe-table@4.16.11_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/vxe-table/index.js"], "sourcesContent": ["import { computed } from 'vue';\nimport XEUtils from 'xe-utils';\nexport function useCellView(props) {\n    const currColumn = computed(() => {\n        const { renderParams } = props;\n        return renderParams.column;\n    });\n    const currRow = computed(() => {\n        const { renderParams } = props;\n        return renderParams.row;\n    });\n    const cellOptions = computed(() => {\n        const { renderOpts } = props;\n        return renderOpts.props || {};\n    });\n    const cellModel = computed({\n        get() {\n            const { renderParams } = props;\n            const { row, column } = renderParams;\n            return XEUtils.get(row, column.field);\n        },\n        set(value) {\n            const { renderParams } = props;\n            const { row, column } = renderParams;\n            return XEUtils.set(row, column.field, value);\n        }\n    });\n    return {\n        currColumn,\n        currRow,\n        cellModel,\n        cellOptions\n    };\n}\n", "import { VxeUI } from '../ui';\nimport VxeTableComponent from './src/table';\nimport { useCellView } from './src/use';\nexport const VxeTable = Object.assign({}, VxeTableComponent, {\n    install(app) {\n        app.component(VxeTableComponent.name, VxeTableComponent);\n    }\n});\nconst tableHandle = {\n    useCellView\n};\nif (VxeUI.dynamicApp) {\n    VxeUI.dynamicApp.component(VxeTableComponent.name, VxeTableComponent);\n}\nVxeUI.component(VxeTableComponent);\nVxeUI.tableHandle = tableHandle;\nexport const Table = VxeTable;\nexport default VxeTable;\n", "import VxeTable from '../table';\nexport * from '../table';\nexport default VxeTable;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AACA,sBAAoB;AACb,SAAS,YAAY,OAAO;AAC/B,QAAM,aAAa,SAAS,MAAM;AAC9B,UAAM,EAAE,aAAa,IAAI;AACzB,WAAO,aAAa;AAAA,EACxB,CAAC;AACD,QAAM,UAAU,SAAS,MAAM;AAC3B,UAAM,EAAE,aAAa,IAAI;AACzB,WAAO,aAAa;AAAA,EACxB,CAAC;AACD,QAAM,cAAc,SAAS,MAAM;AAC/B,UAAM,EAAE,WAAW,IAAI;AACvB,WAAO,WAAW,SAAS,CAAC;AAAA,EAChC,CAAC;AACD,QAAM,YAAY,SAAS;AAAA,IACvB,MAAM;AACF,YAAM,EAAE,aAAa,IAAI;AACzB,YAAM,EAAE,KAAK,OAAO,IAAI;AACxB,aAAO,gBAAAA,QAAQ,IAAI,KAAK,OAAO,KAAK;AAAA,IACxC;AAAA,IACA,IAAI,OAAO;AACP,YAAM,EAAE,aAAa,IAAI;AACzB,YAAM,EAAE,KAAK,OAAO,IAAI;AACxB,aAAO,gBAAAA,QAAQ,IAAI,KAAK,OAAO,OAAO,KAAK;AAAA,IAC/C;AAAA,EACJ,CAAC;AACD,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;;;AC9BO,IAAM,WAAW,OAAO,OAAO,CAAC,GAAG,eAAmB;AAAA,EACzD,QAAQ,KAAK;AACT,QAAI,UAAU,cAAkB,MAAM,aAAiB;AAAA,EAC3D;AACJ,CAAC;AACD,IAAM,cAAc;AAAA,EAChB;AACJ;AACA,IAAI,MAAM,YAAY;AAClB,QAAM,WAAW,UAAU,cAAkB,MAAM,aAAiB;AACxE;AACA,MAAM,UAAU,aAAiB;AACjC,MAAM,cAAc;AACb,IAAM,QAAQ;AACrB,IAAOC,iBAAQ;;;ACff,IAAO,oBAAQC;", "names": ["XEUtils", "table_default", "table_default"]}