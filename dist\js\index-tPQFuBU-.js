import{P as O,_ as l,aG as lt,k as j,j as X,g as it,m as K,aH as st,r as pt,h as ft,a as ct,O as ut,i as dt,aI as mt,a5 as gt,B as W,aJ as vt,z as U,d as bt}from"./bootstrap-CFDAkNgp.js";import{T as wt}from"./Trigger-D2zZP_An.js";import{a4 as M,x as T,a5 as H,ao as ht,J as k,P as G,Y as yt}from"../jse/index-index-B2UBupFX.js";import{i as Ct,r as Ot,g as Pt}from"./colors-KzMfSzFw.js";const y={adjustX:1,adjustY:1},C=[0,0],Q={left:{points:["cr","cl"],overflow:y,offset:[-4,0],targetOffset:C},right:{points:["cl","cr"],overflow:y,offset:[4,0],targetOffset:C},top:{points:["bc","tc"],overflow:y,offset:[0,-4],targetOffset:C},bottom:{points:["tc","bc"],overflow:y,offset:[0,4],targetOffset:C},topLeft:{points:["bl","tl"],overflow:y,offset:[0,-4],targetOffset:C},leftTop:{points:["tr","tl"],overflow:y,offset:[-4,0],targetOffset:C},topRight:{points:["br","tr"],overflow:y,offset:[0,-4],targetOffset:C},rightTop:{points:["tl","tr"],overflow:y,offset:[4,0],targetOffset:C},bottomRight:{points:["tr","br"],overflow:y,offset:[0,4],targetOffset:C},rightBottom:{points:["bl","br"],overflow:y,offset:[4,0],targetOffset:C},bottomLeft:{points:["tl","bl"],overflow:y,offset:[0,4],targetOffset:C},leftBottom:{points:["br","bl"],overflow:y,offset:[-4,0],targetOffset:C}},_t={prefixCls:String,id:String,overlayInnerStyle:O.any},St=M({compatConfig:{MODE:3},name:"TooltipContent",props:_t,setup(t,o){let{slots:e}=o;return()=>{var n;return T("div",{class:`${t.prefixCls}-inner`,id:t.id,role:"tooltip",style:t.overlayInnerStyle},[(n=e.overlay)===null||n===void 0?void 0:n.call(e)])}}});var At=function(t,o){var e={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&o.indexOf(n)<0&&(e[n]=t[n]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,n=Object.getOwnPropertySymbols(t);a<n.length;a++)o.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(t,n[a])&&(e[n[a]]=t[n[a]]);return e};function J(){}const Tt=M({compatConfig:{MODE:3},name:"Tooltip",inheritAttrs:!1,props:{trigger:O.any.def(["hover"]),defaultVisible:{type:Boolean,default:void 0},visible:{type:Boolean,default:void 0},placement:O.string.def("right"),transitionName:String,animation:O.any,afterVisibleChange:O.func.def(()=>{}),overlayStyle:{type:Object,default:void 0},overlayClassName:String,prefixCls:O.string.def("rc-tooltip"),mouseEnterDelay:O.number.def(.1),mouseLeaveDelay:O.number.def(.1),getPopupContainer:Function,destroyTooltipOnHide:{type:Boolean,default:!1},align:O.object.def(()=>({})),arrowContent:O.any.def(null),tipId:String,builtinPlacements:O.object,overlayInnerStyle:{type:Object,default:void 0},popupVisible:{type:Boolean,default:void 0},onVisibleChange:Function,onPopupAlign:Function,arrow:{type:Boolean,default:!0}},setup(t,o){let{slots:e,attrs:n,expose:a}=o;const p=H(),s=()=>{const{prefixCls:f,tipId:d,overlayInnerStyle:b}=t;return[t.arrow?T("div",{class:`${f}-arrow`,key:"arrow"},[lt(e,t,"arrowContent")]):null,T(St,{key:"content",prefixCls:f,id:d,overlayInnerStyle:b},{overlay:e.overlay})]};a({getPopupDomNode:()=>p.value.getPopupDomNode(),triggerDOM:p,forcePopupAlign:()=>{var f;return(f=p.value)===null||f===void 0?void 0:f.forcePopupAlign()}});const v=H(!1),P=H(!1);return ht(()=>{const{destroyTooltipOnHide:f}=t;if(typeof f=="boolean")v.value=f;else if(f&&typeof f=="object"){const{keepParent:d}=f;v.value=d===!0,P.value=d===!1}}),()=>{const{overlayClassName:f,trigger:d,mouseEnterDelay:b,mouseLeaveDelay:_,overlayStyle:w,prefixCls:S,afterVisibleChange:z,transitionName:B,animation:$,placement:I,align:V,destroyTooltipOnHide:F,defaultVisible:R}=t,E=At(t,["overlayClassName","trigger","mouseEnterDelay","mouseLeaveDelay","overlayStyle","prefixCls","afterVisibleChange","transitionName","animation","placement","align","destroyTooltipOnHide","defaultVisible"]),N=l({},E);t.visible!==void 0&&(N.popupVisible=t.visible);const L=l(l(l({popupClassName:f,prefixCls:S,action:d,builtinPlacements:Q,popupPlacement:I,popupAlign:V,afterPopupVisibleChange:z,popupTransitionName:B,popupAnimation:$,defaultPopupVisible:R,destroyPopupOnHide:v.value,autoDestroy:P.value,mouseLeaveDelay:_,popupStyle:w,mouseEnterDelay:b},N),n),{onPopupVisibleChange:t.onVisibleChange||J,onPopupAlign:t.onPopupAlign||J,ref:p,arrow:!!t.arrow,popup:s()});return T(wt,L,{default:e.default})}}}),$t=()=>({trigger:[String,Array],open:{type:Boolean,default:void 0},visible:{type:Boolean,default:void 0},placement:String,color:String,transitionName:String,overlayStyle:j(),overlayInnerStyle:j(),overlayClassName:String,openClassName:String,prefixCls:String,mouseEnterDelay:Number,mouseLeaveDelay:Number,getPopupContainer:Function,arrowPointAtCenter:{type:Boolean,default:void 0},arrow:{type:[Boolean,Object],default:!0},autoAdjustOverflow:{type:[Boolean,Object],default:void 0},destroyTooltipOnHide:{type:Boolean,default:void 0},align:j(),builtinPlacements:j(),children:Array,onVisibleChange:Function,"onUpdate:visible":Function,onOpenChange:Function,"onUpdate:open":Function}),xt={adjustX:1,adjustY:1},q={adjustX:0,adjustY:0},Rt=[0,0];function Z(t){return typeof t=="boolean"?t?xt:q:l(l({},q),t)}function kt(t){const{arrowWidth:o=4,horizontalArrowShift:e=16,verticalArrowShift:n=8,autoAdjustOverflow:a,arrowPointAtCenter:p}=t,s={left:{points:["cr","cl"],offset:[-4,0]},right:{points:["cl","cr"],offset:[4,0]},top:{points:["bc","tc"],offset:[0,-4]},bottom:{points:["tc","bc"],offset:[0,4]},topLeft:{points:["bl","tc"],offset:[-(e+o),-4]},leftTop:{points:["tr","cl"],offset:[-4,-(n+o)]},topRight:{points:["br","tc"],offset:[e+o,-4]},rightTop:{points:["tl","cr"],offset:[4,-(n+o)]},bottomRight:{points:["tr","bc"],offset:[e+o,4]},rightBottom:{points:["bl","cr"],offset:[4,n+o]},bottomLeft:{points:["tl","bc"],offset:[-(e+o),4]},leftBottom:{points:["br","cl"],offset:[-4,n+o]}};return Object.keys(s).forEach(c=>{s[c]=p?l(l({},s[c]),{overflow:Z(a),targetOffset:Rt}):l(l({},Q[c]),{overflow:Z(a)}),s[c].ignoreShake=!0}),s}function Bt(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];for(let o=0,e=t.length;o<e;o++)if(t[o]!==void 0)return t[o]}function Nt(t,o){const e=Ct(o),n=X({[`${t}-${o}`]:o&&e}),a={},p={};return o&&!e&&(a.background=o,p["--antd-arrow-background-color"]=o),{className:n,overlayStyle:a,arrowStyle:p}}function D(t){let o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";return t.map(e=>`${o}${e}`).join(",")}const tt=8;function jt(t){const o=tt,{sizePopupArrow:e,contentRadius:n,borderRadiusOuter:a,limitVerticalRadius:p}=t,s=e/2-Math.ceil(a*(Math.sqrt(2)-1)),c=(n>12?n+2:12)-s,v=p?o-s:c;return{dropdownArrowOffset:c,dropdownArrowOffsetVertical:v}}function Dt(t,o){const{componentCls:e,sizePopupArrow:n,marginXXS:a,borderRadiusXS:p,borderRadiusOuter:s,boxShadowPopoverArrow:c}=t,{colorBg:v,showArrowCls:P,contentRadius:f=t.borderRadiusLG,limitVerticalRadius:d}=o,{dropdownArrowOffsetVertical:b,dropdownArrowOffset:_}=jt({sizePopupArrow:n,contentRadius:f,borderRadiusOuter:s,limitVerticalRadius:d}),w=n/2+a;return{[e]:{[`${e}-arrow`]:[l(l({position:"absolute",zIndex:1,display:"block"},Ot(n,p,s,v,c)),{"&:before":{background:v}})],[[`&-placement-top ${e}-arrow`,`&-placement-topLeft ${e}-arrow`,`&-placement-topRight ${e}-arrow`].join(",")]:{bottom:0,transform:"translateY(100%) rotate(180deg)"},[`&-placement-top ${e}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(100%) rotate(180deg)"},[`&-placement-topLeft ${e}-arrow`]:{left:{_skip_check_:!0,value:_}},[`&-placement-topRight ${e}-arrow`]:{right:{_skip_check_:!0,value:_}},[[`&-placement-bottom ${e}-arrow`,`&-placement-bottomLeft ${e}-arrow`,`&-placement-bottomRight ${e}-arrow`].join(",")]:{top:0,transform:"translateY(-100%)"},[`&-placement-bottom ${e}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(-100%)"},[`&-placement-bottomLeft ${e}-arrow`]:{left:{_skip_check_:!0,value:_}},[`&-placement-bottomRight ${e}-arrow`]:{right:{_skip_check_:!0,value:_}},[[`&-placement-left ${e}-arrow`,`&-placement-leftTop ${e}-arrow`,`&-placement-leftBottom ${e}-arrow`].join(",")]:{right:{_skip_check_:!0,value:0},transform:"translateX(100%) rotate(90deg)"},[`&-placement-left ${e}-arrow`]:{top:{_skip_check_:!0,value:"50%"},transform:"translateY(-50%) translateX(100%) rotate(90deg)"},[`&-placement-leftTop ${e}-arrow`]:{top:b},[`&-placement-leftBottom ${e}-arrow`]:{bottom:b},[[`&-placement-right ${e}-arrow`,`&-placement-rightTop ${e}-arrow`,`&-placement-rightBottom ${e}-arrow`].join(",")]:{left:{_skip_check_:!0,value:0},transform:"translateX(-100%) rotate(-90deg)"},[`&-placement-right ${e}-arrow`]:{top:{_skip_check_:!0,value:"50%"},transform:"translateY(-50%) translateX(-100%) rotate(-90deg)"},[`&-placement-rightTop ${e}-arrow`]:{top:b},[`&-placement-rightBottom ${e}-arrow`]:{bottom:b},[D(["&-placement-topLeft","&-placement-top","&-placement-topRight"].map(S=>S+=":not(&-arrow-hidden)"),P)]:{paddingBottom:w},[D(["&-placement-bottomLeft","&-placement-bottom","&-placement-bottomRight"].map(S=>S+=":not(&-arrow-hidden)"),P)]:{paddingTop:w},[D(["&-placement-leftTop","&-placement-left","&-placement-leftBottom"].map(S=>S+=":not(&-arrow-hidden)"),P)]:{paddingRight:{_skip_check_:!0,value:w}},[D(["&-placement-rightTop","&-placement-right","&-placement-rightBottom"].map(S=>S+=":not(&-arrow-hidden)"),P)]:{paddingLeft:{_skip_check_:!0,value:w}}}}}const It=t=>{const{componentCls:o,tooltipMaxWidth:e,tooltipColor:n,tooltipBg:a,tooltipBorderRadius:p,zIndexPopup:s,controlHeight:c,boxShadowSecondary:v,paddingSM:P,paddingXS:f,tooltipRadiusOuter:d}=t;return[{[o]:l(l(l(l({},pt(t)),{position:"absolute",zIndex:s,display:"block","&":[{width:"max-content"},{width:"intrinsic"}],maxWidth:e,visibility:"visible","&-hidden":{display:"none"},"--antd-arrow-background-color":a,[`${o}-inner`]:{minWidth:c,minHeight:c,padding:`${P/2}px ${f}px`,color:n,textAlign:"start",textDecoration:"none",wordWrap:"break-word",backgroundColor:a,borderRadius:p,boxShadow:v},[["&-placement-left","&-placement-leftTop","&-placement-leftBottom","&-placement-right","&-placement-rightTop","&-placement-rightBottom"].join(",")]:{[`${o}-inner`]:{borderRadius:Math.min(p,tt)}},[`${o}-content`]:{position:"relative"}}),Pt(t,(b,_)=>{let{darkColor:w}=_;return{[`&${o}-${b}`]:{[`${o}-inner`]:{backgroundColor:w},[`${o}-arrow`]:{"--antd-arrow-background-color":w}}}})),{"&-rtl":{direction:"rtl"}})},Dt(K(t,{borderRadiusOuter:d}),{colorBg:"var(--antd-arrow-background-color)",showArrowCls:"",contentRadius:p,limitVerticalRadius:!0}),{[`${o}-pure`]:{position:"relative",maxWidth:"none"}}]},Vt=(t,o)=>it("Tooltip",n=>{if((o==null?void 0:o.value)===!1)return[];const{borderRadius:a,colorTextLightSolid:p,colorBgDefault:s,borderRadiusOuter:c}=n,v=K(n,{tooltipMaxWidth:250,tooltipColor:p,tooltipBorderRadius:a,tooltipBg:s,tooltipRadiusOuter:c>4?4:c});return[It(v),st(n,"zoom-big-fast")]},n=>{let{zIndexPopupBase:a,colorBgSpotlight:p}=n;return{zIndexPopup:a+70,colorBgDefault:p}})(t),Et=(t,o)=>{const e={},n=l({},t);return o.forEach(a=>{t&&a in t&&(e[a]=t[a],delete n[a])}),{picked:e,omitted:n}},Lt=()=>l(l({},$t()),{title:O.any}),Yt=()=>({trigger:"hover",align:{},placement:"top",mouseEnterDelay:.1,mouseLeaveDelay:.1,arrowPointAtCenter:!1,autoAdjustOverflow:!0}),Ht=M({compatConfig:{MODE:3},name:"ATooltip",inheritAttrs:!1,props:ft(Lt(),{trigger:"hover",align:{},placement:"top",mouseEnterDelay:.1,mouseLeaveDelay:.1,arrowPointAtCenter:!1,autoAdjustOverflow:!0}),slots:Object,setup(t,o){let{slots:e,emit:n,attrs:a,expose:p}=o;const{prefixCls:s,getPopupContainer:c,direction:v,rootPrefixCls:P}=ct("tooltip",t),f=k(()=>{var r;return(r=t.open)!==null&&r!==void 0?r:t.visible}),d=G(Bt([t.open,t.visible])),b=G();let _;yt(f,r=>{U.cancel(_),_=U(()=>{d.value=!!r})});const w=()=>{var r;const i=(r=t.title)!==null&&r!==void 0?r:e.title;return!i&&i!==0},S=r=>{const i=w();f.value===void 0&&(d.value=i?!1:r),i||(n("update:visible",r),n("visibleChange",r),n("update:open",r),n("openChange",r))};p({getPopupDomNode:()=>b.value.getPopupDomNode(),open:d,forcePopupAlign:()=>{var r;return(r=b.value)===null||r===void 0?void 0:r.forcePopupAlign()}});const B=k(()=>{var r;const{builtinPlacements:i,autoAdjustOverflow:A,arrow:m,arrowPointAtCenter:h}=t;let u=h;return typeof m=="object"&&(u=(r=m.pointAtCenter)!==null&&r!==void 0?r:h),i||kt({arrowPointAtCenter:u,autoAdjustOverflow:A})}),$=r=>r||r==="",I=r=>{const i=r.type;if(typeof i=="object"&&r.props&&((i.__ANT_BUTTON===!0||i==="button")&&$(r.props.disabled)||i.__ANT_SWITCH===!0&&($(r.props.disabled)||$(r.props.loading))||i.__ANT_RADIO===!0&&$(r.props.disabled))){const{picked:A,omitted:m}=Et(vt(r),["position","left","right","top","bottom","float","display","zIndex"]),h=l(l({display:"inline-block"},A),{cursor:"not-allowed",lineHeight:1,width:r.props&&r.props.block?"100%":void 0}),u=l(l({},m),{pointerEvents:"none"}),g=W(r,{style:u},!0);return T("span",{style:h,class:`${s.value}-disabled-compatible-wrapper`},[g])}return r},V=()=>{var r,i;return(r=t.title)!==null&&r!==void 0?r:(i=e.title)===null||i===void 0?void 0:i.call(e)},F=(r,i)=>{const A=B.value,m=Object.keys(A).find(h=>{var u,g;return A[h].points[0]===((u=i.points)===null||u===void 0?void 0:u[0])&&A[h].points[1]===((g=i.points)===null||g===void 0?void 0:g[1])});if(m){const h=r.getBoundingClientRect(),u={top:"50%",left:"50%"};m.indexOf("top")>=0||m.indexOf("Bottom")>=0?u.top=`${h.height-i.offset[1]}px`:(m.indexOf("Top")>=0||m.indexOf("bottom")>=0)&&(u.top=`${-i.offset[1]}px`),m.indexOf("left")>=0||m.indexOf("Right")>=0?u.left=`${h.width-i.offset[0]}px`:(m.indexOf("right")>=0||m.indexOf("Left")>=0)&&(u.left=`${-i.offset[0]}px`),r.style.transformOrigin=`${u.left} ${u.top}`}},R=k(()=>Nt(s.value,t.color)),E=k(()=>a["data-popover-inject"]),[N,L]=Vt(s,k(()=>!E.value));return()=>{var r,i;const{openClassName:A,overlayClassName:m,overlayStyle:h,overlayInnerStyle:u}=t;let g=(i=ut((r=e.default)===null||r===void 0?void 0:r.call(e)))!==null&&i!==void 0?i:null;g=g.length===1?g[0]:g;let Y=d.value;if(f.value===void 0&&w()&&(Y=!1),!g)return null;const x=I(dt(g)&&!mt(g)?g:T("span",null,[g])),et=X({[A||`${s.value}-open`]:!0,[x.props&&x.props.class]:x.props&&x.props.class}),ot=X(m,{[`${s.value}-rtl`]:v.value==="rtl"},R.value.className,L.value),rt=l(l({},R.value.overlayStyle),u),nt=R.value.arrowStyle,at=l(l(l({},a),t),{prefixCls:s.value,arrow:!!t.arrow,getPopupContainer:c==null?void 0:c.value,builtinPlacements:B.value,visible:Y,ref:b,overlayClassName:ot,overlayStyle:l(l({},nt),h),overlayInnerStyle:rt,onVisibleChange:S,onPopupAlign:F,transitionName:gt(P.value,"zoom-big-fast",t.transitionName)});return N(T(Tt,at,{default:()=>[d.value?W(x,{class:et}):x],arrowContent:()=>T("span",{class:`${s.value}-arrow-content`},null),overlay:V}))}}}),Wt=bt(Ht);export{Wt as T,kt as a,Dt as b,$t as c,Lt as d,Bt as f,jt as g,Yt as t};
