import{C as A}from"./index-DOXLVRHg.js";import he from"./index-CGqxGK2L.js";import{B as fe}from"./index-DXEBJLLx.js";import{R as ne,C as O,S as se}from"./index-CHRdc_8y.js";import De from"./index-BE7dsRID.js";import{_ as x,j as X,i as Ae,b as H,a7 as Ve,P as w,ap as te,K as E,h as Te,ar as Ie,au as ie,cH as We,z as pe,g as Ue,m as Ge,r as Ke,d as Xe,G as J,H as I,v as D,s as Ye,k as Je,a as qe,F as W,cm as Qe,c9 as U,bu as Ze}from"./bootstrap-CFDAkNgp.js";import et from"./index-DeoqXZOX.js";import{B as le}from"./Trigger-D2zZP_An.js";import{x as d,a4 as Y,a5 as me,a9 as Be,az as Pe,J as ue,P as q,Y as tt,bU as nt,be as st,T as ve,av as ge,ab as oe,ac as b,a7 as m,aB as f,F as at,aC as ot,aa as rt,aj as F,aV as be,ai as G}from"../jse/index-index-B2UBupFX.js";import{d as it,T as lt}from"./index-tPQFuBU-.js";import{u as ut}from"./FormItemContext-CoieKSxA.js";import"./index-mn9Xzl0e.js";import{P as xe}from"./index-Ci1_yKfn.js";import"./index-1BiZfdtR.js";import"./shallowequal-CNCY1mYq.js";import"./_arrayIncludes-B8uzE354.js";import"./Overflow--DaGju1E.js";import"./index-DQZjs6Lb.js";import"./ResizeObserver.es-CDE7jhPe.js";import"./collapseMotion-DiwOar_A.js";import"./slide-BhgK1D9k.js";import"./useRefs-f0KzY-v7.js";import"./hasIn-Bt_d2Zq4.js";import"./isMobile-8sZ0LT6r.js";import"./useMergedState-C4x1IDb9.js";import"./isPlainObject-0t1li2J1.js";import"./index-B2Lu6Z2W.js";import"./BaseInput-Dslq5mxC.js";import"./DownOutlined-CVWF16Fu.js";import"./CheckOutlined-CZeS3E7o.js";import"./SearchOutlined-DqQ4RgbY.js";import"./move-IXaXzbNk.js";import"./statusUtils-D62pPzYs.js";import"./colors-KzMfSzFw.js";import"./Col-Bjak4A2I.js";import"./useFlexGapSupport-TvfJoknb.js";const de=(e,s)=>{let{attrs:t}=s;const{included:n,vertical:a,style:o,class:r}=t;let{length:i,offset:l,reverse:h}=t;i<0&&(h=!h,i=Math.abs(i),l=100-l);const v=a?{[h?"top":"bottom"]:`${l}%`,[h?"bottom":"top"]:"auto",height:`${i}%`}:{[h?"right":"left"]:`${l}%`,[h?"left":"right"]:"auto",width:`${i}%`},y=x(x({},o),v);return n?d("div",{class:r,style:y},null):null};de.inheritAttrs=!1;const dt=(e,s,t,n,a,o)=>{const r=Object.keys(s).map(parseFloat).sort((i,l)=>i-l);if(t&&n)for(let i=a;i<=o;i+=n)r.indexOf(i)===-1&&r.push(i);return r},ke=(e,s)=>{let{attrs:t}=s;const{prefixCls:n,vertical:a,reverse:o,marks:r,dots:i,step:l,included:h,lowerBound:v,upperBound:y,max:C,min:M,dotStyle:c,activeDotStyle:u}=t,p=C-M,V=dt(a,r,i,l,M,C).map(g=>{const B=`${Math.abs(g-M)/p*100}%`,P=!h&&g===y||h&&g<=y&&g>=v;let T=a?x(x({},c),{[o?"top":"bottom"]:B}):x(x({},c),{[o?"right":"left"]:B});P&&(T=x(x({},T),u));const $=X({[`${n}-dot`]:!0,[`${n}-dot-active`]:P,[`${n}-dot-reverse`]:o});return d("span",{class:$,style:T,key:g},null)});return d("div",{class:`${n}-step`},[V])};ke.inheritAttrs=!1;const _e=(e,s)=>{let{attrs:t,slots:n}=s;const{class:a,vertical:o,reverse:r,marks:i,included:l,upperBound:h,lowerBound:v,max:y,min:C,onClickLabel:M}=t,c=Object.keys(i),u=n.mark,p=y-C,V=c.map(parseFloat).sort((g,B)=>g-B).map(g=>{const B=typeof i[g]=="function"?i[g]():i[g],P=typeof B=="object"&&!Ae(B);let T=P?B.label:B;if(!T&&T!==0)return null;u&&(T=u({point:g,label:T}));const $=!l&&g===h||l&&g<=h&&g>=v,S=X({[`${a}-text`]:!0,[`${a}-text-active`]:$}),k={marginBottom:"-50%",[r?"top":"bottom"]:`${(g-C)/p*100}%`},R={transform:`translateX(${r?"50%":"-50%"})`,msTransform:`translateX(${r?"50%":"-50%"})`,[r?"right":"left"]:`${(g-C)/p*100}%`},_=o?k:R,z=P?x(x({},_),B.style):_,L={[Ve?"onTouchstartPassive":"onTouchstart"]:N=>M(N,g)};return d("span",H({class:S,style:z,key:g,onMousedown:N=>M(N,g)},L),[T])});return d("div",{class:a},[V])};_e.inheritAttrs=!1;const He=Y({compatConfig:{MODE:3},name:"Handle",inheritAttrs:!1,props:{prefixCls:String,vertical:{type:Boolean,default:void 0},offset:Number,disabled:{type:Boolean,default:void 0},min:Number,max:Number,value:Number,tabindex:w.oneOfType([w.number,w.string]),reverse:{type:Boolean,default:void 0},ariaLabel:String,ariaLabelledBy:String,ariaValueTextFormatter:Function,onMouseenter:{type:Function},onMouseleave:{type:Function},onMousedown:{type:Function}},setup(e,s){let{attrs:t,emit:n,expose:a}=s;const o=me(!1),r=me(),i=()=>{document.activeElement===r.value&&(o.value=!0)},l=p=>{o.value=!1,n("blur",p)},h=()=>{o.value=!1},v=()=>{var p;(p=r.value)===null||p===void 0||p.focus()},y=()=>{var p;(p=r.value)===null||p===void 0||p.blur()},C=()=>{o.value=!0,v()},M=p=>{p.preventDefault(),v(),n("mousedown",p)};a({focus:v,blur:y,clickFocus:C,ref:r});let c=null;Be(()=>{c=te(document,"mouseup",i)}),Pe(()=>{c==null||c.remove()});const u=ue(()=>{const{vertical:p,offset:V,reverse:g}=e;return p?{[g?"top":"bottom"]:`${V}%`,[g?"bottom":"top"]:"auto",transform:g?null:"translateY(+50%)"}:{[g?"right":"left"]:`${V}%`,[g?"left":"right"]:"auto",transform:`translateX(${g?"+":"-"}50%)`}});return()=>{const{prefixCls:p,disabled:V,min:g,max:B,value:P,tabindex:T,ariaLabel:$,ariaLabelledBy:S,ariaValueTextFormatter:k,onMouseenter:R,onMouseleave:_}=e,z=X(t.class,{[`${p}-handle-click-focused`]:o.value}),L={"aria-valuemin":g,"aria-valuemax":B,"aria-valuenow":P,"aria-disabled":!!V},N=[t.style,u.value];let j=T||0;(V||T===null)&&(j=null);let Z;k&&(Z=k(P));const ae=x(x(x(x({},t),{role:"slider",tabindex:j}),L),{class:z,onBlur:l,onKeydown:h,onMousedown:M,onMouseenter:R,onMouseleave:_,ref:r,style:N});return d("div",H(H({},ae),{},{"aria-label":$,"aria-labelledby":S,"aria-valuetext":Z}),null)}}});function re(e,s){try{return Object.keys(s).some(t=>e.target===s[t].ref)}catch(t){return!1}}function Oe(e,s){let{min:t,max:n}=s;return e<t||e>n}function ye(e){return e.touches.length>1||e.type.toLowerCase()==="touchend"&&e.touches.length>0}function Se(e,s){let{marks:t,step:n,min:a,max:o}=s;const r=Object.keys(t).map(parseFloat);if(n!==null){const l=Math.pow(10,Fe(n)),h=Math.floor((o*l-a*l)/(n*l)),v=Math.min((e-a)/n,h),y=Math.round(v)*n+a;r.push(y)}const i=r.map(l=>Math.abs(e-l));return r[i.indexOf(Math.min(...i))]}function Fe(e){const s=e.toString();let t=0;return s.indexOf(".")>=0&&(t=s.length-s.indexOf(".")-1),t}function we(e,s){let t=1;return window.visualViewport&&(t=+(window.visualViewport.width/document.body.getBoundingClientRect().width).toFixed(2)),(e?s.clientY:s.pageX)/t}function Ce(e,s){let t=1;return window.visualViewport&&(t=+(window.visualViewport.width/document.body.getBoundingClientRect().width).toFixed(2)),(e?s.touches[0].clientY:s.touches[0].pageX)/t}function $e(e,s){const t=s.getBoundingClientRect();return e?t.top+t.height*.5:window.scrollX+t.left+t.width*.5}function ce(e,s){let{max:t,min:n}=s;return e<=n?n:e>=t?t:e}function Le(e,s){const{step:t}=s,n=isFinite(Se(e,s))?Se(e,s):0;return t===null?n:parseFloat(n.toFixed(Fe(t)))}function Q(e){e.stopPropagation(),e.preventDefault()}function ct(e,s,t){const n={increase:(r,i)=>r+i,decrease:(r,i)=>r-i},a=n[e](Object.keys(t.marks).indexOf(JSON.stringify(s)),1),o=Object.keys(t.marks)[a];return t.step?n[e](s,t.step):Object.keys(t.marks).length&&t.marks[o]?t.marks[o]:s}function Re(e,s,t){const n="increase",a="decrease";let o=n;switch(e.keyCode){case E.UP:o=s&&t?a:n;break;case E.RIGHT:o=!s&&t?a:n;break;case E.DOWN:o=s&&t?n:a;break;case E.LEFT:o=!s&&t?n:a;break;case E.END:return(r,i)=>i.max;case E.HOME:return(r,i)=>i.min;case E.PAGE_UP:return(r,i)=>r+i.step*2;case E.PAGE_DOWN:return(r,i)=>r-i.step*2;default:return}return(r,i)=>ct(o,r,i)}var ht=function(e,s){var t={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&s.indexOf(n)<0&&(t[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)s.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(t[n[a]]=e[n[a]]);return t};function K(){}function Ne(e){const s={id:String,min:Number,max:Number,step:Number,marks:w.object,included:{type:Boolean,default:void 0},prefixCls:String,disabled:{type:Boolean,default:void 0},handle:Function,dots:{type:Boolean,default:void 0},vertical:{type:Boolean,default:void 0},reverse:{type:Boolean,default:void 0},minimumTrackStyle:w.object,maximumTrackStyle:w.object,handleStyle:w.oneOfType([w.object,w.arrayOf(w.object)]),trackStyle:w.oneOfType([w.object,w.arrayOf(w.object)]),railStyle:w.object,dotStyle:w.object,activeDotStyle:w.object,autofocus:{type:Boolean,default:void 0},draggableTrack:{type:Boolean,default:void 0}};return Y({compatConfig:{MODE:3},name:"CreateSlider",mixins:[le,e],inheritAttrs:!1,props:Te(s,{prefixCls:"rc-slider",min:0,max:100,step:1,marks:{},included:!0,disabled:!1,dots:!1,vertical:!1,reverse:!1,trackStyle:[{}],handleStyle:[{}],railStyle:{},dotStyle:{},activeDotStyle:{}}),emits:["change","blur","focus"],data(){const{step:t,max:n,min:a}=this;return this.handlesRefs={},{}},mounted(){this.$nextTick(()=>{this.document=this.sliderRef&&this.sliderRef.ownerDocument;const{autofocus:t,disabled:n}=this;t&&!n&&this.focus()})},beforeUnmount(){this.$nextTick(()=>{this.removeDocumentEvents()})},methods:{defaultHandle(t){var{index:n,directives:a,className:o,style:r}=t,i=ht(t,["index","directives","className","style"]);if(delete i.dragging,i.value===null)return null;const l=x(x({},i),{class:o,style:r,key:n});return d(He,l,null)},onDown(t,n){let a=n;const{draggableTrack:o,vertical:r}=this.$props,{bounds:i}=this.$data,l=o&&this.positionGetValue?this.positionGetValue(a)||[]:[],h=re(t,this.handlesRefs);if(this.dragTrack=o&&i.length>=2&&!h&&!l.map((v,y)=>{const C=y?!0:v>=i[y];return y===l.length-1?v<=i[y]:C}).some(v=>!v),this.dragTrack)this.dragOffset=a,this.startBounds=[...i];else{if(!h)this.dragOffset=0;else{const v=$e(r,t.target);this.dragOffset=a-v,a=v}this.onStart(a)}},onMouseDown(t){if(t.button!==0)return;this.removeDocumentEvents();const n=this.$props.vertical,a=we(n,t);this.onDown(t,a),this.addDocumentMouseEvents()},onTouchStart(t){if(ye(t))return;const n=this.vertical,a=Ce(n,t);this.onDown(t,a),this.addDocumentTouchEvents(),Q(t)},onFocus(t){const{vertical:n}=this;if(re(t,this.handlesRefs)&&!this.dragTrack){const a=$e(n,t.target);this.dragOffset=0,this.onStart(a),Q(t),this.$emit("focus",t)}},onBlur(t){this.dragTrack||this.onEnd(),this.$emit("blur",t)},onMouseUp(){this.handlesRefs[this.prevMovedHandleIndex]&&this.handlesRefs[this.prevMovedHandleIndex].clickFocus()},onMouseMove(t){if(!this.sliderRef){this.onEnd();return}const n=we(this.vertical,t);this.onMove(t,n-this.dragOffset,this.dragTrack,this.startBounds)},onTouchMove(t){if(ye(t)||!this.sliderRef){this.onEnd();return}const n=Ce(this.vertical,t);this.onMove(t,n-this.dragOffset,this.dragTrack,this.startBounds)},onKeyDown(t){this.sliderRef&&re(t,this.handlesRefs)&&this.onKeyboard(t)},onClickMarkLabel(t,n){t.stopPropagation(),this.onChange({sValue:n}),this.setState({sValue:n},()=>this.onEnd(!0))},getSliderStart(){const t=this.sliderRef,{vertical:n,reverse:a}=this,o=t.getBoundingClientRect();return n?a?o.bottom:o.top:window.scrollX+(a?o.right:o.left)},getSliderLength(){const t=this.sliderRef;if(!t)return 0;const n=t.getBoundingClientRect();return this.vertical?n.height:n.width},addDocumentTouchEvents(){this.onTouchMoveListener=te(this.document,"touchmove",this.onTouchMove),this.onTouchUpListener=te(this.document,"touchend",this.onEnd)},addDocumentMouseEvents(){this.onMouseMoveListener=te(this.document,"mousemove",this.onMouseMove),this.onMouseUpListener=te(this.document,"mouseup",this.onEnd)},removeDocumentEvents(){this.onTouchMoveListener&&this.onTouchMoveListener.remove(),this.onTouchUpListener&&this.onTouchUpListener.remove(),this.onMouseMoveListener&&this.onMouseMoveListener.remove(),this.onMouseUpListener&&this.onMouseUpListener.remove()},focus(){var t;this.$props.disabled||(t=this.handlesRefs[0])===null||t===void 0||t.focus()},blur(){this.$props.disabled||Object.keys(this.handlesRefs).forEach(t=>{var n,a;(a=(n=this.handlesRefs[t])===null||n===void 0?void 0:n.blur)===null||a===void 0||a.call(n)})},calcValue(t){const{vertical:n,min:a,max:o}=this,r=Math.abs(Math.max(t,0)/this.getSliderLength());return n?(1-r)*(o-a)+a:r*(o-a)+a},calcValueByPos(t){const a=(this.reverse?-1:1)*(t-this.getSliderStart());return this.trimAlignValue(this.calcValue(a))},calcOffset(t){const{min:n,max:a}=this,o=(t-n)/(a-n);return Math.max(0,o*100)},saveSlider(t){this.sliderRef=t},saveHandle(t,n){this.handlesRefs[t]=n}},render(){const{prefixCls:t,marks:n,dots:a,step:o,included:r,disabled:i,vertical:l,reverse:h,min:v,max:y,maximumTrackStyle:C,railStyle:M,dotStyle:c,activeDotStyle:u,id:p}=this,{class:V,style:g}=this.$attrs,{tracks:B,handles:P}=this.renderSlider(),T=X(t,V,{[`${t}-with-marks`]:Object.keys(n).length,[`${t}-disabled`]:i,[`${t}-vertical`]:l,[`${t}-horizontal`]:!l}),$={vertical:l,marks:n,included:r,lowerBound:this.getLowerBound(),upperBound:this.getUpperBound(),max:y,min:v,reverse:h,class:`${t}-mark`,onClickLabel:i?K:this.onClickMarkLabel},S={[Ve?"onTouchstartPassive":"onTouchstart"]:i?K:this.onTouchStart};return d("div",H(H({id:p,ref:this.saveSlider,tabindex:"-1",class:T},S),{},{onMousedown:i?K:this.onMouseDown,onMouseup:i?K:this.onMouseUp,onKeydown:i?K:this.onKeyDown,onFocus:i?K:this.onFocus,onBlur:i?K:this.onBlur,style:g}),[d("div",{class:`${t}-rail`,style:x(x({},C),M)},null),B,d(ke,{prefixCls:t,vertical:l,reverse:h,marks:n,dots:a,step:o,included:r,lowerBound:this.getLowerBound(),upperBound:this.getUpperBound(),max:y,min:v,dotStyle:c,activeDotStyle:u},null),P,d(_e,$,{mark:this.$slots.mark}),Ie(this)])}})}const ft=Y({compatConfig:{MODE:3},name:"Slider",mixins:[le],inheritAttrs:!1,props:{defaultValue:Number,value:Number,disabled:{type:Boolean,default:void 0},autofocus:{type:Boolean,default:void 0},tabindex:w.oneOfType([w.number,w.string]),reverse:{type:Boolean,default:void 0},min:Number,max:Number,ariaLabelForHandle:String,ariaLabelledByForHandle:String,ariaValueTextFormatterForHandle:String,startPoint:Number},emits:["beforeChange","afterChange","change"],data(){const e=this.defaultValue!==void 0?this.defaultValue:this.min,s=this.value!==void 0?this.value:e;return{sValue:this.trimAlignValue(s),dragging:!1}},watch:{value:{handler(e){this.setChangeValue(e)},deep:!0},min(){const{sValue:e}=this;this.setChangeValue(e)},max(){const{sValue:e}=this;this.setChangeValue(e)}},methods:{setChangeValue(e){const s=e!==void 0?e:this.sValue,t=this.trimAlignValue(s,this.$props);t!==this.sValue&&(this.setState({sValue:t}),Oe(s,this.$props)&&this.$emit("change",t))},onChange(e){const s=!ie(this,"value"),t=e.sValue>this.max?x(x({},e),{sValue:this.max}):e;s&&this.setState(t);const n=t.sValue;this.$emit("change",n)},onStart(e){this.setState({dragging:!0});const{sValue:s}=this;this.$emit("beforeChange",s);const t=this.calcValueByPos(e);this.startValue=t,this.startPosition=e,t!==s&&(this.prevMovedHandleIndex=0,this.onChange({sValue:t}))},onEnd(e){const{dragging:s}=this;this.removeDocumentEvents(),(s||e)&&this.$emit("afterChange",this.sValue),this.setState({dragging:!1})},onMove(e,s){Q(e);const{sValue:t}=this,n=this.calcValueByPos(s);n!==t&&this.onChange({sValue:n})},onKeyboard(e){const{reverse:s,vertical:t}=this.$props,n=Re(e,t,s);if(n){Q(e);const{sValue:a}=this,o=n(a,this.$props),r=this.trimAlignValue(o);if(r===a)return;this.onChange({sValue:r}),this.$emit("afterChange",r),this.onEnd()}},getLowerBound(){const e=this.$props.startPoint||this.$props.min;return this.$data.sValue>e?e:this.$data.sValue},getUpperBound(){return this.$data.sValue<this.$props.startPoint?this.$props.startPoint:this.$data.sValue},trimAlignValue(e){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(e===null)return null;const t=x(x({},this.$props),s),n=ce(e,t);return Le(n,t)},getTrack(e){let{prefixCls:s,reverse:t,vertical:n,included:a,minimumTrackStyle:o,mergedTrackStyle:r,length:i,offset:l}=e;return d(de,{class:`${s}-track`,vertical:n,included:a,offset:l,reverse:t,length:i,style:x(x({},o),r)},null)},renderSlider(){const{prefixCls:e,vertical:s,included:t,disabled:n,minimumTrackStyle:a,trackStyle:o,handleStyle:r,tabindex:i,ariaLabelForHandle:l,ariaLabelledByForHandle:h,ariaValueTextFormatterForHandle:v,min:y,max:C,startPoint:M,reverse:c,handle:u,defaultHandle:p}=this,V=u||p,{sValue:g,dragging:B}=this,P=this.calcOffset(g),T=V({class:`${e}-handle`,prefixCls:e,vertical:s,offset:P,value:g,dragging:B,disabled:n,min:y,max:C,reverse:c,index:0,tabindex:i,ariaLabel:l,ariaLabelledBy:h,ariaValueTextFormatter:v,style:r[0]||r,ref:k=>this.saveHandle(0,k),onFocus:this.onFocus,onBlur:this.onBlur}),$=M!==void 0?this.calcOffset(M):0,S=o[0]||o;return{tracks:this.getTrack({prefixCls:e,reverse:c,vertical:s,included:t,offset:$,minimumTrackStyle:a,mergedTrackStyle:S,length:P-$}),handles:T}}}}),pt=Ne(ft),ee=e=>{let{value:s,handle:t,bounds:n,props:a}=e;const{allowCross:o,pushable:r}=a,i=Number(r),l=ce(s,a);let h=l;return!o&&t!=null&&n!==void 0&&(t>0&&l<=n[t-1]+i&&(h=n[t-1]+i),t<n.length-1&&l>=n[t+1]-i&&(h=n[t+1]-i)),Le(h,a)},mt={defaultValue:w.arrayOf(w.number),value:w.arrayOf(w.number),count:Number,pushable:We(w.oneOfType([w.looseBool,w.number])),allowCross:{type:Boolean,default:void 0},disabled:{type:Boolean,default:void 0},reverse:{type:Boolean,default:void 0},tabindex:w.arrayOf(w.number),prefixCls:String,min:Number,max:Number,autofocus:{type:Boolean,default:void 0},ariaLabelGroupForHandles:Array,ariaLabelledByGroupForHandles:Array,ariaValueTextFormatterGroupForHandles:Array,draggableTrack:{type:Boolean,default:void 0}},vt=Y({compatConfig:{MODE:3},name:"Range",mixins:[le],inheritAttrs:!1,props:Te(mt,{count:1,allowCross:!0,pushable:!1,tabindex:[],draggableTrack:!1,ariaLabelGroupForHandles:[],ariaLabelledByGroupForHandles:[],ariaValueTextFormatterGroupForHandles:[]}),emits:["beforeChange","afterChange","change"],displayName:"Range",data(){const{count:e,min:s,max:t}=this,n=Array(...Array(e+1)).map(()=>s),a=ie(this,"defaultValue")?this.defaultValue:n;let{value:o}=this;o===void 0&&(o=a);const r=o.map((l,h)=>ee({value:l,handle:h,props:this.$props}));return{sHandle:null,recent:r[0]===t?0:r.length-1,bounds:r}},watch:{value:{handler(e){const{bounds:s}=this;this.setChangeValue(e||s)},deep:!0},min(){const{value:e}=this;this.setChangeValue(e||this.bounds)},max(){const{value:e}=this;this.setChangeValue(e||this.bounds)}},methods:{setChangeValue(e){const{bounds:s}=this;let t=e.map((n,a)=>ee({value:n,handle:a,bounds:s,props:this.$props}));if(s.length===t.length){if(t.every((n,a)=>n===s[a]))return null}else t=e.map((n,a)=>ee({value:n,handle:a,props:this.$props}));if(this.setState({bounds:t}),e.some(n=>Oe(n,this.$props))){const n=e.map(a=>ce(a,this.$props));this.$emit("change",n)}},onChange(e){if(!ie(this,"value"))this.setState(e);else{const a={};["sHandle","recent"].forEach(o=>{e[o]!==void 0&&(a[o]=e[o])}),Object.keys(a).length&&this.setState(a)}const n=x(x({},this.$data),e).bounds;this.$emit("change",n)},positionGetValue(e){const s=this.getValue(),t=this.calcValueByPos(e),n=this.getClosestBound(t),a=this.getBoundNeedMoving(t,n),o=s[a];if(t===o)return null;const r=[...s];return r[a]=t,r},onStart(e){const{bounds:s}=this;this.$emit("beforeChange",s);const t=this.calcValueByPos(e);this.startValue=t,this.startPosition=e;const n=this.getClosestBound(t);this.prevMovedHandleIndex=this.getBoundNeedMoving(t,n),this.setState({sHandle:this.prevMovedHandleIndex,recent:this.prevMovedHandleIndex});const a=s[this.prevMovedHandleIndex];if(t===a)return;const o=[...s];o[this.prevMovedHandleIndex]=t,this.onChange({bounds:o})},onEnd(e){const{sHandle:s}=this;this.removeDocumentEvents(),s||(this.dragTrack=!1),(s!==null||e)&&this.$emit("afterChange",this.bounds),this.setState({sHandle:null})},onMove(e,s,t,n){Q(e);const{$data:a,$props:o}=this,r=o.max||100,i=o.min||0;if(t){let C=o.vertical?-s:s;C=o.reverse?-C:C;const M=r-Math.max(...n),c=i-Math.min(...n),u=Math.min(Math.max(C/(this.getSliderLength()/100),c),M),p=n.map(V=>Math.floor(Math.max(Math.min(V+u,r),i)));a.bounds.map((V,g)=>V===p[g]).some(V=>!V)&&this.onChange({bounds:p});return}const{bounds:l,sHandle:h}=this,v=this.calcValueByPos(s),y=l[h];v!==y&&this.moveTo(v)},onKeyboard(e){const{reverse:s,vertical:t}=this.$props,n=Re(e,t,s);if(n){Q(e);const{bounds:a,sHandle:o}=this,r=a[o===null?this.recent:o],i=n(r,this.$props),l=ee({value:i,handle:o,bounds:a,props:this.$props});if(l===r)return;this.moveTo(l,!0)}},getClosestBound(e){const{bounds:s}=this;let t=0;for(let n=1;n<s.length-1;n+=1)e>=s[n]&&(t=n);return Math.abs(s[t+1]-e)<Math.abs(s[t]-e)&&(t+=1),t},getBoundNeedMoving(e,s){const{bounds:t,recent:n}=this;let a=s;const o=t[s+1]===t[s];return o&&t[n]===t[s]&&(a=n),o&&e!==t[s+1]&&(a=e<t[s+1]?s:s+1),a},getLowerBound(){return this.bounds[0]},getUpperBound(){const{bounds:e}=this;return e[e.length-1]},getPoints(){const{marks:e,step:s,min:t,max:n}=this,a=this.internalPointsCache;if(!a||a.marks!==e||a.step!==s){const o=x({},e);if(s!==null)for(let i=t;i<=n;i+=s)o[i]=i;const r=Object.keys(o).map(parseFloat);r.sort((i,l)=>i-l),this.internalPointsCache={marks:e,step:s,points:r}}return this.internalPointsCache.points},moveTo(e,s){const t=[...this.bounds],{sHandle:n,recent:a}=this,o=n===null?a:n;t[o]=e;let r=o;this.$props.pushable!==!1?this.pushSurroundingHandles(t,r):this.$props.allowCross&&(t.sort((i,l)=>i-l),r=t.indexOf(e)),this.onChange({recent:r,sHandle:r,bounds:t}),s&&(this.$emit("afterChange",t),this.setState({},()=>{this.handlesRefs[r].focus()}),this.onEnd())},pushSurroundingHandles(e,s){const t=e[s],{pushable:n}=this,a=Number(n);let o=0;if(e[s+1]-t<a&&(o=1),t-e[s-1]<a&&(o=-1),o===0)return;const r=s+o,i=o*(e[r]-t);this.pushHandle(e,r,o,a-i)||(e[s]=e[r]-o*a)},pushHandle(e,s,t,n){const a=e[s];let o=e[s];for(;t*(o-a)<n;){if(!this.pushHandleOnePoint(e,s,t))return e[s]=a,!1;o=e[s]}return!0},pushHandleOnePoint(e,s,t){const n=this.getPoints(),o=n.indexOf(e[s])+t;if(o>=n.length||o<0)return!1;const r=s+t,i=n[o],{pushable:l}=this,h=Number(l),v=t*(e[r]-i);return this.pushHandle(e,r,t,h-v)?(e[s]=i,!0):!1},trimAlignValue(e){const{sHandle:s,bounds:t}=this;return ee({value:e,handle:s,bounds:t,props:this.$props})},ensureValueNotConflict(e,s,t){let{allowCross:n,pushable:a}=t;const o=this.$data||{},{bounds:r}=o;if(e=e===void 0?o.sHandle:e,a=Number(a),!n&&e!=null&&r!==void 0){if(e>0&&s<=r[e-1]+a)return r[e-1]+a;if(e<r.length-1&&s>=r[e+1]-a)return r[e+1]-a}return s},getTrack(e){let{bounds:s,prefixCls:t,reverse:n,vertical:a,included:o,offsets:r,trackStyle:i}=e;return s.slice(0,-1).map((l,h)=>{const v=h+1,y=X({[`${t}-track`]:!0,[`${t}-track-${v}`]:!0});return d(de,{class:y,vertical:a,reverse:n,included:o,offset:r[v-1],length:r[v]-r[v-1],style:i[h],key:v},null)})},renderSlider(){const{sHandle:e,bounds:s,prefixCls:t,vertical:n,included:a,disabled:o,min:r,max:i,reverse:l,handle:h,defaultHandle:v,trackStyle:y,handleStyle:C,tabindex:M,ariaLabelGroupForHandles:c,ariaLabelledByGroupForHandles:u,ariaValueTextFormatterGroupForHandles:p}=this,V=h||v,g=s.map(T=>this.calcOffset(T)),B=`${t}-handle`,P=s.map((T,$)=>{let S=M[$]||0;(o||M[$]===null)&&(S=null);const k=e===$;return V({class:X({[B]:!0,[`${B}-${$+1}`]:!0,[`${B}-dragging`]:k}),prefixCls:t,vertical:n,dragging:k,offset:g[$],value:T,index:$,tabindex:S,min:r,max:i,reverse:l,disabled:o,style:C[$],ref:R=>this.saveHandle($,R),onFocus:this.onFocus,onBlur:this.onBlur,ariaLabel:c[$],ariaLabelledBy:u[$],ariaValueTextFormatter:p[$]})});return{tracks:this.getTrack({bounds:s,prefixCls:t,reverse:l,vertical:n,included:a,offsets:g,trackStyle:y}),handles:P}}}}),gt=Ne(vt),bt=Y({compatConfig:{MODE:3},name:"SliderTooltip",inheritAttrs:!1,props:it(),setup(e,s){let{attrs:t,slots:n}=s;const a=q(null),o=q(null);function r(){pe.cancel(o.value),o.value=null}function i(){o.value=pe(()=>{var h;(h=a.value)===null||h===void 0||h.forcePopupAlign(),o.value=null})}const l=()=>{r(),e.open&&i()};return tt([()=>e.open,()=>e.title],()=>{l()},{flush:"post",immediate:!0}),nt(()=>{l()}),Pe(()=>{r()}),()=>d(lt,H(H({ref:a},e),t),n)}}),xt=e=>{const{componentCls:s,controlSize:t,dotSize:n,marginFull:a,marginPart:o,colorFillContentHover:r}=e;return{[s]:x(x({},Ke(e)),{position:"relative",height:t,margin:`${o}px ${a}px`,padding:0,cursor:"pointer",touchAction:"none","&-vertical":{margin:`${a}px ${o}px`},[`${s}-rail`]:{position:"absolute",backgroundColor:e.colorFillTertiary,borderRadius:e.borderRadiusXS,transition:`background-color ${e.motionDurationMid}`},[`${s}-track`]:{position:"absolute",backgroundColor:e.colorPrimaryBorder,borderRadius:e.borderRadiusXS,transition:`background-color ${e.motionDurationMid}`},"&:hover":{[`${s}-rail`]:{backgroundColor:e.colorFillSecondary},[`${s}-track`]:{backgroundColor:e.colorPrimaryBorderHover},[`${s}-dot`]:{borderColor:r},[`${s}-handle::after`]:{boxShadow:`0 0 0 ${e.handleLineWidth}px ${e.colorPrimaryBorderHover}`},[`${s}-dot-active`]:{borderColor:e.colorPrimary}},[`${s}-handle`]:{position:"absolute",width:e.handleSize,height:e.handleSize,outline:"none",[`${s}-dragging`]:{zIndex:1},"&::before":{content:'""',position:"absolute",insetInlineStart:-e.handleLineWidth,insetBlockStart:-e.handleLineWidth,width:e.handleSize+e.handleLineWidth*2,height:e.handleSize+e.handleLineWidth*2,backgroundColor:"transparent"},"&::after":{content:'""',position:"absolute",insetBlockStart:0,insetInlineStart:0,width:e.handleSize,height:e.handleSize,backgroundColor:e.colorBgElevated,boxShadow:`0 0 0 ${e.handleLineWidth}px ${e.colorPrimaryBorder}`,borderRadius:"50%",cursor:"pointer",transition:`
            inset-inline-start ${e.motionDurationMid},
            inset-block-start ${e.motionDurationMid},
            width ${e.motionDurationMid},
            height ${e.motionDurationMid},
            box-shadow ${e.motionDurationMid}
          `},"&:hover, &:active, &:focus":{"&::before":{insetInlineStart:-((e.handleSizeHover-e.handleSize)/2+e.handleLineWidthHover),insetBlockStart:-((e.handleSizeHover-e.handleSize)/2+e.handleLineWidthHover),width:e.handleSizeHover+e.handleLineWidthHover*2,height:e.handleSizeHover+e.handleLineWidthHover*2},"&::after":{boxShadow:`0 0 0 ${e.handleLineWidthHover}px ${e.colorPrimary}`,width:e.handleSizeHover,height:e.handleSizeHover,insetInlineStart:(e.handleSize-e.handleSizeHover)/2,insetBlockStart:(e.handleSize-e.handleSizeHover)/2}}},[`${s}-mark`]:{position:"absolute",fontSize:e.fontSize},[`${s}-mark-text`]:{position:"absolute",display:"inline-block",color:e.colorTextDescription,textAlign:"center",wordBreak:"keep-all",cursor:"pointer",userSelect:"none","&-active":{color:e.colorText}},[`${s}-step`]:{position:"absolute",background:"transparent",pointerEvents:"none"},[`${s}-dot`]:{position:"absolute",width:n,height:n,backgroundColor:e.colorBgElevated,border:`${e.handleLineWidth}px solid ${e.colorBorderSecondary}`,borderRadius:"50%",cursor:"pointer",transition:`border-color ${e.motionDurationSlow}`,"&-active":{borderColor:e.colorPrimaryBorder}},[`&${s}-disabled`]:{cursor:"not-allowed",[`${s}-rail`]:{backgroundColor:`${e.colorFillSecondary} !important`},[`${s}-track`]:{backgroundColor:`${e.colorTextDisabled} !important`},[`
          ${s}-dot
        `]:{backgroundColor:e.colorBgElevated,borderColor:e.colorTextDisabled,boxShadow:"none",cursor:"not-allowed"},[`${s}-handle::after`]:{backgroundColor:e.colorBgElevated,cursor:"not-allowed",width:e.handleSize,height:e.handleSize,boxShadow:`0 0 0 ${e.handleLineWidth}px ${new st(e.colorTextDisabled).onBackground(e.colorBgContainer).toHexString()}`,insetInlineStart:0,insetBlockStart:0},[`
          ${s}-mark-text,
          ${s}-dot
        `]:{cursor:"not-allowed !important"}}})}},je=(e,s)=>{const{componentCls:t,railSize:n,handleSize:a,dotSize:o}=e,r=s?"paddingBlock":"paddingInline",i=s?"width":"height",l=s?"height":"width",h=s?"insetBlockStart":"insetInlineStart",v=s?"top":"insetInlineStart";return{[r]:n,[l]:n*3,[`${t}-rail`]:{[i]:"100%",[l]:n},[`${t}-track`]:{[l]:n},[`${t}-handle`]:{[h]:(n*3-a)/2},[`${t}-mark`]:{insetInlineStart:0,top:0,[v]:a,[i]:"100%"},[`${t}-step`]:{insetInlineStart:0,top:0,[v]:n,[i]:"100%",[l]:n},[`${t}-dot`]:{position:"absolute",[h]:(n-o)/2}}},yt=e=>{const{componentCls:s,marginPartWithMark:t}=e;return{[`${s}-horizontal`]:x(x({},je(e,!0)),{[`&${s}-with-marks`]:{marginBottom:t}})}},St=e=>{const{componentCls:s}=e;return{[`${s}-vertical`]:x(x({},je(e,!1)),{height:"100%"})}},wt=Ue("Slider",e=>{const s=Ge(e,{marginPart:(e.controlHeight-e.controlSize)/2,marginFull:e.controlSize/2,marginPartWithMark:e.controlHeightLG-e.controlSize});return[xt(s),yt(s),St(s)]},e=>{const t=e.controlHeightLG/4,n=e.controlHeightSM/2,a=e.lineWidth+1,o=e.lineWidth+1*3;return{controlSize:t,railSize:4,handleSize:t,handleSizeHover:n,dotSize:8,handleLineWidth:a,handleLineWidthHover:o}});var Me=function(e,s){var t={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&s.indexOf(n)<0&&(t[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)s.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(t[n[a]]=e[n[a]]);return t};const Ct=e=>typeof e=="number"?e.toString():"",$t=()=>({id:String,prefixCls:String,tooltipPrefixCls:String,range:I([Boolean,Object]),reverse:D(),min:Number,max:Number,step:I([Object,Number]),marks:Je(),dots:D(),value:I([Array,Number]),defaultValue:I([Array,Number]),included:D(),disabled:D(),vertical:D(),tipFormatter:I([Function,Object],()=>Ct),tooltipOpen:D(),tooltipVisible:D(),tooltipPlacement:Ye(),getTooltipPopupContainer:J(),autofocus:D(),handleStyle:I([Array,Object]),trackStyle:I([Array,Object]),onChange:J(),onAfterChange:J(),onFocus:J(),onBlur:J(),"onUpdate:value":J()}),Mt=Y({compatConfig:{MODE:3},name:"ASlider",inheritAttrs:!1,props:$t(),slots:Object,setup(e,s){let{attrs:t,slots:n,emit:a,expose:o}=s;const{prefixCls:r,rootPrefixCls:i,direction:l,getPopupContainer:h,configProvider:v}=qe("slider",e),[y,C]=wt(r),M=ut(),c=q(),u=q({}),p=(S,k)=>{u.value[S]=k},V=ue(()=>e.tooltipPlacement?e.tooltipPlacement:e.vertical?l.value==="rtl"?"left":"right":"top"),g=()=>{var S;(S=c.value)===null||S===void 0||S.focus()},B=()=>{var S;(S=c.value)===null||S===void 0||S.blur()},P=S=>{a("update:value",S),a("change",S),M.onFieldChange()},T=S=>{a("blur",S)};o({focus:g,blur:B});const $=S=>{var{tooltipPrefixCls:k}=S,R=S.info,{value:_,dragging:z,index:L}=R,N=Me(R,["value","dragging","index"]);const{tipFormatter:j,tooltipOpen:Z=e.tooltipVisible,getTooltipPopupContainer:ae}=e,ze=j?u.value[L]||z:!1,Ee=Z||Z===void 0&&ze;return d(bt,{prefixCls:k,title:j?j(_):"",open:Ee,placement:V.value,transitionName:`${i.value}-zoom-down`,key:L,overlayClassName:`${r.value}-tooltip`,getPopupContainer:ae||(h==null?void 0:h.value)},{default:()=>[d(He,H(H({},N),{},{value:_,onMouseenter:()=>p(L,!0),onMouseleave:()=>p(L,!1)}),null)]})};return()=>{const{tooltipPrefixCls:S,range:k,id:R=M.id.value}=e,_=Me(e,["tooltipPrefixCls","range","id"]),z=v.getPrefixCls("tooltip",S),L=X(t.class,{[`${r.value}-rtl`]:l.value==="rtl"},C.value);l.value==="rtl"&&!_.vertical&&(_.reverse=!_.reverse);let N;return typeof k=="object"&&(N=k.draggableTrack),y(k?d(gt,H(H(H({},t),_),{},{step:_.step,draggableTrack:N,class:L,ref:c,handle:j=>$({tooltipPrefixCls:z,prefixCls:r.value,info:j}),prefixCls:r.value,onChange:P,onBlur:T}),{mark:n.mark}):d(pt,H(H(H({},t),_),{},{id:R,step:_.step,class:L,ref:c,handle:j=>$({tooltipPrefixCls:z,prefixCls:r.value,info:j}),prefixCls:r.value,onChange:P,onBlur:T}),{mark:n.mark}))}}}),Vt=Xe(Mt),Tt={class:"p-5"},Bt={class:"flex items-center justify-between"},Pt={class:"text-center"},kt={class:"text-center"},_t={class:"text-xl font-bold"},Ht={class:"space-y-4"},Ot={class:"flex items-center justify-between rounded bg-gray-50 p-4"},Ft={class:"flex items-center justify-between rounded bg-gray-50 p-4"},Lt={class:"rounded bg-gray-50 p-4"},Rt={class:"mb-3 flex items-center justify-between"},Nt={class:"text-lg font-bold"},jt={class:"flex justify-center"},zt={class:"space-y-3"},Et={class:"flex justify-between"},Dt={class:"font-medium"},At={class:"flex justify-between"},It={class:"flex justify-between"},Wt={class:"font-medium"},Ut={class:"flex justify-between"},Gt={class:"font-medium"},Kt={class:"flex justify-between"},Xt={class:"font-medium"},Yt={class:"flex justify-between"},Jt={class:"font-medium text-orange-500"},qt=Y({name:"IrrigationMobile",__name:"index",setup(e){const s=q(1),t=q([{id:1,name:"东风灌站",status:"online",isRunning:!0,flow:85.6,targetFlow:90,power:450,voltage:380,current:125.8,waterLevel:78.5,temperature:45.2,pressure:2.5,runningTime:8.5,totalRunTime:2580,lastMaintenance:"2024-01-10",nextMaintenance:"2024-04-10"},{id:2,name:"红旗灌站",status:"maintenance",isRunning:!1,flow:0,targetFlow:75,power:280,voltage:0,current:0,waterLevel:65.2,temperature:25,pressure:0,runningTime:0,totalRunTime:1890,lastMaintenance:"2023-12-15",nextMaintenance:"2024-03-15"},{id:3,name:"胜利灌站",status:"online",isRunning:!0,flow:42.3,targetFlow:50,power:150,voltage:380,current:68.5,waterLevel:82.1,temperature:38.8,pressure:1.8,runningTime:6.2,totalRunTime:1245,lastMaintenance:"2024-01-05",nextMaintenance:"2024-07-05"}]),n=ue(()=>t.value.find(c=>c.id===s.value)||t.value[0]),a=ve({autoMode:!0,targetFlow:90,maxPower:500,emergencyStop:!1}),o=ve({visible:!1,title:"",content:"",action:null}),r=c=>{switch(c){case"online":return"success";case"offline":return"error";case"maintenance":return"warning";default:return"default"}},i=c=>{switch(c){case"online":return"在线";case"offline":return"离线";case"maintenance":return"维修中";default:return"未知"}},l=()=>{if(n.value.status!=="online"){U.error("灌站不在线，无法启动");return}o.title="确认启动",o.content=`确定要启动 ${n.value.name} 吗？`,o.action=()=>{n.value.isRunning=!0,n.value.flow=Math.random()*50+50,n.value.current=Math.random()*50+80,n.value.pressure=Math.random()*1+1.5,U.success("灌站启动成功")},o.visible=!0},h=()=>{o.title="确认停止",o.content=`确定要停止 ${n.value.name} 吗？`,o.action=()=>{n.value.isRunning=!1,n.value.flow=0,n.value.current=0,n.value.pressure=0,U.success("灌站停止成功")},o.visible=!0},v=()=>{o.title="紧急停止",o.content="紧急停止将立即关闭所有设备，确定执行吗？",o.action=()=>{a.emergencyStop=!0,n.value.isRunning=!1,n.value.flow=0,n.value.current=0,n.value.pressure=0,U.warning("已执行紧急停止"),setTimeout(()=>{a.emergencyStop=!1},3e3)},o.visible=!0},y=c=>{if(!n.value.isRunning){U.error("灌站未运行，无法调节流量");return}a.targetFlow=c,setTimeout(()=>{n.value.targetFlow=c,n.value.flow=c*(.9+Math.random()*.2),U.success(`目标流量已调节至 ${c} m³/h`)},1e3)},C=c=>{a.autoMode=c,U.info(c?"已切换到自动模式":"已切换到手动模式")},M=()=>{o.action&&o.action(),o.visible=!1};return Be(()=>{setInterval(()=>{t.value.forEach(c=>{c.isRunning&&c.status==="online"&&(c.flow+=(Math.random()-.5)*2,c.flow=Math.max(0,Math.min(c.flow,c.targetFlow*1.1)),c.current+=(Math.random()-.5)*5,c.current=Math.max(0,c.current),c.temperature+=(Math.random()-.5)*1,c.temperature=Math.max(20,Math.min(c.temperature,60)),c.waterLevel+=(Math.random()-.5)*.5,c.waterLevel=Math.max(50,Math.min(c.waterLevel,100)),c.runningTime+=1/3600)})},3e3)}),(c,u)=>(oe(),ge("div",Tt,[d(m(A),{title:"专用APP功能特色",class:"mb-5"},{default:b(()=>[d(m(ne),{gutter:16},{default:b(()=>[d(m(O),{span:6},{default:b(()=>u[5]||(u[5]=[f("div",{class:"rounded bg-blue-50 p-4 text-center"},[f("div",{class:"mb-2 text-2xl"},"🚀"),f("div",{class:"font-bold text-blue-600"},"启停操作"),f("div",{class:"text-sm text-gray-600"},"远程控制灌站启停")],-1)])),_:1,__:[5]}),d(m(O),{span:6},{default:b(()=>u[6]||(u[6]=[f("div",{class:"rounded bg-red-50 p-4 text-center"},[f("div",{class:"mb-2 text-2xl"},"⚠️"),f("div",{class:"font-bold text-red-600"},"故障报警"),f("div",{class:"text-sm text-gray-600"},"实时故障推送提醒")],-1)])),_:1,__:[6]}),d(m(O),{span:6},{default:b(()=>u[7]||(u[7]=[f("div",{class:"rounded bg-green-50 p-4 text-center"},[f("div",{class:"mb-2 text-2xl"},"💧"),f("div",{class:"font-bold text-green-600"},"流量显示"),f("div",{class:"text-sm text-gray-600"},"实时流量数据监控")],-1)])),_:1,__:[7]}),d(m(O),{span:6},{default:b(()=>u[8]||(u[8]=[f("div",{class:"rounded bg-purple-50 p-4 text-center"},[f("div",{class:"mb-2 text-2xl"},"📱"),f("div",{class:"font-bold text-purple-600"},"移动便捷"),f("div",{class:"text-sm text-gray-600"},"随时随地操作管理")],-1)])),_:1,__:[8]})]),_:1})]),_:1}),d(m(A),{title:"选择灌站",class:"mb-5"},{default:b(()=>[d(m(he),{value:s.value,"onUpdate:value":u[0]||(u[0]=p=>s.value=p),style:{width:"100%"},size:"large"},{default:b(()=>[(oe(!0),ge(at,null,ot(t.value,p=>(oe(),rt(m(he).Option,{key:p.id,value:p.id},{default:b(()=>[f("div",Bt,[f("span",null,F(p.name),1),d(m(fe),{status:r(p.status),text:i(p.status)},null,8,["status","text"])])]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1}),d(m(A),{title:n.value.name+" - 状态概览",class:"mb-5"},{default:b(()=>[d(m(ne),{gutter:[16,16]},{default:b(()=>[d(m(O),{span:12},{default:b(()=>[f("div",Pt,[f("div",{class:be(["mb-2 text-2xl font-bold",{"text-green-500":n.value.isRunning,"text-red-500":!n.value.isRunning}])},F(n.value.isRunning?"运行中":"已停止"),3),d(m(fe),{status:r(n.value.status),text:i(n.value.status)},null,8,["status","text"])])]),_:1}),d(m(O),{span:12},{default:b(()=>[f("div",kt,[u[9]||(u[9]=f("div",{class:"text-lg text-gray-600"},"运行时间",-1)),f("div",_t,F(n.value.runningTime.toFixed(1))+"h ",1)])]),_:1})]),_:1})]),_:1},8,["title"]),d(m(A),{title:"实时监控",class:"mb-5"},{default:b(()=>[d(m(ne),{gutter:[16,16]},{default:b(()=>[d(m(O),{span:12},{default:b(()=>[d(m(se),{title:"当前流量",value:n.value.flow,suffix:"m³/h",precision:1,"value-style":{color:n.value.isRunning?"#52c41a":"#999",fontSize:"20px"}},null,8,["value","value-style"]),d(m(xe),{percent:n.value.flow/n.value.targetFlow*100,"stroke-color":n.value.isRunning?"#52c41a":"#d9d9d9",class:"mt-2"},null,8,["percent","stroke-color"])]),_:1}),d(m(O),{span:12},{default:b(()=>[d(m(se),{title:"功率",value:n.value.isRunning?n.value.power:0,suffix:"kW","value-style":{color:n.value.isRunning?"#1890ff":"#999",fontSize:"20px"}},null,8,["value","value-style"])]),_:1}),d(m(O),{span:12},{default:b(()=>[d(m(se),{title:"电流",value:n.value.current,suffix:"A",precision:1,"value-style":{color:n.value.isRunning?"#faad14":"#999",fontSize:"18px"}},null,8,["value","value-style"])]),_:1}),d(m(O),{span:12},{default:b(()=>[d(m(se),{title:"水位",value:n.value.waterLevel,suffix:"%",precision:1,"value-style":{color:"#722ed1",fontSize:"18px"}},null,8,["value"]),d(m(xe),{percent:n.value.waterLevel,"stroke-color":"#722ed1",class:"mt-2"},null,8,["percent"])]),_:1})]),_:1})]),_:1}),d(m(A),{title:"控制面板",class:"mb-5"},{default:b(()=>[f("div",Ht,[f("div",Ot,[u[12]||(u[12]=f("span",{class:"font-medium"},"运行控制",-1)),d(m(De),{size:"large"},{default:b(()=>[d(m(W),{type:"primary",size:"large",disabled:n.value.isRunning||n.value.status!=="online",onClick:l},{default:b(()=>u[10]||(u[10]=[G(" 启动 ")])),_:1,__:[10]},8,["disabled"]),d(m(W),{danger:"",size:"large",disabled:!n.value.isRunning,onClick:h},{default:b(()=>u[11]||(u[11]=[G(" 停止 ")])),_:1,__:[11]},8,["disabled"])]),_:1})]),f("div",Ft,[u[13]||(u[13]=f("span",{class:"font-medium"},"自动模式",-1)),d(m(et),{checked:a.autoMode,"onUpdate:checked":u[1]||(u[1]=p=>a.autoMode=p),onChange:C},null,8,["checked"])]),f("div",Lt,[f("div",Rt,[u[14]||(u[14]=f("span",{class:"font-medium"},"目标流量",-1)),f("span",Nt,F(a.targetFlow)+" m³/h",1)]),d(m(Vt),{value:a.targetFlow,"onUpdate:value":u[2]||(u[2]=p=>a.targetFlow=p),min:0,max:150,step:5,disabled:!a.autoMode||!n.value.isRunning,onAfterChange:y},null,8,["value","disabled"])]),f("div",jt,[d(m(W),{danger:"",size:"large",type:"primary",disabled:a.emergencyStop,onClick:v,class:"h-16 w-full text-xl font-bold"},{default:b(()=>[G(F(a.emergencyStop?"紧急停止中...":"紧急停止"),1)]),_:1},8,["disabled"])])])]),_:1}),d(m(A),{title:"设备信息",class:"mb-5"},{default:b(()=>[f("div",zt,[f("div",Et,[u[15]||(u[15]=f("span",null,"电压：",-1)),f("span",Dt,F(n.value.voltage)+"V",1)]),f("div",At,[u[16]||(u[16]=f("span",null,"温度：",-1)),f("span",{class:be(["font-medium",{"text-red-500":n.value.temperature>50,"text-orange-500":n.value.temperature>40,"text-green-500":n.value.temperature<=40}])},F(n.value.temperature.toFixed(1))+"°C ",3)]),f("div",It,[u[17]||(u[17]=f("span",null,"压力：",-1)),f("span",Wt,F(n.value.pressure.toFixed(1))+"MPa",1)]),f("div",Ut,[u[18]||(u[18]=f("span",null,"累计运行：",-1)),f("span",Gt,F(n.value.totalRunTime)+"h",1)]),f("div",Kt,[u[19]||(u[19]=f("span",null,"上次维护：",-1)),f("span",Xt,F(n.value.lastMaintenance),1)]),f("div",Yt,[u[20]||(u[20]=f("span",null,"下次维护：",-1)),f("span",Jt,F(n.value.nextMaintenance),1)])])]),_:1}),d(m(A),{title:"快捷操作"},{default:b(()=>[d(m(ne),{gutter:[16,16]},{default:b(()=>[d(m(O),{span:12},{default:b(()=>[d(m(W),{block:"",size:"large",type:"default"},{default:b(()=>u[21]||(u[21]=[G(" 查看历史数据 ")])),_:1,__:[21]})]),_:1}),d(m(O),{span:12},{default:b(()=>[d(m(W),{block:"",size:"large",type:"default"},{default:b(()=>u[22]||(u[22]=[G(" 故障报警 ")])),_:1,__:[22]})]),_:1}),d(m(O),{span:12},{default:b(()=>[d(m(W),{block:"",size:"large",type:"default"},{default:b(()=>u[23]||(u[23]=[G(" 维护记录 ")])),_:1,__:[23]})]),_:1}),d(m(O),{span:12},{default:b(()=>[d(m(W),{block:"",size:"large",type:"default"},{default:b(()=>u[24]||(u[24]=[G(" 联系管理员 ")])),_:1,__:[24]})]),_:1})]),_:1})]),_:1}),d(m(Qe),{open:o.visible,"onUpdate:open":u[3]||(u[3]=p=>o.visible=p),title:o.title,onOk:M,onCancel:u[4]||(u[4]=p=>o.visible=!1)},{default:b(()=>[f("p",null,F(o.content),1)]),_:1},8,["open","title"])]))}}),Ln=Ze(qt,[["__scopeId","data-v-9528b04e"]]);export{Ln as default};
