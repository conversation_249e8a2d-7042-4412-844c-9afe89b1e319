import{b0 as e}from"./bootstrap-CFDAkNgp.js";import{A as r}from"./authentication-DJjcFBOt.js";import{a4 as s,J as t,a1 as o,aa as c,ab as i,a7 as a}from"../jse/index-index-B2UBupFX.js";import"./theme-toggle.vue_vue_type_script_setup_true_lang-CM98aRHS.js";const h=s({__name:"auth",setup(m){const p=t(()=>o.app.name),n=t(()=>o.logo.source);return(u,l)=>(i(),c(a(r),{"app-name":p.value,logo:n.value,"page-description":a(e)("authentication.pageDesc"),"page-title":a(e)("authentication.pageTitle")},null,8,["app-name","logo","page-description","page-title"]))}});export{h as default};
