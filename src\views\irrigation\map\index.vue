<script lang="ts" setup>
import { onMounted, ref, reactive, nextTick, computed, watch } from 'vue';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ton,
  Card,
  Col,
  Descriptions,
  Drawer,
  message,
  Progress,
  Row,
  Select,
  Space,
  Statistic,
  Table,
  TabPane,
  Tabs,
  Tag,
} from 'ant-design-vue';
import {
  loadAMap,
  createMap,
  addMarkersToMap,
  clearMarkers,
  type StationMarker,
} from '#/utils/amap';

defineOptions({ name: 'IrrigationMap' });

// 地图容器引用
const mapContainer = ref<HTMLDivElement>();
const map = ref<any>(null);

// 抽屉显示控制
const drawerVisible = ref(false);
const selectedStation = ref<any>(null);

// 筛选条件
const filterOptions = reactive({
  status: 'all', // all, online, offline, maintenance
  type: 'all', // all, large, medium, small, micro
});

// 模拟154座提灌站数据 - 体现所有业务需求
const stationData = ref<StationMarker[]>([
  {
    id: 1,
    name: '东风灌站',
    longitude: 104.0668,
    latitude: 30.5728,
    status: 'online',
    type: 'large',
    power: 450,
    buildYear: 2018,
    irrigationArea: 1200,
    manager: '张三',
    phone: '13800138001',
    flow: 85.6,
    waterLevel: 78.5,
    voltage: 380,
    current: 125.8,
    // 新增业务需求字段
    lineStatus: 'normal', // 线路状态：正常/老旧/已更换
    safetyLevel: 'high', // 安全等级
    maintenanceRecord: '2024-01-10完成主水泵保养，2023-12-15更换老旧线路',
    equipmentStatus: 'normal', // 设备状态
    pipelineStatus: 'normal', // 管道状态
    lastInspection: '2024-01-15', // 最后检查时间
    nextMaintenance: '2024-04-15', // 下次维护时间
  },
  {
    id: 2,
    name: '红旗灌站',
    longitude: 104.0868,
    latitude: 30.5928,
    status: 'maintenance',
    type: 'medium',
    power: 280,
    buildYear: 2015,
    irrigationArea: 800,
    manager: '李四',
    phone: '13800138002',
    flow: 0,
    waterLevel: 65.2,
    voltage: 0,
    current: 0,
    // 体现老旧线路改造需求
    lineStatus: 'aging', // 老旧线路待改造
    safetyLevel: 'medium',
    maintenanceRecord: '2024-01-08正在进行老旧线路更换改造，消除安全隐患',
    equipmentStatus: 'need_repair',
    pipelineStatus: 'normal',
    lastInspection: '2024-01-08',
    nextMaintenance: '2024-03-15',
  },
  {
    id: 3,
    name: '胜利灌站',
    longitude: 104.1068,
    latitude: 30.6128,
    status: 'online',
    type: 'small',
    power: 150,
    buildYear: 2020,
    irrigationArea: 500,
    manager: '王五',
    phone: '13800138003',
    flow: 42.3,
    waterLevel: 82.1,
    voltage: 380,
    current: 68.5,
    // 体现设备检查维修需求
    lineStatus: 'replaced', // 已更换新线路
    safetyLevel: 'high',
    maintenanceRecord:
      '2024-01-05完成进排水设施设备检查维修，更换零配件管道，保证正常进排水',
    equipmentStatus: 'replaced', // 设备已更换
    pipelineStatus: 'repaired', // 管道已维修
    lastInspection: '2024-01-15',
    nextMaintenance: '2024-07-15',
  },
  // 添加更多模拟数据...
]);

// 生成更多模拟数据
const generateMoreStations = () => {
  const names = [
    '新华',
    '建设',
    '和平',
    '光明',
    '团结',
    '友谊',
    '前进',
    '向阳',
    '丰收',
    '希望',
  ];
  const types = ['large', 'medium', 'small', 'micro'];
  const statuses = ['online', 'offline', 'maintenance'];

  for (let i = 4; i <= 154; i++) {
    const randomName = names[Math.floor(Math.random() * names.length)];
    const randomType = types[Math.floor(Math.random() * types.length)];
    const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];

    stationData.value.push({
      id: i,
      name: `${randomName}灌站${i}`,
      longitude: 104.0668 + (Math.random() - 0.5) * 0.5,
      latitude: 30.5728 + (Math.random() - 0.5) * 0.5,
      status: randomStatus,
      type: randomType,
      power:
        randomType === 'large'
          ? 400 + Math.random() * 100
          : randomType === 'medium'
            ? 200 + Math.random() * 100
            : randomType === 'small'
              ? 100 + Math.random() * 100
              : 50 + Math.random() * 50,
      buildYear: 2010 + Math.floor(Math.random() * 14),
      irrigationArea:
        randomType === 'large'
          ? 800 + Math.random() * 400
          : randomType === 'medium'
            ? 400 + Math.random() * 400
            : randomType === 'small'
              ? 200 + Math.random() * 300
              : 100 + Math.random() * 200,
      manager: `管理员${i}`,
      phone: `138${String(i).padStart(8, '0')}`,
      flow: randomStatus === 'online' ? Math.random() * 100 : 0,
      waterLevel: 60 + Math.random() * 30,
      voltage: randomStatus === 'online' ? 380 : 0,
      current: randomStatus === 'online' ? Math.random() * 150 : 0,
    });
  }
};

// 统计数据
const statistics = computed(() => {
  const total = stationData.value.length;
  const online = stationData.value.filter((s) => s.status === 'online').length;
  const offline = stationData.value.filter(
    (s) => s.status === 'offline',
  ).length;
  const maintenance = stationData.value.filter(
    (s) => s.status === 'maintenance',
  ).length;

  return {
    total,
    online,
    offline,
    maintenance,
    onlineRate: ((online / total) * 100).toFixed(1),
  };
});

// 筛选后的数据
const filteredStations = computed(() => {
  return stationData.value.filter((station) => {
    const statusMatch =
      filterOptions.status === 'all' || station.status === filterOptions.status;
    const typeMatch =
      filterOptions.type === 'all' || station.type === filterOptions.type;
    return statusMatch && typeMatch;
  });
});

// 地图标记点数组
const markers = ref<any[]>([]);

// 初始化地图
const initMap = async () => {
  if (!mapContainer.value) return;

  try {
    // 加载高德地图
    await loadAMap();

    // 创建地图实例
    map.value = createMap(mapContainer.value, {
      zoom: 10,
      center: [104.0668, 30.5728], // 成都坐标
    });

    // 添加标记点
    addMarkers();

    message.success('地图加载成功');
  } catch (error) {
    console.error('地图加载失败:', error);
    message.error('地图加载失败，请检查网络连接或API Key配置');
  }
};

// 添加地图标记
const addMarkers = () => {
  if (!map.value) return;

  // 清除现有标记
  clearMarkers(markers.value, map.value);
  markers.value = [];

  // 添加新标记
  const newMarkers = addMarkersToMap(
    filteredStations.value,
    map.value,
    showStationDetail,
  );
  markers.value = newMarkers;

  // 设置全局函数供信息窗体调用
  (window as any).showStationDetail = showStationDetail;
};

// 显示灌站详情
const showStationDetail = (station: any) => {
  selectedStation.value = station;
  drawerVisible.value = true;
};

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case 'online':
      return 'success';
    case 'offline':
      return 'error';
    case 'maintenance':
      return 'warning';
    default:
      return 'default';
  }
};

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'online':
      return '在线';
    case 'offline':
      return '离线';
    case 'maintenance':
      return '维修中';
    default:
      return '未知';
  }
};

// 获取类型文本
const getTypeText = (type: string) => {
  switch (type) {
    case 'large':
      return '大型';
    case 'medium':
      return '中型';
    case 'small':
      return '小型';
    case 'micro':
      return '微型';
    default:
      return '未知';
  }
};

onMounted(async () => {
  generateMoreStations();
  await nextTick();
  initMap();
});

// 监听筛选条件变化
watch([() => filterOptions.status, () => filterOptions.type], () => {
  addMarkers();
});
</script>

<template>
  <div class="p-5">
    <!-- 统计概览 -->
    <Card title="灌站分布概览" class="mb-5">
      <Row :gutter="16">
        <Col :span="4">
          <Statistic
            title="总数量"
            :value="statistics.total"
            suffix="座"
            :value-style="{ color: '#1890ff' }"
          />
        </Col>
        <Col :span="4">
          <Statistic
            title="在线"
            :value="statistics.online"
            suffix="座"
            :value-style="{ color: '#52c41a' }"
          />
        </Col>
        <Col :span="4">
          <Statistic
            title="离线"
            :value="statistics.offline"
            suffix="座"
            :value-style="{ color: '#ff4d4f' }"
          />
        </Col>
        <Col :span="4">
          <Statistic
            title="维修中"
            :value="statistics.maintenance"
            suffix="座"
            :value-style="{ color: '#faad14' }"
          />
        </Col>
        <Col :span="4">
          <Statistic
            title="在线率"
            :value="statistics.onlineRate"
            suffix="%"
            :value-style="{ color: '#722ed1' }"
          />
        </Col>
      </Row>
    </Card>

    <!-- 业务需求展示 -->
    <Card title="智能化升级改造进度" class="mb-5">
      <Row :gutter="16">
        <Col :span="6">
          <div class="text-center">
            <div class="text-lg font-bold text-green-600">线路改造</div>
            <Progress :percent="78" stroke-color="#52c41a" />
            <div class="mt-1 text-sm text-gray-500">老旧线路更换进度</div>
          </div>
        </Col>
        <Col :span="6">
          <div class="text-center">
            <div class="text-lg font-bold text-blue-600">设备检修</div>
            <Progress :percent="85" stroke-color="#1890ff" />
            <div class="mt-1 text-sm text-gray-500">设施设备检查维修</div>
          </div>
        </Col>
        <Col :span="6">
          <div class="text-center">
            <div class="text-lg font-bold text-purple-600">安全等级</div>
            <Progress :percent="92" stroke-color="#722ed1" />
            <div class="mt-1 text-sm text-gray-500">安全隐患消除率</div>
          </div>
        </Col>
        <Col :span="6">
          <div class="text-center">
            <div class="text-lg font-bold text-orange-600">运行效率</div>
            <Progress :percent="88" stroke-color="#faad14" />
            <div class="mt-1 text-sm text-gray-500">高效稳定运行率</div>
          </div>
        </Col>
      </Row>
    </Card>

    <!-- 筛选控制 -->
    <Card title="筛选条件" class="mb-5">
      <Row :gutter="16" align="middle">
        <Col :span="4">
          <span class="mr-2">运行状态：</span>
          <Select v-model:value="filterOptions.status" style="width: 120px">
            <Select.Option value="all">全部</Select.Option>
            <Select.Option value="online">在线</Select.Option>
            <Select.Option value="offline">离线</Select.Option>
            <Select.Option value="maintenance">维修中</Select.Option>
          </Select>
        </Col>
        <Col :span="4">
          <span class="mr-2">灌站类型：</span>
          <Select v-model:value="filterOptions.type" style="width: 120px">
            <Select.Option value="all">全部</Select.Option>
            <Select.Option value="large">大型</Select.Option>
            <Select.Option value="medium">中型</Select.Option>
            <Select.Option value="small">小型</Select.Option>
            <Select.Option value="micro">微型</Select.Option>
          </Select>
        </Col>
        <Col :span="4">
          <Button type="primary" @click="addMarkers">刷新地图</Button>
        </Col>
      </Row>
    </Card>

    <!-- 地图容器 -->
    <Card title="三农大数据平台 - 提灌站地图分布" class="mb-5">
      <template #extra>
        <Space>
          <Tag color="green">在线: {{ statistics.online }}座</Tag>
          <Tag color="red">离线: {{ statistics.offline }}座</Tag>
          <Tag color="orange">维修: {{ statistics.maintenance }}座</Tag>
        </Space>
      </template>
      <div
        ref="mapContainer"
        class="flex h-96 w-full items-center justify-center bg-gray-100 text-gray-500"
      >
        <div class="text-center">
          <div class="mb-2 text-lg">高德地图容器</div>
          <div class="text-sm">
            集成高德地图API，显示154座提灌站的精确位置分布
          </div>
          <div class="mt-2 text-sm">
            当前显示 {{ filteredStations.length }} 个灌站标记点
          </div>
          <div class="mt-2 text-xs text-blue-500">
            点击标记点查看详细信息：经纬度坐标、设备功率、建设年限、灌溉面积、管护人员等
          </div>
        </div>
      </div>
    </Card>

    <!-- 灌站列表 -->
    <Card title="灌站列表">
      <Row :gutter="[16, 16]">
        <Col
          v-for="station in filteredStations.slice(0, 12)"
          :key="station.id"
          :span="6"
        >
          <Card
            size="small"
            :title="station.name"
            class="cursor-pointer transition-shadow hover:shadow-md"
            @click="showStationDetail(station)"
          >
            <div class="space-y-2">
              <div class="flex justify-between">
                <span>状态：</span>
                <Badge
                  :status="getStatusColor(station.status)"
                  :text="getStatusText(station.status)"
                />
              </div>
              <div class="flex justify-between">
                <span>类型：</span>
                <Tag>{{ getTypeText(station.type) }}</Tag>
              </div>
              <div class="flex justify-between">
                <span>功率：</span>
                <span>{{ station.power }}kW</span>
              </div>
              <div class="flex justify-between">
                <span>流量：</span>
                <span>{{ station.flow.toFixed(1) }}m³/h</span>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
      <div
        v-if="filteredStations.length > 12"
        class="mt-4 text-center text-gray-500"
      >
        显示前12个灌站，共{{ filteredStations.length }}个
      </div>
    </Card>

    <!-- 灌站详情抽屉 -->
    <Drawer
      v-model:open="drawerVisible"
      title="灌站详细信息 - 三农大数据平台"
      width="600"
      placement="right"
    >
      <div v-if="selectedStation">
        <Tabs default-active-key="1">
          <TabPane key="1" tab="基本信息">
            <Descriptions title="基本信息" :column="1" bordered>
              <Descriptions.Item label="灌站名称">
                {{ selectedStation.name }}
              </Descriptions.Item>
              <Descriptions.Item label="运行状态">
                <Badge
                  :status="getStatusColor(selectedStation.status)"
                  :text="getStatusText(selectedStation.status)"
                />
              </Descriptions.Item>
              <Descriptions.Item label="灌站类型">
                <Tag>{{ getTypeText(selectedStation.type) }}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="设备功率">
                {{ selectedStation.power }}kW
              </Descriptions.Item>
              <Descriptions.Item label="建设年限">
                {{ selectedStation.buildYear }}年
              </Descriptions.Item>
              <Descriptions.Item label="灌溉面积">
                {{ selectedStation.irrigationArea }}亩
              </Descriptions.Item>
              <Descriptions.Item label="管护人员">
                {{ selectedStation.manager }}
              </Descriptions.Item>
              <Descriptions.Item label="联系电话">
                {{ selectedStation.phone }}
              </Descriptions.Item>
            </Descriptions>
          </TabPane>

          <TabPane key="2" tab="位置坐标">
            <Descriptions title="地理位置信息" :column="1" bordered>
              <Descriptions.Item label="经度">
                {{ selectedStation.longitude.toFixed(6) }}°
              </Descriptions.Item>
              <Descriptions.Item label="纬度">
                {{ selectedStation.latitude.toFixed(6) }}°
              </Descriptions.Item>
              <Descriptions.Item label="坐标系统">
                WGS84 (GPS坐标系)
              </Descriptions.Item>
            </Descriptions>
          </TabPane>

          <TabPane key="3" tab="线路改造">
            <Descriptions title="线路状态与改造" :column="1" bordered>
              <Descriptions.Item label="线路状态">
                <Tag
                  :color="
                    selectedStation.lineStatus === 'normal'
                      ? 'green'
                      : selectedStation.lineStatus === 'aging'
                        ? 'orange'
                        : 'blue'
                  "
                >
                  {{
                    selectedStation.lineStatus === 'normal'
                      ? '正常'
                      : selectedStation.lineStatus === 'aging'
                        ? '老旧待改造'
                        : '已更换'
                  }}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="安全等级">
                <Tag
                  :color="
                    selectedStation.safetyLevel === 'high'
                      ? 'green'
                      : selectedStation.safetyLevel === 'medium'
                        ? 'orange'
                        : 'red'
                  "
                >
                  {{
                    selectedStation.safetyLevel === 'high'
                      ? '高'
                      : selectedStation.safetyLevel === 'medium'
                        ? '中'
                        : '低'
                  }}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="改造说明">
                {{
                  selectedStation.lineStatus === 'aging'
                    ? '需要进行老旧线路更换改造，消除安全隐患'
                    : selectedStation.lineStatus === 'replaced'
                      ? '已完成线路更换改造，安全隐患已消除'
                      : '线路状态良好'
                }}
              </Descriptions.Item>
            </Descriptions>
          </TabPane>

          <TabPane key="4" tab="设备检修">
            <Descriptions title="设施设备状态" :column="1" bordered>
              <Descriptions.Item label="设备状态">
                <Tag
                  :color="
                    selectedStation.equipmentStatus === 'normal'
                      ? 'green'
                      : selectedStation.equipmentStatus === 'need_repair'
                        ? 'red'
                        : 'blue'
                  "
                >
                  {{
                    selectedStation.equipmentStatus === 'normal'
                      ? '正常'
                      : selectedStation.equipmentStatus === 'need_repair'
                        ? '需要维修'
                        : '已更换'
                  }}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="管道状态">
                <Tag
                  :color="
                    selectedStation.pipelineStatus === 'normal'
                      ? 'green'
                      : selectedStation.pipelineStatus === 'blocked'
                        ? 'red'
                        : 'blue'
                  "
                >
                  {{
                    selectedStation.pipelineStatus === 'normal'
                      ? '正常'
                      : selectedStation.pipelineStatus === 'blocked'
                        ? '堵塞'
                        : '已维修'
                  }}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="最后检查">
                {{ selectedStation.lastInspection }}
              </Descriptions.Item>
              <Descriptions.Item label="下次维护">
                {{ selectedStation.nextMaintenance }}
              </Descriptions.Item>
              <Descriptions.Item label="维修记录">
                {{ selectedStation.maintenanceRecord }}
              </Descriptions.Item>
            </Descriptions>
          </TabPane>
        </Tabs>

        <Descriptions title="实时数据" :column="1" bordered class="mt-4">
          <Descriptions.Item label="当前流量">
            {{ selectedStation.flow.toFixed(1) }}m³/h
          </Descriptions.Item>
          <Descriptions.Item label="水位">
            {{ selectedStation.waterLevel.toFixed(1) }}%
          </Descriptions.Item>
          <Descriptions.Item label="电压">
            {{ selectedStation.voltage }}V
          </Descriptions.Item>
          <Descriptions.Item label="电流">
            {{ selectedStation.current.toFixed(1) }}A
          </Descriptions.Item>
        </Descriptions>

        <Descriptions title="位置信息" :column="1" bordered class="mt-4">
          <Descriptions.Item label="经度">
            {{ selectedStation.longitude.toFixed(6) }}
          </Descriptions.Item>
          <Descriptions.Item label="纬度">
            {{ selectedStation.latitude.toFixed(6) }}
          </Descriptions.Item>
        </Descriptions>
      </div>
    </Drawer>
  </div>
</template>

<style scoped>
.ant-card-small .ant-card-head {
  min-height: 38px;
}

.ant-card-small .ant-card-body {
  padding: 12px;
}
</style>
