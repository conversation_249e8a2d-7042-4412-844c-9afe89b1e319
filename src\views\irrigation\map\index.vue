<script lang="ts" setup>
import { onMounted, ref, reactive, nextTick, computed, watch } from 'vue';
import {
  Card,
  Row,
  Col,
  Statistic,
  Select,
  Button,
  Drawer,
  Descriptions,
  Tag,
  Badge,
} from 'ant-design-vue';

defineOptions({ name: 'IrrigationMap' });

// 地图容器引用
const mapContainer = ref<HTMLDivElement>();
const map = ref<any>(null);

// 抽屉显示控制
const drawerVisible = ref(false);
const selectedStation = ref<any>(null);

// 筛选条件
const filterOptions = reactive({
  status: 'all', // all, online, offline, maintenance
  type: 'all', // all, large, medium, small, micro
});

// 模拟154座提灌站数据
const stationData = ref([
  {
    id: 1,
    name: '东风灌站',
    longitude: 104.0668,
    latitude: 30.5728,
    status: 'online',
    type: 'large',
    power: 450,
    buildYear: 2018,
    irrigationArea: 1200,
    manager: '张三',
    phone: '13800138001',
    flow: 85.6,
    waterLevel: 78.5,
    voltage: 380,
    current: 125.8,
  },
  {
    id: 2,
    name: '红旗灌站',
    longitude: 104.0868,
    latitude: 30.5928,
    status: 'maintenance',
    type: 'medium',
    power: 280,
    buildYear: 2015,
    irrigationArea: 800,
    manager: '李四',
    phone: '13800138002',
    flow: 0,
    waterLevel: 65.2,
    voltage: 0,
    current: 0,
  },
  {
    id: 3,
    name: '胜利灌站',
    longitude: 104.1068,
    latitude: 30.6128,
    status: 'online',
    type: 'small',
    power: 150,
    buildYear: 2020,
    irrigationArea: 500,
    manager: '王五',
    phone: '13800138003',
    flow: 42.3,
    waterLevel: 82.1,
    voltage: 380,
    current: 68.5,
  },
  // 添加更多模拟数据...
]);

// 生成更多模拟数据
const generateMoreStations = () => {
  const names = [
    '新华',
    '建设',
    '和平',
    '光明',
    '团结',
    '友谊',
    '前进',
    '向阳',
    '丰收',
    '希望',
  ];
  const types = ['large', 'medium', 'small', 'micro'];
  const statuses = ['online', 'offline', 'maintenance'];

  for (let i = 4; i <= 154; i++) {
    const randomName = names[Math.floor(Math.random() * names.length)];
    const randomType = types[Math.floor(Math.random() * types.length)];
    const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];

    stationData.value.push({
      id: i,
      name: `${randomName}灌站${i}`,
      longitude: 104.0668 + (Math.random() - 0.5) * 0.5,
      latitude: 30.5728 + (Math.random() - 0.5) * 0.5,
      status: randomStatus,
      type: randomType,
      power:
        randomType === 'large'
          ? 400 + Math.random() * 100
          : randomType === 'medium'
            ? 200 + Math.random() * 100
            : randomType === 'small'
              ? 100 + Math.random() * 100
              : 50 + Math.random() * 50,
      buildYear: 2010 + Math.floor(Math.random() * 14),
      irrigationArea:
        randomType === 'large'
          ? 800 + Math.random() * 400
          : randomType === 'medium'
            ? 400 + Math.random() * 400
            : randomType === 'small'
              ? 200 + Math.random() * 300
              : 100 + Math.random() * 200,
      manager: `管理员${i}`,
      phone: `138${String(i).padStart(8, '0')}`,
      flow: randomStatus === 'online' ? Math.random() * 100 : 0,
      waterLevel: 60 + Math.random() * 30,
      voltage: randomStatus === 'online' ? 380 : 0,
      current: randomStatus === 'online' ? Math.random() * 150 : 0,
    });
  }
};

// 统计数据
const statistics = computed(() => {
  const total = stationData.value.length;
  const online = stationData.value.filter((s) => s.status === 'online').length;
  const offline = stationData.value.filter(
    (s) => s.status === 'offline',
  ).length;
  const maintenance = stationData.value.filter(
    (s) => s.status === 'maintenance',
  ).length;

  return {
    total,
    online,
    offline,
    maintenance,
    onlineRate: ((online / total) * 100).toFixed(1),
  };
});

// 筛选后的数据
const filteredStations = computed(() => {
  return stationData.value.filter((station) => {
    const statusMatch =
      filterOptions.status === 'all' || station.status === filterOptions.status;
    const typeMatch =
      filterOptions.type === 'all' || station.type === filterOptions.type;
    return statusMatch && typeMatch;
  });
});

// 初始化地图
const initMap = async () => {
  // 这里需要引入高德地图API
  // 由于是演示，我们创建一个模拟的地图容器
  if (!mapContainer.value) return;

  // 模拟地图初始化
  console.log('地图初始化完成');

  // 在实际项目中，这里会是：
  // const AMap = window.AMap;
  // map.value = new AMap.Map(mapContainer.value, {
  //   zoom: 11,
  //   center: [104.0668, 30.5728],
  // });

  // 添加标记点
  addMarkers();
};

// 添加地图标记
const addMarkers = () => {
  // 模拟添加标记点
  filteredStations.value.forEach((station) => {
    // 在实际项目中，这里会创建地图标记
    console.log(`添加标记: ${station.name}`);
  });
};

// 显示灌站详情
const showStationDetail = (station: any) => {
  selectedStation.value = station;
  drawerVisible.value = true;
};

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case 'online':
      return 'success';
    case 'offline':
      return 'error';
    case 'maintenance':
      return 'warning';
    default:
      return 'default';
  }
};

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'online':
      return '在线';
    case 'offline':
      return '离线';
    case 'maintenance':
      return '维修中';
    default:
      return '未知';
  }
};

// 获取类型文本
const getTypeText = (type: string) => {
  switch (type) {
    case 'large':
      return '大型';
    case 'medium':
      return '中型';
    case 'small':
      return '小型';
    case 'micro':
      return '微型';
    default:
      return '未知';
  }
};

onMounted(async () => {
  generateMoreStations();
  await nextTick();
  initMap();
});

// 监听筛选条件变化
watch([() => filterOptions.status, () => filterOptions.type], () => {
  addMarkers();
});
</script>

<template>
  <div class="p-5">
    <!-- 统计概览 -->
    <Card title="灌站分布概览" class="mb-5">
      <Row :gutter="16">
        <Col :span="4">
          <Statistic
            title="总数量"
            :value="statistics.total"
            suffix="座"
            :value-style="{ color: '#1890ff' }"
          />
        </Col>
        <Col :span="4">
          <Statistic
            title="在线"
            :value="statistics.online"
            suffix="座"
            :value-style="{ color: '#52c41a' }"
          />
        </Col>
        <Col :span="4">
          <Statistic
            title="离线"
            :value="statistics.offline"
            suffix="座"
            :value-style="{ color: '#ff4d4f' }"
          />
        </Col>
        <Col :span="4">
          <Statistic
            title="维修中"
            :value="statistics.maintenance"
            suffix="座"
            :value-style="{ color: '#faad14' }"
          />
        </Col>
        <Col :span="4">
          <Statistic
            title="在线率"
            :value="statistics.onlineRate"
            suffix="%"
            :value-style="{ color: '#722ed1' }"
          />
        </Col>
      </Row>
    </Card>

    <!-- 筛选控制 -->
    <Card title="筛选条件" class="mb-5">
      <Row :gutter="16" align="middle">
        <Col :span="4">
          <span class="mr-2">运行状态：</span>
          <Select v-model:value="filterOptions.status" style="width: 120px">
            <Select.Option value="all">全部</Select.Option>
            <Select.Option value="online">在线</Select.Option>
            <Select.Option value="offline">离线</Select.Option>
            <Select.Option value="maintenance">维修中</Select.Option>
          </Select>
        </Col>
        <Col :span="4">
          <span class="mr-2">灌站类型：</span>
          <Select v-model:value="filterOptions.type" style="width: 120px">
            <Select.Option value="all">全部</Select.Option>
            <Select.Option value="large">大型</Select.Option>
            <Select.Option value="medium">中型</Select.Option>
            <Select.Option value="small">小型</Select.Option>
            <Select.Option value="micro">微型</Select.Option>
          </Select>
        </Col>
        <Col :span="4">
          <Button type="primary" @click="addMarkers">刷新地图</Button>
        </Col>
      </Row>
    </Card>

    <!-- 地图容器 -->
    <Card title="灌站地图分布" class="mb-5">
      <div
        ref="mapContainer"
        class="flex h-96 w-full items-center justify-center bg-gray-100 text-gray-500"
      >
        <div class="text-center">
          <div class="mb-2 text-lg">高德地图容器</div>
          <div class="text-sm">请配置高德地图API Key后显示实际地图</div>
          <div class="mt-2 text-sm">
            当前显示 {{ filteredStations.length }} 个灌站标记点
          </div>
        </div>
      </div>
    </Card>

    <!-- 灌站列表 -->
    <Card title="灌站列表">
      <Row :gutter="[16, 16]">
        <Col
          v-for="station in filteredStations.slice(0, 12)"
          :key="station.id"
          :span="6"
        >
          <Card
            size="small"
            :title="station.name"
            class="cursor-pointer transition-shadow hover:shadow-md"
            @click="showStationDetail(station)"
          >
            <div class="space-y-2">
              <div class="flex justify-between">
                <span>状态：</span>
                <Badge
                  :status="getStatusColor(station.status)"
                  :text="getStatusText(station.status)"
                />
              </div>
              <div class="flex justify-between">
                <span>类型：</span>
                <Tag>{{ getTypeText(station.type) }}</Tag>
              </div>
              <div class="flex justify-between">
                <span>功率：</span>
                <span>{{ station.power }}kW</span>
              </div>
              <div class="flex justify-between">
                <span>流量：</span>
                <span>{{ station.flow.toFixed(1) }}m³/h</span>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
      <div
        v-if="filteredStations.length > 12"
        class="mt-4 text-center text-gray-500"
      >
        显示前12个灌站，共{{ filteredStations.length }}个
      </div>
    </Card>

    <!-- 灌站详情抽屉 -->
    <Drawer
      v-model:open="drawerVisible"
      title="灌站详细信息"
      width="500"
      placement="right"
    >
      <div v-if="selectedStation">
        <Descriptions title="基本信息" :column="1" bordered>
          <Descriptions.Item label="灌站名称">
            {{ selectedStation.name }}
          </Descriptions.Item>
          <Descriptions.Item label="运行状态">
            <Badge
              :status="getStatusColor(selectedStation.status)"
              :text="getStatusText(selectedStation.status)"
            />
          </Descriptions.Item>
          <Descriptions.Item label="灌站类型">
            <Tag>{{ getTypeText(selectedStation.type) }}</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="设备功率">
            {{ selectedStation.power }}kW
          </Descriptions.Item>
          <Descriptions.Item label="建设年限">
            {{ selectedStation.buildYear }}年
          </Descriptions.Item>
          <Descriptions.Item label="灌溉面积">
            {{ selectedStation.irrigationArea }}亩
          </Descriptions.Item>
          <Descriptions.Item label="管护人员">
            {{ selectedStation.manager }}
          </Descriptions.Item>
          <Descriptions.Item label="联系电话">
            {{ selectedStation.phone }}
          </Descriptions.Item>
        </Descriptions>

        <Descriptions title="实时数据" :column="1" bordered class="mt-4">
          <Descriptions.Item label="当前流量">
            {{ selectedStation.flow.toFixed(1) }}m³/h
          </Descriptions.Item>
          <Descriptions.Item label="水位">
            {{ selectedStation.waterLevel.toFixed(1) }}%
          </Descriptions.Item>
          <Descriptions.Item label="电压">
            {{ selectedStation.voltage }}V
          </Descriptions.Item>
          <Descriptions.Item label="电流">
            {{ selectedStation.current.toFixed(1) }}A
          </Descriptions.Item>
        </Descriptions>

        <Descriptions title="位置信息" :column="1" bordered class="mt-4">
          <Descriptions.Item label="经度">
            {{ selectedStation.longitude.toFixed(6) }}
          </Descriptions.Item>
          <Descriptions.Item label="纬度">
            {{ selectedStation.latitude.toFixed(6) }}
          </Descriptions.Item>
        </Descriptions>
      </div>
    </Drawer>
  </div>
</template>

<style scoped>
.ant-card-small .ant-card-head {
  min-height: 38px;
}

.ant-card-small .ant-card-body {
  padding: 12px;
}
</style>
