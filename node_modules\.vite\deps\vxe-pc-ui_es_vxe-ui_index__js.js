import {
  config,
  setup,
  ui_default,
  version
} from "./chunk-KAYKK4JO.js";
import {
  GLOBAL_EVENT_KEYS,
  VxeCore,
  VxeUI,
  checkVersion,
  clipboard,
  commands,
  component,
  coreVersion,
  createEvent,
  formats,
  getComponent,
  getConfig,
  getI18n,
  getIcon,
  getLanguage,
  getSlotVNs,
  getTheme,
  globalEvents,
  globalResize,
  globalStore,
  handleCheckInfo,
  hasComponent,
  hasLanguage,
  hooks,
  interceptor,
  log,
  menus,
  permission,
  renderCustomIcon,
  renderEmptyElement,
  renderGlobalIcon,
  renderer,
  setConfig,
  setI18n,
  setIcon,
  setLanguage,
  setTheme,
  use,
  useFns,
  usePermission,
  useSize,
  validators
} from "./chunk-KJAC55GV.js";
import "./chunk-G6CDOZZI.js";
import "./chunk-ZCM5A7SR.js";
import "./chunk-WDDBQLJB.js";
import "./chunk-V4OQ3NZ2.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.9.29_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-ui/index.js
var vxe_ui_default = ui_default;
export {
  GLOBAL_EVENT_KEYS,
  VxeCore,
  VxeUI,
  checkVersion,
  clipboard,
  commands,
  component,
  config,
  coreVersion,
  createEvent,
  vxe_ui_default as default,
  formats,
  getComponent,
  getConfig,
  getI18n,
  getIcon,
  getLanguage,
  getSlotVNs,
  getTheme,
  globalEvents,
  globalResize,
  globalStore,
  handleCheckInfo,
  hasComponent,
  hasLanguage,
  hooks,
  interceptor,
  log,
  menus,
  permission,
  renderCustomIcon,
  renderEmptyElement,
  renderGlobalIcon,
  renderer,
  setConfig,
  setI18n,
  setIcon,
  setLanguage,
  setTheme,
  setup,
  use,
  useFns,
  usePermission,
  useSize,
  validators,
  version
};
//# sourceMappingURL=vxe-pc-ui_es_vxe-ui_index__js.js.map
