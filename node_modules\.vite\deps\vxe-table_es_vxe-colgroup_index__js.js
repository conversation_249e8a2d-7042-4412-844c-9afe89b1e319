import {
  columnProps
} from "./chunk-RWIJGK6Q.js";
import {
  assembleColumn,
  cell_default,
  destroyColumn,
  watchColumn
} from "./chunk-ZPB2PSRD.js";
import {
  defineVxeComponent
} from "./chunk-H6N5ZCWJ.js";
import "./chunk-4QY7LSTU.js";
import "./chunk-RWHEUJNV.js";
import {
  VxeUI
} from "./chunk-KJAC55GV.js";
import "./chunk-G6CDOZZI.js";
import {
  createCommentVNode,
  h,
  inject,
  onMounted,
  onUnmounted,
  provide,
  ref
} from "./chunk-ZCM5A7SR.js";
import "./chunk-WDDBQLJB.js";
import "./chunk-V4OQ3NZ2.js";

// ../../node_modules/.pnpm/vxe-table@4.16.11_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/table/src/group.js
var group_default = defineVxeComponent({
  name: "VxeColgroup",
  props: columnProps,
  setup(props, { slots }) {
    const refElem = ref();
    const $xeTable = inject("$xeTable", null);
    const $xeParentColgroup = inject("$xeColgroup", null);
    if (!$xeTable) {
      return () => createCommentVNode();
    }
    const columnConfig = cell_default.createColumn($xeTable, props);
    const columnSlots = {};
    if (slots.header) {
      columnSlots.header = slots.header;
    }
    columnConfig.slots = columnSlots;
    columnConfig.children = [];
    watchColumn($xeTable, props, columnConfig);
    onMounted(() => {
      const elem = refElem.value;
      if (elem) {
        assembleColumn($xeTable, elem, columnConfig, $xeParentColgroup);
      }
    });
    onUnmounted(() => {
      destroyColumn($xeTable, columnConfig);
    });
    const renderVN = () => {
      return h("div", {
        ref: refElem
      }, slots.default ? slots.default() : []);
    };
    const $xeColgroup = { columnConfig };
    provide("$xeColgroup", $xeColgroup);
    provide("$xeGrid", null);
    provide("$xeGantt", null);
    return renderVN;
  }
});

// ../../node_modules/.pnpm/vxe-table@4.16.11_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/colgroup/index.js
var VxeColgroup = Object.assign({}, group_default, {
  install(app) {
    app.component(group_default.name, group_default);
    app.component("VxeTableColgroup", group_default);
  }
});
if (VxeUI.dynamicApp) {
  VxeUI.dynamicApp.component(group_default.name, group_default);
  VxeUI.dynamicApp.component("VxeTableColgroup", group_default);
}
VxeUI.component(group_default);
var Colgroup = VxeColgroup;
var colgroup_default = VxeColgroup;

// ../../node_modules/.pnpm/vxe-table@4.16.11_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/vxe-colgroup/index.js
var vxe_colgroup_default = colgroup_default;
export {
  Colgroup,
  VxeColgroup,
  vxe_colgroup_default as default
};
//# sourceMappingURL=vxe-table_es_vxe-colgroup_index__js.js.map
