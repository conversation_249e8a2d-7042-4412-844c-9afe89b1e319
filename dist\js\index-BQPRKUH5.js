import{bu as B}from"./bootstrap-CFDAkNgp.js";import{_ as N,a as V,S as P,b as R,d as A,c as F}from"./analysis-overview.vue_vue_type_script_setup_true_lang-CtUzIjiz.js";import{C as p}from"./index-DOXLVRHg.js";import{R as D,C as s,S as n}from"./index-CHRdc_8y.js";import{P as E}from"./index-Ci1_yKfn.js";import{u as v,_}from"./use-echarts-Dor8gHz9.js";import{T as $}from"./index-DEfsrzsO.js";import{B as x}from"./index-DXEBJLLx.js";import{T as L}from"./index-BhH5F5SY.js";import{a4 as O,T as W,P as u,a9 as j,av as g,ab as r,x as e,ac as a,aB as i,a7 as t,aa as c,aq as w,F as q,ai as z,aj as M}from"../jse/index-index-B2UBupFX.js";import"./index-DpxZFE0y.js";import"./TabsList.vue_vue_type_script_setup_true_lang-DM4no0zC.js";import"./CardTitle.vue_vue_type_script_setup_true_lang-DM2nXXp9.js";import"./index-1BiZfdtR.js";import"./shallowequal-CNCY1mYq.js";import"./Trigger-D2zZP_An.js";import"./ResizeObserver.es-CDE7jhPe.js";import"./_arrayIncludes-B8uzE354.js";import"./Overflow--DaGju1E.js";import"./index-DQZjs6Lb.js";import"./index-tPQFuBU-.js";import"./colors-KzMfSzFw.js";import"./collapseMotion-DiwOar_A.js";import"./slide-BhgK1D9k.js";import"./useRefs-f0KzY-v7.js";import"./hasIn-Bt_d2Zq4.js";import"./isMobile-8sZ0LT6r.js";import"./useMergedState-C4x1IDb9.js";import"./isPlainObject-0t1li2J1.js";import"./Col-Bjak4A2I.js";import"./useFlexGapSupport-TvfJoknb.js";import"./CheckOutlined-CZeS3E7o.js";import"./index-DYfzhziO.js";import"./index-B2Lu6Z2W.js";import"./BaseInput-Dslq5mxC.js";import"./DownOutlined-CVWF16Fu.js";import"./SearchOutlined-DqQ4RgbY.js";import"./move-IXaXzbNk.js";import"./index-C5usYyko.js";import"./index-DfHX-m0D.js";import"./index-CGqxGK2L.js";import"./FormItemContext-CoieKSxA.js";import"./statusUtils-D62pPzYs.js";import"./index-C5ScQeGh.js";import"./index-BDBY1qBK.js";import"./Checkbox-Dt0Z6J8a.js";import"./index-UTpExPeP.js";import"./index-BCzosP6o.js";import"./EyeOutlined-MQsW2UzL.js";import"./debounce-CesRCMoz.js";const X={class:"p-5"},G=O({name:"IrrigationOverview",__name:"index",setup(H){const T=[{icon:P,title:"在线灌站",totalTitle:"总灌站数",totalValue:154,value:142},{icon:R,title:"运行中",totalTitle:"运行率",totalValue:92.2,value:142},{icon:A,title:"故障报警",totalTitle:"今日报警",totalValue:8,value:3},{icon:F,title:"维修中",totalTitle:"维修完成率",totalValue:85.5,value:12}],l=W({totalFlow:2856.8,totalPower:1245.6,irrigationArea:8520,waterLevel:85.2}),h=[{label:"流量趋势",value:"flow"},{label:"功率监控",value:"power"}],f=u(),{renderEcharts:k}=v(f),d=u(),{renderEcharts:b}=v(d),C=[{title:"灌站名称",dataIndex:"stationName",key:"stationName"},{title:"报警类型",dataIndex:"alarmType",key:"alarmType"},{title:"报警时间",dataIndex:"alarmTime",key:"alarmTime"},{title:"状态",dataIndex:"status",key:"status"}],I=u([{key:"1",stationName:"东风灌站",alarmType:"水泵故障",alarmTime:"2024-01-15 14:30:25",status:"processing"},{key:"2",stationName:"红旗灌站",alarmType:"电压异常",alarmTime:"2024-01-15 13:45:12",status:"processing"},{key:"3",stationName:"胜利灌站",alarmType:"流量异常",alarmTime:"2024-01-15 12:20:08",status:"resolved"}]);return j(()=>{k({title:{text:"24小时流量趋势",left:"center"},tooltip:{trigger:"axis",formatter:"{b}: {c} m³/h"},xAxis:{type:"category",data:Array.from({length:24}).map((S,o)=>`${o}:00`)},yAxis:{type:"value",name:"流量(m³/h)"},series:[{name:"流量",type:"line",smooth:!0,data:[2200,2100,2e3,1950,1900,2e3,2200,2400,2600,2800,2900,3e3,3100,3200,3150,3100,3e3,2900,2800,2700,2600,2500,2400,2300],areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(24, 144, 255, 0.6)"},{offset:1,color:"rgba(24, 144, 255, 0.1)"}]}}}]}),b({title:{text:"功率分布",left:"center"},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} kW ({d}%)"},series:[{name:"功率分布",type:"pie",radius:["40%","70%"],data:[{value:450,name:"大型灌站"},{value:380,name:"中型灌站"},{value:280,name:"小型灌站"},{value:135,name:"微型灌站"}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]})}),(S,o)=>(r(),g("div",X,[e(t(p),{class:"mb-5"},{default:a(()=>o[0]||(o[0]=[i("div",{class:"text-center"},[i("h1",{class:"mb-2 text-3xl font-bold text-blue-600"},"三农大数据平台")],-1)])),_:1,__:[0]}),e(t(N),{items:T}),e(t(p),{title:"实时监控数据",class:"mt-5"},{default:a(()=>[e(t(D),{gutter:16},{default:a(()=>[e(t(s),{span:6},{default:a(()=>[e(t(n),{title:"总流量",value:l.totalFlow,suffix:"m³/h","value-style":{color:"#1890ff"}},null,8,["value"])]),_:1}),e(t(s),{span:6},{default:a(()=>[e(t(n),{title:"总功率",value:l.totalPower,suffix:"kW","value-style":{color:"#52c41a"}},null,8,["value"])]),_:1}),e(t(s),{span:6},{default:a(()=>[e(t(n),{title:"灌溉面积",value:l.irrigationArea,suffix:"亩","value-style":{color:"#faad14"}},null,8,["value"])]),_:1}),e(t(s),{span:6},{default:a(()=>[i("div",null,[o[1]||(o[1]=i("div",{class:"mb-2 text-sm text-gray-500"},"水位监控",-1)),e(t(E),{percent:l.waterLevel,"stroke-color":{"0%":"#108ee9","100%":"#87d068"}},null,8,["percent"])])]),_:1})]),_:1})]),_:1}),e(t(V),{tabs:h,class:"mt-5"},{flow:a(()=>[e(t(_),{ref_key:"flowChartRef",ref:f,style:{height:"400px"}},null,512)]),power:a(()=>[e(t(_),{ref_key:"powerChartRef",ref:d,style:{height:"400px"}},null,512)]),_:1}),e(t(p),{title:"最新报警信息",class:"mt-5"},{default:a(()=>[e(t($),{columns:C,"data-source":I.value,pagination:!1,size:"small"},{bodyCell:a(({column:y,record:m})=>[y.key==="status"?(r(),g(q,{key:0},[m.status==="processing"?(r(),c(t(x),{key:0,status:"processing",text:"处理中"})):m.status==="resolved"?(r(),c(t(x),{key:1,status:"success",text:"已解决"})):w("",!0)],64)):y.key==="alarmType"?(r(),c(t(L),{key:1,color:"red"},{default:a(()=>[z(M(m.alarmType),1)]),_:2},1024)):w("",!0)]),_:1},8,["data-source"])]),_:1})]))}}),Mt=B(G,[["__scopeId","data-v-f18a502f"]]);export{Mt as default};
